# MCP Servers Configuration

This directory contains an example `mcp_servers.json` file that demonstrates how to configure various MCP (Model Context Protocol) servers for use with the Orbot automation server.

## Configuration Structure

Each server entry in the JSON array has the following structure:

```json
{
  "name": "server-name",           // Unique identifier for the server
  "type": "stdio",                 // Server type: "stdio" or "sse"
  "command": "npx",                // Command to execute (stdio only)
  "args": ["package", "args"],     // Command arguments (stdio only)
  "url": "https://...",           // Server URL (sse only)
  "env": {                        // Environment variables
    "KEY": "${ENV_VAR}"           // Supports ${VAR} substitution
  },
  "metadata": {                   // Optional metadata
    "description": "...",
    "requires": ["..."]
  }
}
```

## Environment Variable Substitution

The configuration supports environment variable substitution using the `${VAR_NAME}` syntax. This allows you to keep sensitive information like API keys out of the configuration file.

## Available Servers

### Basic Servers

1. **filesystem** - File system operations
   - Access: `/tmp` directory by default
   - No additional configuration required

2. **memory** - In-memory key-value storage
   - Useful for maintaining conversation context
   - No additional configuration required

### Browser Automation

3. **__sys__playwright** - Playwright browser automation
   - Special server that integrates with the runtime browser session
   - Automatically receives CDP endpoint when available

4. **puppeteer** - Alternative browser automation
   - Runs in headless mode by default
   - Independent of the runtime browser

### Developer Tools

5. **github** - GitHub repository operations
   - Requires: `GITHUB_TOKEN` environment variable
   - Provides: Repository management, issues, PRs, etc.

6. **git** - Local git operations
   - Works with current directory by default
   - No additional configuration required

### Databases

7. **sqlite** - SQLite database operations
   - Default path: `${HOME}/databases`
   - Can be customized via args

8. **postgres** - PostgreSQL operations
   - Requires: `DATABASE_URL` environment variable
   - Full database management capabilities

### Cloud & Infrastructure

9. **aws** - AWS services integration
   - Requires: AWS credentials (`AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`, `AWS_REGION`)
   - Provides: EC2, S3, Lambda, and other AWS service operations

10. **docker** - Docker container management
    - Requires: Docker daemon running
    - Uses `uvx` command instead of `npx`

11. **kubernetes** - Kubernetes cluster operations
    - Requires: `KUBECONFIG` or default k8s config
    - Full cluster management capabilities

### Communication & Collaboration

12. **slack** - Slack workspace integration
    - Requires: `SLACK_BOT_TOKEN` and `SLACK_APP_TOKEN`
    - Send messages, manage channels, etc.

13. **jira** - Atlassian Jira integration
    - Requires: `JIRA_URL`, `JIRA_EMAIL`, `JIRA_API_TOKEN`
    - Issue tracking and project management

### Other Services

14. **google-drive** - Google Drive operations
    - Requires: `GOOGLE_DRIVE_CREDENTIALS`
    - File management in Google Drive

15. **stripe** - Payment processing
    - Requires: `STRIPE_SECRET_KEY`
    - Payment and subscription management

## Usage

### Automatic Loading

Place the `mcp_servers.json` file in the project root. The system will automatically load and connect to these servers on startup.

### Environment Variables

You can also configure servers via the `MCP_SERVERS` environment variable:

```bash
export MCP_SERVERS='[{"name": "filesystem", "type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "/tmp"]}]'
```

### Manual Connection

Users can manually connect to MCP servers using chat commands:

```
/mcp connect {"name": "github", "type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-github"], "env": {"GITHUB_TOKEN": "your-token"}}
```

## Security Considerations

1. **Environment Variables**: Always use environment variable substitution for sensitive data
2. **Allowed Executables**: Only `npx` and `uvx` are allowed by default (configured in `.chainlit/config.toml`)
3. **File System Access**: Be careful with filesystem server paths - limit access to necessary directories
4. **Network Access**: SSE servers make HTTP connections - ensure you trust the endpoints

## Troubleshooting

- If a server fails to connect, check the logs for error messages
- Ensure required environment variables are set
- Verify that the MCP server packages are installed (`npm install -g @modelcontextprotocol/server-*`)
- For Python-based servers, ensure `uvx` is available (`pip install uvx`)

## Custom Servers

You can create custom MCP servers by implementing the Model Context Protocol. See the [MCP documentation](https://modelcontextprotocol.io) for details on creating custom servers.