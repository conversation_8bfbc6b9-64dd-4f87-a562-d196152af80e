syntax = "proto3";

package common;

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/common";

message ReviewerList {
  // Will be deprecated once we shift assignment strategy to
  // assignment_config_options
  repeated WorkflowUser users = 1 [deprecated=true];
  // Round_number will start from 1
  int32 round_number = 2;
  // review_trigger_condition stores whether the round will require a review from human or not
  ReviewTriggerCondition trigger_condition = 3;
  // AssignmentConfigOptions stores different strategies to follow while
  // assigning user to a task
  WorkflowAssignmentOption assignment_option = 4;
}

message WorkflowUser {
  string user = 1;
  // Allows an user to reject future tasks from this workflow.
  bool enabled = 2;
}


message ReviewTriggerCondition {
  // percent_of_random_sample moved inside params so this is deprecated
  float percent_of_random_sample = 1 [deprecated=true];
  ConditionType condition_type = 2;
  // Contain all the params required for different condition types
  ConditionOptions condition_options = 3;
}

enum ConditionType {
  CONDITION_TYPE_UNSPECIFIED = 0; // Default value if none is specified.
  CONDITION_TYPE_ANY_EXTRACTED_FIELD = 1;
  CONDITION_TYPE_AVERAGE_CONFIDENCE_SCORE = 2;
  CONDITION_TYPE_SPECIFIC_EXTRACTED_FIELD = 3;
  CONDITION_TYPE_ANY_EMPTY_PREDICTIONS = 4;
  CONDITION_TYPE_RANDOM_SAMPLE_PERCENT = 5;
}


enum LogicalOperator {
  LOGICAL_OPERATOR_UNSPECIFIED = 0;
  LOGICAL_OPERATOR_AND = 1;
  LOGICAL_OPERATOR_OR = 2;
  LOGICAL_OPERATOR_NOT = 3;
}

message CompositeGroupCondition {
  LogicalOperator logical_operator = 1;
  repeated Condition conditions = 2;
  repeated CompositeGroupCondition nested_conditions = 3;
  int32 group_index = 4;
}

message ConditionOptions {
  oneof param {
    // group_condition will contain all the specific attribute conditions i.e xyz > 20
    CompositeGroupCondition group_condition = 1;
    // percent_of_random_sample moved here also as we are using condition types
    float percent_of_random_sample = 2;
    // confidence_score is for avg and any extracted field condition types
    float confidence_score = 3;
  }
}


message Condition {
  // We should no longer use attribute, it will be deprecated soon, use attribute_type instead
  string attribute = 1 [deprecated=true];
  Operator operator = 2;
  string value = 3;
  AttributeType attribute_type = 4;
}

message AttributeType {
  // For simple entity, parent will be empty
  string parent = 1;
  string name = 2;
}

enum Operator {
  OPERATOR_UNSPECIFIED = 0;
  OPERATOR_EQUAL = 2;
  OPERATOR_LESS_THAN = 3;
  OPERATOR_GREATER_THAN = 4;
  OPERATOR_CONTAINS = 5;
  OPERATOR_EXISTS = 6;
  OPERATOR_DOES_NOT_EXIST = 7;
  OPERATOR_GREATER_THAN_EQUAL = 8;
  OPERATOR_LESS_THAN_EQUAL = 9;
}

message WorkflowAssignmentOption{
  // It takes priority over basic round robin assignment but not over manual assignment
  // Set of conditions to be evaluated for corresponding assignment to user group
  repeated ConditionalAssignment conditional_assignment = 1;
  // Basic round robin Configurations
  BasicRoundRobin basic_round_robin = 2;
  // If the admin wants to leave the assignment to themselves or a group of
  // user can directly assign themselves, this can be configured.
  // Note: If this field is set, the other assignment options will be ignored.
  ManualAssignment manual_assignment = 3;
}


message ConditionalAssignment {
  CompositeGroupCondition group_condition = 1;
  repeated WorkflowUser users = 2;
}

message BasicRoundRobin {
  // max number of tasks to be assigned to a reviewer
  // Current default is 10
  int32 number_of_tasks = 1;
  repeated WorkflowUser users = 2;
}

message ManualAssignment {
  repeated WorkflowUser users = 1;
}