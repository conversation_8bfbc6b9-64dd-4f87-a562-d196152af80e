syntax = "proto3";

package common;

import "buf/validate/validate.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/common";

enum AnnouncementType {
  UNSPECIFIED = 0;
  RELEASE_NOTES = 1;
}

message AnnouncementContentBlock {
  AnnouncementContentMarkdown body = 1 [(buf.validate.field).required = true];
}

message AnnouncementContentMarkdown {
  string data = 1 [(buf.validate.field).string.min_len = 1];
}
