syntax = "proto3";

package common;

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/common";

message Hyperparameter {
  // Resource name of hyperparamerter => hyperparameter/{hyperparameter_id}
  string name = 1;
  string display_name = 2;
  // FewShotTunableConfig is used to configure the few-shot model, 
  // pipeline and temperature.
  FewShotTunableConfig few_shot_tunable_config = 3;
  // DocumentClassificationTunableConfig is used to configure the document 
  // classification model.
  DocumentClassificationTunableConfig document_classification_tunable_config = 4;
  // FetchSimilarDocumentsTunableConfig is used to configure the fetch similar 
  // documents count and model type.
  FetchSimilarDocumentsTunableConfig fetch_similar_documents_tunable_config = 5;
}

message FewShotTunableConfig {
  optional string temperature = 1;
  // Specifies the parameters to be used for Nucleus Sampling.
  NucleusSamplingParameters nucleus_sampling_parameters = 2;
  optional int32 max_chunk_size = 3;
  string pipeline_type = 4;
  string model_name = 5;
  string model_family = 6;
  // ModelInfo used to specify the transcription and extraction model.
  ModelInfo model_info = 7;
  optional bool remove_entities_without_bbox = 8;
  // EntityCalibration is used to specify the calibration maps for simple and
  // child entities.
  repeated EntityCalibration simple_entity_calibration = 9;
  repeated EntityCalibration child_entity_calibration = 10;
  // If set, disables rolling the LLM token output
  optional bool disable_rolling_llm_token_output = 11;
  // If set, disables the REM fallback searching approach
  optional bool disable_rem_fallback = 12;
  // Maximum number of times to retry getting a complete answer from the LLM if
  // it gets cut off mid-response
  optional int32 max_answer_rolls = 13;
  // Extra rules for entity extraction quality improvements
  optional string extra_rules = 14;
  // Automatic confidence calibration settings
  AutoConfidenceCalibrationConfig auto_confidence_calibration_config = 15;
  // Enable judgement
  optional bool enable_judgement = 16;
  // Judgement pipeline type
  optional string judgement_pipeline_type = 17;
  // Judgement model info
  optional ModelInfo judgement_model_info = 18;
}

message DocumentClassificationTunableConfig {
  string model_name = 1;
  string model_family = 2;
  string pipeline_type = 3;
  map<string, string> label_to_description_map = 4;
  string fallback_model = 5;
}

message FetchSimilarDocumentsTunableConfig {
  // Number of most similar examples to fetch from selection pool
  optional int32 fetch_count = 1;
  string model_name = 2;
  string model_family = 3;
  // Max number of examples to fetch from previous ground truth docs
  optional int32 max_examples_in_selection_pool = 4;
}

message NucleusSamplingParameters {
  optional string top_p = 1;
  optional int32 top_k = 2;
  optional int32 candidate_count = 3;
}

message ModelSpec {
  string model_name = 1;
  string fallback_model = 2;
}

message ModelInfo {
  ModelSpec transcription_model_spec = 1;
  ModelSpec extraction_model_spec = 2;
}

message EntityCalibration {
  float uncalibrated_confidence = 1;
  float calibrated_confidence = 2;
}

message AutoConfidenceCalibrationConfig {
  bool enable = 1;
  optional int32 num_tasks_between_calibrations = 2;
}
