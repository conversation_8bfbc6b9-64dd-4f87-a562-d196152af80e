syntax = "proto3";

package pb.v1alpha2;

import "google/protobuf/empty.proto";
import "pb/v1alpha1/orbot_workflow.proto";
import "pb/v1alpha2/connector.proto";
import "pb/v1alpha2/process_discovery_service.proto";
import "pb/v1alpha2/workflows_service.proto";
import "pb/v1alpha2/tasks_service.proto";
import "google/protobuf/field_mask.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha2";

service ArchivedResources {
  rpc ListArchivedResources(ListArchivedResourcesRequest) returns (ListArchivedResourcesResponse) {}
  rpc RestoreArchivedResources(RestoreArchivedResourcesRequest) returns (google.protobuf.Empty) {}
  // RestoreDeletedResources initiates an asynchronous restoration of specified resources.
  // This operation returns immediately, allowing the restoration process to complete in the background.
  rpc RestoreDeletedResources(RestoreDeletedResourcesRequest) returns (RestoreDeletedResourcesResponse) {}
}

message ListArchivedResourcesRequest {
  // Organization resource name. Format: organizations/{ID}
  string org_resource_name = 1;
  // Supported filter: "display_name_prefix={SEARCH_KEY},type={workflow|task|connector|execution|process}"
  // By default, the filter will contain the value of type = {task}.
  string filter = 2;
  // Use this to send only relevant data in response
  // - If Field Mask is not send or is sent with empty paths then the result will contain
  //    the complete object
  // - Valid values for field mask are: display_name, deleted_object_info
  // - Field mask will always contain `name` field. Please donot send it in Paths to avoid errors.
  google.protobuf.FieldMask field_mask = 3;
  // Default is 5 (when page_size is missing or set to 0). Max value is 20.
  // Ordered by resource display name.
  int32 page_size = 4;
  int32 page_number = 5;
}

message ArchivedResource {
  oneof resource {
    Workflow workflow = 1;
    Task task = 2;
    Connector connector = 3;
    v1alpha1.WorkflowTask execution = 4;
    v1alpha2.UserDefinedProcess process = 5;
  }
}

message ListArchivedResourcesResponse {
  repeated ArchivedResource archived_resource = 1;
  // Total available resource size.
  // Note it is NOT the remaining available resource size after the current response.
  int32 total_size = 2;
}

message RestoreArchivedResourcesRequest {
  // Name of the resource
  repeated string name = 1;
  // Organization resource name. Format: organizations/{ID}
  string org_resource_name = 2;
}

message RestoreDeletedResourcesRequest {
  // Resources to restore
  repeated ArchivedResource deleted_resources = 1;
}

message RestoreDeletedResourcesResponse {
  // Operation ID for tracking the restoration process in Temporal.
  // Currently, there is no provision for the frontend to query for results,
  // but this might be added in the future if needed.
  string operation_id = 1;
}
