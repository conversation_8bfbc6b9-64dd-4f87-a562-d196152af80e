syntax = "proto3";

package pb.v1alpha2;

import "automation_mining/ontology/data_models.proto";
import "buf/validate/validate.proto";
import "common/user_profile.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "pb/v1alpha1/execution_warning.proto";
import "pb/v1alpha1/performance.proto";
import "common/common.proto";
import "pb/v1alpha2/execution_steps.proto";
import "pb/v1alpha2/workflows_service.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha2";


service Tasks {
  rpc CreateTask(CreateTaskRequest) returns (Task) {}
  // The difference between CreateBatchTasks and CreateTask are:
  //   1. CreateBatchTasks only supports API automation tasks.
  //   2. CreateBatchTasks returns task ids instead of an empty Task object.
  rpc CreateBatchTasks(CreateBatchTasksRequest) returns (CreateBatchTasksResponse) {}
  rpc ListTasks(ListTasksRequest) returns (ListTasksResponse) {}
  rpc UpdateTask(UpdateTaskRequest) returns (Task) {}
  rpc UpdateBatchTasks(UpdateBatchTasksRequest) returns (UpdateBatchTasksResponse) {}
  rpc GetTask(GetTaskRequest) returns (Task) {}
  rpc DeleteTask(DeleteTaskRequest) returns (google.protobuf.Empty){}
  rpc DeleteBatchTasks(DeleteBatchTasksRequest) returns (DeleteBatchTasksResponse) {}
  // Copies tasks from one workflow to another, request user needs to be the admin of both the workflows
  rpc CopyTasks(CopyTasksRequest) returns (CopyTasksResponse) {}
  rpc RetryTasks(RetryTasksRequest) returns (RetryTasksResponse) {}

  // This api is used to update tasks generated by Orbot executions
  rpc UpdateReviewTask(UpdateReviewTaskRequest) returns (Task) {}

  // This api is used to export tasks based on a filter or all tasks in the org
  // to a csv file. The csv file will be sent to the user's email asynchrounously.
  // The csv file will contain the data for the users to be able to analyze the performance 
  // of the tasks. 
  rpc ExportTasks(ExportTasksRequest) returns (ExportTasksResponse) {}
  // This api is used to download the csv file containing the tasks data.
  // This will validate the user's token and send the signed url to the user
  // if the user is authorized to download the file.
  // Else it will return a permission denied error.
  rpc ExportTasksDownloadFile(ExportTasksDownloadFileRequest) 
    returns (ExportTasksDownloadFileResponse) {}
  // This api is used to download json output for completed task.
  rpc DownloadTaskResult(DownloadTaskResultRequest) 
    returns (stream DownloadTaskResultResponse) {}
}

message CreateTaskRequest {
  // The parent resource name where the Task is to be created.
  // Format of parent workflows/{WID} (parent workflow) or executions/{execution_id} (parent execution)
  string parent = 1;
  oneof input {
    // The Task resources to create. Name field can be empty or otherwise is ignored.
    Task task = 2;
    // To trigger specific workflow for selected file.
    // Use files filed inside files_wrapper instead. 
    // Kept this field to maintain backward compatibility.
    automation_mining.ontology.File file = 3;
    // To trigger multiple tasks for a single parent workflow.
    // Defined this way as we cannot use repeated fields in oneof.
    FilesWrapper files_wrapper = 4;
  }

  message FilesWrapper {
    // Use this field to create multiple tasks at once 
    // for a single parent workflow.
    repeated automation_mining.ontology.File files = 1;
  }
}

message CreateBatchTasksRequest {
  // The parent resource name where the Task is to be created.
  // Format of parent workflows/{WID} (parent workflow)
  string parent = 1;
  // The files to create tasks for.
  repeated automation_mining.ontology.File files = 2;
}

message CreateBatchTasksResponse {
  message CreateTaskResponse {
    oneof result {
      // The task id of the created task.
      string task_id = 1;
      // The error message if the task creation failed.
      string error_msg = 2;
    }
    // uploaded file name of the task
    string file_name = 3;
  }
  repeated CreateTaskResponse results = 1;
}

// The ListTasksApi performs differently with differnt type of users.
// For Admin users. 
// 1) They can leave the parent field as empty and get all the tasks 
// which are part of the organisation to which admin belongs.
// 2) They can query based on a workflow level by setting the parent field to workflows/<workflow_id>.
// 3) They can even query based on different users, by using the filter field.
// For Workflow users.
// 1) If the user wants to list all the tasks inside an organisation assigned to himself/herself, 
// leave the parent field as empty.
// 2) Otherwise set parent field to list tasks inside the workflow assigned to the user.  
// "username" filter is required for workflow users.
// Setting filter username property to any other user's username will be unauthorized 
// and will return a permission denied error.
message ListTasksRequest {
  // The parent resource name where the Task was created. It must be one of the two formats:
  // 1. "organizations/{ORGANIZATION_ID}/workflows/{WID}" to list tasks for a particular workflow
  // 2. "organizations/{ORGANIZATION_ID}" to list tasks for an organization
  string parent = 1;
  // Default is 10 (when page_size is missing or set to 0). Max value is 20.
  // Ordered by ascending Task resource name.
  int32 page_size = 2;
  // Use this to continue the previous list requests.
  // Its value should be same with previous response's next_page_token.
  string page_token = 3;
  // Supported filter: "status={STATUS},username={username},round={RoundNumber},display_name_prefix={SEARCH_KEY},usernames={semicolon separated usernames},last_update_time_lt={UNIX_TIME_SEC},last_update_time_gt={UNIX_TIME_SEC}"
  // username filter will be deprecated, use usernames filter instead
  // multiple status values filter with a dash (-) separator, eg. "status=created-ready"
  // RoundNumber is to get the pending task list with provided review round, this will only work if the status in ready state
  // If usernames filter is not provided, then all tasks in the org will be returned.
  // If OrbyUnassigned is added in usernames filter, then all tasks that are not assigned to any user will be returned.
  // last_update_time_lt={UNIX_TIME_SEC}
  // last_update_time_gt={UNIX_TIME_SEC}
  string filter = 4;
  // Use this to send only relevant data in response
  // - If Field Mask is not send or is sent with empty paths then the result will contain
  //    the complete object
  // - Valid values for field mask are: task_name, confidence, status, create_time, complete_time,
  //    time_saved, tags, task_resource_name, steps, performance, ready_time
  // - Field mask will always contain `name` field. Please donot send it in Paths to avoid errors.
  google.protobuf.FieldMask field_mask = 5;
  int32 page_number = 6;
}

message ListTasksResponse {
  // Ordered by ascending Task resource name.
  repeated Task tasks = 1;
  // If the value is "", it means no more results for the request.
  string next_page_token = 2;
  // Total available Task size.
  // Note it is NOT the remaining available Task size after the current response.
  int32 total_size = 3;
}

message UpdateTaskRequest {
  Task task = 1;
  // Valid values: status, steps, reviewed_saved_nsec, decline_reason,
  // If empty, all updatable fields will be updated based on input task.
  // reviewed_saved_nsec mask also includes the review sessions update, 
  // We do not use review_sessions mask and only keep the reviewed_saved_nsec
  // mask to keep consistency for FE.
  google.protobuf.FieldMask field_mask = 2;
}

message UpdateReviewTaskRequest {
  Task task = 1;
  // Valid values: status, execution_steps, reviewed_saved_nsec, decline_reason,
  // If empty, all updatable fields will be updated based on input task.
  google.protobuf.FieldMask field_mask = 2;
}

message UpdateBatchTasksRequest {
  repeated Task tasks = 1;
  // Valid values: email
  // If empty, all updatable fields will be updated based on input tasks
  // current limit is 100 task updates at a time
  google.protobuf.FieldMask field_mask = 2;
}

// Used in BatchTaskResponse to denote tasks that could not be updated
// Failure can be because of bad requests or server fault
message MissedTask {
  Task task = 1;
  string error_msg = 2;
}

message UpdateBatchTasksResponse {
  repeated Task tasks = 1;
  // Tasks that were unable to be updated. Can be because of bad requests or server fault
  repeated MissedTask missed_tasks = 2;
}

message GetTaskRequest {
  // Name of the Task
  string name = 1;
  // Use this to send only relevant data in response
  // - If Field Mask is not send or is sent with empty paths then the result will contain
  //    the complete object
  // - Valid values for field mask are:display_name, entity_types
  // - Field mask will always contain `name` field. Please do not send it in Paths to avoid errors.
  google.protobuf.FieldMask field_mask = 2;
}

message DeleteTaskRequest {
  string name = 1;
  string deleted_reason = 2;
}

message DeleteBatchTasksRequest {
  repeated string names = 1;
  string deleted_reason = 2;
}

message DeleteBatchTasksResponse {
  // Tasks that were unable to be deleted. 
  // Only `name` field is populated in the task object.
  repeated MissedTask missed_tasks = 1;
}

message CopyTasksRequest {
  // Name of the Source Workflow
  string source_workflow_resource_name = 1;
  // Name of the Destination Workflow
  string destination_workflow_resource_name = 2;
  // Supported filter is "task_resource_names=<>-<>-<>". List of completed task names to be copied, 
  // multiple task names should use a dash (-) separator
  // if filter = "" all completed tasks will be copied.
  string filter = 3;
  // Send completed email notification to the user, who initiated the copy task request
  bool send_email_notification = 4;
  // Name of the destination organization
  // will use source org as destination org if this is empty
  string destination_org_resource_name = 5;
}

message CopyTasksResponse {
  string message = 1;
}

message RetryTasksRequest {
  // The organization which the tasks belong to. Users can only retry tasks of one org at a time.
  string org_resource_name = 1;
  // The resource names of tasks. Format: workflows/{WID}/tasks/{TID}
  repeated string names = 2;
}

message RetryTasksResponse {
  // Tasks that were unable to be retried.
  repeated MissedTask missed_tasks = 1;
}

message Task {
  // Resource name for task. Format: workflows/{WID}/tasks/{TID} or 
  // executions/{execution_id}/tasks/{task_id} (new format), since now task is directly part of a execution rather than workflow
  string name = 1;
  google.protobuf.Timestamp create_time = 2;
  google.protobuf.Timestamp ready_time = 3;
  // we use a single timestamp of obsoletion, rejection and acceptance 
  // since they are all end status of a task
  google.protobuf.Timestamp complete_time = 4;
  v1alpha1.Performance performance = 5;
  repeated ExecutionStep execution_steps = 6;
  enum STATUS {
    STATUS_UNSPECIFIED = 0;
    // Tasks are initially set with state CREATED before the execution engine starts processing.
    // CREATED state tasks should not be used by frontend.
    CREATED = 1;
    // ACCEPTED tasks are those tasks that are accepted by the user using the UI.
    ACCEPTED = 2;
    REJECTED_INCORRECT = 3;
    REJECTED_ALREADY_COMPLETED = 4;
    // READY state tasks are the one that can be viewed to the user on the UI.
    READY = 5;
    // Tasks are marked as OBSOLETE for various reason, field OBSOLETE_REASON stores the reason
    OBSOLETE = 6;
    // COMPLETED tasks are those that are marked completed by the server which require no further actions.
    COMPLETED = 7;
    // Tasks are marked as RETRYING when the task is being retried.
    RETRYING = 8;
  }
  STATUS status = 7;
  bool human_review = 8;
  // Failed task reasons identify by our system
  enum OBSOLETE_REASON {
    OBSOLETE_REASON_UNSPECIFIED = 0;
    LOW_DOCUMENT_CLASSIFICATION_SCORE = 1;
    LARGE_DOCUMENT_SIZE = 2;
    FAILED_AFTER_ACCEPTED = 3;
    UNMATCHED_CLASSIFICATION = 4;
    // TODO: This reason is very generic, in the future we can have specific reasons defined and handled.
    FAILED_EXECUTION_ENGINE = 5;
    INVALID_DOCUMENT_MIME_TYPE = 6;
    TEMPORAL_WORKFLOW_NOT_FOUND = 7;
    TEMPORAL_WORKFLOW_FAILED = 8;
    NO_AVAILABLE_USERS = 9;
    MAX_WORKFLOW_LIMIT_REACHED = 10;
    FAILED_TO_CREATE_TASK = 11;
    FILE_DECRYPTION_FAILED = 12;
    WRONG_FILE_FORMAT = 13;
    USER_CANCELLED_EXECUTION = 14;
  }
  OBSOLETE_REASON obsolete_reason = 9;
  string time_saved = 10 [deprecated = true];
  // Stores the username of the user who is assigned to the task
  string username = 11;
  // Id of the organization the workflow belongs to. Format: organizations/{ID}
  string organization_resource_name = 12;
  string display_name = 13;
  string workflow_display_name = 14;
  // Currently it stores modified, original, auto based on user's modification on the document 
  // auto tags are used for for all tasks in autopilot mode workflow and eligible tasks in the assisted mode
  repeated string tags = 15;
  //total time spends to review the task by all the reviewers
  string reviewed_time = 16;
  UserDeclinedTaskReason decline_reason = 17;
  Workflow.Mode workflow_mode_when_created = 18;
  // contain all the information for the reviews needed to get it approved
  repeated Review reviews = 19;
  // Name of the task from which this task is copied from
  string copied_from_task_resource_name = 20;
  // Only useful for tasks generated through workflow connectors
  string connector_source_task_resource_name = 21;
  // This is the need attention threshold value
  // for the default mode when the task is created.
  double need_attention_threshold_default_mode = 22;
  // This field allows soft deletion over time by
  // marking workflow for deletion without immediately deleting it.
  common.DeletedObjectInfo deleted_object_info = 23;
  // Profile info of the user who is assigned to the task, 
  // can be empty if the task is not assigned to any user
  common.UserProfileInfo assignee = 24;
  // Whether this task can be retried manually
  // currently only some system declined tasks can be retried
  bool retryable = 25;
  enum PERMISSION {
    // User has no permission
    PERMISSION_UNSPECIFIED = 0;
    // User can view/review the task
    PERMISSION_VIEW = 1;
    // User can delete the task
    PERMISSION_DELETE = 2;
    // User can assign the task to others
    PERMISSION_ASSIGN = 3;
  }
  repeated PERMISSION permissions = 26;
  // Task description.
  string description = 27;
  // Flag to indicate if the raw file is deleted.
  // There is also a corresponding Step with ActivityType
  // "ACTIVITY_DELETE_RAW_FILE" in the execution_steps if raw_file_deleted
  // is true.
  // Add this redundant field to make workflow_executions_view union easier.
  bool raw_file_deleted = 28;
  // Show how long the task will be retained in the system.
  // Calculation: (complete_time / deleted_time) + organization retention 
  // policy duration - current time.
  // If tasks is not deleted and not in final status(complete/obsolete/
  // rejected), or if organization doesn't set retention policy, this field
  // will be null.
  google.protobuf.Duration remaining_retention = 29;
  // Time when the task is last updated. 
  google.protobuf.Timestamp last_update_time = 30;
  // The warnings generated during the execution of few shot workflow
  repeated v1alpha1.ExecutionWarning.Warning execution_warnings = 31;
}

// User provided task decline reason
message UserDeclinedTaskReason {
  enum TYPE {
    TYPE_UNSPECIFIED = 0;
    WRONG_DOCUMENT = 1;
    CANNOT_EDIT_CONTENT = 2;
    OTHER = 3;
  }
  TYPE type = 1;
  string description = 2;
}

message IdleSession {
  google.protobuf.Timestamp start_time = 1; 
  int32 duration_msec = 2; // The unit is miliseconds 
}

// review sessions are used to track the reviewed time and idle time spent by 
// the reviewer on the task in a single review
message ReviewSession {
  // The reviwer profile who is reviewing the task
  common.UserProfileInfo reviewer = 1;
  // The start time of the review session
  google.protobuf.Timestamp start_time = 2; 
  // The end time of the review session
  google.protobuf.Timestamp end_time = 3;
  // The idle session for each idle time in the review session 
  repeated IdleSession idle_sessions = 4;
}

// one review can have multiple reviewers due to auto save
// review is closed when user declines or submits in the HITL page
// review session is closed when the HITL page is closed
message Review {
  // Do not use, use reviewer instead.
  string user = 1;
  string reviewed_time = 2;
  common.UserProfileInfo reviewer = 3;
  // 1. NORMAL_REVIEW:
  //   When the review happened right before the task is marked complete,
  // 2. MODIFICATION_REVIEW:
  //   When the review happened after the task is marked complete.
  // 3. PARTIAL_NORMAL_REVIEW:
  //   When the review results is partially reviewed by user, and will not
  //   mark task complete. The type is used to save partial review results.
  enum Type {
    TYPE_UNSPECIFIED = 0;
    NORMAL_REVIEW = 1;
    MODIFICATION_REVIEW = 2;
    PARTIAL_NORMAL_REVIEW = 3;
  }
  Type review_type = 4;

  repeated ReviewSession sessions = 5;
}

message ExportTasksRequest {
  // If we want to get all the tasks, then we can leave the filter field empty
  // To include multiple filters add comma seperated key-value pairs, like
  // "usernames=user1;user2,workflow_resource_names=workflows/123;workflows/456,status=COMPLETED"
  // Supported filters: usernames, workflow_resource_names, start_time, end_time, status, display_name_prefix
  // username: filter tasks based on assigned users. Use semicolon separated usernames for multiple users.
  // workflow_resource_names: filter tasks for specific workflows. Each workflow will be of the form "workflows/{WID}". Use semicolon separated workflow names for multiple workflows.
  // start_time: filter tasks based on the start time (seconds since epoch).
  // end_time: filter tasks based on the end time (seconds since epoch).
  // Both start_time and end_time are required for time filters.
  // status: filter tasks based on status. Use semicolon separated status for multiple statuses.
  // display_name_prefix: filter tasks based on task display name prefix.
  string filter = 1;
  // time_offset is hours to offset the time from UTC, eg time_offset=-7 for PST and time_offset=5.5 for IST.
  float time_offset = 2 [(buf.validate.field).float = { gte: -12.0, lte: 14.0 }];
}

message ExportTasksResponse {

}

message ExportTasksDownloadFileRequest {
  // The notification id of the export tasks request
  string notification_id = 1 [(buf.validate.field).required = true];
}

message ExportTasksDownloadFileResponse {
  // The signed url to download the file
  string signed_url = 1;
  // The file name to be downloaded
  string file_name = 2;
}

message DownloadTaskResultRequest {
  // The task resource name
  string name = 1 [(buf.validate.field).required = true];
}

message DownloadTaskResultResponse {
  // The output json data
  bytes data_chunk = 1;
}
