syntax = "proto3";

package pb.v1alpha2;

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha2";
import "pb/v1alpha2/connector.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "common/data_query_params.proto";

service Connectors {
    rpc CreateConnector (CreateConnectorRequest) returns (Connector) {}
    rpc ListConnectors (ListConnectorRequest) returns (ListConnectorResponse) {}
    rpc DeleteConnector (DeleteConnectorRequest) returns (google.protobuf.Empty) {}
    rpc GetConnector (GetConnectorRequest) returns (Connector) {}
    rpc UpdateConnector (UpdateConnectorRequest) returns (Connector) {}
  }

message CreateConnectorRequest{
  Connector connector = 1;
}

message ListConnectorRequest {
  // Default is 10 (when page_size is missing or set to 0). Max value is 20.
  // Ordered by display name.
  int32 page_size = 1;
  string filter = 2 [deprecated = true];
  // Use this to continue the previous list requests.
  // Its value should be same with previous response's next_page_token.
  // Please reuse the same filter and page size.
  string page_token = 3;
  // Use this to send only relevant data in response
  // - If Field Mask is not send or is sent with empty paths then the result will contain
  //    the complete object
  // - Valid values for field mask are: display_name, description, source_workflow_resource_name, destination_workflow_resource_name and group_condition.
  google.protobuf.FieldMask field_mask = 4;
  // Organization resource name. Format: organizations/{ID}
  string org_resource_name = 5;
  int32 page_number = 6;
  ListConnectorRequestFilter list_filter = 7;
  // The order of fields will effect the sorting order.
  // Supported fields: display_name, creator_email, last_modified_time
  repeated common.SortField sort = 8;
}

message ListConnectorRequestFilter {
  // Format: workflows/Id
  repeated string workflow_resource_names = 1;
  // Format: workflows/Id
  repeated string source_workflow_resource_names = 2;
  // Format: workflows/Id
  repeated string destination_workflow_resource_names = 3;
  repeated string creator_username = 4;
  string name_prefix = 5;
  // To return only connectors created before this time
  // Needs to be UNIX_TIME_SEC
  int64 create_time_lt = 6;
  // To return only connectors created after this time
  // Needs to be UNIX_TIME_SEC
  int64 create_time_gt = 7;
  int64 last_update_time_lt = 8;
  int64 last_update_time_gt = 9;
}

message ListConnectorResponse {
  repeated Connector connectors = 1;
  // If the value is "", it means no further results for the request.
  string next_page_token = 2;
  // Total available suggestion size.
  // Note it is NOT the remaining available suggestion size after the current response.
  int32 total_size = 3;
}

message GetConnectorRequest{
  // The resource name of the connector to be retrived, format connectors/{ID}
  string name = 1;
}

message DeleteConnectorRequest{
  // The resource name of the connector to be deleted, format connectors/{ID}
  string name = 1;
  string deleted_reason = 2;
}

message UpdateConnectorRequest{
  Connector connector = 1;
  // Support display_name, description, source_workflow_resource_name, destination_workflow_resource_name,
  // group_condition, assignment_config
  // If field_mask is empty, all updatable fields will be updated based on
  // the provided connector.
  // Note: If source_workflow_resource_name is present, group_condition must be present
  google.protobuf.FieldMask field_mask = 2;
}
