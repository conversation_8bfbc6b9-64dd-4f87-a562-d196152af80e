syntax = "proto3";

package pb.v1alpha2;

import "common/common.proto";
import "google/protobuf/timestamp.proto";
import "common/user_profile.proto";
import "common/review.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha2";


message Connector {
  // Resource name. Format: connectors/{ID}
  string name = 1;
  string display_name = 2;
  string description = 3;
  WorkflowInfo source_workflow = 4;
  WorkflowInfo destination_workflow = 5;
  common.CompositeGroupCondition group_condition = 6;
  // Organization resource name. Format: organizations/{ID}
  string org_resource_name = 7;
  AssignmentConfig assignment_config = 8;
  // This field allows soft deletion over time by
  // marking workflow for deletion without immediately deleting it.
  common.DeletedObjectInfo deleted_object_info = 9;
  // Do not use, use creator instead.
  string creator_email = 10;
  google.protobuf.Timestamp create_time = 11;
  google.protobuf.Timestamp last_modified_time = 12;
  // Creator info of the connector
  common.UserProfileInfo creator = 13;
}

// Used to store the configurations to do preferential assignments
// across source and destination tasks
message AssignmentConfig {
  // Bool indicating preference for same user in source workflow and destination workflow
  // Currently only source R1 user is being considered
  bool preserve_assignee = 1;
}

message WorkflowInfo {
  // Wokflow resource name. Format: workflows/{ID}
  string workflow_resource_name = 1;
  string workflow_name = 2;
}
