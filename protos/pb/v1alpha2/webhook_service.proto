syntax="proto3";

package pb.v1alpha2;

import "buf/validate/validate.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha2";

service Webhooks {
  rpc CreateWebhook(CreateWebhookRequest) returns (Webhook) {}
  rpc UpdateWebhook (UpdateWebhookRequest) returns (Webhook) {}
  rpc DeleteWebhook (DeleteWebhookRequest) returns (google.protobuf.Empty) {}
  rpc GetWebhook (GetWebhookRequest) returns (Webhook) {}
  rpc ListWebhooks (ListWebhooksRequest) returns (ListWebhooksResponse) {}
}

message Webhook {
  string id = 1;
  string display_name = 2;
  string description = 3;
  enum EventType {
    EVENT_TYPE_UNSPECIFIED = 0;
    EVENT_TYPE_EXECUTION_COMPLETED = 1;
    EVENT_TYPE_EXECUTION_FAILED = 2;
    EVENT_TYPE_EXECUTION_CANCELLED = 3;
  }
  repeated EventType event_types = 4;
  enum Status {
    STATUS_UNSPECIFIED = 0;
    STATUS_ENABLED = 1;
    STATUS_DISABLED = 2;
  }
  Status status = 5;
  string endpoint_url = 6;
  // Organization resource name. Format: organizations/{ID}
  string org_resource_name = 7;
  // Workflow resource name. Format: workflows/{ID}
  repeated string workflow_resource_names = 8;
  string secret = 9;
  string disabled_reason = 10;
  google.protobuf.Timestamp created_at = 11;
  string created_by = 12;
  google.protobuf.Timestamp updated_at = 13;
  string updated_by = 14;
}

message CreateWebhookRequest {
  string display_name = 1 [(buf.validate.field).string.min_len = 1];
  string description = 2;
  repeated Webhook.EventType event_types = 3 [
    (buf.validate.field).repeated.min_items = 1,
    (buf.validate.field).repeated.unique = true,
    (buf.validate.field).repeated.items = {
      enum: {
        in: [1, 2, 3]
      }
    }
  ];
  Webhook.Status status = 4 [(buf.validate.field).enum = { in: [1, 2] }];
  string endpoint_url = 5 [
    (buf.validate.field).string.uri = true
  ];
  repeated string workflow_resource_names = 6 [
    (buf.validate.field).repeated.unique = true,
    (buf.validate.field).repeated.items = {
      string: {
        min_len: 1
      }
    }
  ];
}

message UpdateWebhookRequest {
  string id = 1 [(buf.validate.field).string.min_len = 1];
  // Support display_name, description, event_types, status, endpoint_url,
  // workflow_resource_names
  google.protobuf.FieldMask field_mask = 2;
  string display_name = 3;
  string description = 4;
  repeated Webhook.EventType event_types = 5 [
    (buf.validate.field).repeated.unique = true,
    (buf.validate.field).repeated.items = {
      enum: {
        in: [1, 2, 3]
      }
    }
  ];
  Webhook.Status status = 6 [(buf.validate.field).enum.defined_only = true];
  string endpoint_url = 7;
  repeated string workflow_resource_names = 8 [
    (buf.validate.field).repeated.unique = true,
    (buf.validate.field).repeated.items = {
      string: {
        min_len: 1
      }
    }
  ];
}

message DeleteWebhookRequest {
  string id = 1 [(buf.validate.field).string.min_len = 1];
}

message GetWebhookRequest {
  string id = 1 [(buf.validate.field).string.min_len = 1];
}

message ListWebhooksRequest {
  int32 page_size = 1 [(buf.validate.field).int32 = {gte: 1, lte: 20}];
  int32 page_number = 2 [(buf.validate.field).int32 = {gte: 1}];
}

message ListWebhooksResponse {
  repeated Webhook webhooks = 1;
  int32 total_size = 2;  
}
