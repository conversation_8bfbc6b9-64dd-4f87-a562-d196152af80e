syntax="proto3";

package pb.v1alpha2;

import "buf/validate/validate.proto";
import "google/protobuf/any.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha2";

service FeatureFlags {
  rpc CreateFeatureFlag(CreateFeatureFlagRequest) returns (FeatureFlag) {}
  rpc UpdateFeatureFlag(UpdateFeatureFlagRequest) returns (FeatureFlag) {}
  rpc DeleteFeatureFlag(DeleteFeatureFlagRequest) 
    returns (google.protobuf.Empty){}
  rpc GetFeatureFlag(GetFeatureFlagRequest) returns (FeatureFlag) {}
  rpc ListFeatureFlags(ListFeatureFlagsRequest) 
    returns (ListFeatureFlagsResponse) {}
  // GetFeatureFlagsForOrgAndUser returns a map of feature 
  // flag evaluation results for the current user and organization
  rpc GetFeatureFlagsForOrgAndUser(GetFeatureFlagsForOrgAndUserRequest)
    returns (GetFeatureFlagsForOrgAndUserResponse) {}
  // IsFeatureFlagEnabled checks whether a feature flag is enabled.
  // It will check the organization/user/workflow to determine
  // whether the feature flag is enabled. If any one of them is enabled,
  //  then this feature flag is enabled. Workflow id is optional,
  // if not provided, will check only organization/user.
  rpc IsFeatureFlagEnabled(IsFeatureFlagEnabledRequest)
    returns (IsFeatureFlagEnabledResponse) {}
}

message FeatureFlag {
  string id = 1;
  // feature flag name is unique across the system
  string name = 2;
  string description = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
  string created_by = 6;
  string updated_by = 7;
  // Bool value is defined in Rule field using the enabled field
  Rule rule = 8;
  // String and float values are defined in the value field
  FeatureFlagValue value = 9;
}

// Rule defines the conditions for enabling or blocking the feature flag
// If enabled is true, the feature flag is enabled for all users unless 
// the user is in the blocked_users list or the user's organization is in the blocked_orgs list
// If enabled is false, the feature flag is disabled for all users unless
// the user is in the enabled_users list or the user's organization is in the enabled_orgs list
message Rule {
  bool enabled = 1;
  // list of usernames (like email, <EMAIL>)
  repeated string enabled_users = 2;
  // list of organization ids (pure object ids, like 6150ccc742d39feae9fc640g)
  repeated string enabled_orgs = 3;
  repeated string blocked_users = 4;
  repeated string blocked_orgs = 5;
  // list of workflow ids (pure object ids, like 6150ccc742d39feae9fc640g)
  repeated string enabled_workflows = 6;
  repeated string blocked_workflows = 7;
}

message FeatureFlagValue {
  google.protobuf.Any default_value = 1;
  // map of username to value
  map<string, google.protobuf.Any> user_values = 2;
  // map of organization id to value
  map<string, google.protobuf.Any> org_values = 3;
  // map of workflow id to value
  map<string, google.protobuf.Any> workflow_values = 4;
}

message CreateFeatureFlagRequest {
  string name = 1 [(buf.validate.field).string.min_len = 1];
  string description = 2;
  // Bool value is defined in Rule field using the enabled field
  Rule rule = 3;
  // String and float values are defined in the value field
  FeatureFlagValue value = 4;
}

message UpdateFeatureFlagRequest {
  string id = 1;
  string name = 2;
  string description = 3;
  // Bool value is defined in Rule field using the enabled field
  Rule rule = 4;
  // String and float values are defined in the value field
  FeatureFlagValue value = 5;
}

message DeleteFeatureFlagRequest {
  string id = 1;
}

message GetFeatureFlagRequest {
  string id = 1;
}

message ListFeatureFlagsRequest {
  int32 page = 1; // 1-based index
  int32 page_size = 2;
}

message ListFeatureFlagsResponse {
  repeated FeatureFlag feature_flags = 1;
  int32 total_size = 2;
  string next_page_token = 3;
}

// Fetch feature flags for the current user and organization
// User and organization are identified from the context
// 
// Workflow id check is not done in this function, and is an internal function
// that is used internally in the server. This function just checks for user and org
// and returns the feature flag status and values
message GetFeatureFlagsForOrgAndUserRequest {
  // Org resource name is deprecated, use org_id inside metadata instead
  string org_resource_name = 1 [deprecated = true];
}

message GetFeatureFlagsForOrgAndUserResponse {
  // map of feature flag enabled or disabled status
  map<string, bool> feature_flags = 1;
  // map of feature flag value for org and user
  // bool values are stored in feature_flags field
  // string and float values are stored in values field currently
  map<string, google.protobuf.Any> values = 2;
}

message IsFeatureFlagEnabledRequest {
  string name = 1 [(buf.validate.field).string.min_len = 1];
  optional string workflow_id = 2;
}

message IsFeatureFlagEnabledResponse {
  bool enabled = 1;
}
