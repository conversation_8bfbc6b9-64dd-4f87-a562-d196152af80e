syntax = "proto3";

package pb.v1;

import "buf/validate/validate.proto";
import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/api/httpbody.proto";
import "google/protobuf/field_mask.proto";
import "grpc/gateway/protoc_gen_openapiv2/options/annotations.proto";
import "pb/v1/data_model.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Orby Public API";
    version: "v1";
    description: "The Orby Public API provides a set of endpoints to manage and execute workflows within the Orby platform. This API allows developers to create, retrieve, list, and download execution results, facilitating seamless integration with Orby's workflow management system.";
  };
  host: "api.orby.ai";
  schemes: HTTPS;
  responses: {
    key: "400" 
    value: {
      description: "Bad Request. The request is invalid. Please check the request body and try again."
      schema: {
        json_schema: {
          title: "Error"
          example: '{"code": 0,"message": "string"}'
        }
      }
    }
  }
  responses: {
    key: "401" 
    value: {
      description: "Unauthorized. Please check if your API key and organization id are correct."
      schema: {
        json_schema: {
          title: "Error"
          example: '{"code": 0,"message": "string"}'
        }
      }
    }
  }
  responses: {
    key: "404" 
    value: {
      description: "Not Found. The resource you are trying to access does not exist."
      schema: {
        json_schema: {
          title: "Error"
          example: '{"code": 0,"message": "string"}'
        }
      }
    }
  }
  responses: {
    key: "429" 
    value: {
      description: "Rate Limit Exceeded. Please try again later."
    }
  }
  responses: {
    key: "500" 
    value: {
      description: "Internal Server Error. Please try again later or contact Orby support if the issue persists."
      schema: {
        json_schema: {
          title: "Error"
          example: '{"code": 0,"message": "string"}'
        }
      }
    }
  }
  security_definitions: {
    security: {
      key: "ApiKey"
      value: {
        type: TYPE_API_KEY
        in: IN_HEADER
        name: "orby-api-key"
        description: "The API key to use for authentication."
      }
    }
  }
  security: {
    security_requirement: {
      key: "ApiKey"
      value: {}
    }
  }
};

service ExecutionService {
  rpc CreateExecutions(CreateExecutionsRequest) returns (CreateExecutionsResponse) {
    option (google.api.http) = {
        post: "/v1/executions"
        body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Create new executions";
      description: "Creates new executions based on the provided request."
      parameters: {
        headers: [
          {
            name: "orby-org-id"
            description: "The organization id to handle execution requests for."
            required: true
            type: STRING
          }
        ]
      }
      responses: {
        key: "200"
        value: {
          description: "Success. All executions were created successfully. The response contains execution IDs for all documents in the request. This indicates that all documents were accepted for processing."
        }
      }
      responses: {
        key: "207"
        value: {
          description: "Multi-Status. Partial success where some executions were created while others failed. The response contains a mix of execution IDs for successful documents and error messages for failed ones. Common partial failure scenarios include: invalid document formats, virus scan failures, wrong file formats, etc. Executions that were successfully created have already been accepted for processing and require no further action. For the failed executions, please refer to the provided error messages for details and retry as necessary."
        }
      }
      responses: {
        key: "400"
        value: {
          description: "Bad Request. The request failed validation or processing. This can occur due to issues like invalid workflow ID, total file size exceeding limits, malformed request format, or when all documents fail validation (e.g., invalid formats). The response will include detailed error messages to help identify and resolve the specific issues."
        }
      }
    };
  }
  rpc GetExecution(GetExecutionRequest) returns (GetExecutionResponse) {
    option (google.api.http) = {
        get: "/v1/executions/{execution_id}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Get single execution details";
      description: "Retrieves the details of a specific execution by its id."
      parameters: {
        headers: [
          {
            name: "orby-org-id"
            description: "The organization id to handle execution requests for."
            required: true
            type: STRING
          }
        ]
      }
    };
  }
  rpc ListExecutions(ListExecutionsRequest) returns (ListExecutionsResponse) {
    option (google.api.http) = {
        get: "/v1/executions"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Lists executions by page";
      description: "Lists all executions based on the provided request parameters."
      parameters: {
        headers: [
          {
            name: "orby-org-id"
            description: "The organization id to handle execution requests for."
            required: true
            type: STRING
          }
        ]
      }
    };
  }
  // API to download single execution result
  rpc DownloadExecutionResult(DownloadExecutionResultRequest) returns (stream google.api.HttpBody) {
    option (google.api.http) = {
        get: "/v1/executions/{execution_id}/result"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Download single execution result";
      description: "Downloads the result of a specific execution by its id.";
      produces: "application/octet-stream";
      parameters: {
        headers: [
          {
            name: "orby-org-id"
            description: "The organization id to handle execution requests for."
            required: true
            type: STRING
          }
        ]
      }
    };
  }
  // Update an execution
  rpc UpdateExecution(UpdateExecutionRequest) returns (UpdateExecutionResponse) {
    option (google.api.http) = {
        patch: "/v1/executions/{execution_id}"
        body: "execution"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Update single execution info";
      description: "Update the details of a specific execution by its id. Currently supports updating 'customLabels'."
      parameters: {
        headers: [
          {
            name: "orby-org-id"
            description: "The organization id to handle execution requests for."
            required: true
            type: STRING
          }
        ]
      }
    };
  }
  // Batch update multiple executions
  rpc BatchUpdateExecutions(BatchUpdateExecutionsRequest) returns (BatchUpdateExecutionsResponse) {
    option (google.api.http) = {
        post: "/v1/executions/batch-update"
        body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Update multiple executions in a single request";
      description: "Batch update multiple executions. Allows updating custom labels for multiple executions in a single API call."
      parameters: {
        headers: [
          {
            name: "orby-org-id"
            description: "The organization id to handle execution requests for."
            required: true
            type: STRING
          }
        ]
      }
    };
  }
}

message CreateExecutionsRequestSource {
  Document document = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Document to be processed."
    }
  ];
}

message CreateExecutionsRequest {
  string workflow_id = 1 [
    (google.api.field_behavior) = REQUIRED, 
    (buf.validate.field).string.min_len = 1,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The unique identifier of the workflow associated with the execution."
    }
  ];
  repeated CreateExecutionsRequestSource sources = 2 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).repeated = {
      min_items: 1,
      max_items: 100
    },
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "A list of sources limited to 100 sources ."
    }
  ];
}

message ExecutionResult {
  oneof result {
    string execution_id = 1 [
      (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
        description: "The unique identifier of the execution."
      }
    ];
    string error_message = 2 [
      (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
        description: "The error message if the execution failed."
      }
    ];
  }
}

message CreateExecutionsResponse {
  // These represent the results of the execution for each source
  // We keep the order of the input sources and output results to 
  // make it easier to find the result for a specific source
  repeated ExecutionResult results = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The results of the execution for each source, maintaining the order of input sources."
    }
  ];
}

// Output only.
message ExecutionError {
  string type = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Type of the error, e.g. 'LOW_DOCUMENT_CLASSIFICATION_SCORE', 'LARGE_DOCUMENT_SIZE', 'FAILED_AFTER_ACCEPTED', 'UNMATCHED_CLASSIFICATION', 'FAILED_EXECUTION_ENGINE', 'INVALID_DOCUMENT_MIME_TYPE', 'NO_AVAILABLE_USERS', 'MAX_WORKFLOW_LIMIT_REACHED', 'FAILED_TO_CREATE_TASK', 'FILE_DECRYPTION_FAILED', 'WRONG_FILE_FORMAT', 'USER_CANCELLED_EXECUTION'."
    }
  ];
  string details = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Details of the error."
    }
  ];
}

message Execution {
  string id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The unique identifier of the execution."
    }
  ];
  // Output only.
  string name = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The name of the execution."
    }
  ];
  // Output only.
  string workflow_id = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The unique identifier of the workflow associated with the execution."
    }
  ];
  // Output only.
  string workflow_name = 4 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The name of the workflow associated with the execution."
    }
  ];
  // Output only.
  enum Status {
    STATUS_UNSPECIFIED = 0;
    SCHEDULED = 1;
    BLOCKED = 2;
    EXECUTING = 3;
    PENDING_REVIEW = 4;
    COMPLETED = 5;
    FAILED = 6;
    CANCELLED = 7;
  }
  // Output only.
  Status status = 5 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The status of the execution. Possible values are: 'SCHEDULED', 'BLOCKED', 'EXECUTING', 'PENDING_REVIEW', 'COMPLETED', 'FAILED', 'CANCELLED'."
    }
  ];
  // Output only.
  ExecutionError error = 6 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Error details if the execution declined, canceled, or failed."
    }
  ];
  // Output only.
  string source_execution_id = 7 [
    (google.api.field_behavior) = OPTIONAL,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The unique identifier of the source execution that triggers the current execution."
    }
  ];
  // Output only.
  repeated string following_up_execution_ids = 8 [
    (google.api.field_behavior) = OPTIONAL,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The unique identifiers of the following up executions from the current execution."
    }
  ];
  // Output only.
  repeated ExecutionWarning warnings = 9 [
    (google.api.field_behavior) = OPTIONAL,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The warnings generated during the execution of few shot workflow"
    }
  ];
  repeated string custom_labels = 10 [
    (google.api.field_behavior) = OPTIONAL,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Custom labels for the execution. Max 255 characters per label. Label can only contain alphanumeric characters, underscores, and hyphens."
    }
  ];
}

message ExecutionWarning {
  string type = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Type of the warning, e.g. 'WARNING_LLM_OUTPUT_TOKEN_LIMIT_EXCEEDED'."
    }
  ];
  string details = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The detailed description of the warning."
    }
  ];
}

message GetExecutionRequest {
  string execution_id = 1 [
    (google.api.field_behavior) = REQUIRED,  
    (buf.validate.field).string.min_len = 1,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The unique identifier of the execution to retrieve."
    }
  ];
}

message GetExecutionResponse {
  Execution execution = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The execution details."
    }
  ];
}

message ListExecutionFilter {
  repeated string workflow_ids = 1 [
    (google.api.field_behavior) = OPTIONAL,
    (buf.validate.field).repeated.items = {
      string: {
        min_len: 1
      }
    },
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "A list of workflow ids to filter the executions by. A single value example: filter.workflowIds=some_id. A multiple values example: filter.workflowIds=some_id1&filter.workflowIds=some_id2"
    }
  ];
  repeated string statuses = 2 [
    (google.api.field_behavior) = OPTIONAL,
    (buf.validate.field).repeated.items = {
      string: {
        in: [
          "SCHEDULED",
          "BLOCKED",
          "EXECUTING",
          "PENDING_REVIEW",
          "COMPLETED",
          "FAILED",
          "CANCELLED"
        ]
      }
    },
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The status of the executions to filter by. Possible values are: 'SCHEDULED', 'BLOCKED', 'EXECUTING', 'PENDING_REVIEW', 'COMPLETED', 'FAILED', 'CANCELLED'. A single value example: filter.statuses=COMPLETED. A multiple values example: filter.statuses=COMPLETED&filter.statuses=PENDING_REVIEW"
    }
  ];
  string query = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Filter string query for custom labels. Supported examples: 'NOT customLabels:*' (has no labels), 'customLabels:value' (has specific label), 'NOT customLabels:value' (doesn't have specific label), 'customLabels:value1,value2' (has multiple labels)."
    }
  ];
}

message ListExecutionsRequest {
  int32 page_size = 1 [
    (google.api.field_behavior) = OPTIONAL,
    (buf.validate.field).int32 = {gte: 1, lte: 100},
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The number of executions to return per page. Minimum is 1, maximum is 100, default is 10."
    }
  ];
  string page_token = 2 [
    (google.api.field_behavior) = OPTIONAL,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The token for fetching the next page of executions."
    }
  ];
  ListExecutionFilter filter = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The filter criteria for listing executions."
    }
  ];
}

message ListExecutionsResponse {
  repeated Execution executions = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "A list of executions returned in the response."
    }
  ];
  string next_page_token = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The token to retrieve the next page of results."
    }
  ];
}

// DownloadExecutionResultRequest is the request to download the result of a single execution.
message DownloadExecutionResultRequest {
  // The execution ID to download the result for.
  string execution_id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.min_len = 1,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The unique identifier of the execution to retrieve."
    }
  ];
}

// Request message for updating an execution
message UpdateExecutionRequest {
  string execution_id = 1 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string.min_len = 1,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The unique identifier of the execution to update."
    }
  ];

  Execution execution = 2 [
    (google.api.field_behavior) = REQUIRED,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The execution to update."
    }
  ];
  
  google.protobuf.FieldMask update_mask = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The fields to update. Currently supports: 'customLabels'."
    }
  ];
}

// Response message for updating an execution
message UpdateExecutionResponse {
  Execution execution = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The updated execution."
    }
  ];
}

// Request for batch updating executions
message BatchUpdateExecutionsRequest {
  repeated Execution executions = 1 [
    (google.api.field_behavior) = REQUIRED,
    (buf.validate.field).repeated.min_items = 1,
    (buf.validate.field).repeated.max_items = 100,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "List of executions to update. Limited to 100 executions per request."
    }
  ];
  
  google.protobuf.FieldMask update_mask = 2 [
    (google.api.field_behavior) = REQUIRED,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The fields to update for all executions. Currently supports: 'customLabels'."
    }
  ];
}

// Response for batch update
message BatchUpdateExecutionsResponse {
  repeated BatchUpdateResult results = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "Results for each execution update attempt."
    }
  ];
}

// Result for individual execution update
message BatchUpdateResult {
  oneof result {
    Execution execution = 1 [
      (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
        description: "The updated execution if successful."
      }
    ];
    string error = 2 [
      (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
        description: "Error message if the update failed for this execution."
      }
    ];
  }
}
