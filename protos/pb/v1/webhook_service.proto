syntax = "proto3";

package pb.v1;

import "buf/validate/validate.proto";
import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "grpc/gateway/protoc_gen_openapiv2/options/annotations.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Orby Public API for Webhook";
    version: "v1";
    description: "The Orby Public API for Webhook provides a set of endpoints to manage webhooks within the Orby platform. This API allows developers to create, update, retrieve, and list webhooks.";
  };
  host: "api.orby.ai";
  schemes: HTTPS;
  responses: {
    key: "400" 
    value: {
      description: "Bad Request. The request is invalid. Please check the request body and try again."
      schema: {
        json_schema: {
          title: "Error"
          example: '{"code": 0,"message": "string"}'
        }
      }
    }
  }
  responses: {
    key: "401" 
    value: {
      description: "Unauthorized. Please check if your API key and organization id are correct."
      schema: {
        json_schema: {
          title: "Error"
          example: '{"code": 0,"message": "string"}'
        }
      }
    }
  }
  responses: {
    key: "404" 
    value: {
      description: "Not Found. The resource you are trying to access does not exist."
      schema: {
        json_schema: {
          title: "Error"
          example: '{"code": 0,"message": "string"}'
        }
      }
    }
  }
  responses: {
    key: "429" 
    value: {
      description: "Rate Limit Exceeded. Please try again later."
    }
  }
  responses: {
    key: "500" 
    value: {
      description: "Internal Server Error. Please try again later or contact Orby support if the issue persists."
      schema: {
        json_schema: {
          title: "Error"
          example: '{"code": 0,"message": "string"}'
        }
      }
    }
  }
  security_definitions: {
    security: {
      key: "ApiKey"
      value: {
        type: TYPE_API_KEY
        in: IN_HEADER
        name: "orby-api-key"
        description: "The API key to use for authentication."
      }
    }
  }
  security: {
    security_requirement: {
      key: "ApiKey"
      value: {}
    }
  }
};

service WebhookService {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_tag) = {
		description: "The Orby Public API for Webhook provides a set of endpoints to manage webhooks within the Orby platform. This API allows developers to create, update, retrieve, and list webhooks."
	};

  rpc CreateWebhook(CreateWebhookRequest) returns (Webhook) {
    option (google.api.http) = {
      post: "/v1/webhooks"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Create new webhook";
      description: "Creates new webhook based on the provided request."
      parameters: {
        headers: [
          {
            name: "orby-org-id"
            description: "The organization id to handle webhook requests for."
            required: true
            type: STRING
          }
        ]
      }
      responses: {
        key: "200"
        value: {
          description: "Success. Webhook is created successfully."
        }
      }
    };
  }
  rpc GetWebhook(GetWebhookRequest) returns (Webhook) {
    option (google.api.http) = {
        get: "/v1/webhooks/{id}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Get single webhook details";
      description: "Retrieves the details of a specific webhook by its id."
      parameters: {
        headers: [
          {
            name: "orby-org-id"
            description: "The organization id to handle webhook requests for."
            required: true
            type: STRING
          }
        ]
      }
      responses: {
        key: "200"
        value: {
          description: "Success. Webhook is fetched successfully."
        }
      }
    };
  }
  rpc ListWebhooks(ListWebhooksRequest) returns (ListWebhooksResponse) {
    option (google.api.http) = {
      get: "/v1/webhooks"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Lists webhooks by page";
      description: "Lists all webhooks based on the provided request parameters."
      parameters: {
        headers: [
          {
            name: "orby-org-id"
            description: "The organization id to handle webhook requests for."
            required: true
            type: STRING
          }
        ]
      }
      responses: {
        key: "200"
        value: {
          description: "Success. Webhooks are fetched successfully."
        }
      }
    };
  }
  rpc UpdateWebhook(UpdateWebhookRequest) returns (Webhook) {
    option (google.api.http) = {
      patch: "/v1/webhooks/{id}"
      body: "webhook"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Update single webhook info";
      description: "Update the details of a specific webhook by its id. The fields can update include 'displayName', 'description', 'eventTypes', 'status', 'endpointUrl', 'workflowIds'."
      parameters: {
        headers: [
          {
            name: "orby-org-id"
            description: "The organization id to handle webhook requests for."
            required: true
            type: STRING
          }
        ]
      }
    };
  }
  rpc DeleteWebhook(DeleteWebhookRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/webhooks/{id}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "Delete single webhook";
      description: "Delete a specific webhook by its id."
      parameters: {
        headers: [
          {
            name: "orby-org-id"
            description: "The organization id to handle webhook requests for."
            required: true
            type: STRING
          }
        ]
      }
      responses: {
        key: "200" // cannot return 204 because google.protobuf.Empty is always serialized as empty JSON object
        value: {
          description: "Success. Webhook is deleted successfully."
        }
      }
    };
  }
}


message Webhook {
  string id = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The unique identifier of the webhook."
    }
  ];
  string display_name = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The unique display name of the webhook."
    }
  ];
  string description = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The description of the webhook."
    }
  ];
  repeated string event_types = 4 [
    (buf.validate.field).repeated.items = {
      string: {
        in: ["execution.completed", "execution.failed", "execution.cancelled"]
      }
    },
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The event types that the webhook listens to. Possible values are: 'execution.completed', 'execution.failed', 'execution.cancelled'."
    }
  ];
  string status = 5 [
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).string = {
      in: ["enabled", "disabled"]
    },
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The status of the webhook. Possible values are: 'enabled', 'disabled'."
    }
  ];
  string endpoint_url = 6 [
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).string.uri = true,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The URL to which the webhook sends the payload."
    }
  ];
  string org_id = 7 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The id of the organization which webhook registers on."
    }
  ];
  repeated string workflow_ids = 8 [
    (buf.validate.field).repeated.unique = true,
    (buf.validate.field).repeated.items = {
      string: {
        min_len: 1
      }
    },
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The ids of the workflow which webhook registers on."
    }
  ];
  string secret = 9 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The secret key used to sign the payload."
    }
  ];
  string disabled_reason = 10 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The reason for disabling the webhook."
    }
  ];
  google.protobuf.Timestamp created_at = 11 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The timestamp when the webhook was created."
    }
  ];
  string created_by = 12 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The id of user who created the webhook."
    }
  ];
  google.protobuf.Timestamp updated_at = 13 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The timestamp when the webhook was last updated."
    }
  ];
  string updated_by = 14 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The id of user who updated the webhook."
    }
  ];
}

message CreateWebhookRequest {
  string display_name = 1 [
    (buf.validate.field).string.min_len = 1,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The display name of the webhook."
    }
  ];
  string description = 2 [
    (google.api.field_behavior) = OPTIONAL,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The description of the webhook."
    }
  ];
  repeated string event_types = 3 [
    (buf.validate.field).repeated.min_items = 1,
    (buf.validate.field).repeated.items = {
      string: {
        in: ["execution.completed", "execution.failed", "execution.cancelled"]
      }
    },
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The event types that the webhook listens to. Possible values are: 'execution.completed', 'execution.failed', 'execution.cancelled'."
    }
  ];
  string status = 4 [
    (buf.validate.field).string = {
      in: ["enabled", "disabled"]
    },
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The status of the webhook. Possible values are: 'enabled', 'disabled'."
    }
  ];
  string endpoint_url = 5 [
    (buf.validate.field).string.uri = true,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The URL to which the webhook sends the payload."
    }
  ];
  repeated string workflow_ids = 6 [
    (google.api.field_behavior) = OPTIONAL,
    (buf.validate.field).repeated.unique = true,
    (buf.validate.field).repeated.items = {
      string: {
        min_len: 1
      }
    },
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The ids of the workflows which webhook registers on."
    }
  ];
}

message GetWebhookRequest {
  string id = 1 [
    (buf.validate.field).string.min_len = 1,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The unique identifier of the webhook."
    }  
  ];
}

message ListWebhooksRequest {
  int32 page_size = 1 [
    (google.api.field_behavior) = OPTIONAL,
    (buf.validate.field).int32 = {gte: 1, lte: 100},
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The number of webhooks to return in a single page. Minimum is 1, maximum is 100, default is 20."
    }
  ];
  string page_token = 2 [
    (google.api.field_behavior) = OPTIONAL,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The token for fetching the next page of webhooks."
    }
  ];
}

message ListWebhooksResponse {
  repeated Webhook webhooks = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The list of webhooks."
    }
  ];
  string next_page_token = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The token to retrieve the next page of results."
    }
  ];
}

message UpdateWebhookRequest {
  UpdateWebhook webhook = 1 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The webhook to update."
    }
  ];

  google.protobuf.FieldMask field_mask = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The fields to update. Supports 'displayName', 'description', 'eventTypes', 'status', 'endpointUrl', 'workflowIds'."
    }
  ];

  string id = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The unique identifier of the webhook."
    }
  ];
}

message UpdateWebhook {
  string display_name = 2 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The unique display name of the webhook."
    }
  ];
  string description = 3 [
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The description of the webhook."
    }
  ];
  repeated string event_types = 4 [
    (buf.validate.field).repeated.items = {
      string: {
        in: ["execution.completed", "execution.failed", "execution.cancelled"]
      }
    },
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The event types that the webhook listens to. Possible values are: 'execution.completed', 'execution.failed', 'execution.cancelled'."
    }
  ];
  string status = 5 [
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).string = {
      in: ["enabled", "disabled"]
    },
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The status of the webhook. Possible values are: 'enabled', 'disabled'."
    }
  ];
  string endpoint_url = 6 [
    (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED,
    (buf.validate.field).string.uri = true,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The URL to which the webhook sends the payload."
    }
  ];
  repeated string workflow_ids = 7 [
    (buf.validate.field).repeated.unique = true,
    (buf.validate.field).repeated.items = {
      string: {
        min_len: 1
      }
    },
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The ids of the workflow which webhook registers on."
    }
  ];
}

message DeleteWebhookRequest {
  string id = 1 [
    (buf.validate.field).string.min_len = 1,
    (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
      description: "The unique identifier of the webhook."
    }
  ];
}
