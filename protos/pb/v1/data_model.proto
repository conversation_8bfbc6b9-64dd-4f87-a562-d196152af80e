syntax = "proto3";

package pb.v1;

import "buf/validate/validate.proto";
import "google/api/field_behavior.proto";
option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1";

message DocumentBlob {
  // The IANA MIME type of the file
  string mime_type = 1 [(google.api.field_behavior) = REQUIRED,
    (buf.validate.field).string = {
      in: [ 
        "application/pdf",
        "image/jpeg",
        "image/png",
        "image/gif",
        "image/tiff",
        "image/bmp",
        "image/webp"
      ]
    }
  ];
  // Document bytes. Document bytes must be non-empty. 
  bytes content = 2 [
    (buf.validate.field).bytes = {
      min_len: 1
    }
  ];
  // The display name of the document, it supports all Unicode characters except
  // the following:
  // `*`, `?`, `[`, `]`, `%`, `{`, `}`,`'`, `\"`, `,`
  // `=` and `:` are reserved.
  // If not specified, Execution ID will be used as name.
  string name = 3 [
    (buf.validate.field).string = {
      pattern: "^[^*?\\[\\]%{}'\",=:]*$",
      // In order to avoid the filename too long issue and accommodate the
      // gcs file name length limit, we give a little buffer to the filename 
      // for suffix and execution id, and set the max length to 950
      max_bytes: 950
    }
  ];
}

message Document {
  oneof data {
    DocumentBlob document_blob = 1;
  }
}
