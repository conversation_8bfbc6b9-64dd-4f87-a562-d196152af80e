syntax = "proto3";

package pb.orby_internal;

import "pb/v1alpha1/auth_connection.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/"
                    "internal_app";

// This service is used to create, get, and delete credentials.
// This is used internally to create orby-owned credentials. 
// Customer owned credentials are created during create connection API.
service CredentialsService {
  rpc CreateCredential(CreateCredentialRequest) 
    returns (CreateCredentialResponse) {}
}

message AuthorizationCredential {
  // This is the id of the credential
  string id = 1;
  // The ID of the application integration to create a connection for
  string application_integration_id = 2;
  // The human readable name of the credential
  // This is optional.
  string credential_name = 3;
  // The connection parameter for the credential
  oneof connection_param {
    pb.v1alpha1.DelegatedAccessParam delegated_access = 4;
    pb.v1alpha1.ApplicationAccessParam application_access = 5;
  }
  // The ID of the user who created the credential
  string created_by = 6;
}


message CreateCredentialRequest {
  AuthorizationCredential credential = 1;
}

message CreateCredentialResponse {
  // The created credential id
  string id = 1;
}

