syntax = "proto3";

package pb.process_discovery;

import "google/protobuf/timestamp.proto";
import "pb/v1alpha1/orbot_action.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/process_discovery";

// Enum for different attribute types
enum AttributeType {
  ATTRIBUTE_TYPE_UNSPECIFIED = 0;
  ATTRIBUTE_TYPE_STRING = 1;
  ATTRIBUTE_TYPE_INT = 2;
  ATTRIBUTE_TYPE_FLOAT = 3;
  ATTRIBUTE_TYPE_BOOL = 4;
  ATTRIBUTE_TYPE_TIMESTAMP = 5;
}

// Typed attribute value
message AttributeValue {
  oneof value {
    string string_value = 1;       
    int32 int_value = 2;         
    float float_value = 3;        
    bool bool_value = 4;           
    google.protobuf.Timestamp timestamp_value = 5; 
  }
}

// Schema for attributes that a step or a process can have
message Attribute {
  // The key of the attribute.
  string key = 1;
  // The description of the attribute.
  string description = 2;
  // The expected data type of the attribute.
  AttributeType attribute_type = 3;
}

// The category of the step type
enum StepTypeCategory {
  STEP_TYPE_CATEGORY_UNSPECIFIED = 0;
  STEP_TYPE_CATEGORY_APPROVED = 1;
  STEP_TYPE_CATEGORY_DISCOVERED = 2;  // Step type discovered by model but not yet approved by user
}

// Reusable schema for steps
message StepType {
  // Unique ID for the step type (e.g., JIRA::create_ticket).
  string id = 1;
  // Display name for the step type (e.g., "Create Ticket").
  string display_name = 2;
  // Description for the step type.
  string description = 3;
  // The application that the step type is associated with.
  // e.g. "JIRA", "GMAIL", "SALESFORCE"
  string application = 4;
  // Attributes associated with the step type.
  repeated Attribute attributes = 5;
  // Nested sub-steps if applicable.
  repeated StepType sub_steps = 6;

  enum StepIndex{
    STEP_INDEX_UNSPECIFIED = 0;
    STEP_INDEX_START = 1;
    STEP_INDEX_END = 2;
  }
  StepIndex step_index = 7;

  StepTypeCategory step_type_category = 8;
}

// Instance of a step annotation for a single observation.
message StepAnnotation {
    // References the StepType by ID.
    string step_type_id = 1;
    // Maps attribute keys to values.
    map<string, AttributeValue> attribute_values = 2;
}

// StepAnnotation grouped with corresponfing continuous observations.
// Assume that a process instance has following observations with their step types:
// o1, o2, o3, o4, o5, o6
// s1, s1, s2, s2, s1, s3
// Then the step annotations with observations will be as follows. The sequence will 
// be maintained. Only continuous observations are included.
// step_annotations_with_observations: [
//   {
//     step_type_id: "s1"
//     observations: [o1, o2]
//   },
//   {
//     step_type_id: "s2"
//     observations: [o3, o4]
//   },
//   {
//     step_type_id: "s1"
//     observations: [o5]
//   },
//   {
//     step_type_id: "s3"
//     observations: [o6]
//   }
// ]
message StepAnnotationWithObservations {
  // Unique ID for the step annotation with observations.
  string id = 4;
  
  // References the StepType by ID.
  string step_type_id = 1;
  
  // Maps attribute keys to values.
  // These are step level attribute values. These are derived from 
  // observation level attribute values.
  map<string, AttributeValue> step_attribute_values = 2;

  // The observations of the step node execution, including 
  // actions descriptions, screenshots, etc.
  repeated Observation observations = 3;

  // Statistics or derived metadata about this step.
  StepAnnotationStats stats = 5;
}

message StepAnnotationStats {
  // The time taken to complete the step in seconds.
  int32 time_taken_seconds = 1;
}

// A path characterizes specific variations of a process type. We use 
// it to differentiate between different ways to complete a process type, 
// and to connect it to automation instructions.
message ProcessPath {
  string id = 1;
  string display_name = 2;
  string description = 3;
  // The category of the path
  PathCategory path_category = 4;
  // The steps that are part of the path
  repeated string step_type_ids = 5;
}

enum PathCategory {
  PATH_CATEGORY_UNSPECIFIED = 0;
  PATH_CATEGORY_HAPPY_PATH = 1;
  PATH_CATEGORY_ADDITIONAL_PATH = 2;
  PATH_CATEGORY_DISCOVERED = 3;  // Path discovered by model but not yet approved by user
}

// Reusable schema for processes.
message ProcessType {
  // Unique ID for the process type.
  string id = 1;
  // Display name for the process type.
  string display_name = 2;
  // Description for the process type.
  string description = 3;
  // Attributes associated with the process type.
  repeated Attribute attributes = 4;
  // Applications associated with the process type.
  // e.g. "JIRA", "GMAIL", "SALESFORCE"
  repeated string applications = 5;
  // Paths that qualify the process type.
  repeated ProcessPath paths = 6;
}

// Instance of a process annotation for a workflow
message ProcessAnnotation {
  // References the ProcessType by ID
  string process_type_id = 1;
  // Maps attribute keys to values.
  map<string, AttributeValue> attribute_values = 2;
  // The path of the process
  string process_path_id = 3;
}

// Annotation regarding a meaningful workflow from a trace. 
message WorkflowAnnotation {
  // Observations that form a workflow. Normally consecutive, but in cases where the user
  // multi-tasking with different workflows in a trace, they can be non-consecutive.
  repeated string observation_ids = 1;    
  // Process annotation of the workflow.
  ProcessAnnotation process_annotation = 2;
  // Step annotations for the workflow.
  repeated StepAnnotation step_annotations = 3;
}

// Auxiliary information extracted from an observation.
message AuxiliaryObservationInfo {
  // Binary data for the screenshot, encoded as a PNG file.
  bytes before_state_screenshot_data = 1;

  // The total time taken to complete the observation in seconds
  // Calculated as the difference between the timestamp of the
  // current observation and the timestamp of the previous observation in a trace.
  int32 time_taken_seconds = 2;
}

// Observations, currently from Orbot.
message Observation {
  // The unique ID of the observation.
  string id = 1;

  // The observed user event from Orbot.
  pb.v1alpha1.UserEvent user_event = 2;

  // Optional auxiliary information accompanying the observation.
  // Also useful for constructing a standalone dataset with full information without accessing to 
  // databases.
  AuxiliaryObservationInfo auxiliary_observation_info = 3;

  // Attributes extracted for this observation.
  // Empty initially (i.e. when present in the trace), but will be filled by trace annotation pipeline.
  map<string, AttributeValue> attribute_values = 4;
}

// A trace is a sequential collection of observations.
message Trace {
  // The unique ID of the trace. Could be a session ID, a trace ID, etc.
  string id = 1;
  // All observations in the trace.
  repeated Observation observations = 2;
}

// Annotation of a trace.
message TraceAnnotation {
  // The unique ID of the trace being annotated.
  string trace_id = 1;
  // Workflow annotations for the trace.
  repeated WorkflowAnnotation workflow_annotations = 2;
}

// A process discovery dataset without annotation.
message Dataset {
  // The name of the dataset.
  string name = 1;
  // The description of the dataset.
  string description = 2;
  // All traces in the dataset.
  repeated Trace traces = 3;
}

// An annotation of the process discovery dataset.
// Could be annotated by human or algorithms.
message DatasetAnnotation {
  // The name of the annotation
  string name = 1;
  // The name of the dataset being annotated
  string dataset_name = 2;
  // The description of the annotation
  string description = 3;
  // Process types to discover, provided by the user or mined (in the future).
  repeated ProcessType process_types = 4;
  // Step types to discover, provided by the user or mined (in the future).
  repeated StepType step_types = 5;
  // Annotations for the traces. Only populated for annotated traces or traces that have been mined by the algorithms.
  repeated TraceAnnotation trace_annotations = 6;
}

// A file containing a dataset and its annotation. 
// Could be used for storing a dataset and its annotation in a file in a cloud storage.
message DatasetFile {
  // The name of the dataset file
  string name = 1;
  // The description of the dataset file
  string description = 2;
  // The dataset annotation file content
  DatasetAnnotation dataset_annotation = 3;
  // The dataset file content
  Dataset dataset = 4;
}

// Source of a Standard Operating Procedure (SOP) document
// for a process
message SopSource {
  oneof source {
    // GCS URI of the SOP file
    // Supported extensions: .pdf
    string gcs_uri = 1;
    // Text content of the SOP in markdown format
    // currently will be used for debugging purposes
    string text = 2;
    // The SOP file content in bytes
    // This will be used when the SOP file is uploaded from the client
    bytes file_content = 3;
  }
  // The timestamp when the SOP was generated
  google.protobuf.Timestamp generated_time = 4;
}
