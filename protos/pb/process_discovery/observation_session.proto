syntax = "proto3";

package pb.process_discovery;

import "common/user_profile.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/process_discovery";

message ObservationSession {
  string id = 1;
  string name = 2;
  google.protobuf.Timestamp start_time = 3;
  google.protobuf.Timestamp end_time = 4;
  common.UserProfileInfo creator = 5;

  // The total number of actions in the trace. 
  // This value is set when list of traces is fetched.
  optional int32 total_actions = 6;

  enum ProcessingStatus {
    STATUS_UNSPECIFIED = 0;
    // The observation session is processed and ready to be used
    STATUS_COMPLETED = 1;
    // The observation session is in progress, e.g. PII redaction
    STATUS_IN_PROGRESS = 2;
  }
  
  // This will be set by the server when returning list of observation sessions.
  // FE will use this to handle those traces which are not yet processed.
  ProcessingStatus processing_status = 7;
}
