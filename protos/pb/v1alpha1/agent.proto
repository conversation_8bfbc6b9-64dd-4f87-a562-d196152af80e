syntax = "proto3";

package pb.v1alpha1;

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

// Communication between the browser extension and desktop app, which uses the
// Chrome's native messaging through stdin/stdout. The message would be serialized
// using JSON.
//
// see: https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions/Native_messaging
//
message InterAgentMessage {
  // message sent from one agent to the other asking for a registration auth code
  message AuthCodeRequest {
  }
  // if the receiving agent has machine identity set up, it would call the
  // MachineIdentityService.GenerateAuthCode API and forward the
  // information in GenerateAuthResponse using the following message.
  message AuthCodeResponse {
    string auth_code = 1;
    string agent_id = 2;
  }
  // if the receiving agent doesn't have machine identity set up, or the process
  // yields some error, it would return an AuthCodeError message.
  enum AuthCodeError {
    AUTH_CODE_ERROR_UNSPECIFIED = 0;
    AGENT_NOT_REGISTERED = 1;
    API_ERROR = 2;
  }

  // all messages can be sent from either agent if not specified
  oneof message_type {
    AuthCodeRequest auth_code_request = 1;
    AuthCodeResponse auth_code_response = 2;
    AuthCodeError auth_code_error = 3;
  }
}
