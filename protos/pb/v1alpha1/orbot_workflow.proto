syntax = "proto3";

package pb.v1alpha1;

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "pb/v1alpha1/orbot_action.proto";
import "common/user_profile.proto";
import "pb/v1alpha1/schedule.proto";
import "pb/v1alpha1/variables.proto";
import "common/common.proto";
import "common/review.proto";

message Workflow {
  string id = 1;

  // Display name.
  string display_name = 2;

  // User-editable description of the workflow.
  string description = 3;

  // A process is inferred from one or more user recorded examples.
  message Process {
    repeated ActionGroup generated_action_groups = 2 [deprecated = true];
    repeated Action actions = 1;
    string description = 3;
    string id = 4;

    // Execute the process when there is an error detected in any action. The
    // following information would be passed to the process:
    // 1. the environment variables for the current process.
    // 2. the error message.
    // 3. the failed action in serialized JSON format.
    string error_handling_process_id = 6;
  }

  // To be deprecated, use processes instead. Keeping it for now for backward compatibility.
  Process task_execution = 5;
  // A workflow can have one or multiple processes.
  // The first process is by default the entry point if no id is specified when creating a task.
  repeated Process processes = 15;

  // Time when the workflow was created/updated.
  google.protobuf.Timestamp create_time = 6;
  google.protobuf.Timestamp last_update_time = 7;

  enum Status {
    STATUS_UNSPECIFIED = 0;
    DRAFT = 1;
    // A published workflow is visible to all users in the same organization.
    PUBLISHED = 2;
  }
  Status status = 8;

  // Organization which the workflow belongs.
  string org_id = 9;

  // which steps should be present for human review
  enum ActionsForReview {
    REVIEW_MODE_UNSPECIFIED = 0;
    // Currently only ML-based actions, such as ValidateAction, has confidence scores.
    LOW_CONFIDENCE_ACTIONS = 1;
    // The final actions before a workflow finishes is usually form submission,
    // thus we have this option to allow for user review.
    // If the last ActionGroup is a conditional action, then the last actions
    // from both branches would be treated as final actions.
    FINAL_ACTIONS = 2;
    ALL_ACTIONS = 3;
  }
  repeated ActionsForReview actions_for_review = 10;

  // confidence threshold when review for LOW_CONFIDENCE_ACTIONS is enabled.
  float low_confidence_threshold = 11;

  message ExportOutput {
    enum OutputType {
      OUTPUT_TYPE_UNSPECIFIED = 0;
      OUTPUT_TYPE_JSON_FILE = 1;
    }
    OutputType output_type = 1;
    bool encryption_enabled = 2; // stores the encryption required flag
  }
  // generate output for each task corresponding to workflow.
  ExportOutput export_output = 12;

  // reviewers who would be able to access the workflow and create tasks for it
  repeated string reviewer_ids = 13;

  string creator_id = 14;

  reserved 4;
  // Creator info of the workflow
  common.UserProfileInfo creator = 16;

  enum Mode {
    MODE_UNSPECIFIED = 0;
    MODE_ASSISTED = 1;
    MODE_AUTOMATED = 2;
    // Default mode requires human review at every smart action
    // and need the highest level of human review
    MODE_DEFAULT = 3;
  }
  Mode mode = 17;

  repeated string applications = 18;

  repeated Schedule schedules = 19;

  // Admins of the workflow. Creator will be in the list of admins by default.
  repeated string admin_ids = 20;

  repeated common.ReviewerList reviewer_lists = 21;

  message ExecutionAssignment {
    // Assignment strategy for this workflow
    message AssignmentStrategy {
      enum AssignmentStrategyType {
        ASSIGNMENT_STRATEGY_TYPE_UNSPECIFIED = 0;
        BASIC_ROUND_ROBIN = 1;
      }
      AssignmentStrategyType assignment_strategy_type = 1;
    }
    AssignmentStrategy assignment_strategy = 1;

    // list of machines that can execute this workflow
    message Machines {
      string id = 1;
      string name = 2;
    }
    repeated Machines machines = 2;
  }
  ExecutionAssignment execution_assignment = 22;

  WorkflowAdminEmailConfig admin_email_config = 23;

  // Stores information on what secret blocks should be used for what machines/users
  // for what actions.
  repeated WorkflowSecretConfig secret_configs = 24;

  // Environment variables for the workflow. The name is expected to be unique.
  // A default value can be provided and used in execution.
  repeated Variable environment_variables = 25;

  // Tracks how the workflow was created
  // This is used to determine what UI to show for the workflow
  enum CreationMethod {
    CREATION_METHOD_UNSPECIFIED = 0;    // Field is unspecified for workflows created before self service, or from templates
    CREATION_METHOD_SELF_SERVICE = 1;   // Created with self service
  }
  CreationMethod creation_method = 26;

  // ID of the prebuilt template that this workflow is copied from.
  // This field is only set when workflow is a template and is copied from
  // a prebuilt template.
  string template_source_id = 27;
}

message WorkflowAdminEmailConfig {
  // Indicates whether to send emails to newly added admins.
  // Set to false to disable email notifications.
  bool send_email_to_admins = 1;

  // A message to be send to all newly added admins.
  // Skipping this will send a default message.
  string email_message = 2;
}

// A workflow can be executed multiple times.
message WorkflowTask {
  // task id,
  string id = 1;
  string org_id = 20;

  // Workflow name such as 1234
  string workflow_id = 2;
  string process_id = 11;

  repeated WorkflowVariable variables = 3;

  // Time when the task is discovered
  google.protobuf.Timestamp discover_time = 4;
  // Time when the task starts execution.
  google.protobuf.Timestamp start_time = 5;
  // Time when the task completes execution (whether successfully or not).
  google.protobuf.Timestamp end_time = 6;

  enum Status {
    STATUS_UNSPECIFIED = 0;
    // the task has been discovered and created, but we haven't started execution.
    PENDING = 1;
    // the task has been picked up for execution.
    EXECUTING = 2;
    // the execution is suspended and waiting for human review.
    WAITING_FOR_REVIEW = 3;
    // the execution has finished successfully.
    SUCCESS = 4;
    // there was error when executing the workflow and we cannot recover from it.
    // for other cases, there could be unexpected errors which cause direct failure.
    FAIL = 5;
    // the task was terminated by the user.
    TERMINATED = 6;
    // This is for temp purpose to represent waiting proceed executions.
    PENDING_PROCEED = 7;
    // This represents blocked workflows
    BLOCKED = 8;
  }
  Status status = 7;

  // list of actions executed.
  repeated ExecutedAction executed_actions = 8;

  // message if status is FAIL.
  string error_message = 9;

  // Present if it is created by another task via the CreateTask action.
  string parent_task_id = 10;

  // this is not stored in db, only executor_id/creator_id is stored in db.
  // this is assembled from the executor_id/creator_id and users collection.
  common.UserProfileInfo creator = 12;

  string workflow_display_name = 13;
  // The id corresponds to the browser instance that claims the execution
  string connection_id = 14;
  // User who claims and executes the execution
  string executor_id = 15;
  // This field is only populated for executions scheduled from temporal
  // workflow and the id correspond to temporal id
  string schedule_id = 16;

  common.DeletedObjectInfo deleted_object_info = 17;

  // Show how long the task will be retained in the system.
  // Calculation: (end_time / deleted_time) + organization retention policy 
  // duration - current time.
  // If tasks is not deleted and not in final status(success/failed/
  // terminated), or if organization doesn't set retention policy, this field
  // will be null.
  google.protobuf.Duration remaining_retention = 18;
  // Time when the task is last updated.
  google.protobuf.Timestamp last_update_time = 19;
}