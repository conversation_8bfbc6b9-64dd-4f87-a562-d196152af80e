syntax = "proto3";

package pb.v1alpha1;

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

import "google/protobuf/field_mask.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "pb/v1alpha1/orbot_workflow.proto";
import "pb/v1alpha1/orbot_action.proto";
import "pb/v1alpha1/actiongen.proto";
import "pb/v1alpha1/actionprocessing.proto";
import "pb/v1alpha1/element.proto";
import "pb/v1alpha1/elementlocating.proto";
import "pb/v1alpha1/variables.proto";
import "common/data_query_params.proto";

service Orbot {

  rpc CreateWorkflow(CreateWorkflowRequest) returns (Workflow) {}
  // GenerateWorkflow generates a workflow from a document.
  rpc GenerateWorkflowFromPolicy(GenerateWorkflowFromPolicyRequest) returns (Workflow) {}

  rpc GetWorkflow(GetWorkflowRequest) returns (Workflow) {}

  rpc UpdateWorkflow(UpdateWorkflowRequest) returns (Workflow) {}

  rpc AddWorkflowActions(AddWorkflowActionsRequest) returns (google.protobuf.Empty) {}

  rpc ListWorkflows(ListWorkflowsRequest) returns (ListWorkflowsResponse) {}

  rpc ListUnifiedWorkflows (ListWorkflowsRequest) returns (ListWorkflowsResponse) {}

  // DeleteWorkflow initiates an asynchronous deletion of workflow as well as all associated tasks
  // This operation returns immediately, allowing the deletion process to complete in the background.
  rpc DeleteWorkflow(DeleteWorkflowRequest) returns (DeleteWorkflowResponse) {}

  rpc CreateWorkflowTask(CreateWorkflowTaskRequest) returns (WorkflowTask) {}

  rpc GetWorkflowTask(GetWorkflowTaskRequest) returns (WorkflowTask) {}

  // Allows user to update task status, for example, for WAITING_FOR_REVIEW to SUCCESS
  rpc UpdateWorkflowTask(UpdateWorkflowTaskRequest) returns (WorkflowTask) {}

  rpc ListWorkflowTasks(ListWorkflowTasksRequest) returns (
      ListWorkflowTasksResponse) {}

  // DeleteExecutions initiates an asynchronous deletion of specified executions.
  // This operation returns immediately, allowing the deletion process to complete in the background.
  rpc DeleteExecutions(DeleteExecutionsRequest) returns (DeleteExecutionsResponse) {}

  rpc CancelExecution(CancelExecutionRequest) returns (CancelExecutionResponse) {}

  rpc ProcessSmartActions(ProcessSmartActionsRequest) returns (ProcessSmartActionsResponse) {}

  rpc InferMacroActionStep(InferMacroActionStepRequest) returns (InferMacroActionStepResponse) {}

  rpc GenerateActionDescription(GenerateActionDescriptionRequest) returns (GenerateActionDescriptionResponse) {}

  rpc GenerateJsAction(GenerateJsActionRequest) returns (GenerateJsActionResponse) {}

  // Get element candidates for an action.
  rpc GetActionableElements(GetActionableElementsRequest) returns (GetActionableElementsResponse) {}

  // Notify user that review queue is full
  rpc SendEmailForReview(SendEmailForReviewRequest) returns (google.protobuf.Empty) {}

  // Query cached element locators for the given action
  rpc GetElementLocators(GetElementLocatorsRequest) returns (GetElementLocatorsResponse) {}

  // ML service to locate an element on a Web page
  rpc LocateElement(LocateElementRequest) returns (LocateElementResponse) {}

  // Reports that the given ElementLocator works after execution. The validation can be:
  // 1. a human verifies that the step was executed successfully;
  // 2. orbot verifies that the step and following steps work as expected, using heuristics such as screenshots.
  rpc ReportWorkingElementLocator(ReportWorkingElementLocatorRequest) returns (google.protobuf.Empty) {}

  // Create or update a workflow template. We don't support partial update, entire template will be replaced in update.
  rpc SaveWorkflowTemplate(SaveWorkflowTemplateRequest) returns (Workflow) {}

  rpc GetWorkflowTemplate(GetWorkflowTemplateRequest) returns (Workflow) {}

  rpc ListWorkflowTemplates(ListWorkflowTemplatesRequest) returns (ListWorkflowTemplatesResponse) {}

  rpc DeleteWorkflowTemplate(DeleteWorkflowTemplateRequest) returns (DeleteWorkflowTemplateResponse) {}

  rpc SaveUserFeedback(SaveUserFeedbackRequest) returns (SaveUserFeedbackResponse) {}

  rpc SendEmail(SendEmailRequest) returns (SendEmailResponse) {}

  rpc SaveUserEvents(SaveUserEventsRequest) returns (SaveUserEventsResponse) {}

  // Called when the user clicks to start an observation mode.
  rpc StartObservation(StartObservationRequest) returns (StartObservationResponse) {}
  
  // Called when the user ends an observation mode (or closes the window).
  rpc EndObservation(EndObservationRequest) returns (EndObservationResponse) {}

  // Execute a list of actions
  // Returns the outputs of those actions
  rpc ExecuteAction(ExecuteActionRequest) returns (ExecuteActionResponse) {}
}
 
message ExecuteActionRequest {
  string workflow_id = 1;
  string connection_id = 2;
  // Actions to execute, could be from a draft of a workflow
  // Initially we only support executing a single action, but keeping this as repeated
  // to support future extensions
  repeated Action actions = 3;
  // Environment variables required for running the action
  // Key is the name of the environment variable
  map<string, VariableValue> env_variables = 4;
  // Referenced variables from previous action outputs required for running the action
  // Key is the action id of a previous action
  map<string, VariableValue> action_variables = 5;
}

message ExecuteActionResponse {
  // Map of actionIds to their outputs
  map<string, VariableValue> outputs = 1;
  // Map of actionIds to their review task ids
  map<string, string> review_task_id = 2;
  // Execution id
  string execution_id = 3;
}

message GetWorkflowRequest {
  string workflow_id = 1;
  string org_id = 2;
}

message UpdateWorkflowRequest {
  Workflow workflow = 1;
  google.protobuf.FieldMask update_mask = 2;
  string org_id = 3;
}

message CreateWorkflowRequest {
  // Workflow.org_id must be set
  Workflow workflow = 1;

  reserved 2;
}

message ListWorkflowsRequest {
  string org_id = 1;
  int32 page_size = 2;
  // Will be deprecated in the near future, please use page_number
  string page_token = 3;

  // search by name, right now we only support prefix search
  // Deprecated, please use prefix_name filter
  string name_search = 4 [deprecated = true];
  // Indexed from 1
  int32 page_number = 5;
  // If unset:
  //   Admin: list workflows for the organization.
  //   User: list assigned workflows where user is a workflow user.
  // If set:
  //   Admin: list assigned workflows for this user
  //   User: error if set to other users. No difference if set to own email.
  // Will be deprecated shortly, please use the user option in 'filters'
  // Non-admin will only be able to see their own workflows
  string user = 6;
  // Deprecated: moving forward, use WorkflowFilters
  // Supported filters:
  // name_prefix="value"
  // create_time_lt={UNIX_TIME_SEC}
  // create_time_gt={UNIX_TIME_SEC}
  // last_update_time_lt={UNIX_TIME_SEC}
  // last_update_time_gt={UNIX_TIME_SEC}
  // userIds="{Id1}-{Id2}"
  // multiple user Ids can be provided by a dash separator
  // creatorIds="{Id1}-{Id2}"
  // multiple creator Ids can be provided by a dash separator
  // mode={DEFAULT|ASSISTED}
  // multiple mode values can be provided with a dash (-) separator, eg. "mode=DEFAULT-ASSISTED"
  string filter = 7;
  // The order of fields will effect the sorting order.
  // Supported fields: display_name, create_time, last_update_time
  repeated common.SortField sort = 8;
  // Use this to send only relevant data in response
  // - If Field Mask is not send or is sent with empty paths then the result will contain
  //    the complete object
  // - Valid values for field mask are: id, display_name, description, task_execution,
  //    processes, create_time, last_update_time, status, org_id, actions_for_review,
  //    low_confidence_threshold, export_output, reviewer_ids, creator_id
  // - Field mask will always contain `name` field. Please do not send it in Paths to avoid errors.
  google.protobuf.FieldMask field_mask = 9;
  // Filters for listing workflows
  message WorkflowFilters {
    string name_prefix = 1;
    //{UNIX_TIME_SEC}
    int64 create_time_lt = 2;
    //{UNIX_TIME_SEC}
    int64 create_time_gt = 3;
    //{UNIX_TIME_SEC}
    int64 last_update_time_lt = 4;
    //{UNIX_TIME_SEC}
    int64 last_update_time_gt = 5;
    //{true|false}
    bool is_template = 6;
    // {extraction|classification}"
    string type = 7;
    // {status_enabled|status_disabled},
    string status = 8;
    // List of applications for a workflow {Google Sheets, SFTP Server, Gmail, Other}
    repeated string applications = 9;
    // {MODE_UNSPECIFIED|MODE_AUTOMATED|MODE_ASSISTED}
    repeated string modes = 10;
    // List of user ids, refers to reviewers of a workflow
    repeated string user_ids = 11;
    // List of creator ids, refers to creators of a workflow
    repeated string creator_ids = 12;
    // List of user emails, referes to reviewers of a workflow
    repeated string user_emails = 13;
    // List of creator emails for orby workflows (to be deprecated)
    repeated string creator_emails = 14;
  }

  WorkflowFilters workflow_filters = 10;
}

message ListWorkflowsResponse {
  repeated Workflow workflows = 1;
  // Will be deprecated in the near future
  string next_page_token = 2;
  int32 total_size = 3;
}

message DeleteWorkflowRequest {
  string workflow_id = 1;
  string org_id = 2;
  string reason = 3;
}

message DeleteExecutionsRequest {
  // List of IDs for executions to be deleted.
  repeated string execution_ids = 1;

  // Reason for deletion
  string reason = 2;
}

message DeleteExecutionsResponse {
  // Temporal ID for tracking the deletion process in Temporal.
  // Currently, there is no provision for the frontend to query for results,
  // but this might be added in the future if needed.
  string temporal_id = 1;
}

message DeleteWorkflowResponse {
  string operation_id = 1;
}

message CreateWorkflowTaskRequest {
  WorkflowTask task = 1;
  string org_id = 2;
}

message GenerateWorkflowFromPolicyRequest {
  // This is the name of the workflow that will be generated.
  string name = 1;
  // This is the file ID that refers to the document which will be used to generate the workflow.
  // We should first upload the file using the CreateFileUploadRequest API and then use the file ID here.
  string file_id = 2;
}

message GetWorkflowTaskRequest {
  string task_id = 1;
  string org_id = 2;
}


// The lifecycle of Task:
// discover: createTask -> pending
// execute: updateTask -> executing
// pause and waiting for human review: updateTask -> waiting for review
// succeed: updateTask -> success, executedActions
// failed: updateTask -> failed, executedActions, errorMessage
// terminated: updateTask -> terminated, executedActions
message UpdateWorkflowTaskRequest {
  string task_id = 1;
  WorkflowTask.Status status = 2;
  string org_id = 3;

  // overwrite this field everytime
  repeated ExecutedAction executed_actions = 4;
  string error_message = 5;

  // append one or more ExecutedActions to the WorkflowTask.executed_actions field
  repeated ExecutedAction append_executed_actions = 6;

  google.protobuf.Timestamp start_time = 7;
  google.protobuf.Timestamp end_time = 8;

  // User can claim any execution at the initial time of execution.
  string executor_id = 9;
  // Client connection on which the execution is running.
  string connection_id = 10;
}

message AddWorkflowActionsRequest {
  string workflow_id = 1;
  string process_id = 2;
  // Provide context on where to add the actions
  ActionPosition action_position = 3;
  repeated Action actions = 4;
  // Use recording session id to associate recording with user files (screenshot and snapshot)
  string recording_session_id = 5;
}

message ActionPosition {
  // If set, insert directly after this action.
  // Otherwise, insert at beginning of the given scope
  string after_action_id = 1;
  
  message Condition {
    enum Branch {
      BRANCH_UNSPECIFIED = 0;
      THEN_BRANCH = 1;
      ELSE_BRANCH = 2;
    }
    Branch branch = 1;
    string action_id = 2;
  }

  message Loop {
    string action_id = 1;
  }

  message Block {
    string action_id = 1;
  }

  message Process {}

  oneof scope {
    // Insert at the top level of the workflow process
    Process process = 2;
    // Insert within a specific condition action
    Condition condition = 3;
    // Insert within a specific loop action
    Loop loop = 4;
    // Insert within a specific action block
    Block block = 5;
  }
}


message ListWorkflowTasksRequest {
  // Deprecated: use workflow_ids
  string workflow_id = 1 [deprecated = true];
  int32 page_size = 2;
  // Will be deprecated in the near future, please use page_number
  string page_token = 3;
  string org_id = 4;

  // Will be deprecated, please use status in filter instead
  repeated WorkflowTask.Status statuses = 5;
  // Will be deprecated, please use user_ids in filter instead
  repeated string user_ids = 6;

  // Will be deprecated, please use variable_search in filter instead
  string variable_search = 7;

  // filter by the task that creates it via the CreateTask action
  string parent_task_id = 8 [deprecated = true];

  // Will be deprecated, please use workflow_resource_names in filter instead
  repeated string workflow_ids = 9;

  // page index, start from 1
  int32 page_number = 11;

  ListWorkflowTasksRequestFilter filter = 12;

  // The order of fields will effect the sorting order.
  // Supported fields: status, start_time, end_time
  repeated common.SortField sort = 13;
}

message ListWorkflowTasksRequestFilter {
  // text search for all values in WorkflowTask.variables,
  // right now we only support prefix search
  string variable_prefix = 1;
  repeated WorkflowTask.Status statuses = 2;
  // Orbot workflow resource name. Format: orbot_workflows/{ID}
  repeated string workflow_resource_names = 3;
  repeated string user_ids = 4;
  // last_update_time_lt={UNIX_TIME_SEC}
  int64 last_update_time_lt = 5; 
  // last_update_time_gt={UNIX_TIME_SEC}
  int64 last_update_time_gt = 6;
}

message ListWorkflowTasksResponse {
  repeated WorkflowTask tasks = 1;
  // Will be deprecated in the near future
  string next_page_token = 2;
  int32 total_size = 3;
}

message GetActionableElementsRequest {
  string workflow_id = 1;
  string org_id = 2;
  string action_uuid = 3;
  repeated ControlType element_types = 4;
  string process_id = 5;
}

message GetActionableElementsResponse {
  repeated Element elements = 1 [deprecated = true]; // Deprecated in favor of elements_wrapper
  repeated ElementWrapper elements_wrapper = 2;
}

message SendEmailForReviewRequest {
  message WorkflowInfo {
    string workflow_name = 1;
    int32 num_tasks_for_review = 2;
  }
  // the tasks for review might come from one or more workflows.
  repeated WorkflowInfo workflows = 1;
}

message GetWorkflowTemplateRequest {
  string template_id = 1;
  string org_id = 2;
}

message SaveWorkflowTemplateRequest {
  // If template_id is empty or cannot be found, create a new template.
  // Otherwise update the existing template by replacing the entire content.
  string template_id = 1;
  string org_id = 2;
  Workflow workflow_template = 3;
  bool is_preset = 4;
}

message ListWorkflowTemplatesRequest {
  string org_id = 1;
  int32 page_size = 2;
  // Indexed from 1
  int32 page_number = 3;
  // If true, only return prebuilt templates.
  // For prebuilt templates, org_id is empty.
  bool is_prebuilt = 4;
}

message ListWorkflowTemplatesResponse {
  repeated Workflow workflows = 1;
  int32 total_size = 2;
}

message DeleteWorkflowTemplateRequest {
  string template_id = 1;
  string org_id = 2;
}

message DeleteWorkflowTemplateResponse {
}

message SaveUserFeedbackRequest {
  // Indicate what application the feedback is for.
  // e.g. "concur_expenses"
  string application = 1;
  // Unique identifier for the entity the feedback is for.
  // e.g. For Concur expenses, this is the expense key.
  string entity_id = 2;
  // Generic key-value pairs to keep this API flexible.
  // Current use case of Concur expense report requires the following keys:
  //   "report_id", "exception_code", "exception_message",
  //   "confirmed": "true" or "false",
  //   "comment" (optional),
  map<string, string> metadata = 3;
}

message SaveUserFeedbackResponse {
}

message SendEmailRequest {
  message Attachment {
    string filename = 1;
    bytes content = 2;
    string mime_type = 3;
  }

  message Body {
    // plaintext content with MIME type of `text/plain`
    string plain = 1;
    // rich content with MIME type of `text/html`
    string html = 2;
  }

  repeated string recipients = 1;
  string subject = 2;
  Body body = 3;
  repeated Attachment attachments = 4;
}

message SendEmailResponse {
}

message CancelExecutionRequest {
  string execution_id = 1;
  string org_id = 2;
  // If this field is true, we will only cancel the child execution otherwise we will cancel the execution itself
  bool cancel_nested_execution = 3;
}

message CancelExecutionResponse {
}

message SaveUserEventsRequest {
  repeated UserEvent events = 1;
}

message SaveUserEventsResponse {
}

message StartObservationRequest {
  string trace_id = 1;
  string trace_name = 2;
}

message StartObservationResponse {
}

message EndObservationRequest {
  string trace_id = 1;
}

message EndObservationResponse {
}
