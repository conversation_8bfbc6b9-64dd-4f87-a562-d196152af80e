syntax = "proto3";

package pb.v1alpha1;

import "buf/validate/validate.proto";
import "pb/v1alpha1/auth_connection.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

service ApplicationAuthConnectionService {
  // Create a new connection
  rpc CreateConnection(CreateConnectionRequest) 
    returns (CreateConnectionResponse) {}
  // Get a connection by ID
  rpc GetConnection(GetConnectionRequest) 
    returns (GetConnectionResponse) {}
}

message CreateConnectionRequest {
  AuthorizationConnection connection = 1;
}

message CreateConnectionResponse {
  // The created connection
  AuthorizationConnection connection = 1;
}

message GetConnectionRequest {
  // The identifier of the connection to get
  oneof identifier {
    // The ID of the connection to get
    string id = 1 [(buf.validate.field).string.len = 24];
    // Identifier containing connection name and application type
    ConnectionIdentifier connection_identifier = 2;
  }

    message ConnectionIdentifier {
      // Name of the connection (unique for a user in an org)
      string connection_name = 1;
      // Application type (e.g. "GMAIL", "GDRIVE", etc)
      // This is the application type from the application integrations collection 
      string application_type = 2;
    }
}

message GetConnectionResponse {
  AuthorizationConnection connection = 1;
}
