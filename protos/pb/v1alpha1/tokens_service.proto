syntax = "proto3";

package pb.v1alpha1;

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "grpc/gateway/protoc_gen_openapiv2/options/annotations.proto";
import "pb/v1alpha1/users_service.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Orby Web API - Tokens Service";
    version: "v1alpha1";
  };
};

service Tokens {
  // Refresh Orby AI token
  rpc Refresh(RefreshRequest) returns (RefreshResponse) {
    option (google.api.http) = {
      post: "/v1alpha1/tokens/refresh"
      body: "*"
    };
  }

  // Get new Google oauth2 access token
  rpc GetNewGoogleToken (GetNewGoogleTokenRequest) returns (GetNewGoogleTokenResponse) {}

  // This API will be used for an authenticated user to get an auth code
  // for Oauth2.0 authorization flow.
  rpc GetAuthCode(GetAuthCodeRequest) returns (GetAuthCodeResponse) {}
}

message GetAuthCodeRequest {
    OauthParameters oauth_parameters = 1;
}

message GetAuthCodeResponse {
    string redirect_uri = 1;
}

message RefreshRequest {
  string refresh_token = 1;
}

message RefreshResponse {
  string access_token = 1;
  google.protobuf.Timestamp access_token_expires_at = 2;
}

message GetNewGoogleTokenRequest {
  string email = 1;
}

message GetNewGoogleTokenResponse {
  string access_token = 1;
  google.protobuf.Timestamp access_token_expires_at = 2;
}
