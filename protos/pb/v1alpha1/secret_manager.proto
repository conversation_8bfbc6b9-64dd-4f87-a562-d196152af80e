syntax = "proto3";

package pb.v1alpha1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common/user_profile.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

// Provides endpoints to manage customer-defined secrets. See go/secret-manager.
service SecretManager {
  rpc CreateSecret(CreateSecretRequest) returns (CreateSecretResponse) {}
  rpc GetSecret(GetSecretRequest) returns (GetSecretResponse) {}
  rpc UpdateSecret(UpdateSecretRequest) returns (UpdateSecretResponse) {}
  rpc DeleteSecret(DeleteSecretRequest) returns (google.protobuf.Empty) {}

  // Secret Block APIs
  rpc CreateSecretBlock(CreateSecretBlockRequest) returns (CreateSecretBlockResponse) {}
  rpc GetSecretBlock(GetSecretBlockRequest) returns (GetSecretBlockResponse) {}
  rpc UpdateSecretBlock(UpdateSecretBlockRequest) returns (UpdateSecretBlockResponse) {}
  rpc DeleteSecretBlock(DeleteSecretBlockRequest) returns (google.protobuf.Empty) {}
  rpc ListSecretBlocks(ListSecretBlocksRequest) returns (ListSecretBlocksResponse) {}
}
// A SecretBlock is a collection of secrets.
// It can only be created by the organization admin.
// Other users can only update their own secrets in a block.
message SecretBlock {
  string id = 1;
  string org_id = 2;
  google.protobuf.Timestamp created_time = 3;
  google.protobuf.Timestamp last_updated_time = 4;
  string creator_id = 5;
  // This below fields are used to track the last modified information in the changelogs.
  string last_modified_by = 6;

  // A human readable name to describe the secret block. Optional.
  // Previously (before 2025 Feb 11), block name was unique and used to identify.
  string block_name = 7;

  // Explicitly store the origin URL that the secret block should be for
  string origin_url = 10;

  // Public secret blocks are viewable by all users in an organization.
  // Private secret blocks are only viewable by the user who created them.
  // Private secret blocks can only be used by a user manually running a workflow.
  bool is_public = 11;

  // A list of secrets that are in this block
  repeated Secret secrets = 12;

  // The version of the secret block. Used to determine some aspects of
  // backward compatibility.
  SecretVersion version = 13;

  // deprecated: use secrets field instead.
  repeated SecretInfo secret_infos = 8 [deprecated = true];

  common.UserProfileInfo creator = 9;
}

message SecretInfo {
  Secret secret = 1;

  // Whether or not this secret has a private override for the user.
  // If the secret is private, use this field to determine if the user is authorized
  // to access the secrets.
  bool has_override = 2;
}

message Secret {
  // secret Id generated by the server.
  // It should not be set when creating a secret.
  string id = 1;
  // We can remove this field in the future, since we have secrets wrapped in a block. However keeping it for now for backward compatibility.
  string org_id = 2 [deprecated = true];
  google.protobuf.Timestamp created_time = 3 [deprecated = true];
  // a human readable name to describe what the secret is about, it has to be unique in a secret block.
  // this will also be used as a key in the secret block to identify which secret
  // it is.
  string display_name = 4;
  // the secret value.
  string value = 5;
  enum Scope { 
    SCOPE_UNSPECIFIED = 0;
    SCOPE_ORG = 1;
    SCOPE_PRIVATE = 2; 
  }
  // Cannot be changed once set.
  // Assumption: Secrets inside a block needs to be of the same SCOPE.
  // deprecated: use scope in the secret block instead.
  Scope scope = 6 [deprecated = true];

  common.UserProfileInfo last_updater = 7;

  // The version of the secret. Used to determine some aspects of
  // backward compatibility.
  SecretVersion version = 8;
}

enum SecretVersion {
  VERSION_UNSPECIFIED = 0;
  VERSION_V2 = 1;
}

// Refers to a secret value stored in a SecretBlock.
// When there is no secret being associated (such as in a template), block_id
// would be empty.
message SecretValue {
  string block_id = 1;
  // name need to match the secret display_name provided by the user.
  string name = 2;
}

message CreateSecretRequest {
    Secret secret = 1;
}

message CreateSecretResponse {
  // a secret Id generated by the server.
  string id = 1;
}

message CreateSecretBlockRequest {
  SecretBlock secret_block = 1;
}

message CreateSecretBlockResponse {
  // secrets inside, will only have id and display_name fields populated.
  SecretBlock secret_block = 1;
}

message GetSecretBlockRequest {
  string org_id = 1;
  oneof block_identifier {
    // TODO: Remove for backwards comaptibility. Since after Fegb 2025, secret block names
    // are no longer unique, don't allow querying for it.
    // Since block_name is unique within an organization, we can use it to identify the particular block.
    string block_name = 2 [deprecated = true];
    string id = 3;
  }
}

// Returns the secret block regardless if the user has secrets set in the block or not.
// This is so that FE can get info on this secret block (what fields are present), so
// it can prompt the user accordingly.
// If the secret is private scoped, use has_override field on
// SecretInfo to determine if the user has secrets set (is authorized).
message GetSecretBlockResponse {
  // Secrets inside the block will only have id, display_name, SCOPE fields populated.
  // Clients must call GetSecret individually with the secret ids to get the secret value.
  SecretBlock secret_block = 1;
}

message UpdateSecretBlockRequest {
  string org_id = 1;
  oneof block_identifier {
    // TODO: Remove for backwards comaptibility. Since after Fegb 2025, secret block names
    // are no longer unique, don't allow querying for it.
    string block_name = 2 [deprecated = true];
    string id = 3;
  }
  // secrets is the list of secrets to be updated.
  // Secret id or the displayName should be present in the list, whose value needs to be updated.
  // displayName can also be used since displayName of a secret inside a block is unique.
  // Note: Only the value field can be overwritten.
  repeated Secret secrets = 4;
  
  // Normal users can only update their override.
  // However admins can update their override with the public ones.
  // If this field is set to true and the secret scope was public, the secret value passed will update the public secret value.
  // deprecated: secret blocks no longer have overrides
  bool update_default_value = 5 [deprecated = true]; // default to false
}

message UpdateSecretBlockResponse {
  // secrets inside, will only have id and display_name fields populated.
  SecretBlock secret_block = 1;
}

message DeleteSecretBlockRequest {
  string org_id = 1;
  oneof block_identifier {
    // TODO: Remove for backwards comaptibility. Since after Fegb 2025, secret block names
    // are no longer unique, don't allow querying for it.
    string block_name = 2 [deprecated = true];
    string id = 3;
  }
}

message DeleteSecretBlockResponse {}

message ListSecretBlocksRequest {
  string org_id = 1;
}

message ListSecretBlocksResponse {
  // Only the secretId, displayName, Scope in the secret block will be populated.
  repeated SecretBlock secret_blocks = 1;
}

message GetSecretRequest {
  string org_id = 1;

  // use the secret_id field instead.
  // TODO: remove this after the BE/FE migration is completed.
  string id = 2 [deprecated = true];

  oneof secret_request_type {
    // fetching a secret value by its id
    string secret_id = 4;
    // fetch a secret value by the block id and secret name.
    SecretValue secret_value = 5;
  }

  // This field is only valid if the user has an override for the public secret.
  // If true, the response will include the public secret value and not the specific user's override value. 
  // It can only be retrieved by admins.
  // deprecated: secret blocks no longer have overrides
  bool return_public_value = 3 [deprecated = true]; // default to false
}

message GetSecretResponse {
  Secret secret = 1;
}

// Allow users to update a secret value.
message UpdateSecretRequest {
  string org_id = 1;
  string id = 2;
  // Ovewrite the secret name.
  string value = 3;
}

message UpdateSecretResponse {
  // secret Id generated by the server.
  string id = 1;
}

message DeleteSecretRequest {
  string org_id = 1;
  // secret Id generated by the server.
  string id = 2;
}
