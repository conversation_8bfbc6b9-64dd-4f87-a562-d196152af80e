syntax = "proto3";

package pb.v1alpha1;

import "pb/v1alpha1/schema.proto";
import "google/protobuf/field_mask.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";


service Schemas {
  // Create a schema for the current users.
  // This is only used to generate test data now.
  rpc CreateSchema(CreateSchemaRequest) returns (Schema) {}

  // List schemas ordered by ascending schema resource name.
  rpc ListSchemas(ListSchemasRequest) returns (ListSchemasResponse) {}

  // Can only update schema mode, confidence_threshold, and schema_name.
  rpc UpdateSchema(UpdateSchemaRequest) returns (Schema) {}

  rpc GetSchema(GetSchemaRequest) returns (Schema) {}
}

message CreateSchemaRequest {
  // The parent resource name where the schema is to be created.
  string parent = 1;

  // The schema resource to create. Name field can be empty or otherwise is ignored.
  Schema schema = 2;
}

message ListSchemasRequest {
  // The parent resource name where the schema was created.
  string parent = 1;
  // Default is 10 (when page_size is missing or set to 0). Max value is 20.
  // Ordered by ascending schema resource name.
  int32 page_size = 2;
  // Use this to continue the previous list requests.
  // Its value should be same with previous response's next_page_token.
  string page_token = 3;
}

message ListSchemasResponse {
  // Ordered by ascending schema resource name.
  repeated Schema schemas = 1;
  // If the value is "", it means no more results for the request.
  string next_page_token = 2;
  // Total available schema size.
  // Note it is NOT the remaining available schema size after the current response.
  int32 total_size = 3;
}

message UpdateSchemaRequest {
  Schema schema = 1;
  google.protobuf.FieldMask field_mask = 2;
}

message GetSchemaRequest {
  // Name of the Schema
  string name = 1;
  // Use this to send only relevant data in response
  // - If Field Mask is not send or is sent with empty paths then the result will contain
  //    the complete object
  // - Valid values for field mask are:display_name, entity_types
  // - Field mask will always contain `name` field. Please do not send it in Paths to avoid errors.
  google.protobuf.FieldMask field_mask = 2;
}
