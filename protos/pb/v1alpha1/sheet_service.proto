syntax = "proto3";

package pb.v1alpha1;

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

service Sheet {
    rpc GetSheetColumnNames(GetSheetColumnNamesRequest) returns (GetSheetColumnNamesResponse) {}
}

message GetSheetColumnNamesRequest {
    string sheet_id = 1;
    string sheet_tab_name = 2;
}

message GetSheetColumnNamesResponse {
    repeated string sheet_columns = 1;
}
