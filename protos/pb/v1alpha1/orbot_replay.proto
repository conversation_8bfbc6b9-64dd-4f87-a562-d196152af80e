syntax = "proto3";

package pb.v1alpha1;

import "google/protobuf/timestamp.proto";
import "pb/v1alpha1/element.proto";
import "pb/v1alpha1/orbot_action.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

enum Modifier {
  MODIFIER_UNSPECIFIED = 0;
  ALT = 1;
  CTRL = 2;
  COMMAND = 3;
  META = 4;
  SHIFT = 5;
}

// Raw user events used for testing the recording end-to-end.
// If it involves multiple tab, set tab_index to each UserEvent.
// We only support testing of web primitive actions for now, but we can also
// add widget operations like start for loop in the future.
message SimulateEvent {
  // if this navigate is explicitly provided, we would capture it in recorded events.
  message Navigate {
    string url = 1;
  }

  // we only support basic mouse click, not drag-n-drop.
  // NOTE: we don't reuse ClickAction since we may need to migrate it to use
  // ActionParamValue instead in the future.
  message Click {
    ElementLocator locator = 1;
    ClickType type = 2;

    // modifier keys that are pressed when mouse input is dispatched
    repeated Modifier modifiers = 3;
  }

  // Keyboard events can be typing some text, or some keyboard shortcut like Ctrl+C.
  message KeyboardType {
    string text = 1;

    // modifier keys like Control, Command etc.
    repeated Modifier modifiers = 2;
  }

  // Some keyboard shortcuts are different across operation system. We extract
  // the commonly used ones here so we don't need to care about the underlying
  // keys across platforms.
  enum KeyboardShortcut {
    KEYBOARD_SHORTCUT_UNSPECIFIED = 0;
    COPY = 1;
    PASTE = 2;
  }

  // a special mouse events for text selection
  message Selection {
    // create all or partial selection in one or more elements
    message ElementRange {
      ElementLocator element = 1;
      int32 start_offset = 2;
      // optional if it's the same with the start element
      ElementLocator end_element = 3;
      int32 end_offset = 4;
    }

    oneof select_type {
      // select all the content in the element
      ElementLocator element = 1;
      ElementRange range = 2;
    }
  }
  
  // keyboard shortcuts that is used to trigger some registered broswer action, such as copy/paste.
  message ToolUse {
    string name = 1;
    string method = 2;
    // Partial param value from ActionParamValue, only works for toolUse actions.
    message ToolUseActionParamValue {
      string name = 1;
      string json_value = 2;
    }
    // The inputs to the tool use action, now only has 1 parameter in most cases.
    repeated ToolUseActionParamValue inputs = 3;
  }

  enum Instruction {
    INSTRUCTION_UNSPECIFIED = 0;
    ITERATE_START = 1;
    ITERATE_END = 2;
  }

  // 0-based tab index
  int32 tab_index = 1;

  oneof action_type {
    Navigate navigate = 2;
    Click click = 3;
    KeyboardType type = 4;
    Selection selection = 5;
    Instruction instruction = 6;
    KeyboardShortcut keyboard_shortcut = 7;
    ToolUse tool_use = 8;

    // Missing capabilities that are not supported yet:
    // 1. mouse drag-n-drop
    // 2. close a tab
    // 3. navigate back and forward in browser history
  }
}

message PrecedingAction {
  oneof action {
    ClickAction click = 1;
    SetValueAction set_value = 2;
  }
}

// Replay environment using one or more of the following:
// 1. manually crafted HTML, which can reference resources on the Internet, such
//    as JS/CSS on CDNs.
// 2. WARC archive of applications. Right now if you use WARC, we'll disable
//    access to the Internet.
// 3. live applications on the Internet, which is available unless WARC is used.
message ReplayEnv {
  message StaticPage {
    // HTML file path, which is a relative path to the location of the txtpb file.
    string file_path = 1;

    // for simple cases, we can also directly define the HTML
    string html = 2;

    // the HTML can be served at the arbitrary URL. If not provided, the page
    // would be served at http://orbot.test.
    string serve_at_url = 3;

    // Additional resources that are needed for the static page, which can be:
    //
    // 1. plain JavaScript files
    // 2. TypeScript and/or JSX files would go through webpack for build, make
    //    sure the require_build field is set to true.
    // 3. static CSS styles
    //    NOTE: we don't support styles that require build step, such as SCSS
    // 4. other files such as image/audio/video etc.
    //
    message Resource {
      // relative path to the txtpb file that can be referenced from the html.
      string file_path = 1;

      // the path where the HTML refers to the resource.
      // You don't need to specify this if html file and txtpb file are in the
      // same folder and the resource doesn't need build.
      string serve_at_path = 2;

      // For resources that require compilation and/or bundling, such as React
      // apps that are written in JSX or TypeScript files. Right now this is
      // handled by webpack with tsc. When build is required, we would expect
      // a package.json file to be present along with the resource file to
      // specify dependencies.
      bool require_build = 3;
    }
    repeated Resource resources = 4;
  }

  repeated StaticPage static_pages = 1;
  // GCS file path like gs://bucket/file.warc
  string warc_file_path = 2;
  string start_url = 3;

  // Points to a module in packages/webreplay/src/env/modules folder for setting
  // up the environment. This is useful for more complex setup, such as handling
  // authentication and data setup/reset. The module is expected to have a default
  // export function with the following signature:
  //
  // `export default async function(page: Page, extensionWorker: ExtensionProxy) {}`
  //
  // You can issue actions like click through both Playwright Page object as well
  // as the ExtensionProxy. Using ExtensionProxy is preferred since it handles
  // several things automatically such as waiting for page load to complete,
  // elements in iframe etc.
  //
  // Since usually authentication is involved, we would reuse the same browser
  // user directory across different executions, which allows us to reuse the
  // cookie/session if possible and reduce the possibility of being blocked by
  // the application.
  string setup_module = 5;

  // Some pages need to be navigated by triggering some preceding actions.
  // We only support ClickAction, which should cover 90% cases.
  repeated PrecedingAction preceding_actions = 4;

  // The timestamp at the start of the recording.
  // Collected from the JS Date.now() call, which means that the nanoseconds
  // part will always be a multiple of 10^6.
  google.protobuf.Timestamp timestamp = 6;
}

// Allow the evaluate our action execution and recording.
// 1. execute actions and verify execution result if defined.
// 2. when simulate events is provided, perform the events and verify we can
//    generate the corresponding actions. We also verify the action execution
//    if the side effects are also defined.
//
// We focus to test the primitive Web actions that involves DOM and browser APIs,
// which are hard to test without a browser environment. Compared to directly
// testing with Playwright, we provide consistent abilities:
// * leverage our element locating design, which handles iframes/shadow DOMs
//   and allow to wait until the element exist.
// * for click and type, we use Chrome DevTools Protocol to issue raw mouse and
//   keyboard events to avoid limitation such as browser focus.
// * we provide access to recorder instances without UI operation, such as starting
//   a recording session. In this way, we don't need to handle user authentication
//   and operating on side panel to manage recording.
// * abstract away user events so it's easy to write and maintain. for example,
//   we provide native support for commonly used keyboard shortcut that handles
//   platform differences (Ctrl+C vs Command+C for copy).
//
// It's not intended to test the following scenarios/functionalities:
// * UI operations like start/stop recording.
// * action inference like JS action generation
// * verification of ML action execution, as well as HITL review experience
message Replay {
  // if no env is provided, we would use the live application on the Internet.
  ReplayEnv env = 1;

  // one or more user events to simulate.
  // if the first event is not a navigate event, we would open the http://orbot.test
  // before start the recorder (thus no GotoAction would be generated).
  repeated SimulateEvent events = 3;

  // expected generated actions, each with optional verification method.
  repeated Action actions = 4;

  string description = 5;

  // application involves in this replay
  repeated string applications = 6;

  // the generation from simulated events to actions is known to be failing, and
  // would be fixed later. This allows us to check in test cases without causing
  // CI failure. Use the string value to provide an explanation.
  // For some cases, generation will mismatch due to different inputs in events and actions. 
  // Skip retry to reduce execution time.

  ExpectGenerationFailure expect_generation_failure_detail = 11;

  // While the recording is successful, we cannot execute some of the actions.
  // Use the string value to provide an explanation.
  string expect_execution_failure = 9;

  // ignore the test completely, this should only be used when the case cannot
  // be run due to limitations of the Replay system, such as:
  // - consistently failing to set up the environment such as authentication
  // - fail to simulate some user events due to limitations of the implementation
  //
  // for other cases, prefer to use the more specific flag such as
  // expect_generation_failure and expect_execution_failure.

  SkipReason skip_reason = 10;

  // The value for the slow-mo option
  // For some cases, we need to add slow-mo to achieve more stable performance
  // but uniformly increasing this value will make the overall execution too slow.
  int32 slow_mo = 12;
}

message SkipReason {
  message KnownIssue {
    string issue_link = 1;
    string description = 2;
  }
  message Antibot {
    string description = 1;
  }

  oneof skip_type {
    KnownIssue known_issue = 1;
    Antibot anti_bot = 2;
    // Uncategorized skip type, which we can provide a non-empty string to explain the reason
    string other = 3;
  }
}

message ExpectGenerationFailure {
  string description = 1;
  bool skip_retry = 2;
}
