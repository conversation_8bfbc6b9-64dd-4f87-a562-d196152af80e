# proto-file: v1alpha1/orbot_workflow.proto
# proto-message: Workflow

# Example workflow definition for updating DocuSign contract attributes
examples {
  action_groups {
    description: "Update DocuSign contract attributes"
    # step 1: click the Attributes tab on the left to show the attributes form
    prepared_actions {
      click_action {
        element_locator {
          text: 'Attributes'
        }
      }
      uuid: '54583b18-4055-4704-83ca-89b7991df69b'
    }
    # step 2: get list of attributes from the form
    prepared_actions {
      get_form_action {
      }
      uuid: 'eeb7acd8-a41a-4439-ae1f-d473eaa68d17'
    }
    # step 3: extract fields from PDF file
    prepared_actions {
      extract_fields_action {
      }
      uuid: '53cb2440-9cc9-432f-b8ba-d4ab5e8c5be0'
      params {
        reference_value: 'eeb7acd8-a41a-4439-ae1f-d473eaa68d17'
      }
    }
    # step 4: update attribute values on the form
    prepared_actions {
      fill_form_action {
      }
      uuid: '66fe5f1e-753c-4a13-bc78-fe7e6e09cff9'
      params {
        reference_value: '53cb2440-9cc9-432f-b8ba-d4ab5e8c5be0'
      }
    }
    # step 5: click to save the form
    prepared_actions {
      click_action {
        element_locator {
          text: 'Save'
        }
      }
      uuid: '9b64ef2f-943f-4195-849d-eea7bb344f8a'
    }
  }
}

