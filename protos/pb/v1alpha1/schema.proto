syntax = "proto3";

package pb.v1alpha1;

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

// Stores common info or settings for generated suggestions.
message Schema {
    // Resource name for schema, in the format of users\/\*/schemas/\* for user
    // specific schema and schemas/\* for common schema
    string name = 1;
    // Display name for the schema, basic information for users who intended to
    // use the schema
    string display_name = 2;
    // Entity types include all the elements to be extracted from the document
    repeated EntityType entity_types = 3;
}

message EntityType {
  // name for entity type, preferred lower snake_case
  string display_name = 1;
  // The type of the entity, this one can be defined freely by user, either
  // address, integer, or bill, also we could prefill some common types for them
  // in the frontend
  enum Type {
    UNSPECIFIED = 0;
    ADDRESS = 1;
    NUMBER  = 2;
    NAME = 3;
    PHONE = 4;
    MONEY = 5;
  }
  Type type = 2;
  // Nested entity types
  repeated EntityType children = 3;
}
