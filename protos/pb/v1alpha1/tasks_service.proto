syntax = "proto3";

package pb.v1alpha1;

import "pb/v1alpha1/task.proto";
import "google/api/annotations.proto";
import "google/protobuf/field_mask.proto";
import "grpc/gateway/protoc_gen_openapiv2/options/annotations.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Orby Web API - Tasks Service";
    version: "v1alpha1";
  };
};

service Tasks {
  // Create a task for the current users.
  // This is only used to generate test data now.  
  rpc CreateTask(CreateTaskRequest) returns (Task) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=users/*}/tasks"
      body: "task"
    };
  }

  // List tasks ordered by ascending task resource name.
  rpc ListTasks(ListTasksRequest) returns (ListTasksResponse) {
    option (google.api.http) = {
        get: "/v1alpha1/{parent=users/*}/tasks"
      };
  }

  // Can only update task mode, confidence_threshold, and task_name.
  rpc UpdateTask(UpdateTaskRequest) returns (Task) {
    option (google.api.http) = {
        patch: "/v1alpha1/{task.name=users/*/tasks/*}"
        body: "task"
    };
  }
}

message CreateTaskRequest {
    // The parent resource name where the task is to be created.
    // E.g., "users/<EMAIL>"
    string parent = 1;

    // The task resource to create. Name field can be empty or otherwise is ignored.
    Task task = 2;
}

message ListTasksRequest {
    // The parent resource name where the task was created.
    // E.g., users/<EMAIL>
    string parent = 1;
    // Default is 10 (when page_size is missing or set to 0). Max value is 20.
    // Ordered by ascending task resource name.
    int32 page_size = 2;
    // Use this to continue the previous list requests.
    // Its value should be same with previous response's next_page_token.
    string page_token = 3;
  }
  
  message ListTasksResponse {
    // Ordered by ascending task resource name.
    repeated Task tasks = 1;
    // If the value is "", it means no more results for the request.
    string next_page_token = 2;
    // Total available task size.
    // Note it is NOT the remaining available task size after the current response.
    int32 total_size = 3;
  }

  message UpdateTaskRequest {
    Task task = 1;
    // Support mode, confidence_threshold, and task_name, for example "mode,task_name" means
    // only the mode and task_name fields will be updated to the input value.
    // If empty, all these three fields will be updated in the request.
    google.protobuf.FieldMask field_mask = 2;
  }