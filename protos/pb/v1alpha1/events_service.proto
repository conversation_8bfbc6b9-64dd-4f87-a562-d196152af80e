syntax = "proto3";

package pb.v1alpha1;

import "google/protobuf/timestamp.proto";
import "google/api/annotations.proto";
import "grpc/gateway/protoc_gen_openapiv2/options/annotations.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Orby Web API - Events Service";
    version: "v1alpha1";
  };
};

service Events {
  rpc BestEffortUpload (BestEffortUploadRequest) returns (BestEffortUploadResponse) {
    option (google.api.http) = {
      post: "/v1alpha1/events/upload"
      body: "*"
    };
  }
}

message Payload {
  string id = 1;
  string url = 2;
  string method = 3;
  string request_type = 4;
  string request_body = 5;
  string response = 6;
  string response_text = 7;
  int32 response_status = 8;
  string response_status_text = 9;
  string response_type = 10;
  int64 happened_at = 11;
  string payload_id = 12;
}

message Event {
  string payload = 1;
  google.protobuf.Timestamp happened_at = 2;
  string event_id = 3;
  string payload_id = 4;
}

message MissedPayload {
  int32 index = 1;
  string error_msg = 2;
  string event_id = 3;
  string payload_id = 4;
}

message BestEffortUploadRequest {
  repeated Event events = 1;
}

message BestEffortUploadResponse {
  repeated MissedPayload  missed_payloads = 1;
}
