syntax = "proto3";

package pb.v1alpha1;

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

message ExecutionWarning {
  enum Warning {
    WARNING_UNSPECIFIED = 0;
  
    // Indicates that the extracted content was limited by the LLM's output token limits.
    WARNING_LLM_OUTPUT_TOKEN_LIMIT_EXCEEDED = 1;

    // Indicates that a pipeline version fallback occurred during processing (e.g., Hybrid V3 to Hybrid V1).
    WARNING_PIPELINE_FALLBACK = 2;
  }
  Warning warning = 1;

  // The message to display to the user.
  string message = 2;
}
