syntax = "proto3";

package pb.v1alpha1;

import "common/announcement.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "grpc/gateway/protoc_gen_openapiv2/options/annotations.proto";
import "pb/v1alpha1/oauth2_token.proto";
import "pb/v1alpha1/user.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Orby Web API - Users Service";
    version: "v1alpha1";
  };
};

service Users {
  rpc Register (RegisterRequest) returns (RegisterResponse) {
    option (google.api.http) = {
      post: "/v1alpha1/users/register"
      body: "*"
    };
  }
  rpc Login (LoginRequest) returns (LoginResponse) {
    option (google.api.http) = {
      post: "/v1alpha1/users/login"
      body: "*"
    };
  }
  rpc SingleSignOn (SingleSignOnRequest) returns (LoginResponse) {
    option (google.api.http) = {
      post: "/v1alpha1/users/singlesignon"
      body: "*"
    };
  }
  // Log out from a specific session.
  // Client side must remove access and refresh tokens after calling this.
  rpc Logout (LogoutRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=users/*/sessions/*}"
    };
  }
  rpc Get (GetRequest) returns (GetResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/users"
    };
  }
  rpc Update (UpdateRequest) returns (UpdateResponse) {
    option (google.api.http) = {
      patch: "/v1alpha1/users/update"
      body: "*"
    };
  }
  rpc UpdatePassword (UpdatePasswordRequest) returns (UpdatePasswordResponse) {
    option (google.api.http) = {
      patch: "/v1alpha1/users/{email=*}:updatePassword"
      body: "*"
    };
  }
  rpc ListUsers(ListUsersRequest) returns (ListUsersResponse) {}

  // Sends the user's active orgId to BE so that the orgId cookie can be set
  rpc UpdateActiveOrgId(UpdateActiveOrgIdRequest) returns (google.protobuf.Empty) {}

  // TODO: move this api inside update user api, OA-1040
  rpc AddUserToOrganization(AddUserToOrganizationRequest) returns (User) {}
  rpc MicrosoftSingleSignOn (MicrosoftSingleSignOnRequest) returns (LoginResponse) {}
  rpc GoogleSingleSignOnForInternalApp (SingleSignOnRequest) returns (LoginResponse) {}
  rpc GenerateMsAuthUrl (MsAuthUrlRequest) returns (MsAuthUrlResponse) {}
  rpc ExchangeToken(ExchangeTokenRequest) returns (LoginResponse) {}
  rpc GetUserPermissions(GetUserPermissionsRequest) returns (GetUserPermissionsResponse) {}
  rpc UpdateGoogleToken (UpdateGoogleTokenRequest) returns (UpdateGoogleTokenResponse) {}
  rpc UpdateMicrosoftToken (UpdateMicrosoftTokenRequest) returns (UpdateMicrosoftTokenResponse) {}
}

message ExchangeTokenRequest {
  // format: users/{user_id}/sessions/{session_id}
  string session_resource_name = 1;
}

message ListUsersRequest{
  // Default is 10 (when page_size is missing or set to 0). Max value is 50.
  // Ordered by ascending Task resource name.
  // As per OA-2056, max page_size has been increased from 20 to 50
  int32 page_size = 1;
  // Use this to continue the previous list requests.
  // Its value should be same with previous response's next_page_token.
  string page_token = 2;
  // Supported filter: "name_email_prefix={SEARCH_KEY}"
  // SEARCH_KEY will be used to search by email prefix or full_name prefix.
  // "role={admin|user|creator}"
  // "workflow_resource_names={workflows/Id1/Id2/Id3....}"
  // "start_date={date}"
  // "end_date={date}"
  string filter = 3;
  // Organization resource name. Format: organizations/{ID}
  string org_resource_name = 4;
  // Use this to send only relevant data in response
  // - If Field Mask is not sent or is sent with empty paths then the result will contain
  //    the complete object except the google access token for every user
  // - Valid values for field mask are: email, completed_tasks_count.
  google.protobuf.FieldMask field_mask = 5;
  int32 page_number = 6;
}

message ListUsersResponse{
  repeated User users = 1;
  // If the value is "", it means no more results for the request.
  string next_page_token = 2;
  // Total available users size in the organization.
  // Note it is NOT the remaining available users size after the current response.
  int32 total_size = 3;
}

message UpdateRequest {
  // If field_mask is not provided, all updatable user fields will be updated.
  // User.email is not updatable.
  // Supported field_masks:
  // 1. "settings": to update all settings based on User.settings
  // 2. settings.<Key>: e.g., "settings.enable_event_upload" and "settings.not_allowed_sites".
  //    If "settings" field mask is also used, setting.<Key> ones will be ignored.
  // 3. "org_role": to update the user role
  // 4. "org_id": if set to "", will remove the user from organisation.
  // 5. "prerequisite.policies_to_review": to add reviewed policies, won't overwrite.
  // 6. "org_infos": If empty, user will be removed from org resource name. 
  //     If it has one element and the role is different from the current one, user 
  //     will have its org role updated.
  // 7. "cookie_preferences": to update all cookie preferences
  // NOTE: if you want to add new users to the organisation please use the AddUserToOrganization api.
  google.protobuf.FieldMask field_mask = 1;
  User user = 2;
  // Organization resource name. Format: organizations/{ID}
  // We need to check if the requester is either an admin of the organization the user belongs 
  // to or the user himself (preciously we assumed users only belonged to one organization)
  string org_resource_name = 3; 
}

message UpdateResponse {
  User user = 1;
}

message GetUserFilter {
  // If true, the response will include announcements that the user has not seen
  // yet, and mark them as viewed.
  bool include_announcements = 1;
  // If include_announcements is false, this field will be ignored.
  // If announcement_types is empty, will return all types of announcements, 
  // otherwise will only return the specified types. 
  repeated common.AnnouncementType announcement_types = 2;
}

message GetRequest{
  // Use this to send only relevant data in response.
  google.protobuf.FieldMask field_mask = 1;
  GetUserFilter filter = 2;
}

message GetResponse{
  User user = 1;
}

message RegisterRequest {
  string email = 1;
  string password = 2;
}

message RegisterResponse {
  User user = 1;
}

message LoginRequest {
  string email = 1;
  string password = 2;
  // Used for Oauth2 login
  optional OauthParameters oauth_parameters = 3;
}

message OauthParameters {
  string client_id = 1;
  // The type of response to be returned by the Oauth2 server.
  // Currently supported values are "code"
  string response_type = 2;
  // These are all the scopes space separated as this is
  // the way they are passed in query params in oauth requests.
  // Currently supported values are "all"
  string scope = 3;
  // The redirect uri to be used for the oauth2 login.
  string redirect_uri = 4;
  // Code challenge is a hash of the code verifier, generated by the client, which the server uses later to verify the code verifier
  string code_challenge = 5;
  // Code challenge method is the method used to generate the code challenge, currently supported values are "S256" and "plain"
  string code_challenge_method = 6;
}

message SingleSignOnRequest {
  string google_jwt = 1;
  string google_authorization_code = 2;
  // Used for Oauth2 login
  optional OauthParameters oauth_parameters = 3;
}

message MicrosoftSingleSignOnRequest {
  string ms_authorization_code = 1;
  // Used for Oauth2 login
  optional OauthParameters oauth_parameters = 2;
}

message LoginResponse {
  User user = 1;
  // Resource name of the session in the format of "users/abc/sessions/633ab04b366af3af8e7b2312"
  string session_id = 2;
  string access_token = 3;
  string refresh_token = 4;
  google.protobuf.Timestamp access_token_expires_at = 5;
  google.protobuf.Timestamp refresh_token_expires_at = 6;
  // Parameters returned for Oauth2 login
  optional OauthResponse oath_response = 7;
}

message OauthResponse {
  string redirect_url = 1;
}

message LogoutRequest {
  // The resource name of the session to be deleted, which is same with session id,
  // for example "users/abc/sessions/633ab04b366af3af8e7b2312"
  string name = 1;
}

message UpdatePasswordRequest {
  string email = 1;
  string password = 2;
  string new_password = 3;
  string confirm_password = 4;
}

message UpdatePasswordResponse {
  string message = 1;
}

message AddUserToOrganizationRequest {
  string email = 1;
  string org_resource_name = 2;
  User.OrgRole role = 3;
  // Emails which are sent to email addresses that do not exist
  // bounce and affect our reputation score
  // This can be set as false to alleviate this
  bool sendEmail = 4;
}

message MsAuthUrlRequest {
  // List of scopes (eg - Files.ReadWrite, user.read) to request from user
  repeated string scopes = 1;
}

message MsAuthUrlResponse {
  string auth_code_url = 1;
}

message GetUserPermissionsRequest {
  // Organization resource name. Format: organizations/{ID}
  string org_resource_name = 1;
}

message GetUserPermissionsResponse {
  // List of permissions the user has in the organization
  repeated string permitted_actions = 1;
}

message UpdateGoogleTokenRequest {
  string google_authorization_code = 1;
}

message UpdateGoogleTokenResponse {
  Oauth2Token google_token = 1;
}

message UpdateMicrosoftTokenRequest {
  string microsoft_authorization_code = 1;
}

message UpdateMicrosoftTokenResponse {
  Oauth2Token microsoft_token = 1;
}

message UpdateActiveOrgIdRequest {
  // org_id is the current active orgId of the user
  string org_id = 1;
}
