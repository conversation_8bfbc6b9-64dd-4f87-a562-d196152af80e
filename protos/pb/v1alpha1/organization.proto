syntax = "proto3";

package pb.v1alpha1;

import "common/workflow_common.proto";
import "pb/v1alpha1/data_retention_policy.proto";
import "pb/v1alpha1/saml.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

message Organization {
  // Resource name. Format: organizations/{ID}
  string name = 1;
  // Human-friendly label for your organization, shown in user interfaces
  string display_name = 2;
  // Users who can CRUD all resources and be assigned to tasks. 
  repeated string admins = 3;
  // Users who can read or list assigned workflows and be assigned to tasks.
  repeated string users = 4;
  // Template Workflows that can be used to create new Workflows.
  repeated common.WorkflowTemplateType workflow_template_types = 5;
  // If set, all workflows in this organization will be tuned with this
  // hyperparameter resource.
  // Format: hyperparameters/{ID}
  string hyperparameter_resource_name = 6;
  // Users who can create workflows
  repeated string creators = 7;

  // Store SAML configuration for this organization
  SAMLConfig saml_config = 8;

  enum TenantType {
    TENANT_TYPE_UNSPECIFIED = 0;
    // Shared tenant organization will store data in shared resources.
    TENANT_TYPE_SHARED_TENANT = 1;
    // Single tenant organization will store data in separated resources.
    TENANT_TYPE_SINGLE_TENANT = 2;
  }
  // Different tenant type will have different data storage strategy.
  TenantType tenant_type = 9;

  // Data retention policy on organizational level
  DataRetentionPolicy data_retention_policy = 10; 

  // Microsoft tenant id for the organization if the admin has
  // approved Orby to be used for certain users whose microsoft token are not
  // made public
  string microsoft_tenant_id = 11;

  // List of microsoft application ids that are approved to be used by the 
  // organization
  repeated string approved_microsoft_application_ids = 12;

  // This field is only for creation and update.
  // IDs of prebuilt orbot workflow templates that are approved to be used
  // by the organization.
  // When set, BE will copy the templates to be the organization's own workflow
  // templates.
  // Prebuilt templates are stored in the shared orbot_workflow_templates
  // collection and org_id is empty.
  repeated string prebuilt_orbot_workflow_templates = 13;
}
