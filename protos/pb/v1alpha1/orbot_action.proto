syntax = "proto3";

package pb.v1alpha1;

import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "pb/options.proto";
import "pb/v1alpha1/actionprocessing.proto";
import "pb/v1alpha1/element.proto";
import "pb/v1alpha1/ui_event.proto";
import "pb/v1alpha1/secret_manager.proto";
import "pb/v1alpha1/field.proto";
import "pb/v1alpha1/orbot_assets.proto";
import "pb/v1alpha1/variables.proto";
option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

// Simplified representation of a DOM / web page.
// For example, <p>Hello <a>world</a></p> will be represented as
// elements {
//   tag {
//     name: "p"
//     children { text: "Hello " }
//     children { tag { name:"a" children { text:"world" } } }
//   }
// }
message PageContent {
  repeated Element elements = 1;

  message Element {
    // An element should be either a tag element or a text element.
    oneof type {
      // Element with a tag.
      Tag tag = 2;

      // Content of a text element.
      string text = 3;
    }

    message Tag {
      // Id assigned by Orbot for identifying elements.
      string orby_id = 4;
      // Type of the element. For example, a, button, etc.
      string name = 1;
      repeated Element children = 2;
      // HTML attributes selected by the FE. For example, "aria-label" and "placeholder".
      map<string, string> attributes = 3;
    }
  }
}



enum ActionBlockKind {
  ACTION_KIND_UNSPECIFIED = 0;
  PRESET = 1;
  USER_CREATED = 2;
}

// A group of actions that perform some task collectively.
message ActionBlock {
  repeated Action actions = 1;

  ActionBlockKind kind = 2;

  // For a preset action block, set the ID here
  // Preset action blocks are blocks that can be defined on the server side,
  // and then whose actions are fetched at runtime.
  string preset_id = 3;

  // secret block config that is shared among all actions in the ActionBlock,
  // where each action can specify the secret name within the secret block to use.
  string secret_config_id = 4;

  // Define preset options
  oneof action_block_options {
    LoginBlockOptions login_options = 5;
    UIExtractionBlockOptions ui_extraction_options = 6;
  }

  // User input in natural language description of the block's intention
  string prompt = 7;
}

message LoginBlockOptions {
  string origin = 1;
}

// Configuration for extracting data from UI
message UIExtractionBlockOptions {
  // Optionally user can mark an element during recording as target for extraction.
  ElementLocator locator = 1;
  // Extraction script from GenerateExtractionScriptRequest
  string extraction_script = 2;
}


// this struct is used configure information on what secret blocks
// should be used for what machines/users.
message WorkflowSecretConfig {
  string id = 1;

  // Note that MachineSecret and UserSecret are the same, but keep them separate
  // in case we want to configure/add different fields for them later on.
  // Conceptually, they can still be separate things.
  message MachineSecret {
    string machine_id = 1;

    // The secret block must be a public secret block.
    string secret_block_id = 2;
  }

  message UserSecret {
    string user_id = 1;

    // The secret block can either be a public or private secret block.
    string secret_block_id = 2;
  }

  repeated UserSecret user_secrets = 3;
  repeated MachineSecret machine_secrets = 4;

  // If a secret block is not specified for a user or machine, use the
  // default secret block.
  string default_secret_block_id = 5;
}

message PrerequisiteAction {
  enum PrerequisiteType {
    PREREQUISITE_TYPE_UNSPECIFIED = 0;
    // A JsFunctionAction for generating text from user's NL instructions.
    SMART_TEXT = 1;
    // An action for generating boolean-like output from user's NL instructions,
    // which is mostly used for ConditionAction. It can be a JsFunctionAction
    // that generate a boolean value, or a GetElementAction which can be used
    // directly by the ConditionAction.
    SMART_BOOLEAN = 3;
    // A JsFunctionAction for generating text from user's NL instructions.
    DYNAMIC_LOCATOR = 2;
  }

  Action action = 1;
  PrerequisiteType type = 2;
}

// Basic action in Orbot workflow. Each action can be:
// 1. a leaf action that can be executed as the smallest unit, such as click.
// 2. a control flow action such as loop/condition that can contain children action.
// 3. a group of actions, either prepackaged by Orby or user configured.
// Next ID: 59
message Action {
  // action id used to reference between steps for parameter passing.
  string id = 1;

  // Generated description that is shown to the user.
  string description = 2;

  // Prerequisite actions that need to be performed before the current action.
  // This is usually for JsFunctionAction that generate locators or perform data
  // manipulation. Prerequisite actions are hidden from the user for better
  // readability.
  repeated PrerequisiteAction prerequisites = 32;

  oneof action_data {
    ActionBlock block = 3;
    GotoAction goto = 4;
    ClickAction click = 5; // (FM) synonymous with CLICK
    GetFormAction get_form = 6;
    FillFormAction fill_form = 7;
    ExtractFieldsAction extract_fields = 8;
    JsFunctionAction js_function = 9;
    ValidateAction validate = 10;
    ConditionAction condition = 11;
    ForeachAction foreach = 12;
    GetListAction get_list = 13;
    UpdateListAction update_list = 44;
    GetElementAction get_element = 16;
    FlagKeywordsAction flag_keywords = 17;
    DetectDuplicateLineItemsAction detect_duplicate_line_items = 18;
    CreateTaskAction create_task = 19;
    ReconcileItemsAction reconcile_items = 20;
    HoverAction hover = 22; // (FM) synonymous with HOVER
    ExitAction exit = 23;
    SetValueAction set_value = 24; // (FM) synonymous with TYPE and SELECT
    CustomSmartAction custom_smart_action = 25;
    GetDocumentAction get_document = 26;
    ScrollAction scroll_action = 31; // (FM) synonymous with SCROLL
    GenerateTextAction generate_text = 34;
    ClassifyAction classify = 35;
    SendEmailAction send_email = 40;
    GetPasscodeAction get_passcode = 42;
    MacroAction macro = 46;
    LaunchAction launch = 48;
    FocusAction focus = 50;
    ReconcileItemsUnifiedAction reconcile_items_unified_action = 55;
    ExtractStructuredDataAction extract_structured_data_action = 56;
    ToolUseAction tool_use = 58;
  }

  // For all actions that involves review, we would create a task on the server
  // side. When the task is finished, we would create the following execution on
  // the server side. If the review is not needed, i.e. the confidence score is
  // above user threshold, we would create a task that has COMPLETED status, and
  // a new execution would be immediately created on the server side after task
  // creation.
  //
  // With this field defined, the action would complete right after the task is
  // created, i.e. human review is not a blocking operation. For actions that
  // don't have this field but require review, the action execution would block
  // until the review is completed.
  //
  // Proceeding execution would receive the following variables in the order of
  // preference:
  // 1. smart action request and response (smartAction and smartActionResult);
  // 2. additional variables specified in ProceedingTask;
  // 3. all variables from the current process.
  ProceedingExecution proceeding_execution = 45;

  // which tab shall we perform the action for browser automation. tabIndex
  // is generated and assigned during recording and we map them to the dynamic
  // tabId during execution.
  int32 tabIndex = 21;

  // which application window shall we perform the action. When we first launch
  // an application, we assign a non-zero value for the newly opened window and
  // then we can refer to the same windowIndex in following actions.
  //
  // WindowIndex=0 (default, unset) Reserved for browser automation actions.
  // WindowIndex<0 are reserved for specific applications:
  //      -1       Windows Shell (Explorer)
  // WindowIndex>0 can be reserved and associated with a specific application by
  //               sending a LaunchAction
  int32 windowIndex = 49;

  // The page snapshot/screenshot that are picked during the inference.
  // Deprecated: use before_state.root_element instead.
  RecordedFile snapshot = 27 [deprecated = true];

  // Screenshot captured before this action takes place.
  // Deprecated: use before_state.viewport_screenshot instead.
  RecordedFile screenshot = 28 [deprecated = true];

  // Snapshot of the UI taken before the action.
  UiState before_state = 38;

  reserved 36, 37;

  // Verify that the execution of the action is successful.
  // Currently this is only used in tests in the Replay format.
  ActionVerification verification = 29;

  // Configuration to wait until the page load is complete after performing the
  // action.
  ActionCompletionCheck completion_check = 41;

  // (FM) low-level action type; to be compatible with the FM crawler definition
  LowLevelActionType low_level_action_type = 30;

  // These are the IDs for the elements this action operates on.
  // Each ID is unique and corresponds to one dom element found in snapshot.
  repeated string element_ids = 33;

  // Raw UI events before potential processing like merging.
  repeated UiEvent raw_events = 39;

  // Time when the action was first observed during recording.
  google.protobuf.Timestamp observed_at = 43;

  // Time when the action was completed during recording. For most actions,
  // completed_at would be the same as observed_at. However, for actions
  // corresponding to multiple raw events, such as SetValue, completed_at
  // might be significant later than observed_at.
  google.protobuf.Timestamp completed_at = 47;

  // This is only valid if the action is a smart action.
  // It corresponds to the hyperparameter object which needs to be sent along with the smart action request to the ML workflow.
  string hyperparameter_id = 51;

  // Option to use debugger API for this action
  bool use_debugger = 52;

  // Persist the output variable name so that the action can be mapped back to
  // the JS-based definition. For example, varname would be `invoiceDoc` for the
  // following definition: `const invoiceDoc = getDocument`.
  string varname = 53;

  // Output variable that includes
  // name: user facing variable name that is unique in the workflow. Note this can be different from varname.
  // varname is expected to be a single word using camel case or snake case, while name can support more complex naming.
  // type: type definition, optional if the variable type can be inferred from the action
  // value: optional default value for the variable used for modular workflow testing
  Variable output_variable = 54;

  // Debug data that is used for debugging purpose. This is usually a JSON object, but may also be a string.
  // This data may or may not be handled or persisted and is included only for active debugging.
  google.protobuf.Value debug_data = 57;
}

message ProceedingExecution {
  string process_id = 1;
  // all variables from the current process would be inherited. use this field
  // to specify additional variables for the child task.
  repeated ActionParamValue additional_variables = 2;
}

// After the action is performed, we wait until the page is fully loaded before
// performing the next action. Right now the main method is to wait until all
// pending network requests have been finished, and we may also consider other
// methods in the future.
message ActionCompletionCheck {
  message NetworkIdle {
    // If for {networkCheckTime}ms, there is no pending request, we consider as network is idle
    int32 network_check_time = 1;
    // The maximum time to wait for no pending network requests
    int32 timeout = 2;
    // The time to skip checking network requests, this is used for most of no-request actions.
    // If in {skipTime}ms, there is no request out, then we return without waiting for the full {networkCheckTime}
    int32 skip_time = 3;
  }

  NetworkIdle network_idle = 1;
}

message ActionVerification {
  enum OutgoingRequestMethod {
    GET = 0;
    POST = 1;
    PUT = 2;
    DELETE = 3;
  }

  message OutgoingRequestBody {
    oneof body_type {
      string json = 1;
      bytes raw = 2;
    }
  }

  message OutgoingRequest {
    string url = 1;
    OutgoingRequestMethod method = 2;
    OutgoingRequestBody body = 3;
  }

  message ElementAssertion {
    ElementLocator locator = 1;
    bool exists = 2;
    bool visible = 3;
  }

  repeated OutgoingRequest outgoing_requests = 1;
  repeated ElementAssertion element_assertions = 3;

  message ListAssertion {
    message ListLength {
      int32 equal = 1;
    }
    ListLength length = 1;
    ListLength columns = 3;

    message TextAssertion {
      string equal = 1;
      string contains = 2;
    }

    message FieldValueAssertion {
      string key = 1;
      TextAssertion text = 2;
    }

    message ListValue {
      int32 index = 1;
      repeated FieldValueAssertion fields = 2;
    }
    repeated ListValue values = 2;
  }

  ListAssertion list_assertion = 6;

  enum EvaluateScriptContext {
    EVALUATE_SCRIPT_CONTEXT_UNSPECIFIED = 0;
    MAIN_WORLD = 1;
    CONTENT_SCRIPT = 2;
    SERVICE_WORKER = 3;
  }

  message EvaluateScript {
    string script = 1;
    // If no context is specified, the script would be run in the MAIN_WORLD
    EvaluateScriptContext context = 2;
  }

  // JavaScript code to be evaluated in any one of the EvaluateScriptContext(s) to check if the execution
  // is successful. Only the return value true would be treated as success.
  EvaluateScript evaluate_script = 4 [deprecated = true];

  // JavaScript code to be evaluated in any one of the EvaluateScriptContext(s) to check if the execution
  // is successful. Only the return value true would be treated as success.
  // globalThis.actionContext is a global variable that contains the context of the action.
  // It contains the following fields:
  // - output: the output of the action
  repeated EvaluateScript evaluate_scripts = 5;
}

// ActionGroup is a group of actions that are executed together.
// In Dashboard, we also visualize the action group as a single step.
// One ActionGroup can have multiple PreparedActions, but 2 control flow actions (foreach, condition) cannot be in the same level and same group.
// The reason is that it's hard to visualize foreach and condition in the same step.
// But they can be nested. e.g foreach -> condition, or condition -> foreach, or foreach -> foreach (our executor doesn't support yet), or condition -> condition, etc.
// Deprecated: use Action instead
message ActionGroup {
  // Generated description that is shown to the user.
  string description = 1;

  repeated PreparedAction prepared_actions = 2;

  // The page snapshot/screenshot that are picked during the inference.
  RecordedFile snapshot = 3;
  RecordedFile screenshot = 4;

  // uuid for the action group, used to reference between high-level steps.
  string uuid = 5;
}

enum ContextValue {
  CONTEXT_VALUE_UNSPECIFIED = 0;
  LOOP_INDEX = 1;
}

message PartialReferenceValue {
  string reference_value = 1;
  string reference_value_key = 2;
}

// Generic interface to define an action parameter schema.
message ActionParamSpec {
  string name = 1;
  VariableType type = 2;
}

// An action parameter value can be defined in two ways:
// 1. static param that is defined during workflow definition, such as ExtractField
//    with firstName and lastName.
// 2. dynamic param from prior actions. For example, a FillFormAction relies
//    on data from a prior ExtractFieldAction execution.
message ActionParamValue {
  // required for key-value based parameters
  string name = 8;

  oneof ActionParamValueType {
    // json formatted parameter value that will be passed into high-level-actions.
    // the value is fixed when we define the workflow.
    string json_value = 1;
    // parameter value that refers to the output from another action.
    // it should always be an ID of a prior action in the same workflow.
    string reference_value = 2;
    // refers to the environment variable set by the task discovery system.
    // the value is available when we start the workflow.
    string env_value = 3;
    // refers to runtime variable, such as the loop index
    ContextValue context_value = 4;
    // refers to a secret ID with value stored in go/secret-manager.
    // TODO: remove legacy_secret_value after migration
    string legacy_secret_value = 5 [deprecated = true];
    SecretValue secret_value = 7;
    // reference value with a key path, used to extract a specific field
    PartialReferenceValue partial_reference_value = 6;
  }
}

// PreparedActions are generated after recording.
//
// The naming is similar to SQL prepared statement, which is a feature used to
// execute the same (or similar) SQL statements repeatedly with high efficiency.
// Here we preprocess the recorded actions into high-level actions in order for
// it to be executed more efficiently and reliably.
// (FM) this is a superset of the FM atomic action
// Deprecated: use Action instead
message PreparedAction {
  // uuid for the action, used to reference between steps for parameter passing.
  string uuid = 2;

  // The parameters that is used to perform the action.
  // Deprecated: set parameter in each action instead
  repeated ActionParamValue params = 3 [deprecated = true];

  // User might want to stop and review certain actions before proceeding, e.g.
  // clicking the Submit button for a form. This usually happens for operations
  // that has side effects such as create/update/delete.
  bool requiresReview = 15;

  // which tab shall we perform the action. tabIndex is generated and assigned
  // during recording and we map them to the dynamic tabId during execution.
  int32 tabIndex = 21;

  // If this action can be mapped to high-level action.
  oneof HighLevelActionType {
    GotoAction goto_action = 4;
    ClickAction click_action = 5;
    GetFormAction get_form_action = 6;
    FillFormAction fill_form_action = 7;
    ExtractFieldsAction extract_fields_action = 8;
    JsFunctionAction js_function_action = 9;
    ValidateAction validate_action = 10;
    ConditionAction condition_action = 11;
    ForeachAction foreach_action = 12;
    GetListAction get_list_action = 13;
    GetElementAction get_element_action = 16;
    FlagKeywordsAction flag_keywords_action = 17;
    DetectDuplicateLineItemsAction detect_duplicate_line_items_action = 18;
    CreateTaskAction create_task_action = 19;
    ReconcileItemsAction reconcile_items_action = 20;
    HoverAction hover_action = 22;
    ExitAction exit_action = 23;
    SetValueAction set_value_action = 24;
    CustomSmartAction custom_smart_action = 25;
    GetDocumentAction get_document_action = 26;
    GenerateTextAction generate_text_action = 27;
    ClassifyAction classify_action = 28;
    ExtractStructuredDataAction extract_structured_data_action = 29;
  }
}

// Actions generated when we execute the workflow.
// Compared to RecordedAction, this would be initiated by Orbot with a PreparedAction.
// which might be mapped to multiple DOM action, and has execution context like input/output.
message ExecutedAction {
  // the PreparedAction that was executed.
  string prepared_action_uuid = 1;

  // input/output of the action, where each value is serialized in JSON format.
  repeated string param_values = 3;

  // the actual output of an action, there is no necessary bond between output_value and predicted_output_value.
  // e.g reconcile action, output_value is a boolean indicating whether there is fallout or not.
  string output_value = 4;
  // the original predicted value for SmartAction requests in serialized JSON format.
  // duplicated from smart_action_response
  string predicted_output_value = 7 [deprecated = true];

  // Whether we have triggered human review for this action during execution.
  bool human_review_triggered = 8;
  // human review time in milliseconds computed as the total time that the task tab is active for human review.
  int32 human_review_time_in_ms = 9;

  // for billing purpose
  int32 num_processed_pages = 10;

  // Screenshot of the page before the action is executed.
  RecordedFile screenshot = 5;

  // HTML representation of the page before the action takes place.
  RecordedFile snapshot = 6;

  // Persist the request for auditing and debugging.
  ProcessSmartActionsRequest smart_action_request = 11;
  ProcessSmartActionsResponse smart_action_response = 14;
  // if human overrides a value during review, corrected_smart_action_response would store the
  // overridden value. If the value is not overridden, corrected_smart_action_response would be empty.
  ProcessSmartActionsResponse corrected_smart_action_response = 15;

  google.protobuf.Timestamp start_time = 12;
  google.protobuf.Timestamp end_time = 13;

  MacroActionExecution macro_action_execution = 16;

  // Corresponding Id for the HITL task trigger by the action.
  string review_task_id = 17;

  reserved 2;
}

message GotoAction {
  ActionParamValue url = 3 [(pb.options).param_type = STRING];
}

// For launching desktop application with optional parameters, including
// navigating to URLs in the browser.
//
// Example 1: open the given URL in the default browser. Which would open the
// browser if it it not open, create a new empty tab, and navigate to the URL.
// {
//   params: '"https://google.com"'
// }
// NOTE: on the server side, we can send this action directly to the browser
// agent if it is online, otherwise send it to the desktop agent to have it
// start the browser together with the browser agent.
//
// Example 2: open Notepad on Windows. Note we don't need to specify the full
// path for applications that is in the system path.
// {
//   application: 'notepad.exe'
// }
//
// Example 3: open the given Excel file
// {
//   application: 'excel.exe'
//   params: '"D:\\Data\test.xls"'
// }
//
// Example 4: use a generic internal application with full path
// {
//   application: 'C:\\Program Files\SomeCorp\MyTool.exe'
// }
//
message LaunchAction {
  string application = 1;
  repeated ActionParamValue params = 2 [(pb.options).param_type = STRING];
}

enum ClickType {
  // For single click on the left mouse button, use the default UNSPECIFIED type.
  CLICK_TYPE_UNSPECIFIED = 0;
  DOUBLE_CLICK = 1;
  RIGHT_CLICK = 2;
}

message ClickAction {
  // Deprecated: use locator instead.
  ElementLocator element_locator = 1 [deprecated = true];
  // deprecated: use ClickType.RIGHT_CLICK instead.
  bool is_double_click = 2;
  ClickType type = 3;
  ActionParamValue locator = 4 [(pb.options).param_type = ELEMENT_LOCATOR];
}

message HoverAction {
  // Deprecated: use locator instead.
  ElementLocator element_locator = 1 [deprecated = true];
  ActionParamValue locator = 2 [(pb.options).param_type = ELEMENT_LOCATOR];
}

// Get an element on the HTML page, which can be used:
// 1. check if an element (such as a button) exists;
// 2. get the content of the element, such as page/modal titles.
//
// The output can be used to perform different branches of actions. For example,
// * perform receipt validation only if the Receipt Image tab is present in Concur
// * route receipt to additional approvals, if the modal title is "Approval Workflow for Report"
//
// There are a few read/extract actions, each with its own focs:
// * ExtractEntity extract semantic entities from the page, which usually requires LLM understanding.
// * GetForm reads structured field usually from HTML forms.
// * GetElement reads a single HTML elements that are relatively stable, such as buttons.
// * GetList reads list of similar elements.
message GetElementAction {
  // Deprecated: use element_locator instead.
  ElementLocator locator = 1 [deprecated = true];
  ActionParamValue element_locator = 2 [(pb.options).param_type = ELEMENT_LOCATOR];
}

// Get form schema as well as current form value.
//
// Form schema is based on JSON schema, such as:
// {
//   "type": "object"
//   "properties": {
//     "givenName": {
//       "type": "string"
//     }
//   }
// }
//
// From value is a JSON object, such as:
// {
//   "givenName": "John"
// }
message GetFormAction {
  ElementLocator form_locator = 1;
}

// Fill form with the given values.
// Deprecated: use SetValueAction instead
message FillFormAction {
  ElementLocator form_locator = 1;
  // Default to false. If true, press Enter key after filling the form.
  bool pressEnter = 2;
}

// Compared to FillFormAction that can handle multiple form fields, SetValueAction
// only set value for a single form field
// (FM) synonmous with TYPE and SELECT
message SetValueAction {
  ActionParamValue field_locator = 1 [(pb.options).param_type = ELEMENT_LOCATOR];
  ActionParamValue field_value = 2 [(pb.options).param_type = STRING];
  // Default to false. If true, press Enter key after filling the form.
  bool pressEnter = 3;
}

// Extracts fields from one or more documents (HTML, PDF, images etc) with the given schema.
// This action usually requires API call to the Orby server for the document extraction.
message ExtractFieldsAction {
  ActionParamValue document = 3;
  // list of fields to be extracted
  // Deprecated: use entities instead
  repeated string fields = 4 [deprecated = true];
  // List of entities to be extracted, supports nested entities
  repeated Field entities = 5;
}

// Each validate rule can output an string message if validation fails.
message ValidateAction {
  // Source representing the ground truth, it can be a form or a document.
  ActionParamValue source = 6;
  // Target representing the data to be validated, it's usually document but can also be a form.
  ActionParamValue target = 7;

  // NL description of the validation rule, which is sent to validation API.
  string rule = 4;
  // list of fields to be validated
  repeated string fields = 5;

  // Additional metadata for the validate action, e.g. report header
  map<string, ActionParamValue> metadata = 8;

  reserved 1, 2, 3;
}

// Javascript function can be used to generate output in any format from
// 1. zero or more output from prior actions.
// 2. global context, such as current date etc.
message JsFunctionAction {
  // NL description of what the function does.
  string description = 1;

  // function parameter names, which are referred to in the function body
  repeated string param_names = 3;

  repeated ActionParamValue params = 4;

  // generated function body in JavaScript that can be directly executed. If
  // the original function is defined in TypeScript, we would go though a
  // transpiling phrase to remove things like type annotation.
  string body = 2;

  // Persist the original function definition, which may contains TypeScript
  // annotations. NOTE this contains full function definition which includes
  // function name and arguments.
  string raw_definition = 5;

  message TestCaseWithResult {
    GenerateFunctionTestCase demonstration = 1;
    Variable output = 2;
    string diff = 3;
    string id = 4;
  }

  message TestCases {
    repeated TestCaseWithResult demonstrations_with_results = 1;
  }

  // Test cases used for generating/validating the function, including the last test run result.
  TestCases test_cases = 6;
}

// Declare the list of supported methods and schema for a given tool for either
// human or LLM to use in a workflow (either creation or execution). The tool
// spec is maintained on the integration side (such as browser extension or an
// API integration), and is sent to the execution engine during the initializing
// or initial connection (for tools that are on different machines). Execution
// engine maintains the list of registered tools and expose to users/LLM during
// workflow creation and execution.
message ToolUseSpec {
  // tool name, such as browser or Excel.
  string name = 1;

  // The same tool can provide many methods as needed, and each method can declare
  // its usage instructions with input/output schema.
  message ToolMethodSpec {
    // method on the tool, such as click an element in a browser.
    string method = 1;

    // Human-readable description
    string description = 2;

    repeated ActionParamSpec inputs = 3;

    VariableType output = 4;
  }

  repeated ToolMethodSpec methods = 2;
}

// Generic action to invoke a specific method from an external tool.
message ToolUseAction {
  // tool name and method, need to match the corresponding ToolUseSpec
  string name = 1;
  string method = 2;
  repeated ActionParamValue inputs = 3;
}

// Condition actions take a single boolean/string param, and execute different branches.
// For string param, the empty string evaluates to false and non-empty to true.
message ConditionAction {
  ActionParamValue condition = 4;
  // Next action if condition value is true.
  repeated ActionGroup true_actions = 2 [deprecated = true];
  // Next action if condition value is false.
  repeated ActionGroup false_actions = 3 [deprecated = true];

  repeated Action then_actions = 5 [deprecated = true];
  repeated Action else_actions = 6 [deprecated = true];

  // Favor single action that can use ActionBlock to expand to multiple actions
  // over a simple list of actions
  Action then_action = 7;
  Action else_action = 8;
}

message GetListAction {
  ElementLocator list_locator = 1;
}

message UpdateListAction {
  ActionParamValue list_locator = 1 [(pb.options).param_type = ELEMENT_LOCATOR];
  ActionParamValue updates = 2 [(pb.options).param_type = FIELD_UPDATE];
}

message FieldUpdate {
  int32 field_group_index = 1;
  repeated Field fields = 2;
};

message ForeachAction {
  // items to be iterated through
  ActionParamValue items = 4;
  repeated ActionGroup actions = 1 [deprecated = true];
  repeated Action loop_actions = 2;
}

message FlagKeywordsAction {
  repeated string keywords = 1;
  // list of fields to include in flagging keywords
  repeated string fields = 2;
  ActionParamValue source = 3 [(pb.options).param_type = ELEMENT_LOCATOR];
}

message DetectDuplicateLineItemsAction {
  repeated string duplicates = 1;
  ActionParamValue source = 3 [(pb.options).param_type = ELEMENT_LOCATOR];
}

message ReconcileItemsUnifiedAction {
  ActionParamValue source = 1;
  ActionParamValue target = 2;
}

message ExtractStructuredDataAction {
  ActionParamValue policy = 1;
  message InputVariable {
    // The reason of not using variable here is we want to reference the value of the variable from runtimeVariables context.
    // Before BE calls the ProcessSmartACtionRequest, it will create the variable object with the actual value and the associated type and name.
    string name = 1;
    ActionParamValue value = 2;
    VariableType type = 3;
  }
  repeated InputVariable input_variables = 2;
  VariableType output_type = 3;
  // Temporary field to store symbolic code for FE to display/render. We will not execute the code.
  // TODO: deprecate after demo
  string symbolic_code = 4;
}

// Variables that are passed for execution as global env_value
message WorkflowVariable {
  string key = 1;
  // Only allow string values for simplicity, similar to OS environment variables.
  string value = 2;
}

// Create a workflow task
message CreateTaskAction {
  string workflow_id = 1;
  // the process within a workflow to start the task
  string process_id = 3;

  // deprecated, use task_variables instead
  repeated ActionParamValue workflow_variables = 2 [deprecated = true];

  // pass given variables to the following execution. Both key and value can be
  // dynamic.
  message Variable {
    ActionParamValue key = 1;
    ActionParamValue value = 2;
  }
  repeated Variable variables = 4;
}

message ReconcileItemsAction {
  message ItemLocator {
    int32 tab_index = 1;
    ElementLocator field_groups = 2;
    repeated ElementLocator documents = 3;
  }
  repeated ItemLocator items = 4;
}

// Update the data table on the HTML page.
message UpdateDataTableAction {
  ElementLocator table_locator = 1;
}

message ExitAction {
  enum ExitStatus {
    EXIST_STATUS_UNSPECIFIED = 0;
    SUCCESS = 1;
    ERROR = 2;
  }
  ExitStatus status = 1;
  string message = 2;
}

// Allow user to define custom actions that are not supported by default.
message CustomSmartAction {
  // key value pairs as input to the action
  map<string, ActionParamValue> inputs = 1;

  // NL description of what the action does
  string rule = 2;
}

// GetDocument action allows us to capture an unstructured documents from the UI.
// It can be used in the following ways:
// 1. if there is no locator specified, we would take the screenshot of the UI
//    by default. For some applications, we may provide an optimized
//    implementation that fetch the main file on the page.
// 2. if there are locator specified, it can be:
//    2.1 the located element is an image or embedded PDF file. we would fetch
//        the image/file binary to use as the document
//    2.2 otherwise, we would take the screenshot for the element as the document
// 3. if an action is specified, we would expect the action to either trigger a
//    download request, or redirect the current page to a binary file. We would
//    use the file from the request as the document.
// 4. if the url is specified, we would fetch the document directly from the URL.
//    this is useful when we can derive document URL directly, either from the
//    elements on the page (such as an a element), or from the URL.
message GetDocumentAction {
  // Deprecated: use locator instead.
  ElementLocator document_locator = 1 [deprecated = true];

  oneof method {
    ActionParamValue locator = 3  [(pb.options).param_type = ELEMENT_LOCATOR];
    Action action = 2; // user ActionBlock if multiple actions are involved.
    ActionParamValue url = 4  [(pb.options).param_type = STRING]; // retrieve the document from the direct url
  }
}

// A generic action to generate text based on user prompted rule.
// Example: Generate a summary based on the extracted fields from a document
message GenerateTextAction {
  // Expected to be reference value of any previous action output
  repeated ActionParamValue inputs = 1;

  // User defined prompt for text generation
  string prompt = 2;
}

// A generic action to classify any input into predefined categories with a
// confidence score and explanation. A few example use cases:
// - Receipt validation (check a document is a valid receipt according to the policy)
// - Risk analysis (use the confidence score as a measure of risk)
// - Ranking (use the confidence score as measure of relevance)
// - Sentiment analysis, such as is this review positive or negative
message ClassifyAction {
  // Expected to be reference value of any previous action output
  repeated ActionParamValue inputs = 1;

  // Either user defined prompt or preset should be provided for classification
  string prompt = 2;
  Classify.ClassifyPreset preset = 3;
}

// Action to send an email with optional attachment.
message SendEmailAction {
  message Attachment {
    ActionParamValue filename = 1 [(pb.options).param_type = STRING];
    ActionParamValue content = 2 [(pb.options).param_type = DOCUMENT];
  }

  message Body {
    // plaintext content with MIME type of `text/plain`
    ActionParamValue plain = 1 [(pb.options).param_type = STRING];
    // rich content with MIME type of `text/html`
    ActionParamValue html = 2 [(pb.options).param_type = STRING];
  }

  repeated ActionParamValue recipients = 1;
  ActionParamValue subject = 2;
  Body body = 3;
  repeated Attachment attachments = 4;
}

// Get the one-time passcode, which is usually for MFA. Expect to support:
// 1. One-time password: TOTP (RFC 6238) and HOTP (RFC 4226). The secret param is
//    stored in secret manager, while the other parameters are stored inside
//    the action for simplicity. The code is generated locally after fetching
//    the secret value.
// 2. SMS-based code. The phone number is stored locally. During execution, we
//    need to call the Orbot.GetSmsAuthCode API  to get the auth code, and
//
// NOTE: This action only generates auth code, which is expected to be used by a
// following setValue action. For example in a SMS-based MFA workflow, we would
// have the following actions:
// (1) click send code button on the page
// (2) call this action to retrieve the SMS code
// (3) fill in the code on the page
message GetPasscodeAction {
  // See https://github.com/hectorm/otpauth?tab=readme-ov-file#supported-hashing-algorithms
  enum OtpAlgorithm {
    OTP_ALGORITHM_UNSPECIFIED = 0;
    SHA1 = 1;
    SHA224 = 2;
    SHA256 = 3;
    SHA384 = 4;
    SHA512 = 5;
    SHA3_224 = 6;
    SHA3_256 = 7;
    SHA3_384 = 8;
    SHA3_512 = 9;
  }

  // See RFC 6238: https://datatracker.ietf.org/doc/html/rfc6238
  message Totp {
    ActionParamValue secret = 1;
    OtpAlgorithm algorithm = 2;
    int32 digits = 3;
  }

  // See RFC 4226: https://datatracker.ietf.org/doc/html/rfc4226
  message Hotp {
    ActionParamValue secret = 1;
    OtpAlgorithm algorithm = 2;
    int32 digits = 3;
    int32 counter = 4;
  }

  // SMS-based code with a phone number that is registered during workflow setup
  message Sms {
    // Phone number should be in the E.164 format: https://www.twilio.com/docs/glossary/what-e164
    string phone_number = 1;
  }

  oneof auth_method {
    Totp totp = 1;
    Hotp hotp = 2;
    Sms sms = 3;
  }
}

message ScrollAction {
  // Scroll the document by given pixels.
  // Similar to https://developer.mozilla.org/en-US/docs/Web/API/Window/scrollBy
  message ScrollBy {
    float x = 1;
    float y = 2;
  }

  // Scroll the window to a particular place in the document.
  // Similar to https://developer.mozilla.org/en-US/docs/Web/API/Window/scroll
  message ScrollTo {
    message Position {
      float x = 1;
      float y = 2;
    }
    enum PresetPosition {
      PRESET_POSITION_UNSPECIFIED = 0;
      TOP = 1;
      BOTTOM = 2;
      LEFTMOST = 3;
      RIGHTMOST = 4;
    }

    oneof scroll_to_type {
      Position position = 1;
      PresetPosition preset = 2;
    }
  }

  oneof scroll_type {
    ScrollBy scroll_by = 1;
    ScrollTo scroll_to = 2;
  }

  // The scrollable container where the scroll action is observed.
  ActionParamValue container = 3 [(pb.options).param_type = ELEMENT_LOCATOR];
}

// Track when the focus changes between different applications, browser tabs or
// UI elements. Right now this is mainly to allow us to capture those actions
// for Process Discovery observation mode and isn't leveraged by the automation
// execution since the focus change is implicit for the actual action.
//
// Example 1: focus change between different browser tabs
// action {
//   focus {
//   }
//   tabIndex: 1
// }
//
// Example 2: focus change between different applications
// action {
//   focus {
//   }
//   windowIndex: 1
// }
//
// Example 3: focus change between different UI elements for Windows application.
// action {
//   focus {
//     element {
//       json_value: '{"label": "Save"}'
//     }
//   }
//   windowIndex: 1
// }
message FocusAction {
  ActionParamValue element = 3 [(pb.options).param_type = ELEMENT_LOCATOR];
}

message MacroAction {
  oneof macro_action_type {
    MacroActionLogin login = 1;
    MacroActionGeneric generic = 2;
  }
}

message MacroActionLogin {
  oneof secret_identifier {
    // Use the origin (host URL) when the workflow is a template.
    // This is because secret block IDs are different across orgs, so
    // this way, orgs can share templates.
    string origin = 1;
    // Secret block ID must be used when workflow is executing
    string secret_block_id = 2;
  }

  // When MFA is involved for login, we can persist relevant information in
  // GetPasscodeAction.
  //
  // For workflow execution:
  // 1. the server calls ML action inference with the GetPasscodeAction field,
  //    along with all the secret fields (such as username/password).
  //    NOTE: we don't need to resolve any ActionParamValue such as Totp.secret
  //    since that is not needed for action inference.
  // 2. if ML decides it's time to do MFA, it would return an ActionBlock with
  //    two actions: GetPasscodeAction to generate the passcode and SetValue to
  //    paste the value (with ActionParamValue.reference_value) on a UI element.
  //
  // There might be multiple MFA available so this field is marked as repeated.
  repeated GetPasscodeAction passcode_actions = 3;
}

message MacroActionGeneric {
  // The instruction that we'll pass to the LLM to generate the action.
  // Environment variables (ActionParamValue.env_value) can be passed in via
  // jinja syntax, e.g. "{{invoice_id}}" if there is an environment variable called
  // invoice_id.
  // Note that to make things explicit, any variables that the instruction
  // is using needs to be declared in instruction_variables field below.
  string instruction = 1 [deprecated = true];

  // The instruction that we'll pass to the LLM to generate the action.
  // Variables can be passed in via jinja syntax, e.g. "{{invoice_id}}" if there
  // is an MacroActionGeneric.variables with name invoice_id.
  ActionParamValue prompt = 3;

  // The variables that we want the instruction to have access to.
  repeated ActionParamValue instruction_variables = 2 [deprecated = true];

  // pass given variables to the following execution. Both name and value can be
  // dynamic.
  message Variable {
    ActionParamValue name = 1;
    ActionParamValue value = 2;
  }
  repeated Variable variables = 4;
}

enum MacroActionExecutionError {
  ORBOT_ACTION_ERROR_TYPE_UNSPECIFIED = 0;

  // Not sure of the best way to define these errors.
  OTHER = 1;
  ELEMENT_NOT_FOUND = 2;
}

message MacroActionExecution {
  // The action that was executed.
  // It has to be separate because the element from the response will be using
  // the elementID, not the elementLocator or locator that our executor will use.
  // This object shall store that.
  // Will be empty if macro_action_response.success
  // or macro_action_response.error is set
  Action action = 1;

  // Error message from the macro action execution
  MacroActionExecutionError error_type = 2;
  string error_message = 3;

  MacroActionInferContext macro_action_context = 5;
  MacroActionStep macro_action_step = 6;
}

// the context would be populated on the extension side
message MacroActionInferContext {
  UiState ui_state = 1;
}

// result for a MacroAction inference, which can either be an action to execute,
// or terminate the execution either successfully or not.
message MacroActionStep {
  enum TerminateActionErrorType {
    TERMINATE_ACTION_ERROR_TYPE_UNSPECIFIED = 0;
    TRAJECTORY_LENGTH_EXCEEDED = 1;
    LOOP_DETECTED = 2;
  }

  message Success {}

  message Error {
    TerminateActionErrorType type = 1;
    string message = 2;
  }

  // If the ML just want the client to wait for a certain condition to be met.
  message Wait {}

  oneof result {
    // user Action.block if multiple actions can be executed in a step.
    Action action = 1;
    Success success = 2;
    Error error = 3;
    Wait wait = 5;
  }

  // TODO: Add more detailed response regarding model name, family, llm prompt, etc.
  message DebugInfo {
    string llm_response = 1;
  }
  DebugInfo debug_info = 4;
}

message UserEvent {
  string user_id = 1;
  string user_agent = 2;
  string url = 3;
  int32 window_id = 4;
  string session_id = 5;
  int32 tab_id = 6;
  google.protobuf.Timestamp timestamp = 7;
  Action action = 8;
}
