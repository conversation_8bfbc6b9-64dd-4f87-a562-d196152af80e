syntax = "proto3";

package pb.v1alpha1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

// File service provides endpoints to upload and read files with GCS signed
// URLs (https://cloud.google.com/storage/docs/access-control/signed-urls).
service UserFile {
  // Creates a file and returns the signed url for uploading.
  rpc CreateFileUpload (CreateFileUploadRequest) returns (CreateFileUploadResponse) {}

  // Returns the signed url for existing GCS file, after verifying that the user
  // has access to the file.
  rpc GetFileUrl (GetFileUrlRequest) returns (GetFileUrlResponse) {}
}

// File created from a Webpage, such as screenshot and snapshot.
message WebpageFile {
  // the time that the page screenshot/snapshot is taken.
  google.protobuf.Timestamp created_time = 1;

  // the Webpage URL.
  string page_url = 2;
}

// User uploaded files when using ProcessSmartActions API
message DocumentFile {
  google.protobuf.Timestamp created_time = 1;

  string workflow_id = 2;
  string task_id = 3;

  // the URL where the document is downloaded.
  string url = 4;
}

message CreateFileUploadRequest {
  // File MIME type are expected to be stored in GCS file property.
  oneof file {
    WebpageFile screenshot = 1; // Webpage screenshot in image format
    WebpageFile snapshot = 2; // Webpage snapshot in HTML format
    DocumentFile document = 4; // Image/PDF files used for ML actions
  }

  // Organization which the file belongs, required for access control.
  string org_id = 3;
  // Optional workflow to which the file belongs to
  string workflow_id = 5;
  // Optional execution to which the file belongs to
  string execution_id = 6;
}

message CreateFileUploadResponse {
  // file ID that can be used to refer to the file, such as in RecordedAction.
  string id = 1;

  // GCS signed URL for file uploading.
  string upload_url = 2;
}

message GetFileUrlRequest {
  string id = 1;

  // User's active organization
  string org_id = 2;
}

message GetFileUrlResponse {
  // GCS signed URL to read the file. valid for 15 minutes.
  string url = 1;
}
