syntax = "proto3";
package pb.v1alpha1;

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";

import "pb/v1alpha1/element.proto";
import "pb/v1alpha1/orbot_action.proto";
import "pb/v1alpha1/orbot_workflow.proto";

message GetElementLocatorsRequest {
  string workflow_id = 1;
  string action_id = 2;
}

message GetElementLocatorsResponse {
  repeated ElementLocator element_locators = 1;
}

// The request to locate an element on a page.
// Next ID: 11
message LocateElementRequest {
  // Contains the full HTML page, each element in the page has a unique ID assigned by the extension.
  // Deprecated
  PageContent page_content = 1 [deprecated=true];

  // Full DOM tree information; deprecated in favor of root_element_wrapper.
  Element root_element = 8 [deprecated=true];

  // The action that request to locate the element, which includes the ElementLocator.
  PreparedAction action = 2 [deprecated=true];
  Action current_action = 7;

  // The full workflow contains name/description and other actions as context.
  Workflow workflow = 3;

  // Contains the information from previous successful executions
  message PreviousExecution {
    // ExecutedActions contain the ElementLocator and the located element during execution.
    ExecutedAction action = 2;
    // The full page content during action execution.
    PageContent page_content = 1 [deprecated=true];
    Element root_element = 3 [deprecated=true];
    ElementWrapper root_element_wrapper = 4;
  }

  // populated on the server side before sending to ML.
  repeated PreviousExecution previous_executions = 4;

  // Natural language description of the action
  string description = 5;

  // Parameter used internally for LLM API monitoring
  bool bypass_llm_cache = 6;

  // The pipeline (model) type to be used to locate the element.
  // TODO; reconsider the name of this enum and the values.
  enum PipelineType {
    PIPELINE_TYPE_UNSPECIFIED = 0;
    TEXT_GPT_4_TURBO_0125_PREVIEW = 1;
    VISION_ORBY_ACTIO_0_1 = 2;
    ORBY_ACTIO_ACTIONFINDER_0_0_1 = 3;
  }
  PipelineType pipeline_type = 9;

  // Full DOM tree information in the form of ElementWrapper.
  ElementWrapper root_element_wrapper = 10;
}

message LocateElementResponse {
  // The unique ID (generated by Orby) for the located element in PageContent.
  string element_id = 1;
  float confidence = 2;

  enum LocateElementError {
    ERROR_UNSPECIFIED = 0;
    ACTION_NOT_SUPPORTED = 1;
    ERROR_UNKNOWN = 2;
  }
  LocateElementError error = 3;
}

message ReportWorkingElementLocatorRequest {
  string workflow_id = 1;
  string action_id = 2;
  ElementLocator element_locator = 3;
}

// Used to construct examples of ElementLocatorRequests and their expected results together
// for test cases and evaluation datasets.
message LocateElementExample {
  LocateElementRequest request = 1;
  LocateElementResponse response = 2;
}

// Group a set of LocateElementExamples with an optional name.
message LocateElementExamples {
  // identify the set of examples.
  string name = 1;

  repeated LocateElementExample examples = 2;
}
