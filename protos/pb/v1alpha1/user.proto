syntax = "proto3";

package pb.v1alpha1;

import "buf/validate/validate.proto";
import "common/announcement.proto";
import "google/protobuf/timestamp.proto";
import "pb/v1alpha1/oauth2_token.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/v1alpha1";


message User {
  string email = 1; 
  // Key value pairs of user settings. Two keys are supported now:
  // 1. "enable_event_upload". Possible values: true|false
  // 2. "not_allowed_sites". Possible values: a string of websites joined by comma, for example:
  //    "www.citibank.com,www.adp.com". Don't includ protocol prefixes like "http://" or "https://".
  map<string, string> settings = 2;
  // Format: organizations/{ID}
  string org_resource_name = 3;
  string org_display_name = 4;
  // ROLE_ADMIN: can create users and workflows
  // ROLE_CREATOR: can create workflows
  // ROLE_USER: can only review workflows
  enum OrgRole {
    ROLE_UNSPECIFIED = 0;
    ROLE_ADMIN = 1;
    ROLE_USER = 2;
    ROLE_CREATOR = 3;
  }
  OrgRole role = 5;
  // Inactive users won't be assigned to tasks. This value is set to false when
  // an admin user invite new users, and flipped to true when a user has
  // signed into Orby AI once.
  bool activated = 6;
  repeated string assigned_workflows = 7;
  Oauth2Token google_token = 8;
  UserPrerequisite prerequisite = 9;
  // Support for multiple organizations
  repeated OrgInfo org_infos = 10;

  string id = 11;
  string first_name = 12;
  string last_name = 13;
  string full_name = 14;
  string profile_image_url = 15;
  MicrosoftUserInfo microsoft_user_info = 16;

  // Permissions that user has access to.
  // This field will be used in internal-app
  repeated string permitted_actions = 17;

  // Announcements that user has not seen yet.
  repeated Announcement announcements = 18;

  // workflows that user is admin of
  repeated string managed_workflows = 19;

  // Number of completed tasks reviewedby user in a 
  // particular timeframe
  int32 number_of_completed_tasks_reviewed = 20;

  Oauth2Token ms_token = 21;

  GoogleUserInfo google_user_info = 22;

  // Used to update cookie preferences
  repeated CookiePreference cookie_preferences = 23;
}

// Prerequisite that user needs to go through before having access to our
// services including frontend
message UserPrerequisite {
  // New policies or policy versions for user to review and accept.
  repeated PolicyAcceptance policies_to_review = 1;
}

message PolicyAcceptance {
  string policy_name = 1;
  string policy_version = 2;
  string policy_url = 3;
  google.protobuf.Timestamp accepted_at = 4;
  string policy_id = 5;
}

message OrgInfo {
  string org_resource_name = 1;
  string org_display_name = 2;
  User.OrgRole role = 3;
}

message MicrosoftUserInfo {
  string type = 1;
  string base_drive_url = 2;
  // List of scopes (eg - Files.ReadWrite, user.read) currently held by app
  repeated string scopes = 3;

  string email = 4;
}

message GoogleUserInfo {
  string email = 1;
}

message Announcement {
  string id = 1;
  common.AnnouncementType type = 2;
  common.AnnouncementContentMarkdown header = 3;
  repeated common.AnnouncementContentBlock content_blocks = 4;
}

message CookiePreference {
  string key = 1 [(buf.validate.field).string.min_len = 1];
  string value = 2 [(buf.validate.field).string.min_len = 1];
}
