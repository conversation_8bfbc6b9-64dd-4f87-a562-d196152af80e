syntax = "proto3";

package pb;

import "google/protobuf/descriptor.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb";

// Value types for ActionParamValue
enum ValueType {
  PARAM_TYPE_UNSPECIFIED = 0;
  // refers to values that can be parsed into pb.v1alpha1.ElementLocator
  ELEMENT_LOCATOR = 1;
  // refers to an element from a getElement action
  ELEMENT = 2;
  // refers to document object from a prior getDocument action
  DOCUMENT = 3;
  STRING = 4;
  // refers to values that can be parsed into pb.v1alpha1.FIELD_UPDATE
  FIELD_UPDATE = 5;
}

message FieldOptions {
  // Used to annotate expected value types for ActionParamValue fields.
  // An action can accept values of one or more types.
  repeated ValueType param_type = 1;
}

extend google.protobuf.FieldOptions {
  FieldOptions options = 1036;
}
