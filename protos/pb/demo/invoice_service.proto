syntax = "proto3";

package pb.demo;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "grpc/gateway/protoc_gen_openapiv2/options/annotations.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/demo";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Orby Web API - Invoice Service";
    version: "demo";
  };
};

service Invoices {
  // Create a invoice for the current users.
  rpc CreateInvoice(CreateInvoiceRequest) returns (Invoice) {
    option (google.api.http) = {
      post: "/demo/{parent=users/*}/invoices"
      body: "invoice"
    };
  }

  // Invoice details for a perticular invoice
  rpc GetInvoice(GetInvoiceRequest) returns (Invoice) {
    option (google.api.http) = {
        get: "/demo/{name=users/*/invoices/*}"
    };
  }

  // List invoices 
  rpc ListInvoices(ListInvoicesRequest) returns (ListInvoicesResponse) {
    option (google.api.http) = {
        get: "/demo/{parent=users/*}/invoices"
      };
  }

  // Update invoice if status is draft
  rpc UpdateInvoice(UpdateInvoiceRequest) returns (Invoice) {
    option (google.api.http) = {
        patch: "/demo/{invoice.name=users/*/invoices/*}"
        body: "invoice"
    };
  }

  // Delete invoice 
  rpc DeleteInvoice(DeleteInvoiceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
        delete: "/demo/{name=users/*/invoices/*}"
    };
  }
}

message Invoice {
    // Resource name for invoice, in the format of users\/\*/invoices/\*
    string name = 1;
    string vendor_name = 2;
    string vendor_contact = 3;
    string billing_address = 4;
    string number = 5;
    string invoice_date = 6;
    string invoice_due_date = 7;
    string purchase_order_number = 8;
    repeated LineItem line_items = 9;
    string total = 10; // This field is currently declared as string for sandbox testing purpose.
    enum STATUS {
      STATUS_UNSPECIFIED = 0;
      // submited indicates the invoice is submitted, but we can't edit.
      SUBMITTED = 1;
      // draft indicates the invoice is submitted but user can edit.
      DRAFT  = 2;
    }
    STATUS status = 11;
}

message LineItem {
    string id = 1;
    string description = 2;
    string quantity = 3; // This field is currently declared as string for sandbox testing purpose.
    string price = 4; // This field is currently declared as string for sandbox testing purpose.
}

message CreateInvoiceRequest {
    // The parent resource name where the invoice is to be created.
    // E.g., "users/<EMAIL>"
    string  parent = 1;
    Invoice invoice = 2;
}

message ListInvoicesRequest {
    // The parent resource name where the invoice was created.
    // E.g., users/<EMAIL>
    string parent = 1;
    // Default is 10 (when page_size is missing or set to 0). Max value is 20.
    // Ordered by ascending based on invoice date.
    int32 page_size = 2;
    // Use this to continue the previous list requests.
    // Its value should be same with previous response's next_page_token.
    string page_token = 3;
    // Supported filters: "due_date>=", "due_date<=", "number=", "status"=, "vendor_name=",
    // Valid values for due_date filter are in the format YYYY-MM-DD (2023-01-11)
    // Valid values for number filter is any string
    // Valid values for vendor_name filter is any string
    // Valid values for status filter are DRAFT, SUBMITTED, STATUS_UNSPECIFIED
    // Use comma to combine multiple filters: "due_date>=2023-01-11,number=1". 
    string filter = 4;
}
  
message ListInvoicesResponse {
    // Ordered by ascending invoice date.
    repeated Invoice invoices = 1;
    // If the value is "", it means no more results for the request.
    string next_page_token = 2;
    // Total available invoice size.
    // Note it is NOT the remaining available invoice size 
    // after the current response.
    int32 total_size = 3;
}

message UpdateInvoiceRequest {
    Invoice invoice = 1;
}

message GetInvoiceRequest {
  // Name of the Invoice
  string name = 1;
}

message DeleteInvoiceRequest {
  // Name of the Invoice
  string name = 1;
}