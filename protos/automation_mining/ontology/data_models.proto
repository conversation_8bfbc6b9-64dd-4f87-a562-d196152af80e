syntax = "proto3";

package automation_mining.ontology;

import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/automation_mining/ontology";

// Data model for emails.
// Supported applications: gmail, outlook
message Email {
  string thread_id = 1;
  string subject = 2;
  string message_id = 3;
  string from = 4;
  string to = 5;
  string body = 6;
  repeated EmailAttachment attachments = 7;
  // id for communicating with gmail API.
  string api_message_id = 9;
  google.protobuf.Timestamp date = 10;
}

message EmailAttachment {
  // These are the part ids returned by the gmail api to map with the CE responses
  string id = 1;
  string url = 2;
  bytes content = 3;
  string mime_type = 4;
  string file_name = 5;
  // cids are used in src attribute inside html email body, to load attachment content
  string cid = 6;
}

// Data model for files and folders
// Supported applications: Google Doc
message File {
  string id = 1;
  // E.g. Google driver urls for files or folders
  string path = 2;
  string name = 3;
  oneof content {
    string text_content = 4;
    bytes byte_content = 5;
  }
  string mime_type = 6;
  bool is_folder = 7;
}

// Data model for excel sheets
// Supported applications: Google Sheets
message Sheets {
  message Sheet {
    message Cell {
      string value = 1;
    }
    string id = 1;
    string name = 2;
    map<string, Cell> cells = 3;
    // This stores the additional metadata for Sheet.
    //
    // The folling keys are supported in the metadata: entity_name
    //
    // The entity name is either simple or <parent_name>_nested. 
    // The reason why we used _nested is to differentiate between the
    // non-nested entity and nested entity incase the nested entity also
    // is 'simple'
    map<string, google.protobuf.Any> metadata = 4;
  }
  string id = 1;
  string name = 2;
  File folder = 3;
  repeated Sheet sheets = 4;
  // E.g. Google sheets url
  string path = 5;
}

// Data model for invoices
// Supported applications: tbd
message Invoice {
  string id = 1;
  string total_amount = 2;

}

// Data model for purchase orders
// Supported applications: tbd
message PurchaseOrder {
  string id = 1;
  string total_amount = 2;
}

// Data model for receipts.
// Supported applications: tbd
message Receipt {
  string id = 1;
  string total_amount = 2;
}

// Data model for gmail label
// Do not use: For backward compatibility, will be removed once all logic is migrated to EmailLabel.
message GmailLabel {
  string id = 1;
  string name = 2;
  message Color {
    string background_color = 1; 
    string text_color = 2; 
  }
  Color color = 3;
}

// Data model for email label
message EmailLabel {
  string id = 1;
  string name = 2;
  message Color {
    string background_color = 1;
    string text_color = 2;
  }
  Color color = 3;
}
