syntax = "proto3";

package automation_mining;

import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/automation_mining";

// Next ID: 21
enum Activity {
  ACTIVITY_UNSPECIFIED = 0;

  // Email events
  ACTIVITY_NEW_EMAIL = 7;
  ACTIVITY_SEND_EMAIL = 1;
  ACTIVITY_READ_EMAIL = 2;
  ACTIVITY_DOWNLOAD_ATTACHMENT = 3;
  ACTIVITY_CLASSIFY_DOCUMENT = 13;
  ACTIVITY_COMPREHEND_DOCUMENT = 4; // document prerequisite
  ACTIVITY_EXTRACT_ENTITY = 18; // extraction only

  // Drive events
  ACTIVITY_SAVE_FILE = 5;
  ACTIVITY_NEW_FILE_ADDED = 15;

  // Sheet events
  ACTIVITY_UPDATE_SHEET_CELL = 6;
  ACTIVITY_OPEN_SHEET = 8;
  ACTIVITY_CREATE_SHEET_TAB = 9;
  ACTIVITY_PASTE_VALUE_INTO_SHEET_CELL = 14;

  // Indicate that there is no more activities available.
  END = 10;

  // NetSuite events
  ACTIVITY_CREATE_INVOICE = 11;

  // Google Sheets events
  ACTIVITY_ADD_SHEET_ROW = 12;

  // Manual Triggering
  ACTIVITY_MANUAL_TRIGGER = 16;

  // SFTP Triggering
  ACTIVITY_NEW_OBJECT_ADDED = 17;

  // SMART ACTION (Deprecated)
  ACTIVITY_SMART_ACTION = 19;

  // Proceed to next process execution after smart action is reviewed.
  ACTIVITY_PROCEEDING_EXECUTION = 20;

  // raw file deletion event
  ACTIVITY_DELETE_RAW_FILE = 21;

  // Smart Actions
  ACTIVITY_SMART_ACTION_EXTRACT_FIELDS = 22;
  ACTIVITY_SMART_ACTION_VALIDATE_FIELD_VALUES = 23;
  ACTIVITY_SMART_ACTION_FLAG_KEYWORDS = 24;
  ACTIVITY_SMART_ACTION_DETECT_DUPLICATE_LINE_ITEMS = 25;
  ACTIVITY_SMART_ACTION_RECONCILE_LINE_ITEMS = 26;
  ACTIVITY_SMART_ACTION_GENERATE_TEXT = 27;
  ACTIVITY_SMART_ACTION_CLASSIFY = 28;
}

message Event {
  string id = 1;
  string case_id = 2;
  google.protobuf.Timestamp happened_at = 3;
  Activity activity = 4;
  repeated google.protobuf.Any contexts = 5;
}
