# Copyright 2023 Buf Technologies, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("@rules_proto//proto:defs.bzl", "proto_library")
load("@rules_buf//buf:defs.bzl", "buf_lint_test")

# gazelle:proto file

proto_library(
    name = "validate_proto",
    srcs = ["validate.proto"],
    strip_import_prefix = "/proto/protovalidate",
    visibility = ["//visibility:public"],
    deps = [
        ":expression_proto",
        "//proto/protovalidate/buf/validate/priv:priv_proto",
        "@com_google_protobuf//:descriptor_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

buf_lint_test(
    name = "validate_proto_lint",
    config = "//proto/protovalidate:buf.yaml",
    targets = [":validate_proto"],
)

cc_proto_library(
    name = "validate_proto_cc",
    visibility = ["//visibility:public"],
    deps = [":validate_proto"],
)

proto_library(
    name = "expression_proto",
    srcs = ["expression.proto"],
    strip_import_prefix = "/proto/protovalidate",
    visibility = ["//visibility:public"],
)

buf_lint_test(
    name = "expression_proto_lint",
    config = "//proto/protovalidate:buf.yaml",
    targets = [":expression_proto"],
)

cc_proto_library(
    name = "expression_proto_cc",
    visibility = ["//visibility:public"],
    deps = [":expression_proto"],
)
