// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package buf.validate;

import "buf/validate/expression.proto";
import "buf/validate/priv/private.proto";
import "google/protobuf/descriptor.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option go_package = "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate";
option java_multiple_files = true;
option java_outer_classname = "ValidateProto";
option java_package = "build.buf.validate";

// MessageOptions is an extension to google.protobuf.MessageOptions. It allows
// the addition of validation rules at the message level. These rules can be
// applied to incoming messages to ensure they meet certain criteria before
// being processed.
extend google.protobuf.MessageOptions {
  // Rules specify the validations to be performed on this message. By default,
  // no validation is performed against a message.
  optional MessageConstraints message = 1159;
}

// OneofOptions is an extension to google.protobuf.OneofOptions. It allows
// the addition of validation rules on a oneof. These rules can be
// applied to incoming messages to ensure they meet certain criteria before
// being processed.
extend google.protobuf.OneofOptions {
  // Rules specify the validations to be performed on this oneof. By default,
  // no validation is performed against a oneof.
  optional OneofConstraints oneof = 1159;
}

// FieldOptions is an extension to google.protobuf.FieldOptions. It allows
// the addition of validation rules at the field level. These rules can be
// applied to incoming messages to ensure they meet certain criteria before
// being processed.
extend google.protobuf.FieldOptions {
  // Rules specify the validations to be performed on this field. By default,
  // no validation is performed against a field.
  optional FieldConstraints field = 1159;
}

// MessageConstraints represents validation rules that are applied to the entire message.
// It includes disabling options and a list of Constraint messages representing Common Expression Language (CEL) validation rules.
message MessageConstraints {
  // `disabled` is a boolean flag that, when set to true, nullifies any validation rules for this message.
  // This includes any fields within the message that would otherwise support validation.
  //
  // ```proto
  // message MyMessage {
  //   // validation will be bypassed for this message
  //   option (buf.validate.message).disabled = true;
  // }
  // ```
  optional bool disabled = 1;

  // `cel` is a repeated field of type Constraint. Each Constraint specifies a validation rule to be applied to this message.
  // These constraints are written in Common Expression Language (CEL) syntax. For more information on
  // CEL, [see our documentation](https://github.com/bufbuild/protovalidate/blob/main/docs/cel.md).
  //
  //
  // ```proto
  // message MyMessage {
  //   // The field `foo` must be greater than 42.
  //   option (buf.validate.message).cel = {
  //     id: "my_message.value",
  //     message: "value must be greater than 42",
  //     expression: "this.foo > 42",
  //   };
  //   optional int32 foo = 1;
  // }
  // ```
  repeated Constraint cel = 3;
}

// The `OneofConstraints` message type enables you to manage constraints for
// oneof fields in your protobuf messages.
message OneofConstraints {
  // If `required` is true, exactly one field of the oneof must be present. A
  // validation error is returned if no fields in the oneof are present. The
  // field itself may still be a default value; further constraints
  // should be placed on the fields themselves to ensure they are valid values,
  // such as `min_len` or `gt`.
  //
  // ```proto
  // message MyMessage {
  //   oneof value {
  //     // Either `a` or `b` must be set. If `a` is set, it must also be
  //     // non-empty; whereas if `b` is set, it can still be an empty string.
  //     option (buf.validate.oneof).required = true;
  //     string a = 1 [(buf.validate.field).string.min_len = 1];
  //     string b = 2;
  //   }
  // }
  // ```
  optional bool required = 1;
}

// FieldConstraints encapsulates the rules for each type of field. Depending on
// the field, the correct set should be used to ensure proper validations.
message FieldConstraints {
  // `cel` is a repeated field used to represent a textual expression
  // in the Common Expression Language (CEL) syntax. For more information on
  // CEL, [see our documentation](https://github.com/bufbuild/protovalidate/blob/main/docs/cel.md).
  //
  // ```proto
  // message MyMessage {
  //   // The field `value` must be greater than 42.
  //   optional int32 value = 1 [(buf.validate.field).cel = {
  //     id: "my_message.value",
  //     message: "value must be greater than 42",
  //     expression: "this > 42",
  //   }];
  // }
  // ```
  repeated Constraint cel = 23;
  // If `required` is true, the field must be populated. A populated field can be
  // described as "serialized in the wire format," which includes:
  //
  // - the following "nullable" fields must be explicitly set to be considered populated:
  //   - singular message fields (whose fields may be unpopulated/default values)
  //   - member fields of a oneof (may be their default value)
  //   - proto3 optional fields (may be their default value)
  //   - proto2 scalar fields (both optional and required)
  // - proto3 scalar fields must be non-zero to be considered populated
  // - repeated and map fields must be non-empty to be considered populated
  //
  // ```proto
  // message MyMessage {
  //   // The field `value` must be set to a non-null value.
  //   optional MyOtherMessage value = 1 [(buf.validate.field).required = true];
  // }
  // ```
  bool required = 25;
  // Skip validation on the field if its value matches the specified criteria.
  // See Ignore enum for details.
  //
  // ```proto
  // message UpdateRequest {
  //   // The uri rule only applies if the field is populated and not an empty
  //   // string.
  //   optional string url = 1 [
  //     (buf.validate.field).ignore = IGNORE_IF_DEFAULT_VALUE,
  //     (buf.validate.field).string.uri = true,
  //   ];
  // }
  // ```
  Ignore ignore = 27;

  oneof type {
    // Scalar Field Types
    FloatRules float = 1;
    DoubleRules double = 2;
    Int32Rules int32 = 3;
    Int64Rules int64 = 4;
    UInt32Rules uint32 = 5;
    UInt64Rules uint64 = 6;
    SInt32Rules sint32 = 7;
    SInt64Rules sint64 = 8;
    Fixed32Rules fixed32 = 9;
    Fixed64Rules fixed64 = 10;
    SFixed32Rules sfixed32 = 11;
    SFixed64Rules sfixed64 = 12;
    BoolRules bool = 13;
    StringRules string = 14;
    BytesRules bytes = 15;

    // Complex Field Types
    EnumRules enum = 16;
    RepeatedRules repeated = 18;
    MapRules map = 19;

    // Well-Known Field Types
    AnyRules any = 20;
    DurationRules duration = 21;
    TimestampRules timestamp = 22;
  }

  // DEPRECATED: use ignore=IGNORE_ALWAYS instead. TODO: remove this field pre-v1.
  bool skipped = 24 [deprecated = true];
  // DEPRECATED: use ignore=IGNORE_IF_UNPOPULATED instead. TODO: remove this field pre-v1.
  bool ignore_empty = 26 [deprecated = true];
}

// Specifies how FieldConstraints.ignore behaves. See the documentation for
// FieldConstraints.required for definitions of "populated" and "nullable".
enum Ignore {
  // buf:lint:ignore ENUM_NO_ALLOW_ALIAS // allowance for deprecations. TODO: remove pre-v1.
  option allow_alias = true;
  // Validation is only skipped if it's an unpopulated nullable fields.
  //
  // ```proto
  // syntax="proto3";
  //
  // message Request {
  //   // The uri rule applies to any value, including the empty string.
  //   string foo = 1 [
  //     (buf.validate.field).string.uri = true
  //   ];
  //
  //   // The uri rule only applies if the field is set, including if it's
  //   // set to the empty string.
  //   optional string bar = 2 [
  //     (buf.validate.field).string.uri = true
  //   ];
  //
  //   // The min_items rule always applies, even if the list is empty.
  //   repeated string baz = 3 [
  //     (buf.validate.field).repeated.min_items = 3
  //   ];
  //
  //   // The custom CEL rule applies only if the field is set, including if
  //   // it's the "zero" value of that message.
  //   SomeMessage quux = 4 [
  //     (buf.validate.field).cel = {/* ... */}
  //   ];
  // }
  // ```
  IGNORE_UNSPECIFIED = 0;

  // Validation is skipped if the field is unpopulated. This rule is redundant
  // if the field is already nullable. This value is equivalent behavior to the
  // deprecated ignore_empty rule.
  //
  // ```proto
  // syntax="proto3
  //
  // message Request {
  //   // The uri rule applies only if the value is not the empty string.
  //   string foo = 1 [
  //     (buf.validate.field).string.uri = true,
  //     (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  //   ];
  //
  //   // IGNORE_IF_UNPOPULATED is equivalent to IGNORE_UNSPECIFIED in this
  //   // case: the uri rule only applies if the field is set, including if
  //   // it's set to the empty string.
  //   optional string bar = 2 [
  //     (buf.validate.field).string.uri = true,
  //     (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  //   ];
  //
  //   // The min_items rule only applies if the list has at least one item.
  //   repeated string baz = 3 [
  //     (buf.validate.field).repeated.min_items = 3,
  //     (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  //   ];
  //
  //   // IGNORE_IF_UNPOPULATED is equivalent to IGNORE_UNSPECIFIED in this
  //   // case: the custom CEL rule applies only if the field is set, including
  //   // if it's the "zero" value of that message.
  //   SomeMessage quux = 4 [
  //     (buf.validate.field).cel = {/* ... */},
  //     (buf.validate.field).ignore = IGNORE_IF_UNPOPULATED
  //   ];
  // }
  // ```
  IGNORE_IF_UNPOPULATED = 1;

  // Validation is skipped if the field is unpopulated or if it is a nullable
  // field populated with its default value. This is typically the zero or
  // empty value, but proto2 scalars support custom defaults. For messages, the
  // default is a non-null message with all its fields unpopulated.
  //
  // ```proto
  // syntax="proto3
  //
  // message Request {
  //   // IGNORE_IF_DEFAULT_VALUE is equivalent to IGNORE_IF_UNPOPULATED in
  //   // this case; the uri rule applies only if the value is not the empty
  //   // string.
  //   string foo = 1 [
  //     (buf.validate.field).string.uri = true,
  //     (buf.validate.field).ignore = IGNORE_IF_DEFAULT_VALUE
  //   ];
  //
  //   // The uri rule only applies if the field is set to a value other than
  //   // the empty string.
  //   optional string bar = 2 [
  //     (buf.validate.field).string.uri = true,
  //     (buf.validate.field).ignore = IGNORE_IF_DEFAULT_VALUE
  //   ];
  //
  //   // IGNORE_IF_DEFAULT_VALUE is equivalent to IGNORE_IF_UNPOPULATED in
  //   // this case; the min_items rule only applies if the list has at least
  //   // one item.
  //   repeated string baz = 3 [
  //     (buf.validate.field).repeated.min_items = 3,
  //     (buf.validate.field).ignore = IGNORE_IF_DEFAULT_VALUE
  //   ];
  //
  //   // The custom CEL rule only applies if the field is set to a value other
  //   // than an empty message (i.e., fields are unpopulated).
  //   SomeMessage quux = 4 [
  //     (buf.validate.field).cel = {/* ... */},
  //     (buf.validate.field).ignore = IGNORE_IF_DEFAULT_VALUE
  //   ];
  // }
  // ```
  //
  // This rule is affected by proto2 custom default values:
  //
  // ```proto
  // syntax="proto2";
  //
  // message Request {
  //   // The gt rule only applies if the field is set and it's value is not
  //   the default (i.e., not -42). The rule even applies if the field is set
  //   to zero since the default value differs.
  //   optional int32 value = 1 [
  //     default = -42,
  //     (buf.validate.field).int32.gt = 0,
  //     (buf.validate.field).ignore = IGNORE_IF_DEFAULT_VALUE
  //   ];
  // }
  IGNORE_IF_DEFAULT_VALUE = 2;

  // The validation rules of this field will be skipped and not evaluated. This
  // is useful for situations that necessitate turning off the rules of a field
  // containing a message that may not make sense in the current context, or to
  // temporarily disable constraints during development.
  //
  // ```proto
  // message MyMessage {
  //   // The field's rules will always be ignored, including any validation's
  //   // on value's fields.
  //   MyOtherMessage value = 1 [
  //     (buf.validate.field).ignore = IGNORE_ALWAYS];
  // }
  // ```
  IGNORE_ALWAYS = 3;

  // Deprecated: Use IGNORE_IF_UNPOPULATED instead. TODO: Remove this value pre-v1.
  IGNORE_EMPTY = 1 [deprecated = true];
  // Deprecated: Use IGNORE_IF_DEFAULT_VALUE. TODO: Remove this value pre-v1.
  IGNORE_DEFAULT = 2 [deprecated = true];
}

// FloatRules describes the constraints applied to `float` values. These
// rules may also be applied to the `google.protobuf.FloatValue` Well-Known-Type.
message FloatRules {
  // `const` requires the field value to exactly match the specified value. If
  // the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MyFloat {
  //   // value must equal 42.0
  //   float value = 1 [(buf.validate.field).float.const = 42.0];
  // }
  // ```
  optional float const = 1 [(priv.field).cel = {
    id: "float.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];

  oneof less_than {
    // `lt` requires the field value to be less than the specified value (field <
    // value). If the field value is equal to or greater than the specified value,
    // an error message is generated.
    //
    // ```proto
    // message MyFloat {
    //   // value must be less than 10.0
    //   float value = 1 [(buf.validate.field).float.lt = 10.0];
    // }
    // ```
    float lt = 2 [(priv.field).cel = {
      id: "float.lt"
      expression:
        "!has(rules.gte) && !has(rules.gt) && (this.isNan() || this >= rules.lt)"
        "? 'value must be less than %s'.format([rules.lt]) : ''"
    }];

    // `lte` requires the field value to be less than or equal to the specified
    // value (field <= value). If the field value is greater than the specified
    // value, an error message is generated.
    //
    // ```proto
    // message MyFloat {
    //   // value must be less than or equal to 10.0
    //   float value = 1 [(buf.validate.field).float.lte = 10.0];
    // }
    // ```
    float lte = 3 [(priv.field).cel = {
      id: "float.lte"
      expression:
        "!has(rules.gte) && !has(rules.gt) && (this.isNan() || this > rules.lte)"
        "? 'value must be less than or equal to %s'.format([rules.lte]) : ''"
    }];
  }

  oneof greater_than {
    // `gt` requires the field value to be greater than the specified value
    // (exclusive). If the value of `gt` is larger than a specified `lt` or
    // `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyFloat {
    //   // value must be greater than 5.0 [float.gt]
    //   float value = 1 [(buf.validate.field).float.gt = 5.0];
    //
    //   // value must be greater than 5 and less than 10.0 [float.gt_lt]
    //   float other_value = 2 [(buf.validate.field).float = { gt: 5.0, lt: 10.0 }];
    //
    //   // value must be greater than 10 or less than 5.0 [float.gt_lt_exclusive]
    //   float another_value = 3 [(buf.validate.field).float = { gt: 10.0, lt: 5.0 }];
    // }
    // ```
    float gt = 4 [
      (priv.field).cel = {
        id: "float.gt"
        expression:
          "!has(rules.lt) && !has(rules.lte) && (this.isNan() || this <= rules.gt)"
          "? 'value must be greater than %s'.format([rules.gt]) : ''"
      },
      (priv.field).cel = {
        id: "float.gt_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gt && (this.isNan() || this >= rules.lt || this <= rules.gt)"
          "? 'value must be greater than %s and less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "float.gt_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gt && (this.isNan() || (rules.lt <= this && this <= rules.gt))"
          "? 'value must be greater than %s or less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "float.gt_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gt && (this.isNan() || this > rules.lte || this <= rules.gt)"
          "? 'value must be greater than %s and less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "float.gt_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gt && (this.isNan() || (rules.lte < this && this <= rules.gt))"
          "? 'value must be greater than %s or less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      }
    ];

    // `gte` requires the field value to be greater than or equal to the specified
    // value (exclusive). If the value of `gte` is larger than a specified `lt`
    // or `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyFloat {
    //   // value must be greater than or equal to 5.0 [float.gte]
    //   float value = 1 [(buf.validate.field).float.gte = 5.0];
    //
    //   // value must be greater than or equal to 5.0 and less than 10.0 [float.gte_lt]
    //   float other_value = 2 [(buf.validate.field).float = { gte: 5.0, lt: 10.0 }];
    //
    //   // value must be greater than or equal to 10.0 or less than 5.0 [float.gte_lt_exclusive]
    //   float another_value = 3 [(buf.validate.field).float = { gte: 10.0, lt: 5.0 }];
    // }
    // ```
    float gte = 5 [
      (priv.field).cel = {
        id: "float.gte"
        expression:
          "!has(rules.lt) && !has(rules.lte) && (this.isNan() || this < rules.gte)"
          "? 'value must be greater than or equal to %s'.format([rules.gte]) : ''"
      },
      (priv.field).cel = {
        id: "float.gte_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gte && (this.isNan() || this >= rules.lt || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "float.gte_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gte && (this.isNan() || (rules.lt <= this && this < rules.gte))"
          "? 'value must be greater than or equal to %s or less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "float.gte_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gte && (this.isNan() || this > rules.lte || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "float.gte_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gte && (this.isNan() || (rules.lte < this && this < rules.gte))"
          "? 'value must be greater than or equal to %s or less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      }
    ];
  }

  // `in` requires the field value to be equal to one of the specified values.
  // If the field value isn't one of the specified values, an error message
  // is generated.
  //
  // ```proto
  // message MyFloat {
  //   // value must be in list [1.0, 2.0, 3.0]
  //   repeated float value = 1 (buf.validate.field).float = { in: [1.0, 2.0, 3.0] };
  // }
  // ```
  repeated float in = 6 [(priv.field).cel = {
    id: "float.in"
    expression: "!(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `in` requires the field value to not be equal to any of the specified
  // values. If the field value is one of the specified values, an error
  // message is generated.
  //
  // ```proto
  // message MyFloat {
  //   // value must not be in list [1.0, 2.0, 3.0]
  //   repeated float value = 1 (buf.validate.field).float = { not_in: [1.0, 2.0, 3.0] };
  // }
  // ```
  repeated float not_in = 7 [(priv.field).cel = {
    id: "float.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];

  // `finite` requires the field value to be finite. If the field value is
  // infinite or NaN, an error message is generated.
  bool finite = 8 [(priv.field).cel = {
    id: "float.finite"
    expression: "this.isNan() || this.isInf() ? 'value must be finite' : ''"
  }];
}

// DoubleRules describes the constraints applied to `double` values. These
// rules may also be applied to the `google.protobuf.DoubleValue` Well-Known-Type.
message DoubleRules {
  // `const` requires the field value to exactly match the specified value. If
  // the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MyDouble {
  //   // value must equal 42.0
  //   double value = 1 [(buf.validate.field).double.const = 42.0];
  // }
  // ```
  optional double const = 1 [(priv.field).cel = {
    id: "double.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];
  oneof less_than {
    // `lt` requires the field value to be less than the specified value (field <
    // value). If the field value is equal to or greater than the specified
    // value, an error message is generated.
    //
    // ```proto
    // message MyDouble {
    //   // value must be less than 10.0
    //   double value = 1 [(buf.validate.field).double.lt = 10.0];
    // }
    // ```
    double lt = 2 [(priv.field).cel = {
      id: "double.lt"
      expression:
        "!has(rules.gte) && !has(rules.gt) && (this.isNan() || this >= rules.lt)"
        "? 'value must be less than %s'.format([rules.lt]) : ''"
    }];

    // `lte` requires the field value to be less than or equal to the specified value
    // (field <= value). If the field value is greater than the specified value,
    // an error message is generated.
    //
    // ```proto
    // message MyDouble {
    //   // value must be less than or equal to 10.0
    //   double value = 1 [(buf.validate.field).double.lte = 10.0];
    // }
    // ```
    double lte = 3 [(priv.field).cel = {
      id: "double.lte"
      expression:
        "!has(rules.gte) && !has(rules.gt) && (this.isNan() || this > rules.lte)"
        "? 'value must be less than or equal to %s'.format([rules.lte]) : ''"
    }];
  }
  oneof greater_than {
    // `gt` requires the field value to be greater than the specified value
    // (exclusive). If the value of `gt` is larger than a specified `lt` or `lte`,
    // the range is reversed, and the field value must be outside the specified
    // range. If the field value doesn't meet the required conditions, an error
    // message is generated.
    //
    // ```proto
    // message MyDouble {
    //   // value must be greater than 5.0 [double.gt]
    //   double value = 1 [(buf.validate.field).double.gt = 5.0];
    //
    //   // value must be greater than 5 and less than 10.0 [double.gt_lt]
    //   double other_value = 2 [(buf.validate.field).double = { gt: 5.0, lt: 10.0 }];
    //
    //   // value must be greater than 10 or less than 5.0 [double.gt_lt_exclusive]
    //   double another_value = 3 [(buf.validate.field).double = { gt: 10.0, lt: 5.0 }];
    // }
    // ```
    double gt = 4 [
      (priv.field).cel = {
        id: "double.gt"
        expression:
          "!has(rules.lt) && !has(rules.lte) && (this.isNan() || this <= rules.gt)"
          "? 'value must be greater than %s'.format([rules.gt]) : ''"
      },
      (priv.field).cel = {
        id: "double.gt_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gt && (this.isNan() || this >= rules.lt || this <= rules.gt)"
          "? 'value must be greater than %s and less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "double.gt_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gt && (this.isNan() || (rules.lt <= this && this <= rules.gt))"
          "? 'value must be greater than %s or less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "double.gt_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gt && (this.isNan() || this > rules.lte || this <= rules.gt)"
          "? 'value must be greater than %s and less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "double.gt_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gt && (this.isNan() || (rules.lte < this && this <= rules.gt))"
          "? 'value must be greater than %s or less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      }
    ];

    // `gte` requires the field value to be greater than or equal to the specified
    // value (exclusive). If the value of `gte` is larger than a specified `lt` or
    // `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyDouble {
    //   // value must be greater than or equal to 5.0 [double.gte]
    //   double value = 1 [(buf.validate.field).double.gte = 5.0];
    //
    //   // value must be greater than or equal to 5.0 and less than 10.0 [double.gte_lt]
    //   double other_value = 2 [(buf.validate.field).double = { gte: 5.0, lt: 10.0 }];
    //
    //   // value must be greater than or equal to 10.0 or less than 5.0 [double.gte_lt_exclusive]
    //   double another_value = 3 [(buf.validate.field).double = { gte: 10.0, lt: 5.0 }];
    // }
    // ```
    double gte = 5 [
      (priv.field).cel = {
        id: "double.gte"
        expression:
          "!has(rules.lt) && !has(rules.lte) && (this.isNan() || this < rules.gte)"
          "? 'value must be greater than or equal to %s'.format([rules.gte]) : ''"
      },
      (priv.field).cel = {
        id: "double.gte_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gte && (this.isNan() || this >= rules.lt || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "double.gte_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gte && (this.isNan() || (rules.lt <= this && this < rules.gte))"
          "? 'value must be greater than or equal to %s or less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "double.gte_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gte && (this.isNan() || this > rules.lte || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "double.gte_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gte && (this.isNan() || (rules.lte < this && this < rules.gte))"
          "? 'value must be greater than or equal to %s or less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      }
    ];
  }
  // `in` requires the field value to be equal to one of the specified values.
  // If the field value isn't one of the specified values, an error message is
  // generated.
  //
  // ```proto
  // message MyDouble {
  //   // value must be in list [1.0, 2.0, 3.0]
  //   repeated double value = 1 (buf.validate.field).double = { in: [1.0, 2.0, 3.0] };
  // }
  // ```
  repeated double in = 6 [(priv.field).cel = {
    id: "double.in"
    expression: "!(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `not_in` requires the field value to not be equal to any of the specified
  // values. If the field value is one of the specified values, an error
  // message is generated.
  //
  // ```proto
  // message MyDouble {
  //   // value must not be in list [1.0, 2.0, 3.0]
  //   repeated double value = 1 (buf.validate.field).double = { not_in: [1.0, 2.0, 3.0] };
  // }
  // ```
  repeated double not_in = 7 [(priv.field).cel = {
    id: "double.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];

  // `finite` requires the field value to be finite. If the field value is
  // infinite or NaN, an error message is generated.
  bool finite = 8 [(priv.field).cel = {
    id: "double.finite"
    expression: "this.isNan() || this.isInf() ? 'value must be finite' : ''"
  }];
}

// Int32Rules describes the constraints applied to `int32` values. These
// rules may also be applied to the `google.protobuf.Int32Value` Well-Known-Type.
message Int32Rules {
  // `const` requires the field value to exactly match the specified value. If
  // the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MyInt32 {
  //   // value must equal 42
  //   int32 value = 1 [(buf.validate.field).int32.const = 42];
  // }
  // ```
  optional int32 const = 1 [(priv.field).cel = {
    id: "int32.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];
  oneof less_than {
    // `lt` requires the field value to be less than the specified value (field
    // < value). If the field value is equal to or greater than the specified
    // value, an error message is generated.
    //
    // ```proto
    // message MyInt32 {
    //   // value must be less than 10
    //   int32 value = 1 [(buf.validate.field).int32.lt = 10];
    // }
    // ```
    int32 lt = 2 [(priv.field).cel = {
      id: "int32.lt"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this >= rules.lt"
        "? 'value must be less than %s'.format([rules.lt]) : ''"
    }];

    // `lte` requires the field value to be less than or equal to the specified
    // value (field <= value). If the field value is greater than the specified
    // value, an error message is generated.
    //
    // ```proto
    // message MyInt32 {
    //   // value must be less than or equal to 10
    //   int32 value = 1 [(buf.validate.field).int32.lte = 10];
    // }
    // ```
    int32 lte = 3 [(priv.field).cel = {
      id: "int32.lte"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this > rules.lte"
        "? 'value must be less than or equal to %s'.format([rules.lte]) : ''"
    }];
  }
  oneof greater_than {
    // `gt` requires the field value to be greater than the specified value
    // (exclusive). If the value of `gt` is larger than a specified `lt` or
    // `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyInt32 {
    //   // value must be greater than 5 [int32.gt]
    //   int32 value = 1 [(buf.validate.field).int32.gt = 5];
    //
    //   // value must be greater than 5 and less than 10 [int32.gt_lt]
    //   int32 other_value = 2 [(buf.validate.field).int32 = { gt: 5, lt: 10 }];
    //
    //   // value must be greater than 10 or less than 5 [int32.gt_lt_exclusive]
    //   int32 another_value = 3 [(buf.validate.field).int32 = { gt: 10, lt: 5 }];
    // }
    // ```
    int32 gt = 4 [
      (priv.field).cel = {
        id: "int32.gt"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this <= rules.gt"
          "? 'value must be greater than %s'.format([rules.gt]) : ''"
      },
      (priv.field).cel = {
        id: "int32.gt_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)"
          "? 'value must be greater than %s and less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "int32.gt_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)"
          "? 'value must be greater than %s or less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "int32.gt_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)"
          "? 'value must be greater than %s and less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "int32.gt_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)"
          "? 'value must be greater than %s or less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      }
    ];

    // `gte` requires the field value to be greater than or equal to the specified value
    // (exclusive). If the value of `gte` is larger than a specified `lt` or
    // `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyInt32 {
    //   // value must be greater than or equal to 5 [int32.gte]
    //   int32 value = 1 [(buf.validate.field).int32.gte = 5];
    //
    //   // value must be greater than or equal to 5 and less than 10 [int32.gte_lt]
    //   int32 other_value = 2 [(buf.validate.field).int32 = { gte: 5, lt: 10 }];
    //
    //   // value must be greater than or equal to 10 or less than 5 [int32.gte_lt_exclusive]
    //   int32 another_value = 3 [(buf.validate.field).int32 = { gte: 10, lt: 5 }];
    // }
    // ```
    int32 gte = 5 [
      (priv.field).cel = {
        id: "int32.gte"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this < rules.gte"
          "? 'value must be greater than or equal to %s'.format([rules.gte]) : ''"
      },
      (priv.field).cel = {
        id: "int32.gte_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "int32.gte_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "int32.gte_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "int32.gte_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      }
    ];
  }

  // `in` requires the field value to be equal to one of the specified values.
  // If the field value isn't one of the specified values, an error message is
  // generated.
  //
  // ```proto
  // message MyInt32 {
  //   // value must be in list [1, 2, 3]
  //   repeated int32 value = 1 (buf.validate.field).int32 = { in: [1, 2, 3] };
  // }
  // ```
  repeated int32 in = 6 [(priv.field).cel = {
    id: "int32.in"
    expression: "!(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `not_in` requires the field value to not be equal to any of the specified
  // values. If the field value is one of the specified values, an error message
  // is generated.
  //
  // ```proto
  // message MyInt32 {
  //   // value must not be in list [1, 2, 3]
  //   repeated int32 value = 1 (buf.validate.field).int32 = { not_in: [1, 2, 3] };
  // }
  // ```
  repeated int32 not_in = 7 [(priv.field).cel = {
    id: "int32.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];
}

// Int64Rules describes the constraints applied to `int64` values. These
// rules may also be applied to the `google.protobuf.Int64Value` Well-Known-Type.
message Int64Rules {
  // `const` requires the field value to exactly match the specified value. If
  // the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MyInt64 {
  //   // value must equal 42
  //   int64 value = 1 [(buf.validate.field).int64.const = 42];
  // }
  // ```
  optional int64 const = 1 [(priv.field).cel = {
    id: "int64.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];
  oneof less_than {
    // `lt` requires the field value to be less than the specified value (field <
    // value). If the field value is equal to or greater than the specified value,
    // an error message is generated.
    //
    // ```proto
    // message MyInt64 {
    //   // value must be less than 10
    //   int64 value = 1 [(buf.validate.field).int64.lt = 10];
    // }
    // ```
    int64 lt = 2 [(priv.field).cel = {
      id: "int64.lt"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this >= rules.lt"
        "? 'value must be less than %s'.format([rules.lt]) : ''"
    }];

    // `lte` requires the field value to be less than or equal to the specified
    // value (field <= value). If the field value is greater than the specified
    // value, an error message is generated.
    //
    // ```proto
    // message MyInt64 {
    //   // value must be less than or equal to 10
    //   int64 value = 1 [(buf.validate.field).int64.lte = 10];
    // }
    // ```
    int64 lte = 3 [(priv.field).cel = {
      id: "int64.lte"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this > rules.lte"
        "? 'value must be less than or equal to %s'.format([rules.lte]) : ''"
    }];
  }
  oneof greater_than {
    // `gt` requires the field value to be greater than the specified value
    // (exclusive). If the value of `gt` is larger than a specified `lt` or
    // `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyInt64 {
    //   // value must be greater than 5 [int64.gt]
    //   int64 value = 1 [(buf.validate.field).int64.gt = 5];
    //
    //   // value must be greater than 5 and less than 10 [int64.gt_lt]
    //   int64 other_value = 2 [(buf.validate.field).int64 = { gt: 5, lt: 10 }];
    //
    //   // value must be greater than 10 or less than 5 [int64.gt_lt_exclusive]
    //   int64 another_value = 3 [(buf.validate.field).int64 = { gt: 10, lt: 5 }];
    // }
    // ```
    int64 gt = 4 [
      (priv.field).cel = {
        id: "int64.gt"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this <= rules.gt"
          "? 'value must be greater than %s'.format([rules.gt]) : ''"
      },
      (priv.field).cel = {
        id: "int64.gt_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)"
          "? 'value must be greater than %s and less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "int64.gt_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)"
          "? 'value must be greater than %s or less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "int64.gt_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)"
          "? 'value must be greater than %s and less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "int64.gt_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)"
          "? 'value must be greater than %s or less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      }
    ];

    // `gte` requires the field value to be greater than or equal to the specified
    // value (exclusive). If the value of `gte` is larger than a specified `lt`
    // or `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyInt64 {
    //   // value must be greater than or equal to 5 [int64.gte]
    //   int64 value = 1 [(buf.validate.field).int64.gte = 5];
    //
    //   // value must be greater than or equal to 5 and less than 10 [int64.gte_lt]
    //   int64 other_value = 2 [(buf.validate.field).int64 = { gte: 5, lt: 10 }];
    //
    //   // value must be greater than or equal to 10 or less than 5 [int64.gte_lt_exclusive]
    //   int64 another_value = 3 [(buf.validate.field).int64 = { gte: 10, lt: 5 }];
    // }
    // ```
    int64 gte = 5 [
      (priv.field).cel = {
        id: "int64.gte"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this < rules.gte"
          "? 'value must be greater than or equal to %s'.format([rules.gte]) : ''"
      },
      (priv.field).cel = {
        id: "int64.gte_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "int64.gte_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "int64.gte_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "int64.gte_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      }
    ];
  }

  // `in` requires the field value to be equal to one of the specified values.
  // If the field value isn't one of the specified values, an error message is
  // generated.
  //
  // ```proto
  // message MyInt64 {
  //   // value must be in list [1, 2, 3]
  //   repeated int64 value = 1 (buf.validate.field).int64 = { in: [1, 2, 3] };
  // }
  // ```
  repeated int64 in = 6 [(priv.field).cel = {
    id: "int64.in"
    expression: "!(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `not_in` requires the field value to not be equal to any of the specified
  // values. If the field value is one of the specified values, an error
  // message is generated.
  //
  // ```proto
  // message MyInt64 {
  //   // value must not be in list [1, 2, 3]
  //   repeated int64 value = 1 (buf.validate.field).int64 = { not_in: [1, 2, 3] };
  // }
  // ```
  repeated int64 not_in = 7 [(priv.field).cel = {
    id: "int64.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];
}

// UInt32Rules describes the constraints applied to `uint32` values. These
// rules may also be applied to the `google.protobuf.UInt32Value` Well-Known-Type.
message UInt32Rules {
  // `const` requires the field value to exactly match the specified value. If
  // the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MyUInt32 {
  //   // value must equal 42
  //   uint32 value = 1 [(buf.validate.field).uint32.const = 42];
  // }
  // ```
  optional uint32 const = 1 [(priv.field).cel = {
    id: "uint32.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];
  oneof less_than {
    // `lt` requires the field value to be less than the specified value (field <
    // value). If the field value is equal to or greater than the specified value,
    // an error message is generated.
    //
    // ```proto
    // message MyUInt32 {
    //   // value must be less than 10
    //   uint32 value = 1 [(buf.validate.field).uint32.lt = 10];
    // }
    // ```
    uint32 lt = 2 [(priv.field).cel = {
      id: "uint32.lt"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this >= rules.lt"
        "? 'value must be less than %s'.format([rules.lt]) : ''"
    }];

    // `lte` requires the field value to be less than or equal to the specified
    // value (field <= value). If the field value is greater than the specified
    // value, an error message is generated.
    //
    // ```proto
    // message MyUInt32 {
    //   // value must be less than or equal to 10
    //   uint32 value = 1 [(buf.validate.field).uint32.lte = 10];
    // }
    // ```
    uint32 lte = 3 [(priv.field).cel = {
      id: "uint32.lte"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this > rules.lte"
        "? 'value must be less than or equal to %s'.format([rules.lte]) : ''"
    }];
  }
  oneof greater_than {
    // `gt` requires the field value to be greater than the specified value
    // (exclusive). If the value of `gt` is larger than a specified `lt` or
    // `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyUInt32 {
    //   // value must be greater than 5 [uint32.gt]
    //   uint32 value = 1 [(buf.validate.field).uint32.gt = 5];
    //
    //   // value must be greater than 5 and less than 10 [uint32.gt_lt]
    //   uint32 other_value = 2 [(buf.validate.field).uint32 = { gt: 5, lt: 10 }];
    //
    //   // value must be greater than 10 or less than 5 [uint32.gt_lt_exclusive]
    //   uint32 another_value = 3 [(buf.validate.field).uint32 = { gt: 10, lt: 5 }];
    // }
    // ```
    uint32 gt = 4 [
      (priv.field).cel = {
        id: "uint32.gt"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this <= rules.gt"
          "? 'value must be greater than %s'.format([rules.gt]) : ''"
      },
      (priv.field).cel = {
        id: "uint32.gt_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)"
          "? 'value must be greater than %s and less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "uint32.gt_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)"
          "? 'value must be greater than %s or less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "uint32.gt_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)"
          "? 'value must be greater than %s and less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "uint32.gt_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)"
          "? 'value must be greater than %s or less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      }
    ];

    // `gte` requires the field value to be greater than or equal to the specified
    // value (exclusive). If the value of `gte` is larger than a specified `lt`
    // or `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyUInt32 {
    //   // value must be greater than or equal to 5 [uint32.gte]
    //   uint32 value = 1 [(buf.validate.field).uint32.gte = 5];
    //
    //   // value must be greater than or equal to 5 and less than 10 [uint32.gte_lt]
    //   uint32 other_value = 2 [(buf.validate.field).uint32 = { gte: 5, lt: 10 }];
    //
    //   // value must be greater than or equal to 10 or less than 5 [uint32.gte_lt_exclusive]
    //   uint32 another_value = 3 [(buf.validate.field).uint32 = { gte: 10, lt: 5 }];
    // }
    // ```
    uint32 gte = 5 [
      (priv.field).cel = {
        id: "uint32.gte"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this < rules.gte"
          "? 'value must be greater than or equal to %s'.format([rules.gte]) : ''"
      },
      (priv.field).cel = {
        id: "uint32.gte_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "uint32.gte_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "uint32.gte_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "uint32.gte_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      }
    ];
  }

  // `in` requires the field value to be equal to one of the specified values.
  // If the field value isn't one of the specified values, an error message is
  // generated.
  //
  // ```proto
  // message MyUInt32 {
  //   // value must be in list [1, 2, 3]
  //   repeated uint32 value = 1 (buf.validate.field).uint32 = { in: [1, 2, 3] };
  // }
  // ```
  repeated uint32 in = 6 [(priv.field).cel = {
    id: "uint32.in"
    expression: "!(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `not_in` requires the field value to not be equal to any of the specified
  // values. If the field value is one of the specified values, an error
  // message is generated.
  //
  // ```proto
  // message MyUInt32 {
  //   // value must not be in list [1, 2, 3]
  //   repeated uint32 value = 1 (buf.validate.field).uint32 = { not_in: [1, 2, 3] };
  // }
  // ```
  repeated uint32 not_in = 7 [(priv.field).cel = {
    id: "uint32.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];
}

// UInt64Rules describes the constraints applied to `uint64` values. These
// rules may also be applied to the `google.protobuf.UInt64Value` Well-Known-Type.
message UInt64Rules {
  // `const` requires the field value to exactly match the specified value. If
  // the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MyUInt64 {
  //   // value must equal 42
  //   uint64 value = 1 [(buf.validate.field).uint64.const = 42];
  // }
  // ```
  optional uint64 const = 1 [(priv.field).cel = {
    id: "uint64.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];
  oneof less_than {
    // `lt` requires the field value to be less than the specified value (field <
    // value). If the field value is equal to or greater than the specified value,
    // an error message is generated.
    //
    // ```proto
    // message MyUInt64 {
    //   // value must be less than 10
    //   uint64 value = 1 [(buf.validate.field).uint64.lt = 10];
    // }
    // ```
    uint64 lt = 2 [(priv.field).cel = {
      id: "uint64.lt"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this >= rules.lt"
        "? 'value must be less than %s'.format([rules.lt]) : ''"
    }];

    // `lte` requires the field value to be less than or equal to the specified
    // value (field <= value). If the field value is greater than the specified
    // value, an error message is generated.
    //
    // ```proto
    // message MyUInt64 {
    //   // value must be less than or equal to 10
    //   uint64 value = 1 [(buf.validate.field).uint64.lte = 10];
    // }
    // ```
    uint64 lte = 3 [(priv.field).cel = {
      id: "uint64.lte"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this > rules.lte"
        "? 'value must be less than or equal to %s'.format([rules.lte]) : ''"
    }];
  }
  oneof greater_than {
    // `gt` requires the field value to be greater than the specified value
    // (exclusive). If the value of `gt` is larger than a specified `lt` or
    // `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyUInt64 {
    //   // value must be greater than 5 [uint64.gt]
    //   uint64 value = 1 [(buf.validate.field).uint64.gt = 5];
    //
    //   // value must be greater than 5 and less than 10 [uint64.gt_lt]
    //   uint64 other_value = 2 [(buf.validate.field).uint64 = { gt: 5, lt: 10 }];
    //
    //   // value must be greater than 10 or less than 5 [uint64.gt_lt_exclusive]
    //   uint64 another_value = 3 [(buf.validate.field).uint64 = { gt: 10, lt: 5 }];
    // }
    // ```
    uint64 gt = 4 [
      (priv.field).cel = {
        id: "uint64.gt"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this <= rules.gt"
          "? 'value must be greater than %s'.format([rules.gt]) : ''"
      },
      (priv.field).cel = {
        id: "uint64.gt_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)"
          "? 'value must be greater than %s and less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "uint64.gt_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)"
          "? 'value must be greater than %s or less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "uint64.gt_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)"
          "? 'value must be greater than %s and less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "uint64.gt_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)"
          "? 'value must be greater than %s or less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      }
    ];

    // `gte` requires the field value to be greater than or equal to the specified
    // value (exclusive). If the value of `gte` is larger than a specified `lt`
    // or `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyUInt64 {
    //   // value must be greater than or equal to 5 [uint64.gte]
    //   uint64 value = 1 [(buf.validate.field).uint64.gte = 5];
    //
    //   // value must be greater than or equal to 5 and less than 10 [uint64.gte_lt]
    //   uint64 other_value = 2 [(buf.validate.field).uint64 = { gte: 5, lt: 10 }];
    //
    //   // value must be greater than or equal to 10 or less than 5 [uint64.gte_lt_exclusive]
    //   uint64 another_value = 3 [(buf.validate.field).uint64 = { gte: 10, lt: 5 }];
    // }
    // ```
    uint64 gte = 5 [
      (priv.field).cel = {
        id: "uint64.gte"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this < rules.gte"
          "? 'value must be greater than or equal to %s'.format([rules.gte]) : ''"
      },
      (priv.field).cel = {
        id: "uint64.gte_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "uint64.gte_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "uint64.gte_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "uint64.gte_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      }
    ];
  }
  // `in` requires the field value to be equal to one of the specified values.
  // If the field value isn't one of the specified values, an error message is
  // generated.
  //
  // ```proto
  // message MyUInt64 {
  //   // value must be in list [1, 2, 3]
  //   repeated uint64 value = 1 (buf.validate.field).uint64 = { in: [1, 2, 3] };
  // }
  // ```
  repeated uint64 in = 6 [(priv.field).cel = {
    id: "uint64.in"
    expression: "!(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `not_in` requires the field value to not be equal to any of the specified
  // values. If the field value is one of the specified values, an error
  // message is generated.
  //
  // ```proto
  // message MyUInt64 {
  //   // value must not be in list [1, 2, 3]
  //   repeated uint64 value = 1 (buf.validate.field).uint64 = { not_in: [1, 2, 3] };
  // }
  // ```
  repeated uint64 not_in = 7 [(priv.field).cel = {
    id: "uint64.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];
}

// SInt32Rules describes the constraints applied to `sint32` values.
message SInt32Rules {
  // `const` requires the field value to exactly match the specified value. If
  // the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MySInt32 {
  //   // value must equal 42
  //   sint32 value = 1 [(buf.validate.field).sint32.const = 42];
  // }
  // ```
  optional sint32 const = 1 [(priv.field).cel = {
    id: "sint32.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];
  oneof less_than {
    // `lt` requires the field value to be less than the specified value (field
    // < value). If the field value is equal to or greater than the specified
    // value, an error message is generated.
    //
    // ```proto
    // message MySInt32 {
    //   // value must be less than 10
    //   sint32 value = 1 [(buf.validate.field).sint32.lt = 10];
    // }
    // ```
    sint32 lt = 2 [(priv.field).cel = {
      id: "sint32.lt"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this >= rules.lt"
        "? 'value must be less than %s'.format([rules.lt]) : ''"
    }];

    // `lte` requires the field value to be less than or equal to the specified
    // value (field <= value). If the field value is greater than the specified
    // value, an error message is generated.
    //
    // ```proto
    // message MySInt32 {
    //   // value must be less than or equal to 10
    //   sint32 value = 1 [(buf.validate.field).sint32.lte = 10];
    // }
    // ```
    sint32 lte = 3 [(priv.field).cel = {
      id: "sint32.lte"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this > rules.lte"
        "? 'value must be less than or equal to %s'.format([rules.lte]) : ''"
    }];
  }
  oneof greater_than {
    // `gt` requires the field value to be greater than the specified value
    // (exclusive). If the value of `gt` is larger than a specified `lt` or
    // `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MySInt32 {
    //   // value must be greater than 5 [sint32.gt]
    //   sint32 value = 1 [(buf.validate.field).sint32.gt = 5];
    //
    //   // value must be greater than 5 and less than 10 [sint32.gt_lt]
    //   sint32 other_value = 2 [(buf.validate.field).sint32 = { gt: 5, lt: 10 }];
    //
    //   // value must be greater than 10 or less than 5 [sint32.gt_lt_exclusive]
    //   sint32 another_value = 3 [(buf.validate.field).sint32 = { gt: 10, lt: 5 }];
    // }
    // ```
    sint32 gt = 4 [
      (priv.field).cel = {
        id: "sint32.gt"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this <= rules.gt"
          "? 'value must be greater than %s'.format([rules.gt]) : ''"
      },
      (priv.field).cel = {
        id: "sint32.gt_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)"
          "? 'value must be greater than %s and less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sint32.gt_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)"
          "? 'value must be greater than %s or less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sint32.gt_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)"
          "? 'value must be greater than %s and less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "sint32.gt_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)"
          "? 'value must be greater than %s or less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      }
    ];

    // `gte` requires the field value to be greater than or equal to the specified
    // value (exclusive). If the value of `gte` is larger than a specified `lt`
    // or `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MySInt32 {
    //  // value must be greater than or equal to 5 [sint32.gte]
    //  sint32 value = 1 [(buf.validate.field).sint32.gte = 5];
    //
    //  // value must be greater than or equal to 5 and less than 10 [sint32.gte_lt]
    //  sint32 other_value = 2 [(buf.validate.field).sint32 = { gte: 5, lt: 10 }];
    //
    //  // value must be greater than or equal to 10 or less than 5 [sint32.gte_lt_exclusive]
    //  sint32 another_value = 3 [(buf.validate.field).sint32 = { gte: 10, lt: 5 }];
    // }
    // ```
    sint32 gte = 5 [
      (priv.field).cel = {
        id: "sint32.gte"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this < rules.gte"
          "? 'value must be greater than or equal to %s'.format([rules.gte]) : ''"
      },
      (priv.field).cel = {
        id: "sint32.gte_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sint32.gte_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sint32.gte_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "sint32.gte_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      }
    ];
  }

  // `in` requires the field value to be equal to one of the specified values.
  // If the field value isn't one of the specified values, an error message is
  // generated.
  //
  // ```proto
  // message MySInt32 {
  //   // value must be in list [1, 2, 3]
  //   repeated sint32 value = 1 (buf.validate.field).sint32 = { in: [1, 2, 3] };
  // }
  // ```
  repeated sint32 in = 6 [(priv.field).cel = {
    id: "sint32.in"
    expression: "!(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `not_in` requires the field value to not be equal to any of the specified
  // values. If the field value is one of the specified values, an error
  // message is generated.
  //
  // ```proto
  // message MySInt32 {
  //   // value must not be in list [1, 2, 3]
  //   repeated sint32 value = 1 (buf.validate.field).sint32 = { not_in: [1, 2, 3] };
  // }
  // ```
  repeated sint32 not_in = 7 [(priv.field).cel = {
    id: "sint32.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];
}

// SInt64Rules describes the constraints applied to `sint64` values.
message SInt64Rules {
  // `const` requires the field value to exactly match the specified value. If
  // the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MySInt64 {
  //   // value must equal 42
  //   sint64 value = 1 [(buf.validate.field).sint64.const = 42];
  // }
  // ```
  optional sint64 const = 1 [(priv.field).cel = {
    id: "sint64.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];
  oneof less_than {
    // `lt` requires the field value to be less than the specified value (field
    // < value). If the field value is equal to or greater than the specified
    // value, an error message is generated.
    //
    // ```proto
    // message MySInt64 {
    //   // value must be less than 10
    //   sint64 value = 1 [(buf.validate.field).sint64.lt = 10];
    // }
    // ```
    sint64 lt = 2 [(priv.field).cel = {
      id: "sint64.lt"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this >= rules.lt"
        "? 'value must be less than %s'.format([rules.lt]) : ''"
    }];

    // `lte` requires the field value to be less than or equal to the specified
    // value (field <= value). If the field value is greater than the specified
    // value, an error message is generated.
    //
    // ```proto
    // message MySInt64 {
    //   // value must be less than or equal to 10
    //   sint64 value = 1 [(buf.validate.field).sint64.lte = 10];
    // }
    // ```
    sint64 lte = 3 [(priv.field).cel = {
      id: "sint64.lte"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this > rules.lte"
        "? 'value must be less than or equal to %s'.format([rules.lte]) : ''"
    }];
  }
  oneof greater_than {
    // `gt` requires the field value to be greater than the specified value
    // (exclusive). If the value of `gt` is larger than a specified `lt` or
    // `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MySInt64 {
    //   // value must be greater than 5 [sint64.gt]
    //   sint64 value = 1 [(buf.validate.field).sint64.gt = 5];
    //
    //   // value must be greater than 5 and less than 10 [sint64.gt_lt]
    //   sint64 other_value = 2 [(buf.validate.field).sint64 = { gt: 5, lt: 10 }];
    //
    //   // value must be greater than 10 or less than 5 [sint64.gt_lt_exclusive]
    //   sint64 another_value = 3 [(buf.validate.field).sint64 = { gt: 10, lt: 5 }];
    // }
    // ```
    sint64 gt = 4 [
      (priv.field).cel = {
        id: "sint64.gt"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this <= rules.gt"
          "? 'value must be greater than %s'.format([rules.gt]) : ''"
      },
      (priv.field).cel = {
        id: "sint64.gt_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)"
          "? 'value must be greater than %s and less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sint64.gt_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)"
          "? 'value must be greater than %s or less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sint64.gt_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)"
          "? 'value must be greater than %s and less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "sint64.gt_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)"
          "? 'value must be greater than %s or less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      }
    ];

    // `gte` requires the field value to be greater than or equal to the specified
    // value (exclusive). If the value of `gte` is larger than a specified `lt`
    // or `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MySInt64 {
    //   // value must be greater than or equal to 5 [sint64.gte]
    //   sint64 value = 1 [(buf.validate.field).sint64.gte = 5];
    //
    //   // value must be greater than or equal to 5 and less than 10 [sint64.gte_lt]
    //   sint64 other_value = 2 [(buf.validate.field).sint64 = { gte: 5, lt: 10 }];
    //
    //   // value must be greater than or equal to 10 or less than 5 [sint64.gte_lt_exclusive]
    //   sint64 another_value = 3 [(buf.validate.field).sint64 = { gte: 10, lt: 5 }];
    // }
    // ```
    sint64 gte = 5 [
      (priv.field).cel = {
        id: "sint64.gte"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this < rules.gte"
          "? 'value must be greater than or equal to %s'.format([rules.gte]) : ''"
      },
      (priv.field).cel = {
        id: "sint64.gte_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sint64.gte_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sint64.gte_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "sint64.gte_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      }
    ];
  }

  // `in` requires the field value to be equal to one of the specified values.
  // If the field value isn't one of the specified values, an error message
  // is generated.
  //
  // ```proto
  // message MySInt64 {
  //   // value must be in list [1, 2, 3]
  //   repeated sint64 value = 1 (buf.validate.field).sint64 = { in: [1, 2, 3] };
  // }
  // ```
  repeated sint64 in = 6 [(priv.field).cel = {
    id: "sint64.in"
    expression: "!(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `not_in` requires the field value to not be equal to any of the specified
  // values. If the field value is one of the specified values, an error
  // message is generated.
  //
  // ```proto
  // message MySInt64 {
  //   // value must not be in list [1, 2, 3]
  //   repeated sint64 value = 1 (buf.validate.field).sint64 = { not_in: [1, 2, 3] };
  // }
  // ```
  repeated sint64 not_in = 7 [(priv.field).cel = {
    id: "sint64.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];
}

// Fixed32Rules describes the constraints applied to `fixed32` values.
message Fixed32Rules {
  // `const` requires the field value to exactly match the specified value.
  // If the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MyFixed32 {
  //   // value must equal 42
  //   fixed32 value = 1 [(buf.validate.field).fixed32.const = 42];
  // }
  // ```
  optional fixed32 const = 1 [(priv.field).cel = {
    id: "fixed32.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];
  oneof less_than {
    // `lt` requires the field value to be less than the specified value (field <
    // value). If the field value is equal to or greater than the specified value,
    // an error message is generated.
    //
    // ```proto
    // message MyFixed32 {
    //   // value must be less than 10
    //   fixed32 value = 1 [(buf.validate.field).fixed32.lt = 10];
    // }
    // ```
    fixed32 lt = 2 [(priv.field).cel = {
      id: "fixed32.lt"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this >= rules.lt"
        "? 'value must be less than %s'.format([rules.lt]) : ''"
    }];

    // `lte` requires the field value to be less than or equal to the specified
    // value (field <= value). If the field value is greater than the specified
    // value, an error message is generated.
    //
    // ```proto
    // message MyFixed32 {
    //   // value must be less than or equal to 10
    //   fixed32 value = 1 [(buf.validate.field).fixed32.lte = 10];
    // }
    // ```
    fixed32 lte = 3 [(priv.field).cel = {
      id: "fixed32.lte"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this > rules.lte"
        "? 'value must be less than or equal to %s'.format([rules.lte]) : ''"
    }];
  }
  oneof greater_than {
    // `gt` requires the field value to be greater than the specified value
    // (exclusive). If the value of `gt` is larger than a specified `lt` or
    // `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyFixed32 {
    //   // value must be greater than 5 [fixed32.gt]
    //   fixed32 value = 1 [(buf.validate.field).fixed32.gt = 5];
    //
    //   // value must be greater than 5 and less than 10 [fixed32.gt_lt]
    //   fixed32 other_value = 2 [(buf.validate.field).fixed32 = { gt: 5, lt: 10 }];
    //
    //   // value must be greater than 10 or less than 5 [fixed32.gt_lt_exclusive]
    //   fixed32 another_value = 3 [(buf.validate.field).fixed32 = { gt: 10, lt: 5 }];
    // }
    // ```
    fixed32 gt = 4 [
      (priv.field).cel = {
        id: "fixed32.gt"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this <= rules.gt"
          "? 'value must be greater than %s'.format([rules.gt]) : ''"
      },
      (priv.field).cel = {
        id: "fixed32.gt_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)"
          "? 'value must be greater than %s and less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "fixed32.gt_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)"
          "? 'value must be greater than %s or less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "fixed32.gt_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)"
          "? 'value must be greater than %s and less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "fixed32.gt_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)"
          "? 'value must be greater than %s or less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      }
    ];

    // `gte` requires the field value to be greater than or equal to the specified
    // value (exclusive). If the value of `gte` is larger than a specified `lt`
    // or `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyFixed32 {
    //   // value must be greater than or equal to 5 [fixed32.gte]
    //   fixed32 value = 1 [(buf.validate.field).fixed32.gte = 5];
    //
    //   // value must be greater than or equal to 5 and less than 10 [fixed32.gte_lt]
    //   fixed32 other_value = 2 [(buf.validate.field).fixed32 = { gte: 5, lt: 10 }];
    //
    //   // value must be greater than or equal to 10 or less than 5 [fixed32.gte_lt_exclusive]
    //   fixed32 another_value = 3 [(buf.validate.field).fixed32 = { gte: 10, lt: 5 }];
    // }
    // ```
    fixed32 gte = 5 [
      (priv.field).cel = {
        id: "fixed32.gte"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this < rules.gte"
          "? 'value must be greater than or equal to %s'.format([rules.gte]) : ''"
      },
      (priv.field).cel = {
        id: "fixed32.gte_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "fixed32.gte_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "fixed32.gte_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "fixed32.gte_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      }
    ];
  }

  // `in` requires the field value to be equal to one of the specified values.
  // If the field value isn't one of the specified values, an error message
  // is generated.
  //
  // ```proto
  // message MyFixed32 {
  //   // value must be in list [1, 2, 3]
  //   repeated fixed32 value = 1 (buf.validate.field).fixed32 = { in: [1, 2, 3] };
  // }
  // ```
  repeated fixed32 in = 6 [(priv.field).cel = {
    id: "fixed32.in"
    expression: "!(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `not_in` requires the field value to not be equal to any of the specified
  // values. If the field value is one of the specified values, an error
  // message is generated.
  //
  // ```proto
  // message MyFixed32 {
  //   // value must not be in list [1, 2, 3]
  //   repeated fixed32 value = 1 (buf.validate.field).fixed32 = { not_in: [1, 2, 3] };
  // }
  // ```
  repeated fixed32 not_in = 7 [(priv.field).cel = {
    id: "fixed32.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];
}

// Fixed64Rules describes the constraints applied to `fixed64` values.
message Fixed64Rules {
  // `const` requires the field value to exactly match the specified value. If
  // the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MyFixed64 {
  //   // value must equal 42
  //   fixed64 value = 1 [(buf.validate.field).fixed64.const = 42];
  // }
  // ```
  optional fixed64 const = 1 [(priv.field).cel = {
    id: "fixed64.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];
  oneof less_than {
    // `lt` requires the field value to be less than the specified value (field <
    // value). If the field value is equal to or greater than the specified value,
    // an error message is generated.
    //
    // ```proto
    // message MyFixed64 {
    //   // value must be less than 10
    //   fixed64 value = 1 [(buf.validate.field).fixed64.lt = 10];
    // }
    // ```
    fixed64 lt = 2 [(priv.field).cel = {
      id: "fixed64.lt"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this >= rules.lt"
        "? 'value must be less than %s'.format([rules.lt]) : ''"
    }];

    // `lte` requires the field value to be less than or equal to the specified
    // value (field <= value). If the field value is greater than the specified
    // value, an error message is generated.
    //
    // ```proto
    // message MyFixed64 {
    //   // value must be less than or equal to 10
    //   fixed64 value = 1 [(buf.validate.field).fixed64.lte = 10];
    // }
    // ```
    fixed64 lte = 3 [(priv.field).cel = {
      id: "fixed64.lte"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this > rules.lte"
        "? 'value must be less than or equal to %s'.format([rules.lte]) : ''"
    }];
  }
  oneof greater_than {
    // `gt` requires the field value to be greater than the specified value
    // (exclusive). If the value of `gt` is larger than a specified `lt` or
    // `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyFixed64 {
    //   // value must be greater than 5 [fixed64.gt]
    //   fixed64 value = 1 [(buf.validate.field).fixed64.gt = 5];
    //
    //   // value must be greater than 5 and less than 10 [fixed64.gt_lt]
    //   fixed64 other_value = 2 [(buf.validate.field).fixed64 = { gt: 5, lt: 10 }];
    //
    //   // value must be greater than 10 or less than 5 [fixed64.gt_lt_exclusive]
    //   fixed64 another_value = 3 [(buf.validate.field).fixed64 = { gt: 10, lt: 5 }];
    // }
    // ```
    fixed64 gt = 4 [
      (priv.field).cel = {
        id: "fixed64.gt"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this <= rules.gt"
          "? 'value must be greater than %s'.format([rules.gt]) : ''"
      },
      (priv.field).cel = {
        id: "fixed64.gt_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)"
          "? 'value must be greater than %s and less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "fixed64.gt_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)"
          "? 'value must be greater than %s or less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "fixed64.gt_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)"
          "? 'value must be greater than %s and less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "fixed64.gt_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)"
          "? 'value must be greater than %s or less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      }
    ];

    // `gte` requires the field value to be greater than or equal to the specified
    // value (exclusive). If the value of `gte` is larger than a specified `lt`
    // or `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyFixed64 {
    //   // value must be greater than or equal to 5 [fixed64.gte]
    //   fixed64 value = 1 [(buf.validate.field).fixed64.gte = 5];
    //
    //   // value must be greater than or equal to 5 and less than 10 [fixed64.gte_lt]
    //   fixed64 other_value = 2 [(buf.validate.field).fixed64 = { gte: 5, lt: 10 }];
    //
    //   // value must be greater than or equal to 10 or less than 5 [fixed64.gte_lt_exclusive]
    //   fixed64 another_value = 3 [(buf.validate.field).fixed64 = { gte: 10, lt: 5 }];
    // }
    // ```
    fixed64 gte = 5 [
      (priv.field).cel = {
        id: "fixed64.gte"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this < rules.gte"
          "? 'value must be greater than or equal to %s'.format([rules.gte]) : ''"
      },
      (priv.field).cel = {
        id: "fixed64.gte_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "fixed64.gte_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "fixed64.gte_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "fixed64.gte_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      }
    ];
  }

  // `in` requires the field value to be equal to one of the specified values.
  // If the field value isn't one of the specified values, an error message is
  // generated.
  //
  // ```proto
  // message MyFixed64 {
  //   // value must be in list [1, 2, 3]
  //   repeated fixed64 value = 1 (buf.validate.field).fixed64 = { in: [1, 2, 3] };
  // }
  // ```
  repeated fixed64 in = 6 [(priv.field).cel = {
    id: "fixed64.in"
    expression: "!(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `not_in` requires the field value to not be equal to any of the specified
  // values. If the field value is one of the specified values, an error
  // message is generated.
  //
  // ```proto
  // message MyFixed64 {
  //   // value must not be in list [1, 2, 3]
  //   repeated fixed64 value = 1 (buf.validate.field).fixed64 = { not_in: [1, 2, 3] };
  // }
  // ```
  repeated fixed64 not_in = 7 [(priv.field).cel = {
    id: "fixed64.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];
}

// SFixed32Rules describes the constraints applied to `fixed32` values.
message SFixed32Rules {
  // `const` requires the field value to exactly match the specified value. If
  // the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MySFixed32 {
  //   // value must equal 42
  //   sfixed32 value = 1 [(buf.validate.field).sfixed32.const = 42];
  // }
  // ```
  optional sfixed32 const = 1 [(priv.field).cel = {
    id: "sfixed32.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];
  oneof less_than {
    // `lt` requires the field value to be less than the specified value (field <
    // value). If the field value is equal to or greater than the specified value,
    // an error message is generated.
    //
    // ```proto
    // message MySFixed32 {
    //   // value must be less than 10
    //   sfixed32 value = 1 [(buf.validate.field).sfixed32.lt = 10];
    // }
    // ```
    sfixed32 lt = 2 [(priv.field).cel = {
      id: "sfixed32.lt"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this >= rules.lt"
        "? 'value must be less than %s'.format([rules.lt]) : ''"
    }];

    // `lte` requires the field value to be less than or equal to the specified
    // value (field <= value). If the field value is greater than the specified
    // value, an error message is generated.
    //
    // ```proto
    // message MySFixed32 {
    //   // value must be less than or equal to 10
    //   sfixed32 value = 1 [(buf.validate.field).sfixed32.lte = 10];
    // }
    // ```
    sfixed32 lte = 3 [(priv.field).cel = {
      id: "sfixed32.lte"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this > rules.lte"
        "? 'value must be less than or equal to %s'.format([rules.lte]) : ''"
    }];
  }
  oneof greater_than {
    // `gt` requires the field value to be greater than the specified value
    // (exclusive). If the value of `gt` is larger than a specified `lt` or
    // `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MySFixed32 {
    //   // value must be greater than 5 [sfixed32.gt]
    //   sfixed32 value = 1 [(buf.validate.field).sfixed32.gt = 5];
    //
    //   // value must be greater than 5 and less than 10 [sfixed32.gt_lt]
    //   sfixed32 other_value = 2 [(buf.validate.field).sfixed32 = { gt: 5, lt: 10 }];
    //
    //   // value must be greater than 10 or less than 5 [sfixed32.gt_lt_exclusive]
    //   sfixed32 another_value = 3 [(buf.validate.field).sfixed32 = { gt: 10, lt: 5 }];
    // }
    // ```
    sfixed32 gt = 4 [
      (priv.field).cel = {
        id: "sfixed32.gt"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this <= rules.gt"
          "? 'value must be greater than %s'.format([rules.gt]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed32.gt_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)"
          "? 'value must be greater than %s and less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed32.gt_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)"
          "? 'value must be greater than %s or less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed32.gt_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)"
          "? 'value must be greater than %s and less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed32.gt_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)"
          "? 'value must be greater than %s or less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      }
    ];

    // `gte` requires the field value to be greater than or equal to the specified
    // value (exclusive). If the value of `gte` is larger than a specified `lt`
    // or `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MySFixed32 {
    //   // value must be greater than or equal to 5 [sfixed32.gte]
    //   sfixed32 value = 1 [(buf.validate.field).sfixed32.gte = 5];
    //
    //   // value must be greater than or equal to 5 and less than 10 [sfixed32.gte_lt]
    //   sfixed32 other_value = 2 [(buf.validate.field).sfixed32 = { gte: 5, lt: 10 }];
    //
    //   // value must be greater than or equal to 10 or less than 5 [sfixed32.gte_lt_exclusive]
    //   sfixed32 another_value = 3 [(buf.validate.field).sfixed32 = { gte: 10, lt: 5 }];
    // }
    // ```
    sfixed32 gte = 5 [
      (priv.field).cel = {
        id: "sfixed32.gte"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this < rules.gte"
          "? 'value must be greater than or equal to %s'.format([rules.gte]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed32.gte_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed32.gte_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed32.gte_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed32.gte_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      }
    ];
  }

  // `in` requires the field value to be equal to one of the specified values.
  // If the field value isn't one of the specified values, an error message is
  // generated.
  //
  // ```proto
  // message MySFixed32 {
  //   // value must be in list [1, 2, 3]
  //   repeated sfixed32 value = 1 (buf.validate.field).sfixed32 = { in: [1, 2, 3] };
  // }
  // ```
  repeated sfixed32 in = 6 [(priv.field).cel = {
    id: "sfixed32.in"
    expression: "!(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `not_in` requires the field value to not be equal to any of the specified
  // values. If the field value is one of the specified values, an error
  // message is generated.
  //
  // ```proto
  // message MySFixed32 {
  //   // value must not be in list [1, 2, 3]
  //   repeated sfixed32 value = 1 (buf.validate.field).sfixed32 = { not_in: [1, 2, 3] };
  // }
  // ```
  repeated sfixed32 not_in = 7 [(priv.field).cel = {
    id: "sfixed32.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];
}

// SFixed64Rules describes the constraints applied to `fixed64` values.
message SFixed64Rules {
  // `const` requires the field value to exactly match the specified value. If
  // the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MySFixed64 {
  //   // value must equal 42
  //   sfixed64 value = 1 [(buf.validate.field).sfixed64.const = 42];
  // }
  // ```
  optional sfixed64 const = 1 [(priv.field).cel = {
    id: "sfixed64.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];
  oneof less_than {
    // `lt` requires the field value to be less than the specified value (field <
    // value). If the field value is equal to or greater than the specified value,
    // an error message is generated.
    //
    // ```proto
    // message MySFixed64 {
    //   // value must be less than 10
    //   sfixed64 value = 1 [(buf.validate.field).sfixed64.lt = 10];
    // }
    // ```
    sfixed64 lt = 2 [(priv.field).cel = {
      id: "sfixed64.lt"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this >= rules.lt"
        "? 'value must be less than %s'.format([rules.lt]) : ''"
    }];

    // `lte` requires the field value to be less than or equal to the specified
    // value (field <= value). If the field value is greater than the specified
    // value, an error message is generated.
    //
    // ```proto
    // message MySFixed64 {
    //   // value must be less than or equal to 10
    //   sfixed64 value = 1 [(buf.validate.field).sfixed64.lte = 10];
    // }
    // ```
    sfixed64 lte = 3 [(priv.field).cel = {
      id: "sfixed64.lte"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this > rules.lte"
        "? 'value must be less than or equal to %s'.format([rules.lte]) : ''"
    }];
  }
  oneof greater_than {
    // `gt` requires the field value to be greater than the specified value
    // (exclusive). If the value of `gt` is larger than a specified `lt` or
    // `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MySFixed64 {
    //   // value must be greater than 5 [sfixed64.gt]
    //   sfixed64 value = 1 [(buf.validate.field).sfixed64.gt = 5];
    //
    //   // value must be greater than 5 and less than 10 [sfixed64.gt_lt]
    //   sfixed64 other_value = 2 [(buf.validate.field).sfixed64 = { gt: 5, lt: 10 }];
    //
    //   // value must be greater than 10 or less than 5 [sfixed64.gt_lt_exclusive]
    //   sfixed64 another_value = 3 [(buf.validate.field).sfixed64 = { gt: 10, lt: 5 }];
    // }
    // ```
    sfixed64 gt = 4 [
      (priv.field).cel = {
        id: "sfixed64.gt"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this <= rules.gt"
          "? 'value must be greater than %s'.format([rules.gt]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed64.gt_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)"
          "? 'value must be greater than %s and less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed64.gt_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)"
          "? 'value must be greater than %s or less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed64.gt_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)"
          "? 'value must be greater than %s and less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed64.gt_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)"
          "? 'value must be greater than %s or less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      }
    ];

    // `gte` requires the field value to be greater than or equal to the specified
    // value (exclusive). If the value of `gte` is larger than a specified `lt`
    // or `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MySFixed64 {
    //   // value must be greater than or equal to 5 [sfixed64.gte]
    //   sfixed64 value = 1 [(buf.validate.field).sfixed64.gte = 5];
    //
    //   // value must be greater than or equal to 5 and less than 10 [sfixed64.gte_lt]
    //   sfixed64 other_value = 2 [(buf.validate.field).sfixed64 = { gte: 5, lt: 10 }];
    //
    //   // value must be greater than or equal to 10 or less than 5 [sfixed64.gte_lt_exclusive]
    //   sfixed64 another_value = 3 [(buf.validate.field).sfixed64 = { gte: 10, lt: 5 }];
    // }
    // ```
    sfixed64 gte = 5 [
      (priv.field).cel = {
        id: "sfixed64.gte"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this < rules.gte"
          "? 'value must be greater than or equal to %s'.format([rules.gte]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed64.gte_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed64.gte_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed64.gte_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "sfixed64.gte_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      }
    ];
  }

  // `in` requires the field value to be equal to one of the specified values.
  // If the field value isn't one of the specified values, an error message is
  // generated.
  //
  // ```proto
  // message MySFixed64 {
  //   // value must be in list [1, 2, 3]
  //   repeated sfixed64 value = 1 (buf.validate.field).sfixed64 = { in: [1, 2, 3] };
  // }
  // ```
  repeated sfixed64 in = 6 [(priv.field).cel = {
    id: "sfixed64.in"
    expression: "!(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `not_in` requires the field value to not be equal to any of the specified
  // values. If the field value is one of the specified values, an error
  // message is generated.
  //
  // ```proto
  // message MySFixed64 {
  //   // value must not be in list [1, 2, 3]
  //   repeated sfixed64 value = 1 (buf.validate.field).sfixed64 = { not_in: [1, 2, 3] };
  // }
  // ```
  repeated sfixed64 not_in = 7 [(priv.field).cel = {
    id: "sfixed64.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];
}

// BoolRules describes the constraints applied to `bool` values. These rules
// may also be applied to the `google.protobuf.BoolValue` Well-Known-Type.
message BoolRules {
  // `const` requires the field value to exactly match the specified boolean value.
  // If the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MyBool {
  //   // value must equal true
  //   bool value = 1 [(buf.validate.field).bool.const = true];
  // }
  // ```
  optional bool const = 1 [(priv.field).cel = {
    id: "bool.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];
}

// StringRules describes the constraints applied to `string` values These
// rules may also be applied to the `google.protobuf.StringValue` Well-Known-Type.
message StringRules {
  // `const` requires the field value to exactly match the specified value. If
  // the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MyString {
  //   // value must equal `hello`
  //   string value = 1 [(buf.validate.field).string.const = "hello"];
  // }
  // ```
  optional string const = 1 [(priv.field).cel = {
    id: "string.const"
    expression: "this != rules.const ? 'value must equal `%s`'.format([rules.const]) : ''"
  }];

  // `len` dictates that the field value must have the specified
  // number of characters (Unicode code points), which may differ from the number
  // of bytes in the string. If the field value does not meet the specified
  // length, an error message will be generated.
  //
  // ```proto
  // message MyString {
  //   // value length must be 5 characters
  //   string value = 1 [(buf.validate.field).string.len = 5];
  // }
  // ```
  optional uint64 len = 19 [(priv.field).cel = {
    id: "string.len"
    expression: "uint(this.size()) != rules.len ? 'value length must be %s characters'.format([rules.len]) : ''"
  }];

  // `min_len` specifies that the field value must have at least the specified
  // number of characters (Unicode code points), which may differ from the number
  // of bytes in the string. If the field value contains fewer characters, an error
  // message will be generated.
  //
  // ```proto
  // message MyString {
  //   // value length must be at least 3 characters
  //   string value = 1 [(buf.validate.field).string.min_len = 3];
  // }
  // ```
  optional uint64 min_len = 2 [(priv.field).cel = {
    id: "string.min_len"
    expression: "uint(this.size()) < rules.min_len ? 'value length must be at least %s characters'.format([rules.min_len]) : ''"
  }];

  // `max_len` specifies that the field value must have no more than the specified
  // number of characters (Unicode code points), which may differ from the
  // number of bytes in the string. If the field value contains more characters,
  // an error message will be generated.
  //
  // ```proto
  // message MyString {
  //   // value length must be at most 10 characters
  //   string value = 1 [(buf.validate.field).string.max_len = 10];
  // }
  // ```
  optional uint64 max_len = 3 [(priv.field).cel = {
    id: "string.max_len"
    expression: "uint(this.size()) > rules.max_len ? 'value length must be at most %s characters'.format([rules.max_len]) : ''"
  }];

  // `len_bytes` dictates that the field value must have the specified number of
  // bytes. If the field value does not match the specified length in bytes,
  // an error message will be generated.
  //
  // ```proto
  // message MyString {
  //   // value length must be 6 bytes
  //   string value = 1 [(buf.validate.field).string.len_bytes = 6];
  // }
  // ```
  optional uint64 len_bytes = 20 [(priv.field).cel = {
    id: "string.len_bytes"
    expression: "uint(bytes(this).size()) != rules.len_bytes ? 'value length must be %s bytes'.format([rules.len_bytes]) : ''"
  }];

  // `min_bytes` specifies that the field value must have at least the specified
  // number of bytes. If the field value contains fewer bytes, an error message
  // will be generated.
  //
  // ```proto
  // message MyString {
  //   // value length must be at least 4 bytes
  //   string value = 1 [(buf.validate.field).string.min_bytes = 4];
  // }
  //
  // ```
  optional uint64 min_bytes = 4 [(priv.field).cel = {
    id: "string.min_bytes"
    expression: "uint(bytes(this).size()) < rules.min_bytes ? 'value length must be at least %s bytes'.format([rules.min_bytes]) : ''"
  }];

  // `max_bytes` specifies that the field value must have no more than the
  //specified number of bytes. If the field value contains more bytes, an
  // error message will be generated.
  //
  // ```proto
  // message MyString {
  //   // value length must be at most 8 bytes
  //   string value = 1 [(buf.validate.field).string.max_bytes = 8];
  // }
  // ```
  optional uint64 max_bytes = 5 [(priv.field).cel = {
    id: "string.max_bytes"
    expression: "uint(bytes(this).size()) > rules.max_bytes ? 'value length must be at most %s bytes'.format([rules.max_bytes]) : ''"
  }];

  // `pattern` specifies that the field value must match the specified
  // regular expression (RE2 syntax), with the expression provided without any
  // delimiters. If the field value doesn't match the regular expression, an
  // error message will be generated.
  //
  // ```proto
  // message MyString {
  //   // value does not match regex pattern `^[a-zA-Z]//$`
  //   string value = 1 [(buf.validate.field).string.pattern = "^[a-zA-Z]//$"];
  // }
  // ```
  optional string pattern = 6 [(priv.field).cel = {
    id: "string.pattern"
    expression: "!this.matches(rules.pattern) ? 'value does not match regex pattern `%s`'.format([rules.pattern]) : ''"
  }];

  // `prefix` specifies that the field value must have the
  //specified substring at the beginning of the string. If the field value
  // doesn't start with the specified prefix, an error message will be
  // generated.
  //
  // ```proto
  // message MyString {
  //   // value does not have prefix `pre`
  //   string value = 1 [(buf.validate.field).string.prefix = "pre"];
  // }
  // ```
  optional string prefix = 7 [(priv.field).cel = {
    id: "string.prefix"
    expression: "!this.startsWith(rules.prefix) ? 'value does not have prefix `%s`'.format([rules.prefix]) : ''"
  }];

  // `suffix` specifies that the field value must have the
  //specified substring at the end of the string. If the field value doesn't
  // end with the specified suffix, an error message will be generated.
  //
  // ```proto
  // message MyString {
  //   // value does not have suffix `post`
  //   string value = 1 [(buf.validate.field).string.suffix = "post"];
  // }
  // ```
  optional string suffix = 8 [(priv.field).cel = {
    id: "string.suffix"
    expression: "!this.endsWith(rules.suffix) ? 'value does not have suffix `%s`'.format([rules.suffix]) : ''"
  }];

  // `contains` specifies that the field value must have the
  //specified substring anywhere in the string. If the field value doesn't
  // contain the specified substring, an error message will be generated.
  //
  // ```proto
  // message MyString {
  //   // value does not contain substring `inside`.
  //   string value = 1 [(buf.validate.field).string.contains = "inside"];
  // }
  // ```
  optional string contains = 9 [(priv.field).cel = {
    id: "string.contains"
    expression: "!this.contains(rules.contains) ? 'value does not contain substring `%s`'.format([rules.contains]) : ''"
  }];

  // `not_contains` specifies that the field value must not have the
  //specified substring anywhere in the string. If the field value contains
  // the specified substring, an error message will be generated.
  //
  // ```proto
  // message MyString {
  //   // value contains substring `inside`.
  //   string value = 1 [(buf.validate.field).string.not_contains = "inside"];
  // }
  // ```
  optional string not_contains = 23 [(priv.field).cel = {
    id: "string.not_contains"
    expression: "this.contains(rules.not_contains) ? 'value contains substring `%s`'.format([rules.not_contains]) : ''"
  }];

  // `in` specifies that the field value must be equal to one of the specified
  // values. If the field value isn't one of the specified values, an error
  // message will be generated.
  //
  // ```proto
  // message MyString {
  //   // value must be in list ["apple", "banana"]
  //   repeated string value = 1 [(buf.validate.field).string.in = "apple", (buf.validate.field).string.in = "banana"];
  // }
  // ```
  repeated string in = 10 [(priv.field).cel = {
    id: "string.in"
    expression: "!(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `not_in` specifies that the field value cannot be equal to any
  // of the specified values. If the field value is one of the specified values,
  // an error message will be generated.
  // ```proto
  // message MyString {
  //   // value must not be in list ["orange", "grape"]
  //   repeated string value = 1 [(buf.validate.field).string.not_in = "orange", (buf.validate.field).string.not_in = "grape"];
  // }
  // ```
  repeated string not_in = 11 [(priv.field).cel = {
    id: "string.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];

  // `WellKnown` rules provide advanced constraints against common string
  // patterns
  oneof well_known {
    // `email` specifies that the field value must be a valid email address
    // (addr-spec only) as defined by [RFC 5322](https://tools.ietf.org/html/rfc5322#section-3.4.1).
    // If the field value isn't a valid email address, an error message will be generated.
    //
    // ```proto
    // message MyString {
    //   // value must be a valid email address
    //   string value = 1 [(buf.validate.field).string.email = true];
    // }
    // ```
    bool email = 12 [
      (priv.field).cel = {
        id: "string.email"
        message: "value must be a valid email address"
        expression: "this == '' || this.isEmail()"
      },
      (priv.field).cel = {
        id: "string.email_empty"
        message: "value is empty, which is not a valid email address"
        expression: "this != ''"
      }
    ];

    // `hostname` specifies that the field value must be a valid
    // hostname as defined by [RFC 1034](https://tools.ietf.org/html/rfc1034#section-3.5). This constraint doesn't support
    // internationalized domain names (IDNs). If the field value isn't a
    // valid hostname, an error message will be generated.
    //
    // ```proto
    // message MyString {
    //   // value must be a valid hostname
    //   string value = 1 [(buf.validate.field).string.hostname = true];
    // }
    // ```
    bool hostname = 13 [
      (priv.field).cel = {
        id: "string.hostname"
        message: "value must be a valid hostname"
        expression: "this == '' || this.isHostname()"
      },
      (priv.field).cel = {
        id: "string.hostname_empty"
        message: "value is empty, which is not a valid hostname"
        expression: "this != ''"
      }
    ];

    // `ip` specifies that the field value must be a valid IP
    // (v4 or v6) address, without surrounding square brackets for IPv6 addresses.
    // If the field value isn't a valid IP address, an error message will be
    // generated.
    //
    // ```proto
    // message MyString {
    //   // value must be a valid IP address
    //   string value = 1 [(buf.validate.field).string.ip = true];
    // }
    // ```
    bool ip = 14 [
      (priv.field).cel = {
        id: "string.ip"
        message: "value must be a valid IP address"
        expression: "this == '' || this.isIp()"
      },
      (priv.field).cel = {
        id: "string.ip_empty"
        message: "value is empty, which is not a valid IP address"
        expression: "this != ''"
      }
    ];

    // `ipv4` specifies that the field value must be a valid IPv4
    // address. If the field value isn't a valid IPv4 address, an error message
    // will be generated.
    //
    // ```proto
    // message MyString {
    //   // value must be a valid IPv4 address
    //   string value = 1 [(buf.validate.field).string.ipv4 = true];
    // }
    // ```
    bool ipv4 = 15 [
      (priv.field).cel = {
        id: "string.ipv4"
        message: "value must be a valid IPv4 address"
        expression: "this == '' || this.isIp(4)"
      },
      (priv.field).cel = {
        id: "string.ipv4_empty"
        message: "value is empty, which is not a valid IPv4 address"
        expression: "this != ''"
      }
    ];

    // `ipv6` specifies that the field value must be a valid
    // IPv6 address, without surrounding square brackets. If the field value is
    // not a valid IPv6 address, an error message will be generated.
    //
    // ```proto
    // message MyString {
    //   // value must be a valid IPv6 address
    //   string value = 1 [(buf.validate.field).string.ipv6 = true];
    // }
    // ```
    bool ipv6 = 16 [
      (priv.field).cel = {
        id: "string.ipv6"
        message: "value must be a valid IPv6 address"
        expression: "this == '' || this.isIp(6)"
      },
      (priv.field).cel = {
        id: "string.ipv6_empty"
        message: "value is empty, which is not a valid IPv6 address"
        expression: "this != ''"
      }
    ];

    // `uri` specifies that the field value must be a valid,
    // absolute URI as defined by [RFC 3986](https://tools.ietf.org/html/rfc3986#section-3). If the field value isn't a valid,
    // absolute URI, an error message will be generated.
    //
    // ```proto
    // message MyString {
    //   // value must be a valid URI
    //   string value = 1 [(buf.validate.field).string.uri = true];
    // }
    // ```
    bool uri = 17 [
      (priv.field).cel = {
        id: "string.uri"
        message: "value must be a valid URI"
        expression: "this == '' || this.isUri()"
      },
      (priv.field).cel = {
        id: "string.uri_empty"
        message: "value is empty, which is not a valid URI"
        expression: "this != ''"
      }
    ];

    // `uri_ref` specifies that the field value must be a valid URI
    // as defined by [RFC 3986](https://tools.ietf.org/html/rfc3986#section-3) and may be either relative or absolute. If the
    // field value isn't a valid URI, an error message will be generated.
    //
    // ```proto
    // message MyString {
    //   // value must be a valid URI
    //   string value = 1 [(buf.validate.field).string.uri_ref = true];
    // }
    // ```
    bool uri_ref = 18 [(priv.field).cel = {
      id: "string.uri_ref"
      message: "value must be a valid URI"
      expression: "this.isUriRef()"
    }];

    // `address` specifies that the field value must be either a valid hostname
    // as defined by [RFC 1034](https://tools.ietf.org/html/rfc1034#section-3.5)
    // (which doesn't support internationalized domain names or IDNs) or a valid
    // IP (v4 or v6). If the field value isn't a valid hostname or IP, an error
    // message will be generated.
    //
    // ```proto
    // message MyString {
    //   // value must be a valid hostname, or ip address
    //   string value = 1 [(buf.validate.field).string.address = true];
    // }
    // ```
    bool address = 21 [
      (priv.field).cel = {
        id: "string.address"
        message: "value must be a valid hostname, or ip address"
        expression: "this == '' || this.isHostname() || this.isIp()"
      },
      (priv.field).cel = {
        id: "string.address_empty"
        message: "value is empty, which is not a valid hostname, or ip address"
        expression: "this != ''"
      }
    ];

    // `uuid` specifies that the field value must be a valid UUID as defined by
    // [RFC 4122](https://tools.ietf.org/html/rfc4122#section-4.1.2). If the
    // field value isn't a valid UUID, an error message will be generated.
    //
    // ```proto
    // message MyString {
    //   // value must be a valid UUID
    //   string value = 1 [(buf.validate.field).string.uuid = true];
    // }
    // ```
    bool uuid = 22 [
      (priv.field).cel = {
        id: "string.uuid"
        message: "value must be a valid UUID"
        expression: "this == '' || this.matches('^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$')"
      },
      (priv.field).cel = {
        id: "string.uuid_empty"
        message: "value is empty, which is not a valid UUID"
        expression: "this != ''"
      }
    ];

    // `tuuid` (trimmed UUID) specifies that the field value must be a valid UUID as
    // defined by [RFC 4122](https://tools.ietf.org/html/rfc4122#section-4.1.2) with all dashes
    // omitted. If the field value isn't a valid UUID without dashes, an error message
    // will be generated.
    //
    // ```proto
    // message MyString {
    //   // value must be a valid trimmed UUID
    //   string value = 1 [(buf.validate.field).string.tuuid = true];
    // }
    // ```
    bool tuuid = 33 [
      (priv.field).cel = {
        id: "string.tuuid"
        message: "value must be a valid trimmed UUID"
        expression: "this == '' || this.matches('^[0-9a-fA-F]{32}$')"
      },
      (priv.field).cel = {
        id: "string.tuuid_empty"
        message: "value is empty, which is not a valid trimmed UUID"
        expression: "this != ''"
      }
    ];

    // `ip_with_prefixlen` specifies that the field value must be a valid IP (v4 or v6)
    // address with prefix length. If the field value isn't a valid IP with prefix
    // length, an error message will be generated.
    //
    //
    // ```proto
    // message MyString {
    //   // value must be a valid IP with prefix length
    //    string value = 1 [(buf.validate.field).string.ip_with_prefixlen = true];
    // }
    // ```
    bool ip_with_prefixlen = 26 [
      (priv.field).cel = {
        id: "string.ip_with_prefixlen"
        message: "value must be a valid IP prefix"
        expression: "this == '' || this.isIpPrefix()"
      },
      (priv.field).cel = {
        id: "string.ip_with_prefixlen_empty"
        message: "value is empty, which is not a valid IP prefix"
        expression: "this != ''"
      }
    ];

    // `ipv4_with_prefixlen` specifies that the field value must be a valid
    // IPv4 address with prefix.
    // If the field value isn't a valid IPv4 address with prefix length,
    // an error message will be generated.
    //
    // ```proto
    // message MyString {
    //   // value must be a valid IPv4 address with prefix length
    //    string value = 1 [(buf.validate.field).string.ipv4_with_prefixlen = true];
    // }
    // ```
    bool ipv4_with_prefixlen = 27 [
      (priv.field).cel = {
        id: "string.ipv4_with_prefixlen"
        message: "value must be a valid IPv4 address with prefix length"
        expression: "this == '' || this.isIpPrefix(4)"
      },
      (priv.field).cel = {
        id: "string.ipv4_with_prefixlen_empty"
        message: "value is empty, which is not a valid IPv4 address with prefix length"
        expression: "this != ''"
      }
    ];

    // `ipv6_with_prefixlen` specifies that the field value must be a valid
    // IPv6 address with prefix length.
    // If the field value is not a valid IPv6 address with prefix length,
    // an error message will be generated.
    //
    // ```proto
    // message MyString {
    //   // value must be a valid IPv6 address prefix length
    //    string value = 1 [(buf.validate.field).string.ipv6_with_prefixlen = true];
    // }
    // ```
    bool ipv6_with_prefixlen = 28 [
      (priv.field).cel = {
        id: "string.ipv6_with_prefixlen"
        message: "value must be a valid IPv6 address with prefix length"
        expression: "this == '' || this.isIpPrefix(6)"
      },
      (priv.field).cel = {
        id: "string.ipv6_with_prefixlen_empty"
        message: "value is empty, which is not a valid IPv6 address with prefix length"
        expression: "this != ''"
      }
    ];

    // `ip_prefix` specifies that the field value must be a valid IP (v4 or v6) prefix.
    // If the field value isn't a valid IP prefix, an error message will be
    // generated. The prefix must have all zeros for the masked bits of the prefix (e.g.,
    // `*********/16`, not `127.0.0.1/16`).
    //
    // ```proto
    // message MyString {
    //   // value must be a valid IP prefix
    //    string value = 1 [(buf.validate.field).string.ip_prefix = true];
    // }
    // ```
    bool ip_prefix = 29 [
      (priv.field).cel = {
        id: "string.ip_prefix"
        message: "value must be a valid IP prefix"
        expression: "this == '' || this.isIpPrefix(true)"
      },
      (priv.field).cel = {
        id: "string.ip_prefix_empty"
        message: "value is empty, which is not a valid IP prefix"
        expression: "this != ''"
      }
    ];

    // `ipv4_prefix` specifies that the field value must be a valid IPv4
    // prefix. If the field value isn't a valid IPv4 prefix, an error message
    // will be generated. The prefix must have all zeros for the masked bits of
    // the prefix (e.g., `*********/16`, not `127.0.0.1/16`).
    //
    // ```proto
    // message MyString {
    //   // value must be a valid IPv4 prefix
    //    string value = 1 [(buf.validate.field).string.ipv4_prefix = true];
    // }
    // ```
    bool ipv4_prefix = 30 [
      (priv.field).cel = {
        id: "string.ipv4_prefix"
        message: "value must be a valid IPv4 prefix"
        expression: "this == '' || this.isIpPrefix(4, true)"
      },
      (priv.field).cel = {
        id: "string.ipv4_prefix_empty"
        message: "value is empty, which is not a valid IPv4 prefix"
        expression: "this != ''"
      }
    ];

    // `ipv6_prefix` specifies that the field value must be a valid IPv6 prefix.
    // If the field value is not a valid IPv6 prefix, an error message will be
    // generated. The prefix must have all zeros for the masked bits of the prefix
    // (e.g., `2001:db8::/48`, not `2001:db8::1/48`).
    //
    // ```proto
    // message MyString {
    //   // value must be a valid IPv6 prefix
    //    string value = 1 [(buf.validate.field).string.ipv6_prefix = true];
    // }
    // ```
    bool ipv6_prefix = 31 [
      (priv.field).cel = {
        id: "string.ipv6_prefix"
        message: "value must be a valid IPv6 prefix"
        expression: "this == '' || this.isIpPrefix(6, true)"
      },
      (priv.field).cel = {
        id: "string.ipv6_prefix_empty"
        message: "value is empty, which is not a valid IPv6 prefix"
        expression: "this != ''"
      }
    ];

    // `host_and_port` specifies the field value must be a valid host and port
    // pair. The host must be a valid hostname or IP address while the port
    // must be in the range of 0-65535, inclusive. IPv6 addresses must be delimited
    // with square brackets (e.g., `[::1]:1234`).
    bool host_and_port = 32 [
      (priv.field).cel = {
        id: "string.host_and_port"
        message: "value must be a valid host (hostname or IP address) and port pair"
        expression: "this == '' || this.isHostAndPort(true)"
      },
      (priv.field).cel = {
        id: "string.host_and_port_empty"
        message: "value is empty, which is not a valid host and port pair"
        expression: "this != ''"
      }
    ];

    // `well_known_regex` specifies a common well-known pattern
    // defined as a regex. If the field value doesn't match the well-known
    // regex, an error message will be generated.
    //
    // ```proto
    // message MyString {
    //   // value must be a valid HTTP header value
    //   string value = 1 [(buf.validate.field).string.well_known_regex = KNOWN_REGEX_HTTP_HEADER_VALUE];
    // }
    // ```
    //
    // #### KnownRegex
    //
    // `well_known_regex` contains some well-known patterns.
    //
    // | Name                          | Number | Description                               |
    // |-------------------------------|--------|-------------------------------------------|
    // | KNOWN_REGEX_UNSPECIFIED       | 0      |                                           |
    // | KNOWN_REGEX_HTTP_HEADER_NAME  | 1      | HTTP header name as defined by [RFC 7230](https://tools.ietf.org/html/rfc7230#section-3.2)  |
    // | KNOWN_REGEX_HTTP_HEADER_VALUE | 2      | HTTP header value as defined by [RFC 7230](https://tools.ietf.org/html/rfc7230#section-3.2.4) |
    KnownRegex well_known_regex = 24 [
      (priv.field).cel = {
        id: "string.well_known_regex.header_name"
        message: "value must be a valid HTTP header name"
        expression:
          "rules.well_known_regex != 1 || this == '' || this.matches(!has(rules.strict) || rules.strict ?"
          "'^:?[0-9a-zA-Z!#$%&\\'*+-.^_|~\\x60]+$' :"
          "'^[^\\u0000\\u000A\\u000D]+$')"
      },
      (priv.field).cel = {
        id: "string.well_known_regex.header_name_empty"
        message: "value is empty, which is not a valid HTTP header name"
        expression: "rules.well_known_regex != 1 || this != ''"
      },
      (priv.field).cel = {
        id: "string.well_known_regex.header_value"
        message: "value must be a valid HTTP header value"
        expression:
          "rules.well_known_regex != 2 || this.matches(!has(rules.strict) || rules.strict ?"
          "'^[^\\u0000-\\u0008\\u000A-\\u001F\\u007F]*$' :"
          "'^[^\\u0000\\u000A\\u000D]*$')"
      }
    ];
  }

  // This applies to regexes `HTTP_HEADER_NAME` and `HTTP_HEADER_VALUE` to
  // enable strict header validation. By default, this is true, and HTTP header
  // validations are [RFC-compliant](https://tools.ietf.org/html/rfc7230#section-3). Setting to false will enable looser
  // validations that only disallow `\r\n\0` characters, which can be used to
  // bypass header matching rules.
  //
  // ```proto
  // message MyString {
  //   // The field `value` must have be a valid HTTP headers, but not enforced with strict rules.
  //   string value = 1 [(buf.validate.field).string.strict = false];
  // }
  // ```
  optional bool strict = 25;
}

// WellKnownRegex contain some well-known patterns.
enum KnownRegex {
  KNOWN_REGEX_UNSPECIFIED = 0;

  // HTTP header name as defined by [RFC 7230](https://tools.ietf.org/html/rfc7230#section-3.2).
  KNOWN_REGEX_HTTP_HEADER_NAME = 1;

  // HTTP header value as defined by [RFC 7230](https://tools.ietf.org/html/rfc7230#section-3.2.4).
  KNOWN_REGEX_HTTP_HEADER_VALUE = 2;
}

// BytesRules describe the constraints applied to `bytes` values. These rules
// may also be applied to the `google.protobuf.BytesValue` Well-Known-Type.
message BytesRules {
  // `const` requires the field value to exactly match the specified bytes
  // value. If the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MyBytes {
  //   // value must be "\x01\x02\x03\x04"
  //   bytes value = 1 [(buf.validate.field).bytes.const = "\x01\x02\x03\x04"];
  // }
  // ```
  optional bytes const = 1 [(priv.field).cel = {
    id: "bytes.const"
    expression: "this != rules.const ? 'value must be %x'.format([rules.const]) : ''"
  }];

  // `len` requires the field value to have the specified length in bytes.
  // If the field value doesn't match, an error message is generated.
  //
  // ```proto
  // message MyBytes {
  //   // value length must be 4 bytes.
  //   optional bytes value = 1 [(buf.validate.field).bytes.len = 4];
  // }
  // ```
  optional uint64 len = 13 [(priv.field).cel = {
    id: "bytes.len"
    expression: "uint(this.size()) != rules.len ? 'value length must be %s bytes'.format([rules.len]) : ''"
  }];

  // `min_len` requires the field value to have at least the specified minimum
  // length in bytes.
  // If the field value doesn't meet the requirement, an error message is generated.
  //
  // ```proto
  // message MyBytes {
  //   // value length must be at least 2 bytes.
  //   optional bytes value = 1 [(buf.validate.field).bytes.min_len = 2];
  // }
  // ```
  optional uint64 min_len = 2 [(priv.field).cel = {
    id: "bytes.min_len"
    expression: "uint(this.size()) < rules.min_len ? 'value length must be at least %s bytes'.format([rules.min_len]) : ''"
  }];

  // `max_len` requires the field value to have at most the specified maximum
  // length in bytes.
  // If the field value exceeds the requirement, an error message is generated.
  //
  // ```proto
  // message MyBytes {
  //   // value must be at most 6 bytes.
  //   optional bytes value = 1 [(buf.validate.field).bytes.max_len = 6];
  // }
  // ```
  optional uint64 max_len = 3 [(priv.field).cel = {
    id: "bytes.max_len"
    expression: "uint(this.size()) > rules.max_len ? 'value must be at most %s bytes'.format([rules.max_len]) : ''"
  }];

  // `pattern` requires the field value to match the specified regular
  // expression ([RE2 syntax](https://github.com/google/re2/wiki/Syntax)).
  // The value of the field must be valid UTF-8 or validation will fail with a
  // runtime error.
  // If the field value doesn't match the pattern, an error message is generated.
  //
  // ```proto
  // message MyBytes {
  //   // value must match regex pattern "^[a-zA-Z0-9]+$".
  //   optional bytes value = 1 [(buf.validate.field).bytes.pattern = "^[a-zA-Z0-9]+$"];
  // }
  // ```
  optional string pattern = 4 [(priv.field).cel = {
    id: "bytes.pattern"
    expression: "!string(this).matches(rules.pattern) ? 'value must match regex pattern `%s`'.format([rules.pattern]) : ''"
  }];

  // `prefix` requires the field value to have the specified bytes at the
  // beginning of the string.
  // If the field value doesn't meet the requirement, an error message is generated.
  //
  // ```proto
  // message MyBytes {
  //   // value does not have prefix \x01\x02
  //   optional bytes value = 1 [(buf.validate.field).bytes.prefix = "\x01\x02"];
  // }
  // ```
  optional bytes prefix = 5 [(priv.field).cel = {
    id: "bytes.prefix"
    expression: "!this.startsWith(rules.prefix) ? 'value does not have prefix %x'.format([rules.prefix]) : ''"
  }];

  // `suffix` requires the field value to have the specified bytes at the end
  // of the string.
  // If the field value doesn't meet the requirement, an error message is generated.
  //
  // ```proto
  // message MyBytes {
  //   // value does not have suffix \x03\x04
  //   optional bytes value = 1 [(buf.validate.field).bytes.suffix = "\x03\x04"];
  // }
  // ```
  optional bytes suffix = 6 [(priv.field).cel = {
    id: "bytes.suffix"
    expression: "!this.endsWith(rules.suffix) ? 'value does not have suffix %x'.format([rules.suffix]) : ''"
  }];

  // `contains` requires the field value to have the specified bytes anywhere in
  // the string.
  // If the field value doesn't meet the requirement, an error message is generated.
  //
  // ```protobuf
  // message MyBytes {
  //   // value does not contain \x02\x03
  //   optional bytes value = 1 [(buf.validate.field).bytes.contains = "\x02\x03"];
  // }
  // ```
  optional bytes contains = 7 [(priv.field).cel = {
    id: "bytes.contains"
    expression: "!this.contains(rules.contains) ? 'value does not contain %x'.format([rules.contains]) : ''"
  }];

  // `in` requires the field value to be equal to one of the specified
  // values. If the field value doesn't match any of the specified values, an
  // error message is generated.
  //
  // ```protobuf
  // message MyBytes {
  //   // value must in ["\x01\x02", "\x02\x03", "\x03\x04"]
  //   optional bytes value = 1 [(buf.validate.field).bytes.in = {"\x01\x02", "\x02\x03", "\x03\x04"}];
  // }
  // ```
  repeated bytes in = 8 [(priv.field).cel = {
    id: "bytes.in"
    expression: "dyn(rules)['in'].size() > 0 && !(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `not_in` requires the field value to be not equal to any of the specified
  // values.
  // If the field value matches any of the specified values, an error message is
  // generated.
  //
  // ```proto
  // message MyBytes {
  //   // value must not in ["\x01\x02", "\x02\x03", "\x03\x04"]
  //   optional bytes value = 1 [(buf.validate.field).bytes.not_in = {"\x01\x02", "\x02\x03", "\x03\x04"}];
  // }
  // ```
  repeated bytes not_in = 9 [(priv.field).cel = {
    id: "bytes.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];

  // WellKnown rules provide advanced constraints against common byte
  // patterns
  oneof well_known {
    // `ip` ensures that the field `value` is a valid IP address (v4 or v6) in byte format.
    // If the field value doesn't meet this constraint, an error message is generated.
    //
    // ```proto
    // message MyBytes {
    //   // value must be a valid IP address
    //   optional bytes value = 1 [(buf.validate.field).bytes.ip = true];
    // }
    // ```
    bool ip = 10 [
      (priv.field).cel = {
        id: "bytes.ip"
        message: "value must be a valid IP address"
        expression: "this.size() == 0 || this.size() == 4 || this.size() == 16"
      },
      (priv.field).cel = {
        id: "bytes.ip_empty"
        message: "value is empty, which is not a valid IP address"
        expression: "this.size() != 0"
      }
    ];

    // `ipv4` ensures that the field `value` is a valid IPv4 address in byte format.
    // If the field value doesn't meet this constraint, an error message is generated.
    //
    // ```proto
    // message MyBytes {
    //   // value must be a valid IPv4 address
    //   optional bytes value = 1 [(buf.validate.field).bytes.ipv4 = true];
    // }
    // ```
    bool ipv4 = 11 [
      (priv.field).cel = {
        id: "bytes.ipv4"
        message: "value must be a valid IPv4 address"
        expression: "this.size() == 0 || this.size() == 4"
      },
      (priv.field).cel = {
        id: "bytes.ipv4_empty"
        message: "value is empty, which is not a valid IPv4 address"
        expression: "this.size() != 0"
      }
    ];

    // `ipv6` ensures that the field `value` is a valid IPv6 address in byte format.
    // If the field value doesn't meet this constraint, an error message is generated.
    // ```proto
    // message MyBytes {
    //   // value must be a valid IPv6 address
    //   optional bytes value = 1 [(buf.validate.field).bytes.ipv6 = true];
    // }
    // ```
    bool ipv6 = 12 [
      (priv.field).cel = {
        id: "bytes.ipv6"
        message: "value must be a valid IPv6 address"
        expression: "this.size() == 0 || this.size() == 16"
      },
      (priv.field).cel = {
        id: "bytes.ipv6_empty"
        message: "value is empty, which is not a valid IPv6 address"
        expression: "this.size() != 0"
      }
    ];
  }
}

// EnumRules describe the constraints applied to `enum` values.
message EnumRules {
  // `const` requires the field value to exactly match the specified enum value.
  // If the field value doesn't match, an error message is generated.
  //
  // ```proto
  // enum MyEnum {
  //   MY_ENUM_UNSPECIFIED = 0;
  //   MY_ENUM_VALUE1 = 1;
  //   MY_ENUM_VALUE2 = 2;
  // }
  //
  // message MyMessage {
  //   // The field `value` must be exactly MY_ENUM_VALUE1.
  //   MyEnum value = 1 [(buf.validate.field).enum.const = 1];
  // }
  // ```
  optional int32 const = 1 [(priv.field).cel = {
    id: "enum.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];

  // `defined_only` requires the field value to be one of the defined values for
  // this enum, failing on any undefined value.
  //
  // ```proto
  // enum MyEnum {
  //   MY_ENUM_UNSPECIFIED = 0;
  //   MY_ENUM_VALUE1 = 1;
  //   MY_ENUM_VALUE2 = 2;
  // }
  //
  // message MyMessage {
  //   // The field `value` must be a defined value of MyEnum.
  //   MyEnum value = 1 [(buf.validate.field).enum.defined_only = true];
  // }
  // ```
  optional bool defined_only = 2;

  // `in` requires the field value to be equal to one of the
  //specified enum values. If the field value doesn't match any of the
  //specified values, an error message is generated.
  //
  // ```proto
  // enum MyEnum {
  //   MY_ENUM_UNSPECIFIED = 0;
  //   MY_ENUM_VALUE1 = 1;
  //   MY_ENUM_VALUE2 = 2;
  // }
  //
  // message MyMessage {
  //   // The field `value` must be equal to one of the specified values.
  //   MyEnum value = 1 [(buf.validate.field).enum = { in: [1, 2]}];
  // }
  // ```
  repeated int32 in = 3 [(priv.field).cel = {
    id: "enum.in"
    expression: "!(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `not_in` requires the field value to be not equal to any of the
  //specified enum values. If the field value matches one of the specified
  // values, an error message is generated.
  //
  // ```proto
  // enum MyEnum {
  //   MY_ENUM_UNSPECIFIED = 0;
  //   MY_ENUM_VALUE1 = 1;
  //   MY_ENUM_VALUE2 = 2;
  // }
  //
  // message MyMessage {
  //   // The field `value` must not be equal to any of the specified values.
  //   MyEnum value = 1 [(buf.validate.field).enum = { not_in: [1, 2]}];
  // }
  // ```
  repeated int32 not_in = 4 [(priv.field).cel = {
    id: "enum.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];
}

// RepeatedRules describe the constraints applied to `repeated` values.
message RepeatedRules {
  // `min_items` requires that this field must contain at least the specified
  // minimum number of items.
  //
  // Note that `min_items = 1` is equivalent to setting a field as `required`.
  //
  // ```proto
  // message MyRepeated {
  //   // value must contain at least  2 items
  //   repeated string value = 1 [(buf.validate.field).repeated.min_items = 2];
  // }
  // ```
  optional uint64 min_items = 1 [(priv.field).cel = {
    id: "repeated.min_items"
    expression: "uint(this.size()) < rules.min_items ? 'value must contain at least %d item(s)'.format([rules.min_items]) : ''"
  }];

  // `max_items` denotes that this field must not exceed a
  // certain number of items as the upper limit. If the field contains more
  // items than specified, an error message will be generated, requiring the
  // field to maintain no more than the specified number of items.
  //
  // ```proto
  // message MyRepeated {
  //   // value must contain no more than 3 item(s)
  //   repeated string value = 1 [(buf.validate.field).repeated.max_items = 3];
  // }
  // ```
  optional uint64 max_items = 2 [(priv.field).cel = {
    id: "repeated.max_items"
    expression: "uint(this.size()) > rules.max_items ? 'value must contain no more than %s item(s)'.format([rules.max_items]) : ''"
  }];

  // `unique` indicates that all elements in this field must
  // be unique. This constraint is strictly applicable to scalar and enum
  // types, with message types not being supported.
  //
  // ```proto
  // message MyRepeated {
  //   // repeated value must contain unique items
  //   repeated string value = 1 [(buf.validate.field).repeated.unique = true];
  // }
  // ```
  optional bool unique = 3 [(priv.field).cel = {
    id: "repeated.unique"
    message: "repeated value must contain unique items"
    expression: "this.unique()"
  }];

  // `items` details the constraints to be applied to each item
  // in the field. Even for repeated message fields, validation is executed
  // against each item unless skip is explicitly specified.
  //
  // ```proto
  // message MyRepeated {
  //   // The items in the field `value` must follow the specified constraints.
  //   repeated string value = 1 [(buf.validate.field).repeated.items = {
  //     string: {
  //       min_len: 3
  //       max_len: 10
  //     }
  //   }];
  // }
  // ```
  optional FieldConstraints items = 4;
}

// MapRules describe the constraints applied to `map` values.
message MapRules {
  //Specifies the minimum number of key-value pairs allowed. If the field has
  // fewer key-value pairs than specified, an error message is generated.
  //
  // ```proto
  // message MyMap {
  //   // The field `value` must have at least 2 key-value pairs.
  //   map<string, string> value = 1 [(buf.validate.field).map.min_pairs = 2];
  // }
  // ```
  optional uint64 min_pairs = 1 [(priv.field).cel = {
    id: "map.min_pairs"
    expression: "uint(this.size()) < rules.min_pairs ? 'map must be at least %d entries'.format([rules.min_pairs]) : ''"
  }];

  //Specifies the maximum number of key-value pairs allowed. If the field has
  // more key-value pairs than specified, an error message is generated.
  //
  // ```proto
  // message MyMap {
  //   // The field `value` must have at most 3 key-value pairs.
  //   map<string, string> value = 1 [(buf.validate.field).map.max_pairs = 3];
  // }
  // ```
  optional uint64 max_pairs = 2 [(priv.field).cel = {
    id: "map.max_pairs"
    expression: "uint(this.size()) > rules.max_pairs ? 'map must be at most %d entries'.format([rules.max_pairs]) : ''"
  }];

  //Specifies the constraints to be applied to each key in the field.
  //
  // ```proto
  // message MyMap {
  //   // The keys in the field `value` must follow the specified constraints.
  //   map<string, string> value = 1 [(buf.validate.field).map.keys = {
  //     string: {
  //       min_len: 3
  //       max_len: 10
  //     }
  //   }];
  // }
  // ```
  optional FieldConstraints keys = 4;

  //Specifies the constraints to be applied to the value of each key in the
  // field. Message values will still have their validations evaluated unless
  //skip is specified here.
  //
  // ```proto
  // message MyMap {
  //   // The values in the field `value` must follow the specified constraints.
  //   map<string, string> value = 1 [(buf.validate.field).map.values = {
  //     string: {
  //       min_len: 5
  //       max_len: 20
  //     }
  //   }];
  // }
  // ```
  optional FieldConstraints values = 5;
}

// AnyRules describe constraints applied exclusively to the `google.protobuf.Any` well-known type.
message AnyRules {
  // `in` requires the field's `type_url` to be equal to one of the
  //specified values. If it doesn't match any of the specified values, an error
  // message is generated.
  //
  // ```proto
  // message MyAny {
  //   //  The `value` field must have a `type_url` equal to one of the specified values.
  //   google.protobuf.Any value = 1 [(buf.validate.field).any.in = ["type.googleapis.com/MyType1", "type.googleapis.com/MyType2"]];
  // }
  // ```
  repeated string in = 2;

  // requires the field's type_url to be not equal to any of the specified values. If it matches any of the specified values, an error message is generated.
  //
  // ```proto
  // message MyAny {
  //   // The field `value` must not have a `type_url` equal to any of the specified values.
  //   google.protobuf.Any value = 1 [(buf.validate.field).any.not_in = ["type.googleapis.com/ForbiddenType1", "type.googleapis.com/ForbiddenType2"]];
  // }
  // ```
  repeated string not_in = 3;
}

// DurationRules describe the constraints applied exclusively to the `google.protobuf.Duration` well-known type.
message DurationRules {
  // `const` dictates that the field must match the specified value of the `google.protobuf.Duration` type exactly.
  // If the field's value deviates from the specified value, an error message
  // will be generated.
  //
  // ```proto
  // message MyDuration {
  //   // value must equal 5s
  //   google.protobuf.Duration value = 1 [(buf.validate.field).duration.const = "5s"];
  // }
  // ```
  optional google.protobuf.Duration const = 2 [(priv.field).cel = {
    id: "duration.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];
  oneof less_than {
    // `lt` stipulates that the field must be less than the specified value of the `google.protobuf.Duration` type,
    // exclusive. If the field's value is greater than or equal to the specified
    // value, an error message will be generated.
    //
    // ```proto
    // message MyDuration {
    //   // value must be less than 5s
    //   google.protobuf.Duration value = 1 [(buf.validate.field).duration.lt = "5s"];
    // }
    // ```
    google.protobuf.Duration lt = 3 [(priv.field).cel = {
      id: "duration.lt"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this >= rules.lt"
        "? 'value must be less than %s'.format([rules.lt]) : ''"
    }];

    // `lte` indicates that the field must be less than or equal to the specified
    // value of the `google.protobuf.Duration` type, inclusive. If the field's value is greater than the specified value,
    // an error message will be generated.
    //
    // ```proto
    // message MyDuration {
    //   // value must be less than or equal to 10s
    //   google.protobuf.Duration value = 1 [(buf.validate.field).duration.lte = "10s"];
    // }
    // ```
    google.protobuf.Duration lte = 4 [(priv.field).cel = {
      id: "duration.lte"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this > rules.lte"
        "? 'value must be less than or equal to %s'.format([rules.lte]) : ''"
    }];
  }
  oneof greater_than {
    // `gt` requires the duration field value to be greater than the specified
    // value (exclusive). If the value of `gt` is larger than a specified `lt`
    // or `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyDuration {
    //   // duration must be greater than 5s [duration.gt]
    //   google.protobuf.Duration value = 1 [(buf.validate.field).duration.gt = { seconds: 5 }];
    //
    //   // duration must be greater than 5s and less than 10s [duration.gt_lt]
    //   google.protobuf.Duration another_value = 2 [(buf.validate.field).duration = { gt: { seconds: 5 }, lt: { seconds: 10 } }];
    //
    //   // duration must be greater than 10s or less than 5s [duration.gt_lt_exclusive]
    //   google.protobuf.Duration other_value = 3 [(buf.validate.field).duration = { gt: { seconds: 10 }, lt: { seconds: 5 } }];
    // }
    // ```
    google.protobuf.Duration gt = 5 [
      (priv.field).cel = {
        id: "duration.gt"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this <= rules.gt"
          "? 'value must be greater than %s'.format([rules.gt]) : ''"
      },
      (priv.field).cel = {
        id: "duration.gt_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)"
          "? 'value must be greater than %s and less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "duration.gt_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)"
          "? 'value must be greater than %s or less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "duration.gt_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)"
          "? 'value must be greater than %s and less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "duration.gt_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)"
          "? 'value must be greater than %s or less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      }
    ];

    // `gte` requires the duration field value to be greater than or equal to the
    // specified value (exclusive). If the value of `gte` is larger than a
    // specified `lt` or `lte`, the range is reversed, and the field value must
    // be outside the specified range. If the field value doesn't meet the
    // required conditions, an error message is generated.
    //
    // ```proto
    // message MyDuration {
    //  // duration must be greater than or equal to 5s [duration.gte]
    //  google.protobuf.Duration value = 1 [(buf.validate.field).duration.gte = { seconds: 5 }];
    //
    //  // duration must be greater than or equal to 5s and less than 10s [duration.gte_lt]
    //  google.protobuf.Duration another_value = 2 [(buf.validate.field).duration = { gte: { seconds: 5 }, lt: { seconds: 10 } }];
    //
    //  // duration must be greater than or equal to 10s or less than 5s [duration.gte_lt_exclusive]
    //  google.protobuf.Duration other_value = 3 [(buf.validate.field).duration = { gte: { seconds: 10 }, lt: { seconds: 5 } }];
    // }
    // ```
    google.protobuf.Duration gte = 6 [
      (priv.field).cel = {
        id: "duration.gte"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this < rules.gte"
          "? 'value must be greater than or equal to %s'.format([rules.gte]) : ''"
      },
      (priv.field).cel = {
        id: "duration.gte_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "duration.gte_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "duration.gte_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "duration.gte_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      }
    ];
  }

  // `in` asserts that the field must be equal to one of the specified values of the `google.protobuf.Duration` type.
  // If the field's value doesn't correspond to any of the specified values,
  // an error message will be generated.
  //
  // ```proto
  // message MyDuration {
  //   // value must be in list [1s, 2s, 3s]
  //   google.protobuf.Duration value = 1 [(buf.validate.field).duration.in = ["1s", "2s", "3s"]];
  // }
  // ```
  repeated google.protobuf.Duration in = 7 [(priv.field).cel = {
    id: "duration.in"
    expression: "!(this in dyn(rules)['in']) ? 'value must be in list %s'.format([dyn(rules)['in']]) : ''"
  }];

  // `not_in` denotes that the field must not be equal to
  // any of the specified values of the `google.protobuf.Duration` type.
  // If the field's value matches any of these values, an error message will be
  // generated.
  //
  // ```proto
  // message MyDuration {
  //   // value must not be in list [1s, 2s, 3s]
  //   google.protobuf.Duration value = 1 [(buf.validate.field).duration.not_in = ["1s", "2s", "3s"]];
  // }
  // ```
  repeated google.protobuf.Duration not_in = 8 [(priv.field).cel = {
    id: "duration.not_in"
    expression: "this in rules.not_in ? 'value must not be in list %s'.format([rules.not_in]) : ''"
  }];
}

// TimestampRules describe the constraints applied exclusively to the `google.protobuf.Timestamp` well-known type.
message TimestampRules {
  // `const` dictates that this field, of the `google.protobuf.Timestamp` type, must exactly match the specified value. If the field value doesn't correspond to the specified timestamp, an error message will be generated.
  //
  // ```proto
  // message MyTimestamp {
  //   // value must equal 2023-05-03T10:00:00Z
  //   google.protobuf.Timestamp created_at = 1 [(buf.validate.field).timestamp.const = {seconds: 1727998800}];
  // }
  // ```
  optional google.protobuf.Timestamp const = 2 [(priv.field).cel = {
    id: "timestamp.const"
    expression: "this != rules.const ? 'value must equal %s'.format([rules.const]) : ''"
  }];
  oneof less_than {
    // requires the duration field value to be less than the specified value (field < value). If the field value doesn't meet the required conditions, an error message is generated.
    //
    // ```proto
    // message MyDuration {
    //   // duration must be less than 'P3D' [duration.lt]
    //   google.protobuf.Duration value = 1 [(buf.validate.field).duration.lt = { seconds: 259200 }];
    // }
    // ```
    google.protobuf.Timestamp lt = 3 [(priv.field).cel = {
      id: "timestamp.lt"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this >= rules.lt"
        "? 'value must be less than %s'.format([rules.lt]) : ''"
    }];

    // requires the timestamp field value to be less than or equal to the specified value (field <= value). If the field value doesn't meet the required conditions, an error message is generated.
    //
    // ```proto
    // message MyTimestamp {
    //   // timestamp must be less than or equal to '2023-05-14T00:00:00Z' [timestamp.lte]
    //   google.protobuf.Timestamp value = 1 [(buf.validate.field).timestamp.lte = { seconds: 1678867200 }];
    // }
    // ```
    google.protobuf.Timestamp lte = 4 [(priv.field).cel = {
      id: "timestamp.lte"
      expression:
        "!has(rules.gte) && !has(rules.gt) && this > rules.lte"
        "? 'value must be less than or equal to %s'.format([rules.lte]) : ''"
    }];

    // `lt_now` specifies that this field, of the `google.protobuf.Timestamp` type, must be less than the current time. `lt_now` can only be used with the `within` rule.
    //
    // ```proto
    // message MyTimestamp {
    //  // value must be less than now
    //   google.protobuf.Timestamp created_at = 1 [(buf.validate.field).timestamp.lt_now = true];
    // }
    // ```
    bool lt_now = 7 [(priv.field).cel = {
      id: "timestamp.lt_now"
      expression: "this > now ? 'value must be less than now' : ''"
    }];
  }
  oneof greater_than {
    // `gt` requires the timestamp field value to be greater than the specified
    // value (exclusive). If the value of `gt` is larger than a specified `lt`
    // or `lte`, the range is reversed, and the field value must be outside the
    // specified range. If the field value doesn't meet the required conditions,
    // an error message is generated.
    //
    // ```proto
    // message MyTimestamp {
    //   // timestamp must be greater than '2023-01-01T00:00:00Z' [timestamp.gt]
    //   google.protobuf.Timestamp value = 1 [(buf.validate.field).timestamp.gt = { seconds: 1672444800 }];
    //
    //   // timestamp must be greater than '2023-01-01T00:00:00Z' and less than '2023-01-02T00:00:00Z' [timestamp.gt_lt]
    //   google.protobuf.Timestamp another_value = 2 [(buf.validate.field).timestamp = { gt: { seconds: 1672444800 }, lt: { seconds: 1672531200 } }];
    //
    //   // timestamp must be greater than '2023-01-02T00:00:00Z' or less than '2023-01-01T00:00:00Z' [timestamp.gt_lt_exclusive]
    //   google.protobuf.Timestamp other_value = 3 [(buf.validate.field).timestamp = { gt: { seconds: 1672531200 }, lt: { seconds: 1672444800 } }];
    // }
    // ```
    google.protobuf.Timestamp gt = 5 [
      (priv.field).cel = {
        id: "timestamp.gt"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this <= rules.gt"
          "? 'value must be greater than %s'.format([rules.gt]) : ''"
      },
      (priv.field).cel = {
        id: "timestamp.gt_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gt && (this >= rules.lt || this <= rules.gt)"
          "? 'value must be greater than %s and less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "timestamp.gt_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gt && (rules.lt <= this && this <= rules.gt)"
          "? 'value must be greater than %s or less than %s'.format([rules.gt, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "timestamp.gt_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gt && (this > rules.lte || this <= rules.gt)"
          "? 'value must be greater than %s and less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "timestamp.gt_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gt && (rules.lte < this && this <= rules.gt)"
          "? 'value must be greater than %s or less than or equal to %s'.format([rules.gt, rules.lte]) : ''"
      }
    ];

    // `gte` requires the timestamp field value to be greater than or equal to the
    // specified value (exclusive). If the value of `gte` is larger than a
    // specified `lt` or `lte`, the range is reversed, and the field value
    // must be outside the specified range. If the field value doesn't meet
    // the required conditions, an error message is generated.
    //
    // ```proto
    // message MyTimestamp {
    //   // timestamp must be greater than or equal to '2023-01-01T00:00:00Z' [timestamp.gte]
    //   google.protobuf.Timestamp value = 1 [(buf.validate.field).timestamp.gte = { seconds: 1672444800 }];
    //
    //   // timestamp must be greater than or equal to '2023-01-01T00:00:00Z' and less than '2023-01-02T00:00:00Z' [timestamp.gte_lt]
    //   google.protobuf.Timestamp another_value = 2 [(buf.validate.field).timestamp = { gte: { seconds: 1672444800 }, lt: { seconds: 1672531200 } }];
    //
    //   // timestamp must be greater than or equal to '2023-01-02T00:00:00Z' or less than '2023-01-01T00:00:00Z' [timestamp.gte_lt_exclusive]
    //   google.protobuf.Timestamp other_value = 3 [(buf.validate.field).timestamp = { gte: { seconds: 1672531200 }, lt: { seconds: 1672444800 } }];
    // }
    // ```
    google.protobuf.Timestamp gte = 6 [
      (priv.field).cel = {
        id: "timestamp.gte"
        expression:
          "!has(rules.lt) && !has(rules.lte) && this < rules.gte"
          "? 'value must be greater than or equal to %s'.format([rules.gte]) : ''"
      },
      (priv.field).cel = {
        id: "timestamp.gte_lt"
        expression:
          "has(rules.lt) && rules.lt >= rules.gte && (this >= rules.lt || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "timestamp.gte_lt_exclusive"
        expression:
          "has(rules.lt) && rules.lt < rules.gte && (rules.lt <= this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than %s'.format([rules.gte, rules.lt]) : ''"
      },
      (priv.field).cel = {
        id: "timestamp.gte_lte"
        expression:
          "has(rules.lte) && rules.lte >= rules.gte && (this > rules.lte || this < rules.gte)"
          "? 'value must be greater than or equal to %s and less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      },
      (priv.field).cel = {
        id: "timestamp.gte_lte_exclusive"
        expression:
          "has(rules.lte) && rules.lte < rules.gte && (rules.lte < this && this < rules.gte)"
          "? 'value must be greater than or equal to %s or less than or equal to %s'.format([rules.gte, rules.lte]) : ''"
      }
    ];

    // `gt_now` specifies that this field, of the `google.protobuf.Timestamp` type, must be greater than the current time. `gt_now` can only be used with the `within` rule.
    //
    // ```proto
    // message MyTimestamp {
    //   // value must be greater than now
    //   google.protobuf.Timestamp created_at = 1 [(buf.validate.field).timestamp.gt_now = true];
    // }
    // ```
    bool gt_now = 8 [(priv.field).cel = {
      id: "timestamp.gt_now"
      expression: "this < now ? 'value must be greater than now' : ''"
    }];
  }

  // `within` specifies that this field, of the `google.protobuf.Timestamp` type, must be within the specified duration of the current time. If the field value isn't within the duration, an error message is generated.
  //
  // ```proto
  // message MyTimestamp {
  //   // value must be within 1 hour of now
  //   google.protobuf.Timestamp created_at = 1 [(buf.validate.field).timestamp.within = {seconds: 3600}];
  // }
  // ```
  optional google.protobuf.Duration within = 9 [(priv.field).cel = {
    id: "timestamp.within"
    expression: "this < now-rules.within || this > now+rules.within ? 'value must be within %s of now'.format([rules.within]) : ''"
  }];
}
