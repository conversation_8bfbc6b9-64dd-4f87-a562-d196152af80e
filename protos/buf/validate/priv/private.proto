// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package buf.validate.priv;

import "google/protobuf/descriptor.proto";

option go_package = "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate/priv";
option java_multiple_files = true;
option java_outer_classname = "PrivateProto";
option java_package = "build.buf.validate.priv";

extend google.protobuf.FieldOptions {
  // Do not use. Internal to protovalidate library
  optional FieldConstraints field = 1160;
}

// Do not use. Internal to protovalidate library
message FieldConstraints {
  repeated Constraint cel = 1;
}

// Do not use. Internal to protovalidate library
message Constraint {
  string id = 1;
  string message = 2;
  string expression = 3;
}
