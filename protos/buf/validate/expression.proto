// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package buf.validate;

option go_package = "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate";
option java_multiple_files = true;
option java_outer_classname = "ExpressionProto";
option java_package = "build.buf.validate";

// `Constraint` represents a validation rule written in the Common Expression
// Language (CEL) syntax. Each Constraint includes a unique identifier, an
// optional error message, and the CEL expression to evaluate. For more
// information on CEL, [see our documentation](https://github.com/bufbuild/protovalidate/blob/main/docs/cel.md).
//
// ```proto
// message Foo {
//   option (buf.validate.message).cel = {
//     id: "foo.bar"
//     message: "bar must be greater than 0"
//     expression: "this.bar > 0"
//   };
//   int32 bar = 1;
// }
// ```
message Constraint {
  // `id` is a string that serves as a machine-readable name for this Constraint.
  // It should be unique within its scope, which could be either a message or a field.
  string id = 1;

  // `message` is an optional field that provides a human-readable error message
  // for this Constraint when the CEL expression evaluates to false. If a
  // non-empty message is provided, any strings resulting from the CEL
  // expression evaluation are ignored.
  string message = 2;

  // `expression` is the actual CEL expression that will be evaluated for
  // validation. This string must resolve to either a boolean or a string
  // value. If the expression evaluates to false or a non-empty string, the
  // validation is considered failed, and the message is rejected.
  string expression = 3;
}

// `Violations` is a collection of `Violation` messages. This message type is returned by
// protovalidate when a proto message fails to meet the requirements set by the `Constraint` validation rules.
// Each individual violation is represented by a `Violation` message.
message Violations {
  // `violations` is a repeated field that contains all the `Violation` messages corresponding to the violations detected.
  repeated Violation violations = 1;
}

// `Violation` represents a single instance where a validation rule, expressed
// as a `Constraint`, was not met. It provides information about the field that
// caused the violation, the specific constraint that wasn't fulfilled, and a
// human-readable error message.
//
// ```json
// {
//   "fieldPath": "bar",
//   "constraintId": "foo.bar",
//   "message": "bar must be greater than 0"
// }
// ```
message Violation {
  // `field_path` is a machine-readable identifier that points to the specific field that failed the validation.
  // This could be a nested field, in which case the path will include all the parent fields leading to the actual field that caused the violation.
  string field_path = 1;

  // `constraint_id` is the unique identifier of the `Constraint` that was not fulfilled.
  // This is the same `id` that was specified in the `Constraint` message, allowing easy tracing of which rule was violated.
  string constraint_id = 2;

  // `message` is a human-readable error message that describes the nature of the violation.
  // This can be the default error message from the violated `Constraint`, or it can be a custom message that gives more context about the violation.
  string message = 3;

  // `for_key` indicates whether the violation was caused by a map key, rather than a value.
  bool for_key = 4;
}
