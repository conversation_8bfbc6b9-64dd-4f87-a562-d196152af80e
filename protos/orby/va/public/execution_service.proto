syntax = "proto3";

package orby.va.public;

import "orby/va/public/execution_messages.proto";

// ExecutionService manages the lifecycle of workflow executions.
service ExecutionService {
  // Starts a new workflow execution.
  rpc StartExecution(StartExecutionRequest) returns (StartExecutionResponse);

  // Cancels a running workflow execution.
  rpc CancelExecution(CancelExecutionRequest) returns (CancelExecutionResponse);

  // Streams real-time events for a given execution.
  rpc GetExecutionEvents(GetExecutionEventsRequest) 
    returns (stream ExecutionEvent);

  // Submit review request
  rpc RequestReview(RequestReviewRequest) returns (RequestReviewResponse);

  // Mark review as completed
  rpc MarkReviewCompleted(ReviewCompletedRequest) 
    returns (ReviewCompletedResponse);

  // Get review status
  rpc GetReviewStatus(GetReviewStatusRequest) returns (GetReviewStatusResponse);

  // Update execution
  rpc UpdateExecution(UpdateExecutionRequest) returns (Execution);

  // Get paginated Execution logs
  rpc GetExecutionLogs(GetExecutionLogsRequest) 
    returns (GetExecutionLogsResponse);

  // Append execution log
  rpc AppendExecutionLog(AppendExecutionLogRequest) 
    returns (AppendExecutionLogResponse);
}
