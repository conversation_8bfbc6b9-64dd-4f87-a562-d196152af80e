syntax = "proto3";

package orby.va;

import "google/protobuf/empty.proto";
import "orby/va/execution_management.proto";
import "orby/va/public/execution_messages.proto";


service ExecutionManagementService {
  // Get a workflow execution
  rpc GetExecution(GetExecutionRequest) returns (orby.va.public.Execution);

  // Delete a workflow execution
  rpc DeleteExecution(DeleteExecutionRequest) returns (google.protobuf.Empty);

  // List executions
  rpc ListExecutions(ListExecutionsRequest) returns (ListExecutionsResponse);
}
