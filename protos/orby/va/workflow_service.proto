syntax = "proto3";

package orby.va;

import "google/protobuf/empty.proto";
import "orby/va/workflow.proto";

service WorkflowService {
  // Lists workflows with filtering and pagination
  rpc ListWorkflows(ListWorkflowsRequest) returns (ListWorkflowsResponse);
  
  // Creates a new workflow record for chat session tracking
  rpc CreateWorkflow(CreateWorkflowRequest) returns (Workflow);
  
  // Gets a single workflow by ID
  rpc GetWorkflow(GetWorkflowRequest) returns (Workflow);
  
  // Updates workflow with all final data when complete and ready for listing
  rpc UpdateWorkflow(UpdateWorkflowRequest) returns (Workflow);
  
  // Deletes a workflow
  rpc DeleteWorkflow(DeleteWorkflowRequest) returns (google.protobuf.Empty);

  // Gets all workflow creators for dropdown
  rpc GetWorkflowCreators(google.protobuf.Empty) returns (GetWorkflowCreatorsResponse);
}
