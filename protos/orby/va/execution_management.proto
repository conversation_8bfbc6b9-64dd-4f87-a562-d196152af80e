syntax = "proto3";

package orby.va;

import "orby/va/public/execution_messages.proto";

// Request to get a specific workflow execution.
message GetExecutionRequest {
  string execution_id = 1;
}

// Request to delete a workflow execution.
message DeleteExecutionRequest {
  string execution_id = 1;
}

// Request to list workflow executions.
message ListExecutionsRequest {
  string workflow_id = 1;
  // Default is 10 (when page_size is missing or set to 0). Max value is 20
  int32 page_size = 2;
  // Page number - indexed from 1
  int32 page_number = 3;
}

// Response containing a list of executions.
message ListExecutionsResponse {
  repeated orby.va.public.Execution executions = 1;
  // Total available executions size
  int32 total_size = 2;
}
