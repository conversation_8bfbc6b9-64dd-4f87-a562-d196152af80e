syntax = "proto3";

package orby.va;

import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

enum TaskStatus {
  TASK_STATUS_UNSPECIFIED = 0;
  TASK_STATUS_PENDING = 1;
  TASK_STATUS_FAILED = 2;
  TASK_STATUS_COMPLETED = 3;
}

message TaskUsers {
  string creator_id = 1;
}

message TaskTimestamps {
  google.protobuf.Timestamp created_at = 1;
  google.protobuf.Timestamp updated_at = 2;
}

message Task {
  string id = 1;
  string org_id = 2;
  string workflow_id = 3;
  // client-generated key
  string identify_key = 4;
  string execution_id = 5;
  string display_name = 6;
  string description = 7;
  TaskStatus status = 8;
  TaskUsers users = 9;
  TaskTimestamps timestamps = 10;
}

// Filter message for task listing
message ListTasksFilter {
  TaskStatus status = 1;
}

message ListTasksRequest {
  // Default is 10 (when page_size is missing or set to 0). Max value is 20
  int32 page_size = 1;

  // Page number - indexed from 1
  int32 page_number = 2;

  // Structured filter for tasks
  ListTasksFilter filter = 3;
}

message ListTasksResponse {
  repeated Task tasks = 1;
  
  // Total available tasks size
  int32 total_size = 2;
}

message UpdateTaskRequest {
  string id = 1;
  google.protobuf.FieldMask field_mask = 2;
  TaskStatus status = 3;
}

message DeleteTaskRequest {
  string id = 1;
}
