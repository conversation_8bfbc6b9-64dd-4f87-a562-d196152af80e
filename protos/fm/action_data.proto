syntax = "proto3";

package fm;

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "pb/v1alpha1/element.proto";
import "pb/v1alpha1/orbot_action.proto";
import "pb/v1alpha1/document.proto";
import "fm/llm_data.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/fm";

// Tentative, information about the decision made by the agent.
message AgentState {
  repeated fm.LLMInteraction llm_interactions = 1; // the list of queries made by the agent
  string memory = 2; // the memory of the agent, can be a string or a jsonified object
  AgentState previous_failed_agent_state = 3; // the linked list of agent state that did not get grounded into an action at this step
}

message Viewport {
  pb.v1alpha1.DocumentBlob screenshot = 1; // the viewport screenshot
  pb.v1alpha1.Rect viewport_rect = 2; // the "bounding box" of the viewport
}

// The raw representation of a web page observation from BrowserGym.
// Fields overlapping with existing fields in WebState are removed.
message BrowserGymObservation {
  // BrowserGym environment output
  // See https://github.com/orby-ai-engineering/digital-agent/blob/d9d62cce50828d2628ea2c1334824165e2a352ee/scripts/test_run.py#L104
  float reward = 1; // the reward of the current state from BrowserGym
  bool terminated = 2; // Whether this is a terminal state
  bool truncated = 3; // Whether the observation is truncated

  message ChatMessage {
    string role = 1; // the identity of entity sending the message
    google.protobuf.Timestamp timestamp = 2; // the timestamp of the message; from python time.time()
    string message = 3; // the content of the message
  }
  repeated ChatMessage chat_messages = 4; // the chat messages observed at this state

  string legacy_goal = 5; // goal represented as a string; deprecated in favor of goals by BrowserGym
  repeated fm.LLMContent goals = 6; // the goals the agent needs to reach in the environment
  repeated string open_pages_urls = 7; // the URLs of the open pages in the browser
  int32 active_page_index = 8; // the indices of the open pages in the browser
  string dom = 9; // the DOMTree representation of the page from BrowserGym, encoded by json.dumps(dom_object)
  string axtree = 10; // the accessibility tree of the page from BrowserGym, encoded by json.dumps(axtree_object)

  // The extra properties on each element with a browsergym_id (bid) padded by BrowserGym,
  // Created by browsergym.core.src.browsergym.core.obeservation.extract_dom_extra_properties
  // Extra information include:
  // - absolute x and position of the frame node in the parent
  // - frame's absolute position
  // - browsergym_id of each element
  // - visibility of each element
  // - bounding box of each element
  // - whether the element is clickable
  // - set_of_marks ? of each element
  // Encoded by json.dumps(extra_element_properties)
  string extra_element_properties = 11;

  string focused_element_bid = 12; // the browser gym ID of the focused element in the DOMTree, or null if no element is focused
  string last_action = 13; // the last action taken by the agent
  string last_action_error = 14; // the error message of the last action
  google.protobuf.Duration elapsed_time = 15; // the elapsed time from the start of the episode
}

// Information about the current state of the web page.
message WebState {
  string fingerprint = 1; // the fingerprint of the page generated as a random hash (uuid.uuid4()[:SOME_CUTOFF])
  string url = 2; // the URL of the page
  string html = 3;  // the HTML content of the page
  Viewport viewport = 4; // the viewport of the page
  pb.v1alpha1.Element root_element = 5 [deprecated=true];  // the dom tree of the page; deprecated in favor of root_element_wrapper
  BrowserGymObservation browser_gym_observation = 6; // the raw representation of a web page observation from BrowserGym
  pb.v1alpha1.ElementWrapper root_element_wrapper = 7; // the dom tree of the page in the form of an ElementWrapper
}

// The raw representation of an action from BrowserGym.
message BrowserGymAction {
  string action_string = 1; // the raw string representation of the action
}

// Used by FM crawler to represent the action taken by the FM agent.
message ActionData {
  string id = 1; // the ID of the action generated as a random hash (uuid.uuid4()[:SOME_CUTOFF])
  string base_url = 2 [deprecated=true]; // the URL where the action was taken; deprecated in favor of domain
  pb.v1alpha1.Action action = 3; // the action taken by the agent
  Decision decision = 4 [deprecated=true]; // the decision made by the agent; deprecated in favor of agent_state
  WebState before_state = 5; // the state of the page before the action
  WebState after_state = 6; // the state of the page after the action
  bytes playwright_trace = 7; // the trace of the action, encoded by python pickle.dumps(trace)
  string domain = 8; // the domain name of the URL where the action was taken
  BrowserGymAction browser_gym_action = 9; // the raw representation of an action from BrowserGym
  AgentState agent_state = 10; // the state of the agent after the action
}

message Decision {
  string method = 1; // the method used to make the decision (heuristic, agentic, etc.)
  string high_level_task = 2; // the high-level task that the decision is made for
  string low_level_task = 3; // the low-level task that the decision is made for
  string action_description = 4; // the description of the action that the decision is made for
  string nl_output = 5; // the natural language output of the agent
  repeated string failed_decisions = 6; // the list of failed decisions that the agent made THIS TURN
  pb.v1alpha1.Action action = 7; // the action that the agent decided to take
}
