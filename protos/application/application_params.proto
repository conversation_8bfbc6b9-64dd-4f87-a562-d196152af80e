syntax = "proto3";

package application;

import "automation_mining/ontology/data_models.proto";

option go_package = "github.com/orby-ai-engineering/web-api-server/pb/application";

message GDriveParam {
    oneof config {
        Trigger trigger = 1;
        Action action = 2;
    } 
    message Trigger {
        automation_mining.ontology.File file = 1;  // Cover GDrive folders and files.
    
        enum TriggerType {
            TRIGGER_TYPE_UNSPECIFIED = 0;
            TRIGGER_TYPE_NEW_FILE = 1;
        }
        TriggerType type = 2;
        string owner_email = 3;
    }

    message Action {}
}

// Do not use: For backward compatibility, will be removed once all logic is migrated to EmailParam.
message GmailParam {
    oneof config {
        Trigger trigger = 1;
        Action action = 2;
    } 
    message Trigger {
        automation_mining.ontology.Email email = 1;
        enum TriggerType {
            TRIGGER_TYPE_UNSPECIFIED = 0;
            TRIGGER_TYPE_NEW_EMAIL_BODY_ONLY = 1;
            TRIGGER_TYPE_NEW_EMAIL_ATTACHMENT_ONLY = 2;
            TRIGGER_TYPE_NEW_EMAIL_BODY_AND_ATTACHMENT = 3;
        }
        TriggerType type = 2; 
        string owner_email = 3;
        // labels helps in watching on emails of particular kind. 
        // If user wants to watch on all emails in an inbox, labels field needs to contain "inbox" 
        repeated automation_mining.ontology.GmailLabel labels = 4;
    }

    message Action {}
}

message EmailParam {
    oneof config {
        Trigger trigger = 1;
        Action action = 2;
    }
    message Trigger {
        automation_mining.ontology.Email email = 1;
        enum TriggerType {
            TRIGGER_TYPE_UNSPECIFIED = 0;
            TRIGGER_TYPE_NEW_EMAIL_BODY_ONLY = 1;
            TRIGGER_TYPE_NEW_EMAIL_ATTACHMENT_ONLY = 2;
            TRIGGER_TYPE_NEW_EMAIL_BODY_AND_ATTACHMENT = 3;
        }
        TriggerType type = 2;
        string owner_email = 3;
        // labels helps in watching on emails of particular kind.
        // If user wants to watch on all emails in an inbox, labels field needs to contain "inbox"
        repeated automation_mining.ontology.EmailLabel labels = 4;
        // MicrosoftConfig is used to watch on emails from Microsoft Outlook 
        // using application login (where we do not have user's microsoft token
        // and we need to use token for the tenant's application)
        // If this is not set, the email will be watched using user's microsoft token
        optional MicrosoftConfig microsoft_config = 5;
    }

    message Action {}
}

message MicrosoftConfig {
    // Tenant Id of the microsoft org of which the user is a part of
    string microsoft_tenant_id = 1;
    // Microsoft Entra user Id of the user who's microsoft account is 
    // being used without SSO login
    string microsoft_user_id = 2;
    // Microsoft Application Config Id of the microsoft application that is
    // being used to watch on emails
    string microsoft_application_config_id = 3;
}

message GSheetsParam {
    oneof config {
        Trigger trigger = 1;
        Action action = 2;
    } 
    message Trigger {}
    
    message Action {
        automation_mining.ontology.Sheets sheets = 1;
        enum ActionType {
            ACTION_TYPE_UNSPECIFIED = 0;
            ACTION_TYPE_ADD_ROW = 1;
        }
        ActionType type = 2;

        // all these below fields are outdated and should not be used.
        oneof action_option {
            AddRowOption add_row_option = 3;
        }

        message AddRowOption { 
            repeated string mapping_columns = 1;
            int32 exampleDocCount = 2 [deprecated=true]; // Moved this field to EntityExtractionParam
        }
    }
}

// GSheetsParam will be deprecated in favor of 
// SpreadsheetParam in the future to have a more generic name
// to support other spreadsheet applications
message SpreadsheetParam {
    oneof config {
        Trigger trigger = 1;
        Action action = 2;
    } 
    message Trigger {}
    
    message Action {
        automation_mining.ontology.Sheets sheets = 1;
        enum ActionType {
            ACTION_TYPE_UNSPECIFIED = 0;
            ACTION_TYPE_ADD_ROW = 1;
        }
        ActionType type = 2;

        // MicrosoftConfig is used to use Microsoft Excel 
        // using application login (where we do not have user's microsoft token
        // and we need to use token for the tenant's application)
        // If this is not set, the we will use user's microsoft token
        optional MicrosoftConfig microsoft_config = 3;
    }
}

message SFTPParam {
    oneof config {
        Trigger trigger = 1;
        Action action = 2;
    } 
    message Trigger {
        // trigger folder is where the client should put the files
        // The folder path will always be of the format <org_name>/<workflow_name>
        // This field is not editable by the user and is set by the FE and it should show 
        // an instruction that the user should put file under the folder <workflow_name>
        // to start to create tasks.
        string folder_path = 1;
    
        enum TriggerType {
            TRIGGER_TYPE_UNSPECIFIED = 0;
            TRIGGER_TYPE_NEW_OBJECT = 1;
        }
        TriggerType type = 2;
    }

    message Action {}
}
