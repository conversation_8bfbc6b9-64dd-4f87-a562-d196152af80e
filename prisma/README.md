# Prisma Migrations

1. Make the schema in schema.prisma file
2. Set the env variable in the current terminal

```bash
export DATABASE_URL=postgresql://postgres:<password>@<instance_ip>:5432/<db_name>
```

3. Run the migration by giving a name for the migration

```bash
npx prisma migrate dev --name <migration_name>
```

4. The migration SQL file is automatically generated with this command. Once this is generated, the migration will also be applied automatically.
5. Validate in the Cloud SQL instance

# How to setup prisma

Install the following packages globally

```bash
npm install -g @prisma/client prisma
```
