# Check if an image with the current GITHUB_SHA already exists
existing_image=$(gcloud artifacts docker images list us-docker.pkg.dev/$PROJECT_ID/$REPOSITORY/$IMAGE --include-tags --filter=$GITHUB_SHA)

if [[ ! -z "$existing_image" ]]; then
    echo "Server image already built"
    exit 0  
else
    echo "No existing image found, proceeding with build..."

    docker buildx build \
      --file $DOCKERFILE_PATH \
      --cache-from=type=registry,ref=us-docker.pkg.dev/$PROJECT_ID/$REPOSITORY/$IMAGE:$CACHE_TAG \
      --cache-to=type=registry,ref=us-docker.pkg.dev/$PROJECT_ID/$REPOSITORY/$IMAGE:$CACHE_TAG,mode=max \
      --push \
      --tag us-docker.pkg.dev/$PROJECT_ID/$REPOSITORY/$IMAGE:$GITHUB_SHA .
fi
