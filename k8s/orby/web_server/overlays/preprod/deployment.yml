apiVersion: apps/v1
kind: Deployment
metadata:
  name: va-web-server-deployment
spec:
  template:
    spec:
      containers:
        - name: http-server
          env:
            - name: MODE
              value: preprod
            - name: CHAINLIT_DEFAULT_BUCKET
              value: "va_chainlit_data_preprod"
            - name: GOOGLE_CLOUD_PROJECT_ID
              value: orby-ai-backend
            - name: LANGSMITH_PROJECT
              value: "vibe-automation-server-dev" # TODO: Change to the correct project
            - name: LANGSMITH_TRACING
              value: "true"
        - name: grpc-server
          env:
            - name: MODE
              value: preprod
            - name: GOOGLE_CLOUD_PROJECT_ID
              value: orby-ai-backend
