apiVersion: apps/v1
kind: Deployment
metadata:
  name: va-web-server-deployment
  labels:
    app: va-web-server
spec:
  replicas: 9
  selector:
    matchLabels:
      app: va-web-server
  template:
    metadata:
      labels:
        app: va-web-server
    spec:
      containers:
        - name: http-server
          image: LOCATION-docker.pkg.dev/PROJECT_ID/REPOSITORY/IMAGE:TAG
          command: ['uv']
          args: ['run', 'web_server']
          ports:
            - name: http
              containerPort: 8080
          env:
            - name: DEFAULT_SERVICE_ACCOUNT
              value: '<EMAIL>'
            - name: CHAINLIT_DEFAULT_BUCKET
              value: 'va_chainlit_data_dev'
            - name: DEFAULT_DB_NAME
              value: 'dev' # across all env, default mongo db name is dev
            - name: GOOGLE_CLOUD_PROJECT_ID
              value: 'orby-ai-backend-dev'
            - name: UVICORN_PORT
              value: '8080'
            - name: LANGSMITH_PROJECT
              value: 'vibe-automation-server'
            - name: LANGSMITH_ENDPOINT
              value: 'https://api.smith.langchain.com'
            - name: LANGSMITH_TRACING
              value: 'true'
            - name: 'MCP_SERVERS'
              value: '[{"name": "playwright", "type": "stdio", "command": "npx", "args": ["@playwright/mcp@latest"]}]'
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: openai-api-key
                  key: apikey
            - name: GOOGLE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: gemini-api-key
                  key: apikey
            - name: GITHUB_TOKEN
              valueFrom:
                secretKeyRef:
                  name: github-token
                  key: token
            - name: MONGODB_URI
              valueFrom:
                secretKeyRef:
                  name: mongodb-uri
                  key: mongodb_uri
            - name: POSTGRES_URI
              valueFrom:
                secretKeyRef:
                  name: postgres-uri
                  key: postgres_uri
            - name: ANTHROPIC_API_KEY
              valueFrom:
                secretKeyRef:
                  name: anthropic
                  key: apikey
            - name: REDIS_URL
              valueFrom:
                secretKeyRef:
                  name: redis-credentials
                  key: url
            - name: REDIS_STREAM_URL
              valueFrom:
                secretKeyRef:
                  name: redis-credentials
                  key: stream_url
            - name: BB_API_KEY
              valueFrom:
                secretKeyRef:
                  name: browserbase-secrets
                  key: api_key
            - name: BB_PROJECT_ID
              valueFrom:
                secretKeyRef:
                  name: browserbase-secrets
                  key: project_id
            - name: E2B_API_KEY
              valueFrom:
                secretKeyRef:
                  name: e2b-secrets
                  key: api_key
            - name: LANGSMITH_API_KEY
              valueFrom:
                secretKeyRef:
                  name: langsmith-api-key
                  key: apikey
          securityContext:
            allowPrivilegeEscalation: false
            runAsNonRoot: true
            capabilities:
              drop:
                - all
          resources:
            requests:
              cpu: 1000m
              memory: 2Gi
        - name: grpc-server
          image: LOCATION-docker.pkg.dev/PROJECT_ID/REPOSITORY/IMAGE:TAG
          command: ['uv']
          args: ['run', 'execution']
          ports:
            - name: grpc
              containerPort: 9090
            - name: metrics
              containerPort: 9091
          env:
            - name: LANGSMITH_PROJECT
              value: 'vibe-automation-server'
            - name: LANGSMITH_ENDPOINT
              value: 'https://api.smith.langchain.com'
            - name: LANGSMITH_TRACING
              value: 'true'
            - name: DEFAULT_SERVICE_ACCOUNT
              value: '<EMAIL>'
            - name: GCS_AWS_SERVICE_ACCOUNT_EMAIL
              valueFrom:
                secretKeyRef:
                  name: gcs-aws-service-account
                  key: email
            - name: AWS_CLUSTER
              valueFrom:
                secretKeyRef:
                  name: aws-secrets
                  key: cluster
            - name: AWS_ROLE_ARN
              valueFrom:
                secretKeyRef:
                  name: aws-secrets
                  key: role_arn
            - name: AWS_ARTIFACT_REPO_NAME
              valueFrom:
                secretKeyRef:
                  name: aws-secrets
                  key: artifact_repo_name
            - name: AWS_ACCOUNT_ID
              valueFrom:
                secretKeyRef:
                  name: aws-secrets
                  key: account_id
            - name: BB_API_KEY
              valueFrom:
                secretKeyRef:
                  name: browserbase-secrets
                  key: api_key
            - name: BB_PROJECT_ID
              valueFrom:
                secretKeyRef:
                  name: browserbase-secrets
                  key: project_id
            - name: LANGSMITH_API_KEY
              valueFrom:
                secretKeyRef:
                  name: langsmith-api-key
                  key: apikey
            - name: REDIS_URL
              valueFrom:
                secretKeyRef:
                  name: redis-credentials
                  key: url
            - name: REDIS_STREAM_URL
              valueFrom:
                secretKeyRef:
                  name: redis-credentials
                  key: stream_url
          securityContext:
            allowPrivilegeEscalation: false
            runAsNonRoot: true
            capabilities:
              drop:
                - all
          resources:
            requests:
              cpu: 500m
              memory: 500Mi
      tolerations:
        - key: 'va-web-server'
          operator: 'Exists'
          effect: 'NoSchedule'
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              preference:
                matchExpressions:
                  - key: va-web-server
                    operator: Exists
