# This is the envoy config for the local development environment.
# It's replica of the envoy.local.yaml file in the web-server repo, to avoid the need to run the web-server repo.
admin:
  access_log_path: /tmp/admin_access.log
  address:
    socket_address: { address: 0.0.0.0, port_value: 9902 }

static_resources:
  listeners:
    - name: listener_0
      address:
        socket_address: { address: 0.0.0.0, port_value: 8099 }
      filter_chains:
        - filters:
            - name: envoy.filters.network.http_connection_manager
              typed_config:
                '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                codec_type: auto
                stat_prefix: ingress_http
                route_config:
                  name: local_route
                  virtual_hosts:
                    - name: local_service
                      domains: ['*']
                      routes:
                        - match: { prefix: '/pb.orby_internal' }
                          route:
                            cluster: web_api_orby_internal_grpc
                            timeout: 0s
                            max_stream_duration:
                              grpc_timeout_header_max: 0s
                        - match:
                            prefix: '/api/v1/va'
                          route:
                            cluster: va-web-api-server-cluster
                            timeout: 0s
                            max_stream_duration:
                              grpc_timeout_header_max: 0s
                        - match: { prefix: '/pb.v1alpha2' }
                          route:
                            cluster: web_api_v1alpha2_grpc
                            timeout: 0s
                            max_stream_duration:
                              grpc_timeout_header_max: 0s
                        - match: { prefix: '/v1alpha2' }
                          route:
                            cluster: web_api_v1alpha2_http
                            timeout: 0s
                            max_stream_duration:
                              grpc_timeout_header_max: 0s
                        - match: { prefix: '/saml' }
                          route:
                            cluster: web_api_http
                            timeout: 0s
                            max_stream_duration:
                              grpc_timeout_header_max: 0s
                        - match: { prefix: '/auth' }
                          route:
                            cluster: web_api_http
                            timeout: 0s
                            max_stream_duration:
                              grpc_timeout_header_max: 0s
                        - match: { prefix: '/oauth2' }
                          route:
                            cluster: web_api_http
                            timeout: 0s
                            max_stream_duration:
                              grpc_timeout_header_max: 0s
                        - match: { prefix: '/ws-server/v1alpha1' }
                          route:
                            cluster: ws-web-api-server-v1alpha1-cluster
                            upgrade_configs:
                              - upgrade_type: websocket
                            timeout: 0s
                            max_stream_duration:
                              grpc_timeout_header_max: 0s
                        - match: { prefix: '/pb.v1alpha1' }
                          route:
                            cluster: web_api
                            timeout: 0s
                            max_stream_duration:
                              grpc_timeout_header_max: 0s
                        - match: { prefix: '/pb.v1' }
                          route:
                            cluster: web_api_v1_grpc
                            timeout: 0s
                            max_stream_duration:
                              grpc_timeout_header_max: 0s
                        - match: { prefix: '/v1/' }
                          route:
                            cluster: web_api_v1_http
                            timeout: 0s
                            max_stream_duration:
                              grpc_timeout_header_max: 0s
                        - match: { prefix: '/' }
                          route:
                            cluster: web_api
                            timeout: 0s
                            max_stream_duration:
                              grpc_timeout_header_max: 0s
                      cors:
                        allow_origin_string_match:
                          safe_regex:
                            regex: \*
                        allow_methods: GET, PUT, DELETE, POST, OPTIONS
                        allow_headers: authorization,keep-alive,user-agent,cache-control,content-type,content-transfer-encoding,x-accept-content-transfer-encoding,x-accept-response-streaming,x-user-agent,x-grpc-web,grpc-timeout,Access-Control-Allow-Origin,referer,origin,sec-fetch-mode,connection,accept-encoding,accept,accept-language,host,cookie,Access-Control-Allow-Credentials,token
                        max_age: '1728000'
                        expose_headers: custom-header-1,Set-Cookie,grpc-status,grpc-message
                        allow_credentials: true
                http_filters:
                  - name: envoy.filters.http.grpc_web
                    typed_config:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.grpc_web.v3.GrpcWeb
                  - name: envoy.filters.http.cors
                    typed_config:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.cors.v3.Cors
                  - name: envoy.filters.http.router
                    typed_config:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
          # This is added to enable TLS when running gRPC server locally
          # The certificate and key are generated using mkcert
          transport_socket:
            name: envoy.transport_sockets.tls
            typed_config:
              '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.DownstreamTlsContext
              common_tls_context:
                tls_certificates:
                  - certificate_chain: { filename: 'cert/localhost+1.pem' }
                    private_key: { filename: 'cert/localhost+1-key.pem' }
  clusters:
    - name: web_api
      connect_timeout: 0.25s
      type: logical_dns
      http2_protocol_options: {}
      lb_policy: round_robin
      load_assignment:
        cluster_name: cluster_0
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 9090
    - name: web_api_v1alpha2_grpc
      connect_timeout: 0.25s
      type: logical_dns
      http2_protocol_options: {}
      lb_policy: round_robin
      load_assignment:
        cluster_name: cluster_0
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 9091
    - name: ws-web-api-server-v1alpha1-cluster
      connect_timeout: 0.25s
      type: logical_dns
      lb_policy: round_robin
      load_assignment:
        cluster_name: ws-web-api-server-v1alpha1
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 8085
    - name: web_api_orby_internal_grpc
      connect_timeout: 0.25s
      type: logical_dns
      http2_protocol_options: {}
      lb_policy: round_robin
      load_assignment:
        cluster_name: cluster_0
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 9092
    - name: web_api_v1alpha2_http
      connect_timeout: 0.25s
      type: logical_dns
      lb_policy: round_robin
      load_assignment:
        cluster_name: cluster_0
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 8081
    - name: va-web-api-server-cluster
      connect_timeout: 0.25s
      type: logical_dns
      lb_policy: round_robin
      load_assignment:
        cluster_name: cluster_0
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 8180
    - name: web_api_http
      connect_timeout: 0.25s
      type: logical_dns
      lb_policy: round_robin
      load_assignment:
        cluster_name: cluster_0
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 8084
    - name: web_api_internal-api_grpc
      connect_timeout: 0.25s
      type: logical_dns
      http2_protocol_options: {}
      lb_policy: round_robin
      load_assignment:
        cluster_name: cluster_0
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 9092
      health_checks:
        timeout: 1s
        interval: 10s
        unhealthy_threshold: 2
        healthy_threshold: 2
        http_health_check: { path: '/healthcheck' }
    - name: web_api_v1_grpc
      connect_timeout: 0.25s
      type: logical_dns
      http2_protocol_options: {}
      lb_policy: round_robin
      load_assignment:
        cluster_name: cluster_0
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 9094
    - name: web_api_v1_http
      connect_timeout: 0.25s
      type: logical_dns
      lb_policy: round_robin
      load_assignment:
        cluster_name: cluster_0
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: 127.0.0.1
                      port_value: 8086
