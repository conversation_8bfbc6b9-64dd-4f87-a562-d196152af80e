name: 'Setup Build Environment'
description: 'Setup build environment for Vibe Automation Server'

inputs:
  workload_identity_provider:
    required: true
    description: 'Workload identity provider for the service account'
  service_account:
    required: true
    description: 'Service account to use for the build'

runs:
  using: 'composite'
  steps:
    - name: Google Auth
      id: auth
      uses: google-github-actions/auth@v2
      with:
        token_format: 'access_token'
        workload_identity_provider: '${{ inputs.workload_identity_provider }}'
        service_account: '${{ inputs.service_account }}'

    - name: Docker Auth
      id: docker-auth
      uses: docker/login-action@v3
      with:
        username: 'oauth2accesstoken'
        password: '${{ steps.auth.outputs.access_token }}'
        registry: 'us-docker.pkg.dev'

    - name: <PERSON><PERSON> Auth
      uses: google-github-actions/get-gke-credentials@v0.4.0
      with:
        cluster_name: ${{ env.GKE_CLUSTER }}
        location: ${{ env.GKE_REGION }}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
