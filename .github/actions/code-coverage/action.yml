name: "Generate Code Coverage"
description: "Generate code coverage for the package"

inputs:
  package_path:
    required: true
    description: "Path to the package to generate code coverage for"

runs:
  using: "composite"
  steps:
    - name: Calculate Test Coverage
      shell: bash
      id: coverage
      run: |
        uv run coverage run --source=${{ inputs.package_path }} -m pytest
        COVERAGE=$(uv run coverage report --omit="*/__init__.py,*/tests/*,*/scripts/*" --format=total)
        echo "coverage=$COVERAGE%" >> $GITHUB_OUTPUT

    - name: Add Coverage PR Comment
      uses: marocchino/sticky-pull-request-comment@v2
      with:
        recreate: true
        message: |
          Test coverage in ${{ inputs.package_path }} ${{ github.sha }} for PR ${{ github.event.number }}:
          ```
          Overall: ${{ steps.coverage.outputs.coverage }}
          ```
