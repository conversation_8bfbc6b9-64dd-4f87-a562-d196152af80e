name: "Build and Deploy"
description: "Build and deploy va servers"

runs:
  using: "composite"
  steps:
    - name: Read UV version from pyproject.toml or use latest
      shell: bash
      id: uv-version
      run: |
        # Try to extract UV version from pyproject.toml, fallback to "latest"
        UV_VERSION=$(grep -oP '(?<=required-version = ")[^"]*' pyproject.toml 2>/dev/null || echo "latest")
        echo "version=$UV_VERSION" >> $GITHUB_OUTPUT
        echo "Using UV version: $UV_VERSION"

    - name: Install UV
      uses: astral-sh/setup-uv@v6
      with:
        enable-cache: true
        version: ${{ steps.uv-version.outputs.version }}

    - name: Install dependencies from uv.lock file
      shell: bash
      run: uv sync --frozen --no-dev

    - name: Generate proto files
      shell: bash
      run: uv run python -m scripts.generate_protos

    - name: Build & Publish
      shell: bash
      run: |
        chmod +x k8s/scripts/build_server_image.sh
        ./k8s/scripts/build_server_image.sh

    # Deploy the Server Docker image to the GKE cluster
    - name: Deploy Server
      shell: bash
      run: |-
        cd k8s/orby/${{ env.K8S_APP_NAME }}/overlays/${{ env.ENVIRONMENT }}
        kustomize edit set image \
        LOCATION-docker.pkg.dev/PROJECT_ID/REPOSITORY/IMAGE:TAG=us-docker.pkg.dev/$PROJECT_ID/$REPOSITORY/$IMAGE:$GITHUB_SHA
        kustomize build | kubectl apply -n ${{ env.NAMESPACE }} -f - 
        kubectl rollout status -n ${{ env.NAMESPACE }} deployment/$DEPLOYMENT_NAME
        kubectl get services -n ${{ env.NAMESPACE }} -o wide
