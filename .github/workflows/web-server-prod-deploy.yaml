name: Web Server Prod US Build and Deploy

on:
  push:
    tags:
      - "release-20[0-9][0-9][0-1][0-9][0-9][0-9]-rc[0-9]+-prod-us"
      - "release-20[0-9][0-9][0-1][0-9][0-9][0-9]-rc[0-9]+-prod"

env:
  PROJECT_ID: ${{ secrets.GKE_PROJECT }}
  GAR_LOCATION: us
  REPOSITORY: orby-docker
  IMAGE: va-web-server
  GKE_CLUSTER: api-server-prod
  GKE_REGION: us-central1
  DEPLOYMENT_NAME: va-web-server-deployment
  NAMESPACE: prod
  CACHE_TAG: prod-cache
  ENVIRONMENT: prod
  DOCKERFILE_PATH: docker/Dockerfile.web_server
  K8S_APP_NAME: web_server

jobs:
  setup-build-publish-deploy:
    name: Setup, Build, Publish, and Deploy
    runs-on: ubuntu-latest
    timeout-minutes: 30
    environment: prod

    # Add 'id-token' with the intended permissions for workload identity federation
    permissions:
      contents: "read"
      id-token: "write"

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        env:
          GIT_TERMINAL_PROMPT: 1
        with:
          token: ${{ secrets.SUBMODULE_TOKEN }}
          submodules: recursive

      - name: Setup Build Environment
        uses: ./.github/actions/setup
        with:
          workload_identity_provider: "${{ secrets.WIF_PROVIDER }}"
          service_account: "${{ secrets.WIF_SERVICE_ACCOUNT }}"

      - name: Build and Deploy
        uses: ./.github/actions/build-deploy
