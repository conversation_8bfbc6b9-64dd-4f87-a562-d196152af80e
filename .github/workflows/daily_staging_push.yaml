name: Daily Merge from main to staging

on:
  # schedule:
  #   - cron: '0 8 * * *'  # midnight PDT
  workflow_dispatch: # Allows manual triggering of the workflow

jobs:
  merge:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout staging branch
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.SUBMODULE_TOKEN }}
          submodules: recursive

      - name: Configure git
        run: |
          git config --global user.name 'github-actions'
          git config --global user.email '<EMAIL>'

      - name: Sync staging with main
        run: |
          git fetch origin
          git branch -f staging origin/main

      - name: Push changes
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: git push origin staging --force
