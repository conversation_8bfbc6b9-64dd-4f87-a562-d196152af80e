name: Web Server Dev Build and Deploy

on:
  workflow_dispatch: # force manual trigger if needed
    inputs:
      force_build:
        description: "Force Build"
        type: boolean
        required: false
        default: true
  push:
    branches:
      - main
    paths:
      [
        "uv.lock",
        "k8s/orby/web_server/**",
        "packages/agent/**",
        "packages/common/**",
        "packages/execution/**",
        "packages/protos_gen/**",
        "packages/web_server/**",
        "protos/**",
        "docker/**",
        "pyproject.toml",
        "k8s/scripts/**",
      ]

env:
  PROJECT_ID: ${{ secrets.GKE_PROJECT_DEV }}
  GAR_LOCATION: us
  REPOSITORY: orby-docker
  IMAGE: va-web-server
  GKE_CLUSTER: web-api-server-dev
  GKE_REGION: us-central1-c
  DEPLOYMENT_NAME: va-web-server-deployment
  NAMESPACE: dev
  CACHE_TAG: dev-cache
  ENVIRONMENT: dev
  DOCKERFILE_PATH: docker/Dockerfile.web_server
  K8S_APP_NAME: web_server
  ORBY_BASE_URL: https://dev2-grpc.orby.ai

jobs:
  setup-build-publish-deploy:
    name: Setup, Build, Publish, and Deploy
    runs-on: ubuntu-latest
    timeout-minutes: 30

    # Add 'id-token' with the intended permissions for workload identity federation
    permissions:
      contents: "read"
      id-token: "write"

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.SUBMODULE_TOKEN }}
          submodules: recursive

      - name: Setup Build Environment
        uses: ./.github/actions/setup
        with:
          workload_identity_provider: "${{ secrets.WIF_PROVIDER_DEV }}"
          service_account: "${{ secrets.WIF_SERVICE_ACCOUNT_DEV }}"

      - name: Build and Deploy
        uses: ./.github/actions/build-deploy
