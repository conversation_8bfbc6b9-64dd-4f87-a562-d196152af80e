name: Unit Tests

on:
  push:
    branches: ["main", "staging", "release-20[0-9][0-9][0-1][0-9][0-9][0-9]"]
  pull_request:
    branches: ["main", "prod", "release-20[0-9][0-9][0-1][0-9][0-9][0-9]"]

jobs:
  test:
    runs-on: ubuntu-latest

    permissions:
      # Required by ma<PERSON><PERSON><PERSON>/sticky-pull-request-comment:
      # https://github.com/marketplace/actions/sticky-pull-request-comment#error-resource-not-accessible-by-integration
      pull-requests: write

    steps:
      - name: Checkout code along with submodules
        uses: actions/checkout@v4
        env:
          GIT_TERMINAL_PROMPT: 1
        with:
          # Full git history is needed to get a proper
          # list of changed files within during linting
          fetch-depth: 0
          token: ${{ secrets.SUBMODULE_TOKEN }}
          submodules: recursive

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          # Version is specified in .python-version
          # Docs: https://docs.astral.sh/uv/guides/integration/github/#setting-up-python
          python-version-file: ".python-version"

      - name: Read UV version from pyproject.toml or use latest
        shell: bash
        id: uv-version
        run: |
          # Try to extract UV version from pyproject.toml, fallback to "latest"
          UV_VERSION=$(grep -oP '(?<=required-version = ")[^"]*' pyproject.toml 2>/dev/null || echo "latest")
          echo "version=$UV_VERSION" >> $GITHUB_OUTPUT
          echo "Using UV version: $UV_VERSION"

      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          enable-cache: true
          activate-environment: true
          cache-dependency-glob: "uv.lock"
          version: ${{ steps.uv-version.outputs.version }}

      - name: Generate proto files
        run: uv run python -m scripts.generate_protos

      # We use --reinstall instead of --frozen to ensure Python can import generated files from packages/protos_gen/protos_gen,
      # else the package path (python3.13/site-packages/_protos_gen.pth) remains empty.
      # Both frozen and reinstall respect the uv.lock file.
      # We also need to generate the proto files before installing dependencies to get the correct path for protos
      - name: Install dependencies from uv.lock file
        run: uv sync --reinstall

      - name: Check for print statements and logging imports (use common.log instead)
        run: |
          echo "🔍 Checking for print statements and logging imports in packages..."
          
          # Check for print statements (exclude test files and docstring examples)
          PRINT_FILES=$(grep -rn "^\s*print(" packages/common packages/web_server packages/execution --include="*.py" --exclude-dir="tests" --exclude-dir="runtime_init" --exclude="test_*.py" --exclude="*_test.py" || true)
          
          # Check for logging imports (import logging, from logging import, logging.getLogger, etc.)
          # Exclude common.log infrastructure files that legitimately use logging
          LOGGING_FILES=$(grep -rn -E "(^|\s)(import logging|from logging import|logging\.)" packages/common packages/web_server packages/execution --include="*.py" | grep -v "/log/log.py" | grep -v "/utils/logger.py" || true)
          
          VIOLATIONS_FOUND=false
          
          if [ -n "$PRINT_FILES" ]; then
            echo "❌ Found print statements. Please use common.log instead:"
            echo "$PRINT_FILES"
            echo ""
            VIOLATIONS_FOUND=true
          fi
          
          if [ -n "$LOGGING_FILES" ]; then
            echo "❌ Found logging imports. Please use common.log instead:"
            echo "$LOGGING_FILES"
            echo ""
            VIOLATIONS_FOUND=true
          fi
          
          if [ "$VIOLATIONS_FOUND" = true ]; then
            echo "Replace with: from common.log import info, error, debug, warn"
            echo "  print(msg) → info(msg)"
            echo "  logging.info(msg) → info(msg)"
            echo "  logging.error(msg) → error(msg)"
            exit 1
          fi
          
          echo "✅ No print statements or logging imports found"

      - name: Run linter on changed python files
        run: |
          CHANGED_FILES=$(git diff --name-only origin/main...HEAD | grep '^packages/' | grep '\.py$' | xargs -r ls 2>/dev/null || true)

          if [ -z "$CHANGED_FILES" ]; then
            echo "No Python files changed. Skipping linting."
            exit 0
          fi

          # Use xargs to handle multiple files and special characters
          echo "$CHANGED_FILES" | xargs uvx ruff format || { echo "❌ Lint format failed"; exit 1; }
          echo "$CHANGED_FILES" | xargs uvx ruff check || { echo "❌ Lint check failed"; exit 1; }

          echo "✅ Linting complete for changed files"
        env:
          ACTIONS_STEP_DEBUG: true

      - name: Run unit tests
        run: |
          uv run pytest

      - name: Calculate Test Coverage for web_server
        uses: ./.github/actions/code-coverage
        with:
          package_path: "packages/web_server"

      # TODO: Add test coverage for other packages whenever we have tests for them
      # - name: Calculate Test Coverage for agent
      #   uses: ./.github/actions/code-coverage
      #   with:
      #     package_path: "packages/agent"

      - name: Calculate Test Coverage for execution
        uses: ./.github/actions/code-coverage
        with:
          package_path: "packages/execution"

      # - name: Calculate Test Coverage for common
      #   uses: ./.github/actions/code-coverage
      #   with:
      #     package_path: "packages/common"
