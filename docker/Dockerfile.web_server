FROM python:3.13-slim

WORKDIR /app

# Install system dependencies needed for Chromium (Playwright) and Node.js
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    curl \
    wget \
    gnupg \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgl1-mesa-glx \
    libgbm1 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libx11-xcb1 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    xdg-utils \
    nodejs \
    npm \
    --no-install-recommends && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install uv
RUN pip install uv

# Copy dependency files
COPY pyproject.toml uv.lock ./

# Copy all packages
COPY packages/ packages/

# Install Python dependencies
RUN uv sync --frozen --no-dev

# Create non-root user
RUN addgroup --gid 3000 --system orbygroup && \
    adduser --uid 2000 --system --ingroup orbygroup --home /home/<USER>
    mkdir -p /home/<USER>/.cache && \
    chown -R 2000:3000 /app /home/<USER>

# Set environment variables
ENV UV_CACHE_DIR=/home/<USER>/.cache/uv
ENV HOME=/home/<USER>
ENV PYTHONUNBUFFERED=1

# Switch to non-root user
USER 2000:3000

# Install Chromium browser binaries as non-root
RUN uv run playwright install chromium

EXPOSE 8080 9090
