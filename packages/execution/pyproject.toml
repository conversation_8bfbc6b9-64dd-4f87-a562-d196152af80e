[project]
name = "execution"
version = "0.1.0"
description = "Execution service which manages the execution with the browser session"
readme = "README.md"
requires-python = ">=3.13"

dependencies = [
    "browserbase>=1.4.0",
    "common",
    "protos_gen",
    "httpx>=0.27.0",
]

[tool.uv.sources]
common = { workspace = true }
protos_gen = { workspace = true }


[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["execution"]

[project.scripts]
execution = "execution.main:main"

