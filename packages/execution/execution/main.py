import asyncio
import signal
import sys

from common.log import info

from .v1.servers.grpc_server import GrpcServer


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    info(f"Received signal {signum}. Initiating graceful shutdown...")
    sys.exit(0)


async def async_main():
    """Main entry point for the execution service."""
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Create and start the gRPC server
    grpc_server = GrpcServer()

    try:
        await grpc_server.start()

        # Wait for termination
        await grpc_server.wait_for_termination()

    finally:
        await grpc_server.stop()
        info("👋 Execution service stopped")


def main():
    asyncio.run(async_main())


if __name__ == "__main__":
    asyncio.run(main())
