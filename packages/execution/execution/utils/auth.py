import json

from common.models.user import User
from common.utils.auth import AuthPayload, get_auth_payload_from_grpc_metadata
from execution.constants.execution_constants import LOCAL_MODE
from execution.v1.config.settings import config


def get_auth_payload_wrapper(metadata: list[tuple[str, str]]) -> AuthPayload:
    """
    Wrapper to get the auth payload from the request metadata.

    This is a workaround to to login to the system when we are running in local mode.

    We will fetch the user from the local_token cookie and return the auth payload instead
    of extracting it from the metadata which are set by the auth middleware.
    """

    cookie_str = None
    for key, value in metadata:
        if key == "cookie":
            cookie_str = value

    if config.mode == LOCAL_MODE and is_local_token_present(cookie_str):
        cookies = cookie_str.split("; ")
        for cookie in cookies:
            if "local_token=" in cookie:
                local_token = cookie.split("local_token=")[1]
                user_json = json.loads(local_token)
                dbUser = User(**user_json)

                org_id = None
                if len(dbUser.org_ids) > 0:
                    org_id = dbUser.org_ids[0]
                return AuthPayload(
                    user_email=dbUser.email,
                    user_id=dbUser.id,
                    org_id=org_id,
                )
        raise ValueError("Local token not found in the cookie")

    return get_auth_payload_from_grpc_metadata(metadata)


def is_local_token_present(cookieString: str | None) -> bool:
    """
    Check if the local_token is present in the cookie string.
    """
    return cookieString and "local_token=" in cookieString
