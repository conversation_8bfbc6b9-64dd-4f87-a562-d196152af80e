import base64
import os
from pathlib import Path
import subprocess
from typing import Optional

import boto3
from dotenv import load_dotenv
import google.auth
from google.auth import impersonated_credentials
import google.auth.transport.requests
from google.protobuf.json_format import MessageToDict
from orby.va.public.execution_messages_pb2 import EnvironmentVariable
import requests

from common.log import error, info
from execution.clients.cloud_providers.cloud_provider_interface import (
    CloudProviderInterface,
)
from execution.constants.execution_constants import (
    TASK_CONTAINER_IMAGE,
    TASK_DEFINITION_NAME,
)

load_dotenv()

AWS_AUDIENCE = "sts.amazonaws.com"


class AWSClient(CloudProviderInterface):
    _instance: Optional["AWSClient"] = None

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = AWSClient()
        return cls._instance

    def __init__(self):
        if self._instance is not None:
            raise RuntimeError("AWSClient is a singleton")

        self.role_arn = os.environ["AWS_ROLE_ARN"]
        self.region = os.getenv("AWS_REGION", "us-east-1")
        self.repository_name = os.getenv("AWS_ARTIFACT_REPO_NAME")
        self.aws_account_id = os.getenv("AWS_ACCOUNT_ID")
        self.aws_cluster_name = os.getenv("AWS_CLUSTER")
        # Authenticate using web identity
        self._assume_role_with_web_identity()

    def _get_gcp_oidc_token(self) -> str:
        """Automatically get OIDC token — from metadata server in GCP or via gcloud locally"""

        # Fixed audience for AWS
        audience = AWS_AUDIENCE
        gsa_email = os.environ.get("GCS_AWS_SERVICE_ACCOUNT_EMAIL")

        try:
            # Check if running inside GCP (metadata server available)
            requests.get(
                "http://metadata.google.internal",
                headers={"Metadata-Flavor": "Google"},
                timeout=0.001,
            )
        except (
            requests.exceptions.RequestException,
            requests.exceptions.Timeout,
        ) as err:
            # Outside GCP — fallback to gcloud + impersonation
            # For local execution, ensure the user is granted the appropriate IAM policy that permits obtaining a service account token for the GCP service account.
            # For example
            if not gsa_email:
                raise RuntimeError(
                    "GCP_IMPERSONATED_SERVICE_ACCOUNT must be set when running locally"
                ) from err
            token = subprocess.check_output(
                [
                    "gcloud",
                    "auth",
                    "print-identity-token",
                    f"--audiences={audience}",
                    f"--impersonate-service-account={gsa_email}",
                ]
            )
            return token.decode("utf-8").strip()

        # Inside GCP — impersonate gsa_email via IAM API
        if not gsa_email:
            raise RuntimeError("GCP_IMPERSONATED_SERVICE_ACCOUNT must be set")

        # Get default credentials
        source_credentials, _ = google.auth.default()

        # First create impersonated credentials
        target_credentials = impersonated_credentials.Credentials(
            source_credentials=source_credentials,
            target_principal=gsa_email,
            target_scopes=["openid", "https://www.googleapis.com/auth/iam"],
        )

        # Then create ID token credentials from the impersonated credentials
        id_token_credentials = impersonated_credentials.IDTokenCredentials(
            target_credentials,
            target_audience=audience,
            include_email=True,
        )

        auth_req = google.auth.transport.requests.Request()
        id_token_credentials.refresh(auth_req)
        return id_token_credentials.token

    def _assume_role_with_web_identity(self):
        """Use GCP-issued OIDC token to authenticate to AWS"""
        try:
            token = self._get_gcp_oidc_token()
            sts = boto3.client("sts", region_name=self.region)
            credentials = sts.assume_role_with_web_identity(
                RoleArn=self.role_arn,
                RoleSessionName="gcp-to-aws-session",
                WebIdentityToken=token,
            )["Credentials"]

            # Create AWS session using temporary credentials
            self._session = boto3.Session(
                aws_access_key_id=credentials["AccessKeyId"],
                aws_secret_access_key=credentials["SecretAccessKey"],
                aws_session_token=credentials["SessionToken"],
                region_name=self.region,
            )

            self._ecr_client = self._session.client("ecr")
            self._ecs_client = self._session.client("ecs")

        except Exception as e:
            error(
                f"Unexpected error while assuming role with web identity: {e}"
            )
            raise e

    # A helper function for building and uploading the execution template image to ECR.
    def build_and_upload_image(
        self, local_image_path: str, image_name: str, image_tag: str = "latest"
    ):
        """
        Build Docker image locally and push it to AWS ECR.

        Args:
            local_image_path (str): Filesystem path to the directory containing the Docker build context and the 'Dockerfile.execution'.
            image_name (str): Local name to tag the built Docker image (e.g., 'my-image').
            image_tag (str): Tag to use for the image in ECR (default: 'latest').
        """
        try:
            build_context_path = Path(local_image_path).resolve()
            dockerfile_path = build_context_path / "Dockerfile.execution"

            if not dockerfile_path.exists():
                raise FileNotFoundError(
                    f"Dockerfile not found: {dockerfile_path}"
                )
            info(
                f"Building Docker image: {image_name}:{image_tag} from {local_image_path}"
            )

            subprocess.run(
                [
                    "docker",
                    "buildx",
                    "build",
                    "--platform",
                    "linux/amd64",
                    "-t",
                    f"{image_name}:{image_tag}",
                    "--file",
                    str(dockerfile_path),
                    "--load",
                    str(build_context_path),
                ],
                check=True,
                stderr=subprocess.STDOUT,
            )
            info(f"Image built locally: {image_name}:{image_tag}")

            info("Authenticating Docker with ECR...")
            auth_data = self._ecr_client.get_authorization_token()[
                "authorizationData"
            ][0]
            token = base64.b64decode(auth_data["authorizationToken"]).decode()
            username, password = token.split(":")
            proxy_endpoint = auth_data["proxyEndpoint"]

            subprocess.run(
                [
                    "docker",
                    "login",
                    "--username",
                    username,
                    "--password",
                    password,
                    proxy_endpoint,
                ],
                check=True,
                stderr=subprocess.STDOUT,
            )
            info("Docker authenticated with ECR.")

            # Compose ECR URI

            ecr_uri = f"{self.aws_account_id}.dkr.ecr.{self.region}.amazonaws.com/{self.repository_name}"
            ecr_tag_uri = f"{ecr_uri}:{image_tag}"
            info(f"Tagging image: {image_name}:{image_tag} → {ecr_tag_uri}")
            subprocess.run(
                ["docker", "tag", f"{image_name}:{image_tag}", ecr_tag_uri],
                check=True,
            )

            info(f"Pushing image to ECR: {ecr_tag_uri}")
            subprocess.run(
                ["docker", "push", ecr_tag_uri],
                check=True,
                stderr=subprocess.STDOUT,
            )
            info("Docker image uploaded to ECR successfully.")

        except subprocess.CalledProcessError as err:
            error(
                f"Docker command failed while building and uploading image: {err}"
            )
            raise err
        except Exception as e:
            error(f"Unexpected error while building and uploading image: {e}")
            raise e

    # This function is used to create a task set in AWS ECS for the fixed task definition.
    def create_task_set(self, env_variables: list[EnvironmentVariable]):
        """Create a task set in AWS ECS"""
        # Convert EnvironmentVariable objects to dictionaries
        env_vars = [MessageToDict(ev) for ev in env_variables]
        try:
            response = self._ecs_client.run_task(
                cluster=self.aws_cluster_name,
                launchType="FARGATE",
                taskDefinition=TASK_DEFINITION_NAME,
                networkConfiguration={
                    "awsvpcConfiguration": {
                        "subnets": ["subnet-08fc9a6e2dd6ee992"],
                        "securityGroups": ["sg-03f60f955b24d8777"],
                        "assignPublicIp": "ENABLED",
                    }
                },
                overrides={
                    "containerOverrides": [
                        {
                            "name": TASK_CONTAINER_IMAGE,  # must match the container name in the task definition
                            "environment": env_vars,
                        }
                    ]
                },
            )
            info(f"Task set created: {response['tasks'][0]['taskArn']}")
        except Exception as e:
            error(f"Unexpected error while creating task set: {e}")
            raise e

    # This function is used to register a task definition in AWS ECS.
    def register_task_config(self):
        """Register a task definition in AWS ECS"""
        try:
            response = self._ecs_client.register_task_definition(
                family=TASK_DEFINITION_NAME,
                taskRoleArn="ecsTaskRole",
                executionRoleArn="ecsTaskExecutionRole",
                cpu="2048",  # 2 vCPU
                memory="4096",  # 4 GB
                requiresCompatibilities=["FARGATE"],
                networkMode="awsvpc",  # This is required for "FARGATE"
                containerDefinitions=[
                    {
                        "name": TASK_CONTAINER_IMAGE,  # This is the name of the container in the task definition
                        "image": f"{self.aws_account_id}.dkr.ecr.{self.region}.amazonaws.com/{self.repository_name}:latest",
                        "essential": True,
                        "logConfiguration": {
                            "logDriver": "awslogs",
                            "options": {
                                "awslogs-group": "/ecs/execution-server",
                                "awslogs-region": self.region,
                                "awslogs-stream-prefix": "ecs",
                                "awslogs-create-group": "true",
                                "mode": "non-blocking",
                                "max-buffer-size": "25m",
                            },
                        },
                    }
                ],
            )
            info(
                f"Task definition created: {response['taskDefinition']['taskDefinitionArn']}"
            )
        except Exception as e:
            error(f"Unexpected error while registering task config: {e}")
            raise e


def get_ecr_client():
    return AWSClient.get_instance()
