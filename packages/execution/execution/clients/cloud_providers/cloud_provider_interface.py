from abc import ABC, abstractmethod

from orby.va.public.execution_messages_pb2 import EnvironmentVariable


class CloudProviderInterface(ABC):
    """
    Interface for cloud providers.
    """

    @abstractmethod
    def build_and_upload_image(
        self, image_path: str, image_name: str, image_tag: str = "latest"
    ) -> str:
        pass

    @abstractmethod
    def create_task_set(self, env_variables: list[EnvironmentVariable]) -> str:
        pass

    @abstractmethod
    def register_task_config(self) -> str:
        pass
