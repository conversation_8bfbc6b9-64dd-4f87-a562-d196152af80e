# This script is an executable helper script to push the docker image to the aws ecr repository

from execution.clients.cloud_providers.aws_client import get_ecr_client


def run():
    aws_client = get_ecr_client()
    # aws_client.register_task_config()
    aws_client.create_task_set(
        [
            {
                "name": "COMMIT_HASH",
                "value": "990567ee40a28a2b13a55ae4791531f51e34af7a",
            },
            {"name": "CONNECTION_URL", "value": ""},
            {"name": "ANTHROPIC_API_KEY", "value": ""},
        ]
    )
    aws_client.build_and_upload_image(
        "packages/execution/execution/runtime_configs/templates/python3.11",
        "execution-server",
        "latest",
    )


if __name__ == "__main__":
    run()
