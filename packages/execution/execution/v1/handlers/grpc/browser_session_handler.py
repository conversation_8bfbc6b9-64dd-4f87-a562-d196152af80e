import grpc
from orby.va.browser_session_pb2 import (
    CreateSessionRequest,
    CreateSessionResponse,
)
from orby.va.browser_session_service_pb2_grpc import (
    BrowserServiceServicer,
)

from common.log import error

from ...services.browser_service import BrowserService


class BrowserSessionHandler(BrowserServiceServicer):
    """gRPC handler for browser session management service"""

    def __init__(self):
        self.browser_service = BrowserService()

    def CreateSession(
        self, request: CreateSessionRequest, context: grpc.ServicerContext
    ) -> CreateSessionResponse:
        """Handle CreateSession gRPC call."""
        try:
            # For now, just pass through - later add domain model conversion
            created_session = self.browser_service.create_session(request)
            return created_session

        except ValueError as ve:
            error(f"Validation error during CreateSession: {ve}")
            context.abort(grpc.StatusCode.INVALID_ARGUMENT, str(ve))

        except grpc.RpcError as ge:
            error(f"gRPC error during CreateSession: {ge}")
            context.abort(ge.code(), ge.details())

        except Exception as e:
            error(f"Unhandled error during CreateSession: {e}")
            context.abort(
                grpc.StatusCode.INTERNAL, "Internal server error occurred"
            )
