from bson import ObjectId
from google.protobuf.empty_pb2 import Empty
import grpc
from orby.va.task_pb2 import (
    DeleteTaskRequest,
    ListTasksRequest,
    ListTasksResponse,
    Task,
    UpdateTaskRequest,
)
from orby.va.task_service_pb2_grpc import (
    TaskServiceServicer,
)

from common.interceptors.grpc.auth import get_and_validate_auth_payload
from common.log import error
from common.services.task_service import (
    DeleteTaskRequest as DeleteTaskRequestDto,
)
from common.services.task_service import (
    ListTasksFilter,
    TaskService,
)
from common.services.task_service import (
    ListTasksRequest as ListTasksRequestDto,
)
from common.services.task_service import (
    UpdateTaskRequest as UpdateTaskRequestDto,
)
from common.services.tenant_service import TenantService
from common.utils.task_mapper import (
    convert_task_status_proto_to_model,
    task_model_to_proto,
)


class TaskHandler(TaskServiceServicer):
    def __init__(self):
        self._task_service = TaskService()
        self.tenant_service = TenantService()

    async def ListTasks(
        self,
        request: ListTasksRequest,
        context: grpc.ServicerContext,
    ):
        try:
            auth_payload = get_and_validate_auth_payload()
            tenant_info = await self.tenant_service.get_tenant_info(
                org_id=auth_payload.org_id,
            )

            req = ListTasksRequestDto(
                tenant_info=tenant_info,
                page_size=request.page_size,
                page_number=request.page_number,
                filter=ListTasksFilter(),
            )
            if request.filter and request.filter.status:
                req.filter.status = convert_task_status_proto_to_model(
                    request.filter.status
                )

            result = await self._task_service.list_tasks(req)
            return ListTasksResponse(
                tasks=[task_model_to_proto(task) for task in result.tasks],
                total_size=result.total_size,
            )
        except Exception as e:
            error(
                "Failed to list tasks",
                filter=request.filter,
                error=str(e),
            )
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Failed to list tasks")

    async def UpdateTask(
        self,
        request: UpdateTaskRequest,
        context: grpc.ServicerContext,
    ) -> Task:
        if not ObjectId.is_valid(request.id):
            raise ValueError("Invalid task ID")
        task_id = ObjectId(request.id)

        try:
            auth_payload = get_and_validate_auth_payload()
            tenant_info = await self.tenant_service.get_tenant_info(
                org_id=auth_payload.org_id,
            )

            req = UpdateTaskRequestDto(
                tenant_info=tenant_info,
                task_id=task_id,
            )

            for field in request.field_mask.paths:
                if field == "status":
                    req.status = convert_task_status_proto_to_model(
                        request.status
                    )
                else:
                    raise ValueError(
                        f"Invalid field mask {field}, available fields: [status]"
                    )

            result = await self._task_service.update_task(req)
            return task_model_to_proto(result)
        except Exception as e:
            error(
                "Failed to update task",
                task_id=task_id,
                fields=request.field_mask.paths,
                error=str(e),
            )
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Failed to update task")

    async def DeleteTask(
        self,
        request: DeleteTaskRequest,
        context: grpc.ServicerContext,
    ) -> Empty:
        if not ObjectId.is_valid(request.id):
            raise ValueError("Invalid task ID")
        task_id = ObjectId(request.id)

        try:
            auth_payload = get_and_validate_auth_payload()
            tenant_info = await self.tenant_service.get_tenant_info(
                org_id=auth_payload.org_id,
            )
            req = DeleteTaskRequestDto(
                tenant_info=tenant_info,
                task_id=task_id,
            )
            await self._task_service.delete_task(req)
            return Empty()
        except Exception as e:
            error(
                "Failed to delete task",
                task_id=task_id,
                error=str(e),
            )
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Failed to delete task")
