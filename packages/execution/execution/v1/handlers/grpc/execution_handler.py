"""gRPC handler for execution service."""

from datetime import datetime

from bson import ObjectId
from google.protobuf.empty_pb2 import Empty
import grpc
from orby.va.execution_management_pb2 import (
    DeleteExecutionRequest,
    GetExecutionRequest,
    ListExecutionsRequest,
    ListExecutionsResponse,
)
from orby.va.execution_management_service_pb2_grpc import (
    ExecutionManagementServiceServicer,
)
from orby.va.public.execution_messages_pb2 import (
    AppendExecutionLogRequest,
    AppendExecutionLogResponse,
    Execution,
    GetExecutionLogsRequest,
    GetExecutionLogsResponse,
    GetReviewStatusRequest,
    GetReviewStatusResponse,
    RequestReviewRequest,
    RequestReviewResponse,
    ReviewCompletedRequest,
    ReviewCompletedResponse,
    StartExecutionRequest,
    StartExecutionResponse,
    UpdateExecutionRequest,
)
from orby.va.public.execution_service_pb2_grpc import (
    ExecutionServiceServicer,
)

from common.interceptors.grpc.auth import get_and_validate_auth_payload
from common.log import error
from common.services.execution_log_service import ExecutionLogService
from common.services.tenant_service import TenantService
from common.utils.execution_logs import (
    convert_execution_log_model_to_proto,
    convert_execution_log_proto_to_model,
)

from ...services.execution_service import ExecutionService


class ExecutionHandler(
    ExecutionServiceServicer, ExecutionManagementServiceServicer
):
    """gRPC handler that bridges protobuf and business logic."""

    def __init__(self):
        self.execution_service = ExecutionService()
        self.tenant_service = TenantService()
        self.execution_log_service = ExecutionLogService()

    def StartExecution(
        self, request: StartExecutionRequest, context: grpc.ServicerContext
    ) -> StartExecutionResponse:
        """Handle StartExecution gRPC call."""
        try:
            created_execution = self.execution_service.start_execution(request)
            return created_execution
        except Exception as e:
            error(f"{str(e)}")
            context.abort(
                grpc.StatusCode.INTERNAL, "Execution service start-up failed"
            )

    async def RequestReview(
        self, request: RequestReviewRequest, context: grpc.ServicerContext
    ) -> RequestReviewResponse:
        """Handle RequestReview gRPC call."""
        try:
            review_response = await self.execution_service.request_review(
                request
            )
            return review_response
        except Exception as e:
            error(f"{str(e)}")
            await context.abort(
                grpc.StatusCode.INTERNAL, "Creating review failed"
            )

    async def GetReviewStatus(
        self, request: GetReviewStatusRequest, context: grpc.ServicerContext
    ) -> GetReviewStatusResponse:
        """Handle GetReviewStatus gRPC call."""
        try:
            review_status = await self.execution_service.get_review_status(
                request
            )
            return review_status
        except Exception as e:
            error(f"{str(e)}")
            await context.abort(
                grpc.StatusCode.INTERNAL, "Failed to get review status"
            )

    async def MarkReviewCompleted(
        self, request: ReviewCompletedRequest, context: grpc.ServicerContext
    ) -> ReviewCompletedResponse:
        """Handle MarkReviewCompleted gRPC call."""
        try:
            review_response = (
                await self.execution_service.mark_review_completed(request)
            )
            return review_response
        except Exception as e:
            error(f"{str(e)}")
            await context.abort(
                grpc.StatusCode.INTERNAL, "Failed to mark review as completed"
            )

    async def GetExecutionLogs(
        self, request: GetExecutionLogsRequest, context: grpc.ServicerContext
    ) -> GetExecutionLogsResponse:
        """Handle GetExecutionLogs gRPC call."""
        try:
            if not request.execution_id and not ObjectId.is_valid(
                request.execution_id
            ):
                raise ValueError("Invalid execution id")
            execution_id: ObjectId = ObjectId(request.execution_id)
            page_number: int = request.page_number
            page_size: int = request.page_size
            if page_size == 0:
                # default page size
                page_size = 5

            (
                logs,
                total_size,
            ) = await self.execution_log_service.get_execution_logs(
                execution_id=execution_id,
                page_number=page_number,
                page_size=page_size,
            )
            execution_logs = GetExecutionLogsResponse(
                logs=[
                    convert_execution_log_model_to_proto(log) for log in logs
                ],
                total_size=total_size,
            )
            return execution_logs
        except Exception as e:
            error(f"{str(e)}")
            await context.abort(
                grpc.StatusCode.INTERNAL, "Failed to get execution logs"
            )

    async def AppendExecutionLog(
        self, request: AppendExecutionLogRequest, context: grpc.ServicerContext
    ) -> AppendExecutionLogResponse:
        """Handle AppendExecutionLog gRPC call."""
        try:
            db_execution_log = convert_execution_log_proto_to_model(
                proto_execution_log=request.log
            )

            db_execution_log.id = ObjectId()
            db_execution_log.timestamp = datetime.now()

            log = await self.execution_log_service.append_execution_log(
                db_execution_log
            )

            return AppendExecutionLogResponse(
                id=str(log.id),
            )
        except Exception as e:
            error(f"{str(e)}")
            await context.abort(
                grpc.StatusCode.INTERNAL, "Failed to append execution log"
            )

    async def GetExecution(
        self, request: GetExecutionRequest, context: grpc.ServicerContext
    ) -> Execution:
        """Handle GetExecution gRPC call."""
        try:
            auth_payload = get_and_validate_auth_payload()
            tenant_info = await self.tenant_service.get_tenant_info(
                org_id=auth_payload.org_id,
            )
            execution = await self.execution_service.get_execution(
                request, tenant_info
            )
            return execution
        except ValueError as e:
            error(f"{str(e)}")
            await context.abort(
                grpc.StatusCode.INVALID_ARGUMENT, f"Reason: {str(e)}"
            )
        except Exception as e:
            error(f"{str(e)}")
            await context.abort(
                grpc.StatusCode.INTERNAL, "Failed to get execution"
            )

    async def ListExecutions(
        self, request: ListExecutionsRequest, context: grpc.ServicerContext
    ) -> ListExecutionsResponse:
        """Handle ListExecutions gRPC call."""
        try:
            auth_payload = get_and_validate_auth_payload()
            tenant_info = await self.tenant_service.get_tenant_info(
                org_id=auth_payload.org_id,
            )
            response = await self.execution_service.list_executions(
                request, tenant_info
            )
            return response
        except ValueError as e:
            error(f"{str(e)}")
            await context.abort(
                grpc.StatusCode.INVALID_ARGUMENT, f"Reason: {str(e)}"
            )
        except Exception as e:
            error(f"{str(e)}")
            await context.abort(
                grpc.StatusCode.INTERNAL, "Failed to list executions"
            )

    async def DeleteExecution(
        self, request: DeleteExecutionRequest, context: grpc.ServicerContext
    ) -> Empty:
        """Handle DeleteExecution gRPC call."""
        try:
            auth_payload = get_and_validate_auth_payload()
            tenant_info = await self.tenant_service.get_tenant_info(
                org_id=auth_payload.org_id,
            )
            await self.execution_service.delete_execution(request, tenant_info)
            return Empty()
        except ValueError as e:
            error(f"{str(e)}")
            await context.abort(
                grpc.StatusCode.INVALID_ARGUMENT, f"Reason: {str(e)}"
            )
        except Exception as e:
            error(f"{str(e)}")
            await context.abort(
                grpc.StatusCode.INTERNAL, "Failed to delete execution"
            )

    async def UpdateExecution(
        self, request: UpdateExecutionRequest, context: grpc.ServicerContext
    ) -> Execution:
        """Handle UpdateExecution gRPC call."""
        try:
            auth_payload = get_and_validate_auth_payload()
            tenant_info = await self.tenant_service.get_tenant_info(
                org_id=auth_payload.org_id,
            )
            execution = await self.execution_service.update_execution(
                request, tenant_info
            )
            return execution
        except ValueError as e:
            error(f"{str(e)}")
            await context.abort(
                grpc.StatusCode.INVALID_ARGUMENT, f"Reason: {str(e)}"
            )
        except Exception as e:
            error(f"{str(e)}")
            await context.abort(
                grpc.StatusCode.INTERNAL, "Failed to update execution"
            )
