"""User utility gRPC handler."""

import grpc
from orby.va.public.user_utility_service_pb2 import (
    GetUserOrgsRequest,
    GetUserOrgsResponse,
)
from orby.va.public.user_utility_service_pb2_grpc import (
    UserUtilityServiceServicer,
)

from common.log import error, info
from execution.v1.services.user_utility_service import UserUtilityService


class UserUtilityHandler(UserUtilityServiceServicer):
    """gRPC handler for user utility operations."""

    def __init__(self):
        """Initialize the handler."""
        self.service = UserUtilityService()

    async def GetUserOrgs(
        self, request: GetUserOrgsRequest, context: grpc.aio.ServicerContext
    ) -> GetUserOrgsResponse:
        """Handle GetUserOrgs gRPC call."""
        info("Received GetUserOrgs request")
        try:
            # Now we can directly await the async service method
            org_list = await self.service.get_user_organizations(context)

            # convert to proto response
            response = GetUserOrgsResponse()
            for org in org_list:
                org_info = response.org_infos.add()
                org_info.org_id = str(org.id)
                org_info.org_name = org.display_name or ""
            return response
        except Exception as e:
            error(f"Error in GetUserOrgs: {e}")
            raise e
