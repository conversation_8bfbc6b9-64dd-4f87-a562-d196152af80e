import os

from browserbase import Browserbase
from orby.va.browser_session_pb2 import ServiceType

from common.log import error

from .browser_manager import BaseSessionManger


class BrowserbaseManager(BaseSessionManger):
    """Implementation of browser session manager using BrowserBase."""

    def _get_credentials(self, workflow_id: str) -> tuple[str, str]:
        """Retrieve credentials for a given workflow ID."""
        # TODO: Read credentials for workflow_id from mongoDB
        api_key = os.getenv("BB_API_KEY", None)
        project_id = os.getenv("BB_PROJECT_ID", None)
        if not api_key or not project_id:
            raise ValueError(
                "Browserbase API key and project ID must be set in environment variables"
            )
        return (api_key, project_id)

    def _get_context_id(
        self, bb: Browserbase, project_id: str, workflow_id: str
    ) -> str:
        # TODO: Only create context if it doesn't exist for the given workflow
        context = bb.contexts.create(project_id=project_id)
        return context.id

    def create_session(self, workflow_id: str) -> tuple[ServiceType, str, str]:
        try:
            api_key, project_id = self._get_credentials(workflow_id)
            bb = Browserbase(api_key=api_key)
            context_id = self._get_context_id(bb, project_id, workflow_id)

            session = bb.sessions.create(
                project_id=project_id,
                browser_settings={
                    "context": {"id": context_id, "persist": True}
                },
            )

            live_view_info = bb.sessions.debug(session.id)
            return (
                ServiceType.BROWSER_BASE,
                session.connectUrl,
                live_view_info.debuggerFullscreenUrl,
            )

        except Exception as e:
            error(f"Failed to create session via Browserbase: {e}")
            raise RuntimeError("Browser session creation failed") from e
