from abc import ABC, abstractmethod

from orby.va.browser_session_pb2 import ServiceType


class BaseSessionManger(ABC):
    """Abstract base class for browser session managers."""

    @abstractmethod
    def create_session(self, workflow_id: str) -> tuple[ServiceType, str, str]:
        """
        Create a browser session.
        Returns a tuple of (ServiceType, connection URL, live view URL).
        """
        pass
