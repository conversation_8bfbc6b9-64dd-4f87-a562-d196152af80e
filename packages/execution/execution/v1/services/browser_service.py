from enum import Enum

from orby.va.browser_session_pb2 import (
    CreateSessionRequest,
    CreateSessionResponse,
)

from common.log import error

from .browser.browserbase_manager import BrowserbaseManager


class SessionManagerType(Enum):
    BROWSERBASE = "browserbase"


class BrowserService:
    """Service class to manage browser session lifecycle."""

    def __init__(
        self,
        session_manager_type: SessionManagerType = SessionManagerType.BROWSERBASE,
    ):
        self.session_manager = None
        if session_manager_type == SessionManagerType.BROWSERBASE:
            self.session_manager = BrowserbaseManager()

    def create_session(
        self, request: CreateSessionRequest
    ) -> CreateSessionResponse:
        if self.session_manager is None:
            raise RuntimeError("Browser session manager not available")
        try:
            service_type, connection_url, live_view_url = (
                self.session_manager.create_session(request.workflow_id)
            )
            """Create a new browser session."""
            return CreateSessionResponse(
                service_type=service_type,
                connection_url=connection_url,
                live_view_url=live_view_url,
            )
        except Exception as e:
            error(f"Error in creating browser session: {e}")
            raise RuntimeError("Failed to create browser session") from e
