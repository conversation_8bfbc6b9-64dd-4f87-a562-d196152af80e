"""gRPC server implementation for execution service v1."""

import grpc
import grpc.aio
from grpc_reflection.v1alpha import reflection
from orby.va.browser_session_service_pb2_grpc import (
    add_BrowserServiceServicer_to_server,
)
from orby.va.execution_management_service_pb2_grpc import (
    add_ExecutionManagementServiceServicer_to_server,
)
from orby.va.public.execution_service_pb2_grpc import (
    add_ExecutionServiceServicer_to_server,
)
from orby.va.public.user_utility_service_pb2_grpc import (
    add_UserUtilityServiceServicer_to_server,
)
from orby.va.task_service_pb2_grpc import add_TaskServiceServicer_to_server
from orby.va.workflow_service_pb2_grpc import (
    add_WorkflowServiceServicer_to_server,
)
from prometheus_client import start_http_server

from common.interceptors.grpc.auth import AuthInterceptor
from common.log import info
from common.monitoring.base import get_metrics_registry
from common.monitoring.grpc import PrometheusGRPCInterceptor

from ..config.settings import config
from ..handlers.grpc.browser_session_handler import BrowserSessionHandler
from ..handlers.grpc.execution_handler import ExecutionHandler
from ..handlers.grpc.task_handler import TaskHandler
from ..handlers.grpc.user_utility_handler import UserUtilityHandler
from ..handlers.grpc.workflow_handler import WorkflowHandler


class GrpcServer:
    """gRPC server for execution service."""

    def __init__(self):
        self.server = None
        self.execution_handler = ExecutionHandler()
        self.browser_session_handler = BrowserSessionHandler()
        self.user_utility_handler = UserUtilityHandler()
        self.workflow_handler = WorkflowHandler()
        self.task_handler = TaskHandler()
        self.auth_interceptor = AuthInterceptor()
        self.prometheus_interceptor = PrometheusGRPCInterceptor()

    async def start(self) -> None:
        """Start the gRPC server."""
        # Create server with auth interceptor
        self.server: grpc.aio.Server = grpc.aio.server(
            interceptors=[self.prometheus_interceptor, self.auth_interceptor],
        )

        # Add the service handler
        add_ExecutionServiceServicer_to_server(
            self.execution_handler, self.server
        )

        # Add the browser service handler
        add_BrowserServiceServicer_to_server(
            self.browser_session_handler, self.server
        )

        # Add the execution management service handler
        add_ExecutionManagementServiceServicer_to_server(
            self.execution_handler, self.server
        )

        # Add the user utility service handler
        add_UserUtilityServiceServicer_to_server(
            self.user_utility_handler, self.server
        )

        # Add the workflow service handler
        add_WorkflowServiceServicer_to_server(
            self.workflow_handler, self.server
        )
        # Add the task service handler
        add_TaskServiceServicer_to_server(self.task_handler, self.server)

        # Explicitly declare service names
        service_names = (
            reflection.SERVICE_NAME,
            "orby.va.public.ExecutionService",
            "orby.va.BrowserService",
            "orby.va.public.UserUtilityService",
            "orby.va.ExecutionManagementService",
            "orby.va.WorkflowService",
            "orby.va.TaskService",
        )
        reflection.enable_server_reflection(service_names, self.server)

        # Configure port
        listen_addr = f"[::]:{config.port}"
        info(f"gRPC server starting on {listen_addr}")
        self.server.add_insecure_port(listen_addr)

        # Start the server
        await self.server.start()
        info(f"gRPC server started and listening on {listen_addr}")

        # Start the prometheus http server
        start_http_server(
            port=config.prometheus_port, registry=get_metrics_registry()
        )

    async def stop(self, grace_period: int = 5) -> None:
        """Stop the gRPC server."""
        if self.server:
            info("Shutting down gRPC server...")
            await self.server.stop(grace_period)

    async def wait_for_termination(self) -> None:
        """Wait for server termination."""
        if self.server:
            await self.server.wait_for_termination()
