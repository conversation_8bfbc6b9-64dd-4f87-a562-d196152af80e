#!/bin/bash
set -e  # Exit on any error

echo "=== Execution Runtime Starting ==="
if [ -z "${REPO_URL}" ]; then
    REPO_URL="https://github.com/orby-bhavesh/code-repository"
    echo "No REPO_URL provided, defaulting to: ${REPO_URL}"
fi

echo "REPO_URL: ${REPO_URL}"
echo "COMMIT_HASH: ${COMMIT_HASH}"

# Validate required environment variables
if [ -z "${REPO_URL}" ]; then
    echo "ERROR: REPO_URL environment variable is required"
    exit 1
fi

if [ -z "${COMMIT_HASH}" ]; then
    echo "ERROR: COMMIT_HASH environment variable is required"
    exit 1
fi

# Function to clone or update repository
setup_repository() {
    echo "=== Setting up repository ==="
    
    if [ -d ".git" ]; then
        echo "Repository already exists, updating..."
        git fetch origin
    else
        echo "Cloning repository: ${REPO_URL}"
        git clone "${REPO_URL}" .
    fi
    
    echo "Checking out commit: ${COMMIT_HASH}"
    git checkout "${COMMIT_HASH}"
    
    echo "Repository setup complete"
}

# Function to install dependencies
install_dependencies() {
    echo "=== Installing dependencies ==="
    
    # Install from requirements.txt if it exists
    if [ -f "requirements.txt" ]; then
        echo "Installing from requirements.txt..."
        pip install --no-cache-dir -r requirements.txt
    fi
    
    echo "Dependencies installed"
}

# Function to run the main application
run_application() {
    echo "=== Running application ==="
    
    # Look for common entry points
    if [ -f "main.py" ]; then
        echo "Running main.py..."
        python main.py "$@"
    else
        echo "No entry point found (main.py)"
    fi
}

# Main execution flow
cd /home/<USER>/app
setup_repository
install_dependencies
# "$@" expands to every command-line argument provided to the script, ensuring they are forwarded to run_application.
run_application "$@"

echo "=== Execution Runtime Completed Successfully ===" 
