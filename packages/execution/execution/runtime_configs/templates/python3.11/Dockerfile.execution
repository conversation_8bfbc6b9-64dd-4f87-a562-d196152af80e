# This is a template for the Dockerfile for the execution service
# It is used to pull the code from the repository and run the code
# It will be deployed into the amazon ecs cluster, if any changes are made to the Dockerfile, and then the execution service can refer to this Dockerfile to run the code
FROM python:3.11-slim

# Install system dependencies and cleanup in one layer
RUN apt-get update && apt-get install -y \
    git \
    curl \
    && pip install --no-cache-dir --upgrade pip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

RUN pip install browserbase vibe-automation \
    && playwright install
   

# Set working directory
WORKDIR /home/<USER>/app

# Environment variables for the repo and commit
ENV REPO_URL=""
ENV COMMIT_HASH=""
ENV PYTHON_PATH="/home/<USER>/app"

# Copy entrypoint script and make it executable
COPY ../../entrypoint.sh /home/<USER>/entrypoint.sh
# Make the entrypoint script executable
RUN chmod +x /home/<USER>/entrypoint.sh

# Expose port for potential web services in the future
EXPOSE 8080

ENTRYPOINT ["/home/<USER>/entrypoint.sh"]
