import types

from orby.va.browser_session_pb2 import CreateSessionRequest, ServiceType

from execution.v1.services.browser.browserbase_manager import BrowserbaseManager
from execution.v1.services.browser_service import BrowserService


class DummyManager:
    def create_session(self, workflow_id: str):
        return (
            ServiceType.SERVICE_TYPE_UNSPECIFIED,
            "wss://dummy-session",
            "https://dummy-live",
        )


def test_dummy_manager_create_session():
    service = BrowserService()
    service.session_manager = DummyManager()

    request = CreateSessionRequest(workflow_id="workflow_123")
    response = service.create_session(request)

    assert response.service_type == ServiceType.SERVICE_TYPE_UNSPECIFIED
    assert response.connection_url == "wss://dummy-session"
    assert response.live_view_url == "https://dummy-live"


def test_browserbase_manager_create_session(monkeypatch):
    # Mock os.getenv
    monkeypatch.setattr(
        "execution.v1.services.browser.browserbase_manager.os.getenv",
        lambda key, default=None: {
            "BB_API_KEY": "fake_api_key",
            "BB_PROJECT_ID": "fake_project_id",
        }.get(key, default),
    )

    # Mock Browserbase class
    class MockBrowserbase:
        def __init__(self, api_key):
            self.contexts = types.SimpleNamespace(
                create=lambda project_id: types.SimpleNamespace(
                    id="context_abc"
                )
            )
            self.sessions = types.SimpleNamespace(
                create=lambda **kwargs: types.SimpleNamespace(
                    connectUrl="wss://connect-url", id="session_id"
                ),
                debug=lambda session_id: types.SimpleNamespace(
                    debuggerFullscreenUrl="https://live-view"
                ),
            )

    monkeypatch.setattr(
        "execution.v1.services.browser.browserbase_manager.Browserbase",
        MockBrowserbase,
    )

    manager = BrowserbaseManager()
    service_type, conn_url, live_url = manager.create_session("workflow-xyz")

    assert service_type == ServiceType.BROWSER_BASE
    assert conn_url == "wss://connect-url"
    assert live_url == "https://live-view"
