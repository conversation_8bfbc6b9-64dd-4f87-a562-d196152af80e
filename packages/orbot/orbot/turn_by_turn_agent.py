"""
Turn-by-turn agent implementation following Anthropic best practices.
Handles multi-turn conversations with tool calling and streaming responses.
"""

from collections.abc import AsyncGenerator
import copy
from dataclasses import dataclass
from enum import Enum
import typing
from typing import Any

from langchain.schema import (
    AIMessage,
    BaseMessage,
    HumanMessage,
    SystemMessage,
)
from langchain_anthropic import ChatAnthropic
from langchain_core.messages import ToolMessage
from langchain_core.messages.tool import ToolCall
from langchain_core.runnables import RunnableConfig
import tiktoken

from common.log import error, info

from .configuration import Configuration
from .system_prompt import get_compression_prompt
from .tools import (
    ToolRegistry,
    ToolResult,
)


class TurnType(Enum):
    """Types of turns in the conversation."""

    USER_MESSAGE = "user_message"
    TOOL_CALL = "tool_call"
    SPEAKER_SELECTION = "speaker_selection"
    AI_THINKING = "ai_thinking"
    AI_RESPONSE = "ai_response"
    ERROR = "error"


@dataclass
class TurnResult:
    """Result of a single turn in the conversation."""

    turn_type: TurnType
    content: str
    metadata: dict[str, Any]
    tool_name: str | None = None
    status: str | None = None

    def to_dict(self) -> dict[str, Any]:
        """Convert TurnResult to a dictionary for JSON serialization."""
        return {
            "turn_type": self.turn_type.value,
            "content": self.content,
            "metadata": self.metadata,
            "tool_name": self.tool_name,
        }

class TurnByTurnAgent:
    """
    Turn-by-turn agent that handles multi-turn conversations with tool calling.

    Features:
    - Streaming responses for each turn
    - Tool calling with result handling
    - Speaker selection when no tools are called
    - State management across turns
    - Context compression for long conversations
    """

    def __init__(
        self,
        tool_registry: ToolRegistry,
        config: Configuration,
        session_id,
        system_prompt: str,
        compression_prompt: str | None = None,
    ):
        config.validate_required_fields()
        self._config = config
        self.session_id = session_id
        self.tool_registry = tool_registry
        self._system_prompt = system_prompt
        self._compression_prompt = compression_prompt or get_compression_prompt()

        # Store initialization parameters
        self._thinking_config = None
        if self._config.enable_thinking:
            self._thinking_config = {
                "type": "enabled",
                "budget_tokens": self._config.thinking_budget_tokens,
            }

        # Initialize conversation state with system prompt (with cache control)
        self.conversation_history = []
        # Cache the system prompt as it's stable across the conversation
        system_message = SystemMessage(content=self._system_prompt)
        self.conversation_history.append(system_message)
        self.turn_count = 0
        self.bind_tools(self.tool_registry.get_all_tools())
        
        # Initialize tokenizer for Claude models
        # Claude uses cl100k_base encoding (same as GPT-4)
        self._encoding = tiktoken.get_encoding("cl100k_base")


    async def process_user_message(
        self, message: str
    ) -> AsyncGenerator[TurnResult]:
        """
        Process a user message through multiple turns until completion.

        Args:
            message: The user's input message

        Yields:
            TurnResult objects for each turn in the conversation
        """
        # Check if the last message in conversation history was a tool_use without corresponding tool_result
        self._handle_interrupted_tool_calls()
        
        # Add user message to conversation history
        self.conversation_history.append(HumanMessage(content=message))

        # Continue processing turns until completion
        while self.turn_count < self._config.max_turns:
            # Get AI response with potential tool calls
            last_turn_result = None
            async for turn_result in self._process_turn():
                self.turn_count += 1
                last_turn_result = turn_result
                yield turn_result
                if turn_result.turn_type == TurnType.TOOL_CALL and turn_result.tool_name == "message_ask_user":
                    return

            if not last_turn_result:
                info("Turn ended with no turn result.")
                return

            # If no tool calls, decide on next speaker or end conversation
            if last_turn_result and last_turn_result.turn_type == TurnType.AI_RESPONSE:
                # Select next speaker if conversation continues
                next_speaker = await self._select_next_speaker()
                if next_speaker and next_speaker == "assistant":
                    info(
                        f"Turn {self.turn_count}: Yielding SPEAKER_SELECTION result for {next_speaker}"
                    )
                    yield TurnResult(
                        turn_type=TurnType.SPEAKER_SELECTION,
                        content=f"Next speaker: {next_speaker}",
                        metadata={
                            "speaker": next_speaker,
                            "turn": self.turn_count,
                        },
                    )
                    self.conversation_history.append(
                        AIMessage(content="please continue")
                    )
                else:
                    # turn finished and waiting for user input
                    return
            if self.turn_count ==  self._config.max_turns:
                info("max turn has reached")
                self.conversation_history.append(
                    AIMessage(content="Have reached maximum number of steps for this iteration. Should I continue?")
                )
                yield TurnResult(
                    turn_type=TurnType.AI_RESPONSE,
                    content="Have reached maximum number of steps for this iteration. Should I continue?",
                    metadata={"turn": self.turn_count},
                )
                self.turn_count = 0

    def _prepare_messages_with_cache(self, messages: list[BaseMessage]) -> list[BaseMessage]:
        """
        Prepare messages with cache control for optimizing long conversations.
        
        Cache strategy:
            For now, always cache the entire message by setting the cache_control flag on the last message.

            Note: Prompt caching references the entire prompt - tools, system, and messages (in that order) up
            to and including the block designated with cache_control.
        """
        if (len(messages) == 0):
            return messages

        # Log cache strategy
        info("Applying cache control to last message")
        # Create modified messages with cache control
        cached_messages = copy.deepcopy(messages[:-1])
        last_message = copy.deepcopy(messages[-1])
        if isinstance(last_message.content, str):
            last_message.content = [
                {
                    "text": last_message.content,
                    "type": "text",
                    "cache_control": {"type": "ephemeral"},
                }
            ]
        elif last_message.content and isinstance(last_message.content, list):
            typing.cast(list, last_message.content)[-1]["cache_control"] = {"type": "ephemeral"}

        cached_messages.append(last_message)
        return cached_messages

    async def _process_turn(self) -> AsyncGenerator[TurnResult]:
        """Process a single AI turn with potential tool calls."""
        try:
            # Check if compression is needed before processing
            await self._try_compress_chat()
            # Create messages for LangChain with cache control
            messages = self._prepare_messages_with_cache(self.conversation_history)

            # Use session tracking for speaker selection
            config = None
            if self.session_id:
                config = RunnableConfig(configurable={"session_id": self.session_id})
            response = await self.client.ainvoke(
                messages, config=config
            )            
            
            # Check if response has thinking content
            any_response = False
            if hasattr(response, "content") and isinstance(
                response.content, list):
                for block in response.content:
                    # Handle both dict and object formats
                    if isinstance(block, dict):
                        block_type = block.get("type")
                        if block_type == "thinking":
                            thinking_content = block.get("thinking", "")
                            if thinking_content:
                                any_response = True
                                yield TurnResult(
                                    turn_type=TurnType.AI_THINKING,
                                    content=thinking_content,
                                    metadata={"turn": self.turn_count},
                                )
                        elif block_type == "text":
                            text_content = block.get("text", "")
                            if text_content:
                                any_response = True
                                yield TurnResult(
                                    turn_type=TurnType.AI_RESPONSE,
                                    content=text_content,
                                    metadata={"turn": self.turn_count},
                                )
                    elif isinstance(block, str):
                        if block:
                            # Yield AI response
                            any_response = True
                            yield TurnResult(
                                turn_type=TurnType.AI_RESPONSE,
                                content=block,
                                metadata={"turn": self.turn_count},
                            )
            else:
                # Handle simple string content
                if response.content:
                    # Yield AI response
                    any_response = True
                    yield TurnResult(
                        turn_type=TurnType.AI_RESPONSE,
                        content=str(response.content),
                        metadata={"turn": self.turn_count},
                    )
            if any_response or (isinstance(response, AIMessage) and len(response.tool_calls) >0):
                # make sure not empty message is sent
                self.conversation_history.append(response)

            # Process tool calls if any
            if isinstance(response, AIMessage) and len(response.tool_calls) > 0:
                if len(response.tool_calls) > 1:
                    raise Exception(f"model returned multiple tool calls: {response.tool_calls}")
                async for tool_result in self._process_tool_calls(response.tool_calls):
                    yield tool_result

        except Exception as e:
            info(
                f"Turn {self.turn_count}: Yielding ERROR result due to exception: {str(e)}"
            )
            yield TurnResult(
                turn_type=TurnType.ERROR,
                content=f"Error: {str(e)}",
                metadata={"error": True, "turn": self.turn_count},
            )

    async def _process_tool_calls(
        self, tool_calls: list[ToolCall]
    ) -> AsyncGenerator[TurnResult]:
        """Process tool calls and handle their results."""
        for tool_call in tool_calls:
            # Execute tool (this would be implemented based on your tool system)
            tool_result = await self._execute_tool(tool_call)            
            if self._count_string_tokens(tool_result.content) > self._config.max_tool_response:
                tool_result.content = f"Tool result is too long. Truncated tool result (<{self._config.max_tool_response}): {tool_result.content[:self._config.max_tool_response]}"
                info(f"tool result is too long. truncated. Session: {self.session_id}")
            tool_message = ToolMessage(
                tool_call_id=tool_call.get("id", ""),
                content=tool_result.content
                if tool_result.success
                else f"Error: {tool_result.error}",
                status="success" if tool_result.success else "error",
            )
            # Add tool result to conversation for next LLM call
            self.conversation_history.append(tool_message)
            yield TurnResult(
                turn_type=TurnType.TOOL_CALL,
                tool_name=tool_call["name"],
                content=tool_result.content
                if tool_result.success
                else f"Error: {tool_result.error}",
                metadata={
                    "tool_call_id": tool_message.tool_call_id,
                    "tool_call_input": tool_call.get("args", ""),
                    "turn": self.turn_count,
                    "status": tool_message.status
                },
            )

    async def _execute_tool(self, tool_call: ToolCall) -> ToolResult:
        """
        Execute a tool call using the tool registry.
        """

        try:
            # Execute tool through registry
            result = await self.tool_registry.execute_tool(
                tool_call.get("name", ""), tool_call.get("args", "")
            )

            # Return the ToolResult directly
            return result

        except Exception as e:
            error(f"Error executing tool {tool_call["name"]}: {e}")
            return ToolResult(success=False, content="", error=str(e))

    async def _select_next_speaker(self) -> str | None:
        """Select the next speaker based on conversation analysis."""
        try:
            # Analyze the last few messages for context
            recent_messages = (
                self.conversation_history[-3:]
                if len(self.conversation_history) > 3
                else self.conversation_history
            )

            # Get the last AI message
            last_ai_message = None
            for msg in reversed(self.conversation_history):
                if isinstance(msg, AIMessage):
                    last_ai_message = msg
                    break

            if not last_ai_message:
                return "user"  # If no AI message, user should speak

            # Extract text content from the message (handle both string and list formats)
            last_content = self._extract_text_content(last_ai_message.content)

            # Rule 1: If last message is empty or very short, model should continue
            if not last_content or len(last_content) < 10:
                return "assistant"

            # Rule 2: If ends with question mark, user should respond
            if last_content.endswith("?"):
                return "user"

            # Rule 3: If contains phrases requesting user input
            user_request_phrases = [
                "what do you think",
                "can you tell me",
                "would you like",
                "do you want",
                "please provide",
                "let me know",
                "your thoughts",
                "your opinion",
            ]

            if any(
                phrase in last_content.lower()
                for phrase in user_request_phrases
            ):
                return "user"

            # Rule 4: If message seems incomplete (ends mid-sentence)
            incomplete_endings = [
                ", but",
                ", however",
                ", although",
                "for example",
                "such as",
                "including",
                "specifically",
            ]

            if any(
                last_content.lower().endswith(ending)
                for ending in incomplete_endings
            ):
                return "assistant"

            # Rule 5: Use LLM for complex decision making
            speaker_prompt = f"""
            Analyze this conversation and determine who should speak next.
            
            Recent conversation:
            {self._format_conversation_for_analysis(recent_messages)}
            
            Rules:
            1. If the assistant's last response asks a direct question, the user should respond
            2. If the assistant's response seems incomplete or cut off, the assistant should continue
            3. If the assistant completed a thought and provided helpful information without asking questions, the user should respond
            4. If there's a function/tool response, the assistant should speak next
            
            Respond with only one word: "user", "assistant", or "stop" (to end conversation).
            """

            messages = [
                SystemMessage(content=speaker_prompt),
                HumanMessage(content="Who should speak next?"),
            ]

            # Use session tracking for speaker selection
            config = None
            if self.session_id:
                config = RunnableConfig(configurable={"session_id": self.session_id})
            response = await self.client.ainvoke(
                messages, config=config
            )
            
            # Log speaker selection model usage
            if hasattr(response, 'response_metadata'):
                # Just log basic usage for speaker selection without updating cumulative totals
                metadata = response.response_metadata
                usage = metadata.get('usage', {})
                input_tokens = usage.get('input_tokens', 0)
                output_tokens = usage.get('output_tokens', 0)
                cache_read_tokens = usage.get('cache_read_input_tokens', 0)
                
                info(f"Speaker selection model usage: "
                     f"input_tokens={input_tokens}, "
                     f"output_tokens={output_tokens}, "
                     f"cache_read_tokens={cache_read_tokens}")
            
            speaker = (
                self._extract_text_content(response.content).strip().lower()
            )

            if speaker == "stop":
                return None  # End conversation
            elif speaker in ["assistant", "user"]:
                return speaker
            else:
                # Default fallback
                return "user"

        except Exception as e:
            error(f"Error selecting next speaker: {e}")
            return "user"  # Default to user on error

    def _extract_text_content(self, content: str | list[str | dict]) -> str:
        """Extract text content from message content (handles both string and list formats)."""
        if isinstance(content, str):
            return content
        elif isinstance(content, list):
            text_parts = []
            for block in content:
                if isinstance(block, dict) and block.get("type") == "text":
                    text_parts.append(block.get("text", ""))
                elif isinstance(block, str):
                    text_parts.append(block)
            return " ".join(text_parts)
        else:
            return str(content)

    def _format_conversation_for_analysis(
        self, messages: list[BaseMessage]
    ) -> str:
        """Format recent messages for speaker analysis."""
        formatted = []
        for msg in messages:
            if isinstance(msg, HumanMessage):
                formatted.append(
                    f"User: {self._extract_text_content(msg.content)}"
                )
            elif isinstance(msg, AIMessage):
                formatted.append(
                    f"Assistant: {self._extract_text_content(msg.content)}"
                )
            elif isinstance(msg, ToolMessage):
                formatted.append(f"Function: {msg.content}")
        return "\n".join(formatted)

    def reset_conversation(self):
        """Reset the conversation state."""
        self.conversation_history = []
        self.turn_count = 0

    def _handle_interrupted_tool_calls(self):
        """
        Check if the last message in conversation history was a tool_use without corresponding tool_result.
        If so, add a tool result saying that the tool call was canceled by the user.
        """
        if not self.conversation_history:
            return
            
        last_message = self.conversation_history[-1]
        
        # Check if the last message is an AI message with tool calls
        if isinstance(last_message, AIMessage) and hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            # Check if there are corresponding tool results after this message
            # Since we're at the start of process_user_message, any tool calls in the last AI message
            # should have been followed by tool results, but due to interruption they weren't
            
            # Add a tool result for each tool call indicating cancellation
            for tool_call in last_message.tool_calls:
                tool_message = ToolMessage(
                    tool_call_id=tool_call.get("id", ""),
                    content="Tool call was canceled by user interruption.",
                    status="error",
                )
                self.conversation_history.append(tool_message)
                info(f"Added canceled tool result for interrupted tool call: {tool_call.get('name', 'unknown')}")


    def bind_tools(self, tools):
        self.client = ChatAnthropic(
            api_key=self._config.api_key,
            model=self._config.model,  # type: ignore
            max_tokens=self._config.max_tokens, # type: ignore
            temperature=self._config.temperature,
            thinking=self._thinking_config,
        ).bind_tools(tools)

    def _count_string_tokens(self, msg: str) -> int:
        """Count tokens in a single str. """
        return len(self._encoding.encode(msg)) + 4
    
    def _count_message_tokens(self, message: BaseMessage) -> int:
        """Count tokens in a single message."""
        content = self._extract_text_content(message.content)
        # Add overhead for message metadata (role, etc.)
        return self._count_string_tokens(content)
    
    def _count_conversation_tokens(self, messages: list[BaseMessage] | None = None) -> int:
        """Count total tokens in conversation history."""
        messages = messages or self.conversation_history
        total_tokens = 0
        for message in messages:
            total_tokens += self._count_message_tokens(message)
        # Add base prompt overhead
        return total_tokens + 3
    
    def _find_index_after_fraction(self, messages: list[BaseMessage], fraction: float) -> int:
        """Find the index in messages after which we've consumed the given fraction of tokens."""
        total_tokens = self._count_conversation_tokens(messages)
        target_tokens = int(total_tokens * fraction)
        
        current_tokens = 0
        for i, message in enumerate(messages):
            current_tokens += self._count_message_tokens(message)
            if current_tokens >= target_tokens:
                return min(i, len(messages))
        
        return len(messages)
    
    async def _try_compress_chat(self) -> None:
        """
        Compress conversation history when it exceeds token threshold.
        Compress when >70% of limit, always preserve last 30%.
        """
        # Skip if compression is disabled
        if not self._config.compression_enabled:
            return
            
        # Get model's context window size from configuration
        model_context_limit = self._config.context_window_size
        
        # Count current tokens
        current_tokens = self._count_conversation_tokens()
        
        # Check if compression is needed
        if current_tokens < model_context_limit * self._config.compression_token_threshold:
            info(f"No compression needed. Current tokens: {current_tokens}, threshold: {int(model_context_limit * self._config.compression_token_threshold)}")
            return
        
        info(f"Compressing conversation history. Current tokens: {current_tokens}")
        
        # Find the index to compress before (preserve configured percentage)
        compress_before_index = self._find_index_after_fraction(
            self.conversation_history[1:],  # Skip system message
            1 - self._config.compression_preserve_threshold
        ) + 1  # Adjust for skipping system message
        
        if compress_before_index <= 1:
            info("Not enough history to compress")
            return

        # Extract messages to compress and preserve
        messages_to_compress = self.conversation_history[1:compress_before_index]
        preserved_messages = self.conversation_history[compress_before_index:]
        
        try:
            # Create a minimal client for compression (no tools needed)
            compression_client = ChatAnthropic(
                api_key=self._config.api_key,
                model=self._config.model,  # type: ignore
                max_tokens=2000,  # type: ignore
                temperature=0,
            )
            
            compression_context: list[BaseMessage] = [SystemMessage(content=self._compression_prompt)] 
            compression_context.extend(messages_to_compress)
            compression_context.append(HumanMessage(content="First, reason in your scratchpad. Then, generate the <state_snapshot>."))
            response = await compression_client.ainvoke(compression_context)
            
            summary_content = self._extract_text_content(response.content)
            
            # Reconstruct conversation history with compressed summary
            compressed_history = [
                self.conversation_history[0],  # Keep system message
                AIMessage(content=f"[Previous conversation summary]:\n{summary_content}")
            ]
            compressed_history.extend(preserved_messages)
            
            # Update conversation history
            old_token_count = current_tokens
            self.conversation_history = compressed_history
            new_token_count = self._count_conversation_tokens()
            
            info(f"Compression complete. Tokens reduced from {old_token_count} to {new_token_count} "
                 f"(saved {old_token_count - new_token_count} tokens, {100 * (1 - new_token_count/old_token_count):.1f}% reduction)")
            
        except Exception as e:
            error(f"Failed to compress conversation history: {e}")
            # Continue without compression on error
            raise e

