"""
Browser Input Tool implementation.
"""



from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.sandbox.base import RuntimeExecutionError

from .base_tool import RuntimeBaseTool


class BrowserInputInput(BaseModel):
    """Input schema for browser input tool."""
    index: int | None = Field(None, description="(Optional) Index number of the element to overwrite text")
    coordinate_x: float | None = Field(None, description="(Optional) X coordinate of the element to overwrite text")
    coordinate_y: float | None = Field(None, description="(Optional) Y coordinate of the element to overwrite text")
    text: str = Field(..., description="Complete text content to overwrite")
    press_enter: bool = Field(..., description="Whether to press Enter key after input")


class BrowserInputTool(RuntimeBaseTool):
    """Tool for overwriting text in editable elements on the current browser page."""
    
    name: str = "browser_input"
    description: str = "Overwrite text in editable elements on the current browser page. Use when filling content in input fields."
    
    args_schema: ArgsSchema | None = BrowserInputInput
    
    def _run(self, text: str, press_enter: bool, index: int | None = None, coordinate_x: float | None = None, coordinate_y: float | None = None) -> str:
        """Execute browser input synchronously."""
        import asyncio
        return asyncio.run(self._browser_input(text, press_enter, index, coordinate_x, coordinate_y))
    
    async def _arun(self, text: str, press_enter: bool, index: int | None = None, coordinate_x: float | None = None, coordinate_y: float | None = None) -> str:
        """Execute browser input asynchronously."""
        return await self._browser_input(text, press_enter, index, coordinate_x, coordinate_y)
    
    async def _browser_input(self, text: str, press_enter: bool, index: int | None = None, coordinate_x: float | None = None, coordinate_y: float | None = None) -> str:
        """Perform browser input operation."""
        # Ensure runtime is available
        if not await self._ensure_runtime():
            raise RuntimeExecutionError("Runtime not available for browser operations")
        
        runtime = self._get_runtime()
        
        try:
            # Determine input method based on provided parameters
            if index is not None:
                # Input by element index
                result = await runtime.browser_input(index=index, text=text, press_enter=press_enter)
                return f"Input text '{text}' into element at index {index}{'and pressed Enter' if press_enter else ''}. Result: {result}"
            elif coordinate_x is not None and coordinate_y is not None:
                # Input by coordinates
                result = await runtime.browser_input(coordinate_x=coordinate_x, coordinate_y=coordinate_y, text=text, press_enter=press_enter)
                return f"Input text '{text}' at coordinates ({coordinate_x}, {coordinate_y}){'and pressed Enter' if press_enter else ''}. Result: {result}"
            else:
                raise ValueError("Either index or both coordinate_x and coordinate_y must be provided")
                
        except Exception as e:
            error_msg = f"Browser input operation failed: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeExecutionError(error_msg) from e
