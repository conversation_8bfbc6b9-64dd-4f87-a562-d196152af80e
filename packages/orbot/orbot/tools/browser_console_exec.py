"""
Browser Console Exec Tool implementation.
"""


from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.sandbox.base import RuntimeExecutionError

from .base_tool import RuntimeBaseTool


class BrowserConsoleExecInput(BaseModel):
    """Input schema for browser console exec tool."""
    javascript: str = Field(..., description="JavaScript code to execute. Note that the runtime environment is browser console.")


class BrowserConsoleExecTool(RuntimeBaseTool):
    """Tool for executing JavaScript code in browser console."""
    
    name: str = "browser_console_exec"
    description: str = "Execute JavaScript code in browser console. Use when custom scripts need to be executed."
    
    args_schema: ArgsSchema | None = BrowserConsoleExecInput
    
    def _run(self, javascript: str) -> str:
        """Execute browser console exec synchronously."""
        import asyncio
        return asyncio.run(self._browser_console_exec(javascript))
    
    async def _arun(self, javascript: str) -> str:
        """Execute browser console exec asynchronously."""
        return await self._browser_console_exec(javascript)
    
    async def _browser_console_exec(self, javascript: str) -> str:
        """Perform browser console exec operation."""
        # Ensure runtime is available
        if not await self._ensure_runtime():
            raise RuntimeExecutionError("Runtime not available for browser operations")
        
        runtime = self._get_runtime()
        
        try:
            result = await runtime.browser_console_exec(javascript=javascript)
            return f"Executed JavaScript in console:\n{javascript}\n\nResult: {result}"
                
        except Exception as e:
            error_msg = f"Browser console exec operation failed: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeExecutionError(error_msg) from e
