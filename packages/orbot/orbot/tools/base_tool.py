"""
Base tool class with runtime management capabilities.
"""

import logging

from langchain_core.tools import BaseTool

from common.sandbox.base import Runtime
from web_server.session.runtime_manager import get_runtime_session_manager


class RuntimeBaseTool(BaseTool):
    """Base class for tools that need runtime management capabilities."""
    
    # Allow extra fields for runtime management
    model_config = {"extra": "allow"}
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.runtime: Runtime | None = None
        self.logger = logging.getLogger(__name__)
        self._session_id = None
    
    @property
    def session_id(self) -> str:
        """Get session ID lazily from Chainlit context."""
        if self._session_id is None:
            try:
                import chainlit as cl
                self._session_id = cl.context.session.id
            except Exception:
                # Fallback to a default session ID if context not available
                self._session_id = "default_session"
        return self._session_id
    
    async def _ensure_runtime(self) -> bool:
        """Ensure runtime is available and initialized."""
        try:
            if not self.runtime:
                # Initialize runtime using runtime session manager
                self.runtime = await get_runtime_session_manager().get_runtime(
                    session_id=self.session_id
                )
            
            self.logger.info(f"Runtime ready for session: {self.session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to ensure runtime: {e}")
            return False
    
    def _has_runtime(self) -> bool:
        """Check if runtime is available."""
        return self.runtime is not None
    
    def _get_runtime(self) -> Runtime:
        """Get the runtime instance, raising an error if not available."""
        if self.runtime is None:
            raise RuntimeError("Runtime not available for file operations. Call _ensure_runtime() first.")
        return self.runtime
