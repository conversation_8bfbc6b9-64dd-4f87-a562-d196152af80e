"""
File tools implementation based on Gemini CLI patterns.
Provides file operations with security and validation using Runtime system.
"""

from datetime import datetime
from difflib import unified_diff
import mimetypes
import os

from pydantic import BaseModel

# Import Runtime system
from common.sandbox.base import Runtime, RuntimeExecutionError

from .base_tool import RuntimeBaseTool

# Common constants
DEFAULT_DIFF_CONTEXT: int = 3


class FileToolError(Exception):
    """Base exception for file tool errors."""
    pass


class PathValidationError(FileToolError):
    """Path validation error."""
    pass


class FileAccessError(FileToolError):
    """File access error."""
    pass


class PatternError(FileToolError):
    """Pattern matching error."""
    pass


class FileEntry(BaseModel):
    """File/directory entry information."""
    name: str
    path: str
    type: str  # 'file' or 'directory'
    size: int | None = None
    modified: datetime


class FileToolBase(RuntimeBaseTool):
    """Base class for file tools with common utilities using Runtime system."""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
    
    async def _ensure_file_runtime(self) -> Runtime:
        """Ensure runtime is available and return it."""
        success = await self._ensure_runtime()
        if not success:
            raise RuntimeExecutionError("Failed to initialize runtime for file operations")
        return self._get_runtime()
    
    async def _get_full_path(self, relative_path: str) -> str:
        """Get the full path for a relative path using Runtime."""
        try:
            runtime = await self._ensure_file_runtime()
            full_path = runtime.get_full_path(relative_path)
            return str(full_path)
        except (RuntimeExecutionError, RuntimeError):
            # Fallback to using the original path
            return relative_path
    
    async def _read_file_content(self, path: str) -> str:
        """Read file content using Runtime or fallback to direct file operations."""
        try:
            # Try to ensure runtime is available
            runtime = await self._ensure_file_runtime()
            return await runtime.read_file(path)
        except (RuntimeExecutionError, RuntimeError) as e:
            raise FileAccessError(f"Cannot read file {path}: {e}") from e
    
    async def _write_file_content(self, path: str, content: str) -> None:
        """Write file content using Runtime or fallback to direct file operations."""
        try:
            # Try to ensure runtime is available
            runtime = await self._ensure_file_runtime()
            await runtime.write_file(path, content)
        except (RuntimeExecutionError, RuntimeError) as e:
            raise FileAccessError(f"Cannot write file {path}: {e}") from e
    
    def _get_mime_type(self, file_path: str) -> str:
        """Get MIME type of file."""
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or 'application/octet-stream'
    
    async def _read_text_file_lines(self, file_path: str, offset: int = -1, limit: int = -1) -> str:
        """Read text file with optional offset and limit."""
        content = await self._read_file_content(file_path)
        
        lines = content.splitlines()
        
        # Apply offset and limit
        start_idx = offset if offset > 0 else 0
        end_idx = start_idx + (limit if limit > 0 else len(lines))
        
        selected_lines = lines[start_idx:end_idx]
        
        # Add truncation notice if needed
        if limit and len(lines) > end_idx:
            selected_lines.append("... (content truncated)")
        
        return '\n'.join(selected_lines)
    
    async def _generate_diff(self, file_path: str, new_content: str) -> str:
        """Generate unified diff for file changes."""
        try:
            old_content = await self._read_file_content(file_path)
        except FileAccessError:
            old_content = ""
        
        old_lines = old_content.splitlines(keepends=True)
        new_lines = new_content.splitlines(keepends=True)
        
        diff = unified_diff(
            old_lines,
            new_lines,
            fromfile=f"a/{os.path.basename(file_path)}",
            tofile=f"b/{os.path.basename(file_path)}",
            n=DEFAULT_DIFF_CONTEXT
        )
        
        return ''.join(diff)
    
    
    async def _read_text_file(self, file_path: str) -> str:
        """Read text file content."""
        return await self._read_file_content(file_path)
