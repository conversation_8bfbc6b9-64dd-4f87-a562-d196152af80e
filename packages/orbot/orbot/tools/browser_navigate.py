"""
Browser Navigate Tool implementation.
"""



from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.sandbox.base import RuntimeExecutionError

from .base_tool import RuntimeBaseTool


class BrowserNavigateInput(BaseModel):
    """Input schema for browser navigate tool."""
    url: str = Field(..., description="Complete URL to visit. Must include protocol prefix.")


class BrowserNavigateTool(RuntimeBaseTool):
    """Tool for navigating browser to specified URL."""
    
    name: str = "browser_navigate"
    description: str = "Navigate browser to specified URL. Use when accessing new pages is needed."
    
    args_schema: ArgsSchema | None = BrowserNavigateInput
    
    def _run(self, url: str) -> str:
        """Execute browser navigate synchronously."""
        import asyncio
        return asyncio.run(self._browser_navigate(url))
    
    async def _arun(self, url: str) -> str:
        """Execute browser navigate asynchronously."""
        return await self._browser_navigate(url)
    
    async def _browser_navigate(self, url: str) -> str:
        """Perform browser navigate operation."""
        # Ensure runtime is available
        if not await self._ensure_runtime():
            raise RuntimeExecutionError("Runtime not available for browser operations")
        
        runtime = self._get_runtime()
        
        try:
            result = await runtime.browser_navigate(url)
            return result
                
        except Exception as e:
            error_msg = f"Browser navigate operation failed: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeExecutionError(error_msg) from e
