"""
Browser Restart Tool implementation.
"""



from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.sandbox.base import RuntimeExecutionError

from .base_tool import RuntimeBaseTool


class BrowserRestartInput(BaseModel):
    """Input schema for browser restart tool."""
    url: str = Field(..., description="Complete URL to visit after restart. Must include protocol prefix.")


class BrowserRestartTool(RuntimeBaseTool):
    """Tool for restarting browser and navigating to specified URL."""
    
    name: str = "browser_restart"
    description: str = "Restart browser and navigate to specified URL. Use when browser state needs to be reset."
    
    args_schema: ArgsSchema | None = BrowserRestartInput
    
    def _run(self, url: str) -> str:
        """Execute browser restart synchronously."""
        import asyncio
        return asyncio.run(self._browser_restart(url))
    
    async def _arun(self, url: str) -> str:
        """Execute browser restart asynchronously."""
        return await self._browser_restart(url)
    
    async def _browser_restart(self, url: str) -> str:
        """Perform browser restart operation."""
        # Ensure runtime is available
        if not await self._ensure_runtime():
            raise RuntimeExecutionError("Runtime not available for browser operations")
        
        runtime = self._get_runtime()
        
        try:
            result = await runtime.browser_restart(url)
            return result
                
        except Exception as e:
            error_msg = f"Browser restart operation failed: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeExecutionError(error_msg) from e
