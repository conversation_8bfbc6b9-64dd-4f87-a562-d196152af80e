"""
Browser Scroll Down Tool implementation.
"""



from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.sandbox.base import RuntimeExecutionError

from .base_tool import RuntimeBaseTool


class BrowserScrollDownInput(BaseModel):
    """Input schema for browser scroll down tool."""
    to_bottom: bool | None = Field(None, description="(Optional) Whether to scroll directly to page bottom instead of one viewport down.")


class BrowserScrollDownTool(RuntimeBaseTool):
    """Tool for scrolling down the current browser page."""
    
    name: str = "browser_scroll_down"
    description: str = "Scroll down the current browser page. Use when viewing content below or jumping to page bottom."
    
    args_schema: ArgsSchema | None = BrowserScrollDownInput
    
    def _run(self, to_bottom: bool | None = None) -> str:
        """Execute browser scroll down synchronously."""
        import asyncio
        return asyncio.run(self._browser_scroll_down(to_bottom))
    
    async def _arun(self, to_bottom: bool | None = None) -> str:
        """Execute browser scroll down asynchronously."""
        return await self._browser_scroll_down(to_bottom)
    
    async def _browser_scroll_down(self, to_bottom: bool | None = None) -> str:
        """Perform browser scroll down operation."""
        # Ensure runtime is available
        if not await self._ensure_runtime():
            raise RuntimeExecutionError("Runtime not available for browser operations")
        
        runtime = self._get_runtime()
        
        try:
            result = await runtime.browser_scroll_down(to_bottom=to_bottom)
            if to_bottom:
                return f"Scrolled to bottom of page. Result: {result}"
            else:
                return f"Scrolled down one viewport. Result: {result}"
                
        except Exception as e:
            error_msg = f"Browser scroll down operation failed: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeExecutionError(error_msg) from e
