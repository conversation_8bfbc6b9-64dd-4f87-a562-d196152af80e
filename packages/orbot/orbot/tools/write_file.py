"""
Write File Tool implementation based on Gemini CLI patterns.
"""

import os.path

from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.sandbox.base import NotFoundError

from .file_tools import FileAccessError, FileToolBase


class WriteFileInput(BaseModel):
    """Input schema for write file tool."""
    relative_path: str = Field(..., description="Relative path where to write the file")
    content: str = Field(..., description="Content to write to the file")

class WriteFileTool(FileToolBase):
    """Tool for writing content to files with diff generation and safety checks."""
    
    name: str = "write_file"
    description: str = "Writes content to a file. Creates parent directories if they don't exist. Generates a diff showing changes for existing files. Supports creating new files and overwriting existing ones. Uses relative paths from the workspace root."
    
    args_schema: ArgsSchema | None = WriteFileInput
    
    def _run(self, relative_path: str, content: str) -> str:
        """Execute file write synchronously."""
        import asyncio
        return asyncio.run(self._write_file(relative_path, content))
    
    async def _arun(self, relative_path: str, content: str) -> str:
        """Execute file write asynchronously."""
        return await self._write_file(relative_path, content)
    
    async def _write_file(self, relative_path: str, content: str) -> str:
        """Write content to file with validation and diff generation."""
        # Validate that the path is actually relative, not absolute
        if os.path.isabs(relative_path):
            raise FileAccessError(f"Only relative paths are allowed. Got absolute path: {relative_path}")
        content = self._validate_content(content)
       
       # Check if file exists and get info
        diff_output = ""
        old_content = ""
        try:
           # Read current content using runtime
           old_content = await self._read_file_content(relative_path)
           if old_content:
               diff_output = await self._generate_diff(relative_path, content)
        except NotFoundError:
            pass
        except Exception as e:
           diff_output =  diff_output = f"Warning: Could not generate diff: {e}\n"
        
        # Write the file
        await self._write_file_content(relative_path, content)
        
        # Generate result message
        result = []
        
        if len(old_content) > 0:
            result.append(f"File updated: {relative_path}")
            if diff_output:
                result.append("\nChanges made:")
                result.append(diff_output)
        else:
            result.append(f"File created: {relative_path}")
        
        # Add file statistics
        file_size = len(content)
        line_count = content.count('\n') + (1 if content and not content.endswith('\n') else 0)
        result.append("\nFile statistics:")
        result.append(f"- Size: {file_size} bytes")
        result.append(f"- Lines: {line_count}")

        return '\n'.join(result)
    
    def _validate_content(self, content: str) -> str:
        """Validate and normalize content before writing."""
        if not isinstance(content, str):
            raise ValueError("Content must be a string")
        
        # Check for extremely large content
        if len(content) > 100 * 1024 * 1024:  # 100MB
            raise ValueError("Content too large (max 100MB)")
        
        return content
