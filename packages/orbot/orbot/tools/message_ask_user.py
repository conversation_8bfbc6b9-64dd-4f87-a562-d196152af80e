"""
Message Ask User Tool implementation.
Allows the agent to ask questions to the user and wait for responses using Chainlit.
"""

from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.log import debug, info

from .base_tool import RuntimeBaseTool


class MessageAskUserInput(BaseModel):
    """Input schema for message ask user tool."""
    text: str = Field(..., description="Question text to present to user")
    attachments: list[str] | None = Field(
        None, 
        description="(Optional) List of question-related files or reference materials"
    )
    suggest_user_takeover: str | None = Field(
        None,
        description="(Optional) Suggested operation for user takeover",
        pattern="^(none|browser)$"
    )


class MessageAskUserTool(RuntimeBaseTool):
    """
    Tool for asking questions to the user and waiting for responses.
    Used for requesting clarification, asking for confirmation, or gathering additional information.
    """
    
    name: str = "message_ask_user"
    description: str = (
        "Ask user a question and wait for response. Use for requesting clarification, "
        "asking for confirmation, or gathering additional information."
    )
    
    args_schema: ArgsSchema | None = MessageAskUserInput
    
    def _run(self, text: str, attachments: list[str] | None = None, 
             suggest_user_takeover: str | None = None) -> str:
        """Execute message ask user synchronously."""
        import asyncio
        return asyncio.run(self._ask_user(text, attachments, suggest_user_takeover))
    
    async def _arun(self, text: str, attachments: list[str] | None = None,
                    suggest_user_takeover: str | None = None) -> str:
        """Execute message ask user asynchronously."""
        return await self._ask_user(text, attachments, suggest_user_takeover)
    
    async def _ask_user(self, text: str, attachments: list[str] | None = None,
                        suggest_user_takeover: str | None = None) -> str:
        """
        Ask the user a question using Chainlit's AskUserMessage and return their response.
        
        Args:
            text: The question to ask the user
            attachments: Optional files or reference materials
            suggest_user_takeover: Optional suggestion for user takeover (browser)
        
        Returns:
            The user's response as a string
        """
        # Format the question with additional context if provided
        formatted_question = text
        
        # Add attachment information if provided
        if attachments:
            formatted_question += f"\n\nRelated files: {', '.join(attachments)}"
        
        # Add takeover suggestion if provided
        if suggest_user_takeover and suggest_user_takeover == "browser":
            formatted_question += "\n\n💡 Tip: You may want to take over browser control for this task."
        
        # Log the question being asked
        info(f"Asking user: {text}")
        if attachments:
            debug(f"With attachments: {attachments}")
        if suggest_user_takeover:
            debug(f"Suggesting takeover: {suggest_user_takeover}")
        
        return formatted_question
