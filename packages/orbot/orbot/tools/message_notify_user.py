"""
Message Notify User Tool implementation.
Sends messages to users without requiring a response using Chainlit.
"""

import chainlit as cl
from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.log import debug, error, info

from .base_tool import RuntimeBaseTool


class MessageNotifyUserInput(BaseModel):
    """Input schema for message notify user tool."""
    text: str = Field(..., description="Message text to display to user")
    attachments: list[str] | None = Field(
        None, 
        description="(Optional) List of attachments to show to user, can be file paths or URLs"
    )


class MessageNotifyUserTool(RuntimeBaseTool):
    """
    Tool for sending notification messages to the user without requiring a response.
    Used for acknowledging receipt of messages, providing progress updates, 
    reporting task completion, or explaining changes in approach.
    """
    
    name: str = "message_notify_user"
    description: str = (
        "Send a message to user without requiring a response. Use for acknowledging "
        "receipt of messages, providing progress updates, reporting task completion, "
        "or explaining changes in approach."
    )
    
    args_schema: ArgsSchema | None = MessageNotifyUserInput
    
    def _run(self, text: str, attachments: str | list[str] | None = None) -> str:
        """Execute message notify user synchronously."""
        import asyncio
        return asyncio.run(self._notify_user(text, attachments))
    
    async def _arun(self, text: str, attachments: str | list[str] | None = None) -> str:
        """Execute message notify user asynchronously."""
        return await self._notify_user(text, attachments)
    
    async def _notify_user(self, text: str, attachments: str | list[str] | None = None) -> str:
        """
        Send a notification message to the user using Chainlit's Message.
        
        Args:
            text: The message to send to the user
            attachments: Optional files or URLs to attach
        
        Returns:
            Success confirmation message.
        """
        # Log the notification
        info(f"Notifying user: {text[:100]}{'...' if len(text) > 100 else ''}")
        
        try:
            # Create Chainlit elements for attachments if provided
            elements = []
            if attachments:
                debug(f"With attachments: {attachments}")
                if isinstance(attachments, str):
                    attachments = [attachments]
                
                for attachment in attachments:
                    try:
                        if attachment.startswith(('http://', 'https://')):
                            # Create a link element for URLs
                            element = cl.Text(
                                name=attachment.split('/')[-1] or "Link",
                                content=attachment,
                                display="page"
                            )
                        else:
                            # Create a file element for file paths using runtime
                            # Ensure runtime is available
                            if not await self._ensure_runtime():
                                raise Exception("Runtime not available for file operations")
                            
                            # Read file content using runtime
                            try:
                                file_content = await self._get_runtime().read_bytes(attachment)
                            except Exception:
                                # Try reading as text and encode to bytes
                                file_text = await self._get_runtime().read_file(attachment)
                                file_content = file_text.encode('utf-8')
                            
                            element = cl.File(
                                name=attachment.split('/')[-1],
                                content=file_content,
                                display="inline"
                            )
                        # The following is needed for side or page mode
                        # if attachment.startswith(('http://', 'https://')):
                        #     text += f"\n\n🔗 {attachment}"
                        # else:
                        #     text += f"\n\n📎 {attachment}"
                        elements.append(element)
                    except Exception as e:
                        # If we can't create element, add to message text
                        err_msg = f"Failed to get attachment for {attachment}: {e}"
                        return err_msg
            

            # Send the message using Chainlit
            await cl.Message(
                content=text,
                elements=elements if elements else None
            ).send()
            
            return "Message sent to user successfully."
            
        except Exception as e:
            error(f"Error sending notification: {e}")
            return f"Error: Could not send notification - {str(e)}"
        
