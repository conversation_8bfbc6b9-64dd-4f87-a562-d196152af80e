"""
Browser Scroll Up Tool implementation.
"""



from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.sandbox.base import RuntimeExecutionError

from .base_tool import RuntimeBaseTool


class BrowserScrollUpInput(BaseModel):
    """Input schema for browser scroll up tool."""
    to_top: bool | None = Field(None, description="(Optional) Whether to scroll directly to page top instead of one viewport up.")


class BrowserScrollUpTool(RuntimeBaseTool):
    """Tool for scrolling up the current browser page."""
    
    name: str = "browser_scroll_up"
    description: str = "Scroll up the current browser page. Use when viewing content above or returning to page top."
    
    args_schema: ArgsSchema | None = BrowserScrollUpInput
    
    def _run(self, to_top: bool | None = None) -> str:
        """Execute browser scroll up synchronously."""
        import asyncio
        return asyncio.run(self._browser_scroll_up(to_top))
    
    async def _arun(self, to_top: bool | None = None) -> str:
        """Execute browser scroll up asynchronously."""
        return await self._browser_scroll_up(to_top)
    
    async def _browser_scroll_up(self, to_top: bool | None = None) -> str:
        """Perform browser scroll up operation."""
        # Ensure runtime is available
        if not await self._ensure_runtime():
            raise RuntimeExecutionError("Runtime not available for browser operations")
        
        runtime = self._get_runtime()
        
        try:
            result = await runtime.browser_scroll_up(to_top=to_top)
            if to_top:
                return f"Scrolled to top of page. Result: {result}"
            else:
                return f"Scrolled up one viewport. Result: {result}"
                
        except Exception as e:
            error_msg = f"Browser scroll up operation failed: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeExecutionError(error_msg) from e
