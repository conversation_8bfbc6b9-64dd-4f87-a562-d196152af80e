"""Tool registry for managing and discovering tools."""

from dataclasses import dataclass, field
import typing
from typing import Any

import chainlit as cl
from chainlit.session import WebsocketSession
from langchain_core.tools import BaseTool
from mcp import ClientSession

from common.log import error, info, warn
from orbot.configuration import Configuration
from orbot.tools.bash import Ba<PERSON>Tool
from orbot.tools.edit import EditTool
from orbot.tools.find_content_in_file import FindContentInFileTool
from orbot.tools.mcp_tool import MCPTool
from orbot.tools.message_ask_user import MessageAskUserTool
from orbot.tools.message_notify_user import MessageNotifyUserTool
from orbot.tools.read_file import ReadFileTool
from orbot.tools.update_todos import UpdateTodos
from orbot.tools.web_fetch import WebFetchTool
from orbot.tools.web_search import WebSearchTool
from orbot.tools.write_file import WriteFileTool


@dataclass
class ToolResult:
    """Result of a tool execution."""

    success: bool
    content: str
    error: str | None = None
    metadata: dict[str, Any] = field(default_factory=dict)


class ToolRegistry:
    """Registry for managing tools and their execution."""

    def __init__(self, config: Configuration):
        self.tools: dict[str, BaseTool] = {}
        self.registered_servers: set[str] = set()
        self._config = config

    def register_tool(self, tool: BaseTool) -> None:
        """Register a tool in the registry."""
        self.tools[tool.name] = tool

    def get_tool(self, name: str) -> BaseTool | None:
        """Get a tool by name."""
        return self.tools.get(name)

    def get_all_tools(self) -> list[BaseTool]:
        """Get all registered tools."""
        return list(self.tools.values())

    def get_tool_names(self) -> list[str]:
        """Get names of all registered tools."""
        return list(self.tools.keys())

    def get_function_declarations(self) -> list[dict[str, Any]]:
        """Get function declarations for all tools (for LLM)."""
        declarations = []
        for tool in self.tools.values():
            declarations.append(
                {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": tool.args_schema
                    if hasattr(tool, "args_schema")
                    else {},
                }
            )
        return declarations

    async def execute_tool(
        self, name: str, parameters: dict[str, Any]
    ) -> ToolResult:
        """Execute a tool by name with given parameters."""
        tool = self.get_tool(name)
        if not tool:
            return ToolResult(
                success=False, content="", error=f"Tool '{name}' not found"
            )

        try:
            # Execute LangChain tool using ainvoke
            result_str = await tool.ainvoke(parameters)

            info(f"Tool '{name}' executed successfully")
            return ToolResult(
                success=True, content=result_str, metadata={"tool_name": name}
            )

        except Exception as e:
            error(f"Error executing tool '{name}': {e}")
            return ToolResult(success=False, content="", error=str(e))

    def unregister_tool(self, name: str) -> bool:
        """Unregister a tool by name."""
        if name in self.tools:
            del self.tools[name]
            info(f"Unregistered tool: {name}")
            return True
        return False

    def clear_tools(self) -> None:
        """Clear all registered tools."""
        self.tools.clear()
        info("Cleared all tools")

    def list_tools(self) -> list[dict[str, str]]:
        """List all tools with their basic information."""
        return [
            {"name": tool.name, "description": tool.description}
            for tool in self.tools.values()
        ]

    async def discover_mcp_tools(self) -> int:
        """
        Discover MCP tools from all connected MCP sessions and register them.

        Args:
            registry: The tool registry to register tools with

        Returns:
            Number of tools discovered and registered
        """
        session = typing.cast(WebsocketSession, cl.context.session)

        total_tools = 0

        for server_name, (mcp_session, _) in session.mcp_sessions.items():
            # Skip if we've already registered tools from this server
            if server_name in self.registered_servers:
                continue
            await self.discovery_tools_of_mcp_server(server_name, mcp_session)

        return total_tools

    def unregister_mcp_server_tools(self, server_name: str) -> int:
        """
        Unregister all tools from a specific MCP server.

        Args:
            registry: The tool registry to unregister tools from
            server_name: Name of the MCP server

        Returns:
            Number of tools unregistered
        """
        if server_name not in self.registered_servers:
            return 0

        tools_to_remove = []
        for tool in self.get_all_tools():
            if type(tool) is MCPTool and tool.mcp_server == server_name:
                tools_to_remove.append(tool.name)
                self.unregister_tool(tool.name)

        self.registered_servers.discard(server_name)
        info(
            f"Unregistered {len(tools_to_remove)} tools from MCP server: {server_name}"
        )

        return len(tools_to_remove)

    async def install_default_tools(self):
        # Add custom web search tool
        try:
            web_search_tool = WebSearchTool(
                google_api_key=self._config.google_api_key,
                google_search_engine_id=self._config.google_search_engine_id,
            )
            self.register_tool(web_search_tool)
        except ValueError as e:
            info(f"Could not initialize web search tool: {e}")
            info(
                "Please set GOOGLE_API_KEY and GOOGLE_SEARCH_ENGINE_ID environment variables or configure them in the Configuration"
            )

        # Add web fetch tool
        web_fetch_tool = WebFetchTool()
        self.register_tool(web_fetch_tool)
        info("Added web fetch tool")

        # Add file tools
        file_tools = [
            ReadFileTool(),
            WriteFileTool(),
            EditTool(),
            FindContentInFileTool(),
        ]

        for tool in file_tools:
            self.register_tool(tool)
            info(f"Added file tool: {tool.name}")

        # Add communication tools
        message_ask_user_tool = MessageAskUserTool()
        self.register_tool(message_ask_user_tool)
        info("Added message ask user tool")

        message_notify_user_tool = MessageNotifyUserTool()
        self.register_tool(message_notify_user_tool)
        info("Added message notify user tool")

        # Add update todos tool
        self.register_tool(UpdateTodos())
        info("Added update todos tool")

        self.register_tool(BashTool())
        info("Added bash tool")

    async def reregister_bash_tool_with_envs(self, envs: dict[str, str]):
        new_bash_tool = BashTool(envs=envs)
        existing_bash_tool = self.get_tool(new_bash_tool.name)
        if existing_bash_tool:
            self.unregister_tool(existing_bash_tool.name)
        self.register_tool(new_bash_tool)
        info("Re-registered bash tool with envs")

    async def discovery_tools_of_mcp_server(
        self, server_name: str, mcp_session: ClientSession
    ) -> int:
        # Skip if we've already registered tools from this server
        try:
            info(f"Discovering tools from MCP server: {server_name}")
            if server_name in self.registered_servers:
                warn(f"MCP server is already registered {server_name}")
                return 0
            # List available tools
            tools = await mcp_session.list_tools()
            if not tools:
                warn(f"No tools found on MCP server: {server_name}")
                return 0

            # Register each tool
            tool_count = 0
            for tool_info in tools.tools:
                tool_description = (
                    tool_info.description
                    if tool_info.description
                    else "No description available"
                )

                # Create MCP tool adapter
                mcp_tool = MCPTool(
                    mcp_server=server_name,
                    tool_name=tool_info.name,
                    tool_description=tool_description,
                    args_schema=tool_info.inputSchema,
                )

                # Register with the tool registry
                self.register_tool(mcp_tool)
                tool_count += 1
                info(f"Registered MCP tool: {mcp_tool.name}")

            self.registered_servers.add(server_name)
            info(
                f"Registered {len(tools.tools)} tools from MCP server: {server_name}"
            )
            return tool_count
        except Exception as e:
            error(
                f"Error discovering tools from MCP server '{server_name}': {e}"
            )
            return 0
