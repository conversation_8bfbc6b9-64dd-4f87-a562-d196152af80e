"""Tool for managing a checklist of tasks."""

import json
from typing import Literal

from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.log import debug, info

from .base_tool import RuntimeBaseTool


class TodoItem(BaseModel):
    """A single todo item."""

    name: str = Field(..., description="The name/description of the task")
    status: Literal["Not Started", "In Progress", "Completed"] = (
        Field(..., description="The current status of the task")
    )


class UpdateTodosInput(BaseModel):
    """Input schema for updating todos."""

    todos: list[TodoItem] = Field(
        ..., description="The complete list of todo items with their statuses"
    )

TODOS_FILE = "todos.json"

class UpdateTodos(RuntimeBaseTool):
    """Update and manage a checklist of tasks.

    Each task has a name and status. Status can be:
    - "Not Started": Task has not been started yet
    - "In Progress": Task is actively being worked on
    - "Completed": Task has been completed

    The entire todo list must be specified in each update.
    Tasks can be added, removed, or modified in each update.
    """

    name: str = "update_todos"
    description: str = (
        "Update and manage a checklist of tasks. Each update **must** explicitly specify the complete "
        "list of todos with their statuses. Tasks can be added, removed, or have their "
        "status changed."
    )
    args_schema: ArgsSchema | None = UpdateTodosInput

    def _run(self, todos: list[TodoItem]) -> str:
        """Update the todo list synchronously."""
        import asyncio

        return asyncio.run(self._arun(todos))

    async def _arun(self, todos: list[TodoItem]) -> str:
        """Update the todo list asynchronously."""
        return await self._update_todos(todos)

    async def _update_todos(self, todos: list[TodoItem]) -> str:
        """Core logic for updating todos."""
        await self._ensure_runtime()

        # Get existing todos for comparison
        existing_data = await self.get_todos()
        existing_todos = []
        if existing_data:
            existing_todos = [
                TodoItem(**todo) for todo in existing_data
            ]

        # Prepare the data to save
        todos_data = [todo.model_dump() for todo in todos]

        # Save to file
        todos_json = json.dumps(todos_data, indent=2)

        debug(f"Updating todos list with {len(todos)} items")
        runtime = self._get_runtime()
        await runtime.write_file(TODOS_FILE, todos_json)

        # Generate change summary if there were existing todos
        changes = []
        if existing_todos:
            # Create lookup maps for comparison
            existing_map = {todo.name: todo for todo in existing_todos}
            new_map = {todo.name: todo for todo in todos}

            # Find added tasks
            added = [
                todo for todo in todos if todo.name not in existing_map
            ]
            if added:
                changes.append(f"Added {len(added)} task(s):")
                for todo in added:
                    changes.append(f"  + {todo.name}")
                changes.append("")

            # Find removed tasks
            removed = [
                todo for todo in existing_todos if todo.name not in new_map
            ]
            if removed:
                changes.append(f"Removed {len(removed)} task(s):")
                for todo in removed:
                    changes.append(f"  + {todo.name}")
                changes.append("")

            # Find status changes
            status_changes = []
            for todo in todos:
                if todo.name in existing_map:
                    old_todo = existing_map[todo.name]
                    if old_todo.status != todo.status:
                        status_changes.append(
                            f"  + {todo.name}: {old_todo.status} → {todo.status}"
                        )

            if status_changes:
                changes.append(
                    f"Status changed for {len(status_changes)} task(s):"
                )
                changes.extend(status_changes)
                changes.append("")

        summary_parts = []
        # Add changes summary if there were changes
        if changes:
            summary_parts.extend(changes)
            summary_parts.append("")  # Empty line for separation

        # Add task list
        summary_parts.append("\nCurrent Planning:")
        for i, todo in enumerate(todos, 1):
            status_emoji = {
                "Not Started": "⚪",
                "In Progress": "🔵",
                "Completed": "✅",
            }[todo.status]
            summary_parts.append(
                f"{i}. {status_emoji} {todo.name}"
            )

        summary = "\n".join(summary_parts)
        info(f"Todo list updated: {len(todos)} tasks")

        return summary

    async def get_todos(self) -> dict | None:
        """Read the current todo list from file."""
        runtime = self._get_runtime()

        try:
            content = await runtime.read_file(TODOS_FILE)
            return json.loads(content)
        except Exception:
            # File doesn't exist or is invalid
            return None
