"""
Browser Press Key Tool implementation.
"""



from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.sandbox.base import RuntimeExecutionError

from .base_tool import RuntimeBaseTool


class BrowserPressKeyInput(BaseModel):
    """Input schema for browser press key tool."""
    key: str = Field(..., description="Key name to simulate (e.g., Enter, Tab, ArrowUp), supports key combinations (e.g., Control+Enter).")


class BrowserPressKeyTool(RuntimeBaseTool):
    """Tool for simulating key press in the current browser page."""
    
    name: str = "browser_press_key"
    description: str = "Simulate key press in the current browser page. Use when specific keyboard operations are needed."
    
    args_schema: ArgsSchema | None = BrowserPressKeyInput
    
    def _run(self, key: str) -> str:
        """Execute browser press key synchronously."""
        import asyncio
        return asyncio.run(self._browser_press_key(key))
    
    async def _arun(self, key: str) -> str:
        """Execute browser press key asynchronously."""
        return await self._browser_press_key(key)
    
    async def _browser_press_key(self, key: str) -> str:
        """Perform browser press key operation."""
        # Ensure runtime is available
        if not await self._ensure_runtime():
            raise RuntimeExecutionError("Runtime not available for browser operations")
        
        runtime = self._get_runtime()
        
        try:
            result = await runtime.browser_press_key(key=key)
            return f"Pressed key '{key}'. Result: {result}"
                
        except Exception as e:
            error_msg = f"Browser press key operation failed: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeExecutionError(error_msg) from e
