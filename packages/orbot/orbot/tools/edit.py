"""
Edit Tool implementation based on Gemini CLI patterns.
"""

import os.path

from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.sandbox.base import NotFoundError

from .file_tools import FileAccessError, FileToolBase, PatternError


class EditInput(BaseModel):
    """Input schema for edit tool."""
    relative_path: str = Field(..., description="Relative path to the file to edit")
    old_string: str = Field(..., description="Exact text to replace")
    new_string: str = Field(..., description="Replacement text")
    expected_replacements: int = Field(1, description="Expected number of replacements (default: 1)")


class EditTool(FileToolBase):
    """Tool for editing files by replacing exact text strings with validation."""
    
    name: str = "edit"
    description: str = "Edits a file by replacing exact text strings. Performs exact string matching and replacement with occurrence counting. Generates a diff showing the changes made. Supports creating new files if they don't exist. Uses relative paths from the workspace root."
    
    args_schema: ArgsSchema | None = EditInput
    
    def _run(self, relative_path: str, old_string: str, new_string: str, expected_replacements: int = 1) -> str:
        """Execute file edit synchronously."""
        import asyncio
        return asyncio.run(self._edit_file(relative_path, old_string, new_string, expected_replacements))
    
    async def _arun(self, relative_path: str, old_string: str, new_string: str, expected_replacements: int = 1) -> str:
        """Execute file edit asynchronously."""
        return await self._edit_file(relative_path, old_string, new_string, expected_replacements)
    
    async def _edit_file(self, relative_path: str, old_string: str, new_string: str, expected_replacements: int = 1) -> str:
        """Edit file by replacing exact text strings."""
        # Validate that the path is actually relative, not absolute
        if os.path.isabs(relative_path):
            raise FileAccessError(f"Only relative paths are allowed. Got absolute path: {relative_path}")
        
        # Check if file exists and get info
        content = ""
        try:
           # Read current content using runtime
           content = await self._read_file_content(relative_path)
           if old_string not in content:
            raise PatternError(f"Text to replace not found in file: '{old_string[:100]}{'...' if len(old_string) > 100 else ''}'")
        except NotFoundError:
            await self._write_file_content(relative_path, new_string)
        except RuntimeError as rte:
           raise FileAccessError(f"Runtime Error for accessing the file. Error: {rte}") from rte
        
        # Read current content using runtime
        content = await self._read_file_content(relative_path)
        
        # Perform replacement
        if old_string not in content:
            raise FileAccessError(f"Text to replace not found in file: '{old_string[:100]}{'...' if len(old_string) > 100 else ''}'")
        
        # Count occurrences
        occurrence_count = content.count(old_string)
        
        if occurrence_count != expected_replacements:
            raise FileAccessError(
                f"Expected {expected_replacements} occurrences of text, but found {occurrence_count}. "
                f"Text: '{old_string[:100]}{'...' if len(old_string) > 100 else ''}'"
            )
        
        # Perform replacement
        new_content = content.replace(old_string, new_string)
        
        # Generate diff
        diff_output = await self._generate_diff(relative_path, new_content)
        
        # Write updated content using runtime
        await self._write_file_content(relative_path, new_content)
        
        # Generate result
        result = []
        result.append(f"File edited successfully: {relative_path}")
        result.append(f"Replacements made: {occurrence_count}")
        
        if diff_output:
            result.append("\nChanges made:")
            result.append(diff_output)
        
        # Add file statistics
        try:
            file_size = len(content)
            line_count = new_content.count('\n') + (1 if new_content and not new_content.endswith('\n') else 0)
            result.append("\nFile statistics:")
            result.append(f"- Size: {file_size} letters")
            result.append(f"- Lines: {line_count}")
        except Exception:
            pass
        
        return '\n'.join(result)
    
