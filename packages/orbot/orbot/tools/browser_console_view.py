"""
Browser Console View Tool implementation.
"""



from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.sandbox.base import RuntimeExecutionError

from .base_tool import RuntimeBaseTool


class BrowserConsoleViewInput(BaseModel):
    """Input schema for browser console view tool."""
    max_lines: int | None = Field(None, description="(Optional) Maximum number of log lines to return.")


class BrowserConsoleViewTool(RuntimeBaseTool):
    """Tool for viewing browser console output."""
    
    name: str = "browser_console_view"
    description: str = "View browser console output. Use when checking JavaScript logs or debugging page errors."
    
    args_schema: ArgsSchema | None = BrowserConsoleViewInput
    
    def _run(self, max_lines: int | None = None) -> str:
        """Execute browser console view synchronously."""
        import asyncio
        return asyncio.run(self._browser_console_view(max_lines))
    
    async def _arun(self, max_lines: int | None = None) -> str:
        """Execute browser console view asynchronously."""
        return await self._browser_console_view(max_lines)
    
    async def _browser_console_view(self, max_lines: int | None = None) -> str:
        """Perform browser console view operation."""
        # Ensure runtime is available
        if not await self._ensure_runtime():
            raise RuntimeExecutionError("Runtime not available for browser operations")
        
        runtime = self._get_runtime()
        
        try:
            result = await runtime.browser_console_view(max_lines=max_lines)
            lines_msg = f" (max {max_lines} lines)" if max_lines else ""
            return f"Browser console output{lines_msg}:\n{result}"
                
        except Exception as e:
            error_msg = f"Browser console view operation failed: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeExecutionError(error_msg) from e
