"""
Browser Move Mouse Tool implementation.
"""



from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.sandbox.base import RuntimeExecutionError

from .base_tool import RuntimeBaseTool


class BrowserMoveMouseInput(BaseModel):
    """Input schema for browser move mouse tool."""
    coordinate_x: float = Field(..., description="X coordinate of target cursor position")
    coordinate_y: float = Field(..., description="Y coordinate of target cursor position")


class BrowserMoveMouseTool(RuntimeBaseTool):
    """Tool for moving cursor to specified position on the current browser page."""
    
    name: str = "browser_move_mouse"
    description: str = "Move cursor to specified position on the current browser page. Use when simulating user mouse movement."
    
    args_schema: ArgsSchema | None = BrowserMoveMouseInput
    
    def _run(self, coordinate_x: float, coordinate_y: float) -> str:
        """Execute browser move mouse synchronously."""
        import asyncio
        return asyncio.run(self._browser_move_mouse(coordinate_x, coordinate_y))
    
    async def _arun(self, coordinate_x: float, coordinate_y: float) -> str:
        """Execute browser move mouse asynchronously."""
        return await self._browser_move_mouse(coordinate_x, coordinate_y)
    
    async def _browser_move_mouse(self, coordinate_x: float, coordinate_y: float) -> str:
        """Perform browser move mouse operation."""
        # Ensure runtime is available
        if not await self._ensure_runtime():
            raise RuntimeExecutionError("Runtime not available for browser operations")
        
        runtime = self._get_runtime()
        
        try:
            result = await runtime.browser_move_mouse(coordinate_x=coordinate_x, coordinate_y=coordinate_y)
            return f"Moved mouse cursor to coordinates ({coordinate_x}, {coordinate_y}). Result: {result}"
                
        except Exception as e:
            error_msg = f"Browser move mouse operation failed: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeExecutionError(error_msg) from e
