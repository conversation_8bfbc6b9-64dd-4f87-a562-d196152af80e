"""
Find Content in File Tool implementation.
"""

import os.path
import re

from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from .file_tools import FileAccessError, FileToolBase, PatternError


class FindContentInFileInput(BaseModel):
    """Input schema for find content in file tool."""
    relative_path: str = Field(..., description="Relative path to the file to search within")
    regex: str = Field(..., description="Regular expression pattern to match")


class FindContentInFileTool(FileToolBase):
    """Tool for searching text patterns within a specific file using regular expressions."""
    
    name: str = "find_content_in_file"
    description: str = "Search for matching text within file content using regular expressions. Use for finding specific content or patterns in files. Uses relative paths from the workspace root."
    
    args_schema: ArgsSchema | None = FindContentInFileInput
    
    def _run(self, relative_path: str, regex: str) -> str:
        """Execute content search synchronously."""
        import asyncio
        return asyncio.run(self._find_content(relative_path, regex))
    
    async def _arun(self, relative_path: str, regex: str) -> str:
        """Execute content search asynchronously."""
        return await self._find_content(relative_path, regex)
    
    async def _find_content(self, relative_path: str, regex: str) -> str:
        """Search for regex pattern within file content."""
        # Validate that the path is actually relative, not absolute
        if os.path.isabs(relative_path):
            raise FileAccessError(f"Only relative paths are allowed. Got absolute path: {relative_path}")
        
        # Validate regex pattern
        if not regex or not regex.strip():
            raise PatternError("Search pattern cannot be empty")
        
        try:
            compiled_pattern = re.compile(regex)
        except re.error as e:
            raise PatternError(f"Invalid regex pattern '{regex}': {e}") from e

        # Read file content
        try:
            content = await self._read_file_content(relative_path)
        except Exception as e:
            raise FileAccessError(f"Error reading file {relative_path}: {e}") from e
        
        # Search for matches
        matches = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            for match in compiled_pattern.finditer(line):
                matches.append({
                    'line': line_num,
                    'column': match.start() + 1,
                    'match': match.group(),
                    'line_content': line.rstrip('\r')
                })
        
        # Format results
        return await self._format_search_results(relative_path, regex, matches)
    
    async def _format_search_results(self, file_path: str, pattern: str, matches: list[dict]) -> str:
        """Format search results for display."""
        result = []
        result.append("Content search results")
        result.append(f"File: {file_path}")
        result.append(f"Pattern: {pattern}")
        result.append(f"Matches found: {len(matches)}")
        result.append("")
        
        if not matches:
            result.append("No matches found.")
            return '\n'.join(result)
        
        # Group matches by line for cleaner output
        by_line = {}
        for match in matches:
            line_num = match['line']
            if line_num not in by_line:
                by_line[line_num] = {
                    'line_content': match['line_content'],
                    'matches': []
                }
            by_line[line_num]['matches'].append({
                'column': match['column'],
                'match': match['match']
            })
        
        # Sort by line number
        sorted_lines = sorted(by_line.keys())
        
        result.append("Matches:")
        result.append("-" * 50)
        
        for line_num in sorted_lines:
            line_data = by_line[line_num]
            line_content = line_data['line_content']
            line_matches = line_data['matches']
            
            # Show line number and content
            result.append(f"Line {line_num:4d}: {line_content}")
            
            # Show match details for this line
            for match_info in line_matches:
                column = match_info['column']
                match_text = match_info['match']
                result.append(f"           Match at column {column}: '{match_text}'")
            
            result.append("")
        
        # Add summary
        line_count = len(by_line)
        total_matches = len(matches)
        result.append(f"Summary: {total_matches} matches across {line_count} lines")
        
        return '\n'.join(result)
