"""
Browser View Tool implementation.
"""



from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel

from common.sandbox.base import RuntimeExecutionError

from .base_tool import RuntimeBaseTool


class BrowserViewInput(BaseModel):
    """Input schema for browser view tool."""
    pass  # No parameters required


class BrowserViewTool(RuntimeBaseTool):
    """Tool for viewing content of the current browser page."""
    
    name: str = "browser_view"
    description: str = "View content of the current browser page. Use for checking the latest state of previously opened pages."
    
    args_schema: ArgsSchema | None = BrowserViewInput
    
    def _run(self) -> str:
        """Execute browser view synchronously."""
        import asyncio
        return asyncio.run(self._browser_view())
    
    async def _arun(self) -> str:
        """Execute browser view asynchronously."""
        return await self._browser_view()
    
    async def _browser_view(self) -> str:
        """Perform browser view operation."""
        # Ensure runtime is available
        if not await self._ensure_runtime():
            raise RuntimeExecutionError("Runtime not available for browser operations")
        
        runtime = self._get_runtime()
        
        try:
            result = await runtime.browser_view()
            return result
                
        except Exception as e:
            error_msg = f"Browser view operation failed: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeExecutionError(error_msg) from e
