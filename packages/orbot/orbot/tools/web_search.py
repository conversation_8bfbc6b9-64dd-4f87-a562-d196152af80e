"""
Web Search Tool implementation using Google Custom Search API.
Based on the Gemini CLI web search tool pattern.
"""

import os
import re
from typing import Any

from langchain_core.tools import BaseTool
from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field
import requests


class WebSearchInput(BaseModel):
    """Input schema for web search tool."""
    query: str = Field(description="The search query to find information on the web.")
    num_results: int = Field(default=5, description="Number of search results to return (1-10)")


class WebSearchTool(BaseTool):
    """Web search tool using Google Custom Search API."""
    
    name: str = "web_search"
    description: str = """Performs a web search using Google Search (via the Gemini API) and returns the results. This tool is useful for finding information on the internet based on a query"""
    
    args_schema: ArgsSchema | None = WebSearchInput
    
    def __init__(self, google_api_key: str | None = None, google_search_engine_id: str | None = None, **kwargs):
        # Get API credentials from parameters or environment
        api_key = google_api_key or os.getenv("GOOGLE_API_KEY")
        search_engine_id = google_search_engine_id or os.getenv("GOOGLE_SEARCH_ENGINE_ID")
        
        if not api_key:
            raise ValueError("Google API key is required. Provide google_api_key parameter or set GOOGLE_API_KEY environment variable")
        if not search_engine_id:
            raise ValueError("Google Search Engine ID is required. Provide google_search_engine_id parameter or set GOOGLE_SEARCH_ENGINE_ID environment variable")
            
        super().__init__(**kwargs)
        
        # Set as private attributes to avoid Pydantic field issues
        self._api_key = api_key
        self._search_engine_id = search_engine_id
    
    def _run(self, query: str, num_results: int = 5) -> str:
        """Execute web search synchronously."""
        try:
            return self._perform_search(query, num_results)
        except Exception as e:
            return f"Error performing web search: {str(e)}"
    
    async def _arun(self, query: str, num_results: int = 5) -> str:
        """Execute web search asynchronously."""
        try:
            return self._perform_search(query, num_results)
        except Exception as e:
            return f"Error performing web search: {str(e)}"
    
    def _perform_search(self, query: str, num_results: int = 5) -> str:
        """Perform the actual web search using Google Custom Search API."""
        # Validate inputs
        if not query or not query.strip():
            return "Error: Search query cannot be empty"
        
        if num_results < 1 or num_results > 10:
            num_results = 5  # Default to 5 if invalid
        
        try:
            # Prepare search parameters
            params = {
                'key': self._api_key,
                'cx': self._search_engine_id,
                'q': query.strip(),
                'num': min(num_results, 10),  # Google API allows max 10
                'safe': 'active',  # Enable safe search
                'fields': 'items(title,link,snippet,displayLink),searchInformation(totalResults)'
            }
            
            # Make API request
            response = requests.get(
                'https://www.googleapis.com/customsearch/v1',
                params=params,
                timeout=10
            )
            response.raise_for_status()
            
            data = response.json()
            
            # Check if we have results
            if 'items' not in data or not data['items']:
                return f"No search results found for query: '{query}'"
            
            # Format results
            return self._format_search_results(query, data)

        except requests.exceptions.RequestException as e:
            return f"Network error during search: {str(e)}"
        except Exception as e:
            return f"Unexpected error during search: {str(e)}"
    
    def _format_search_results(self, query: str, data: dict[str, Any]) -> str:
        """Format search results into a readable string."""
        results = []
        items = data.get('items', [])
        total_results = data.get('searchInformation', {}).get('totalResults', '0')
        
        # Add header with search info
        results.append(f"Web search results for: '{query}'")
        results.append(f"Found approximately {total_results} results\n")
        
        # Format each result
        for i, item in enumerate(items, 1):
            title = item.get('title', 'No title')
            link = item.get('link', '')
            snippet = item.get('snippet', 'No description available')
            display_link = item.get('displayLink', link)
            
            # Clean up snippet (remove extra whitespace, newlines)
            snippet = re.sub(r'\s+', ' ', snippet).strip()
            
            # Format individual result
            result_text = f"{i}. **{title}**\n"
            result_text += f"   Source: {display_link}\n"
            result_text += f"   {snippet}\n"
            result_text += f"   Link: {link}\n"
            
            results.append(result_text)
        
        # Add footer with usage note
        results.append("\nNote: These are current web search results. Information may change over time.")
        
        return "\n".join(results)
    
    def _clean_text(self, text: str) -> str:
        """Clean text by removing excessive whitespace and special characters."""
        if not text:
            return ""
        
        # Remove HTML tags if any
        text = re.sub(r'<[^>]+>', '', text)
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters that might cause formatting issues
        text = re.sub(r'[^\w\s\-.,!?:;()\'"]', '', text)
        
        return text.strip()
