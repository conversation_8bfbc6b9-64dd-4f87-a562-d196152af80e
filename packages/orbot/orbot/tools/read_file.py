"""
Read File Tool implementation based on Gemini CLI patterns.
"""

import base64
import os.path

from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.sandbox.base import NotFoundError

from .file_tools import FileAccessError, FileToolBase


class ReadFileInput(BaseModel):
    """Input schema for read file tool."""
    relative_path: str = Field(..., description="Relative path to the file to read")
    offset: int | None = Field(None, description="Line offset to start reading from (0-based)")
    limit: int | None = Field(None, description="Maximum number of lines to read")


class ReadFileTool(FileToolBase):
    """Tool for reading file content with support for text, images, and other formats."""
    
    name: str = "read_file"
    description: str = "Reads content from a file. Supports text files, images (PNG, JPG, GIF, WEBP, SVG, BMP), and provides metadata for other file types. Can read partial content using offset and limit parameters. Uses relative paths from the workspace root."
    
    args_schema: ArgsSchema | None = ReadFileInput
    
    def _run(self, relative_path: str, offset: int = -1, limit: int = -1) -> str:
        """Execute file read synchronously."""
        import asyncio
        return asyncio.run(self._read_file(relative_path, offset, limit))
    
    async def _arun(self, relative_path: str, offset: int = -1, limit: int = -1) -> str:
        """Execute file read asynchronously."""
        return await self._read_file(relative_path, offset, limit)
    
    async def _read_file(self, relative_path: str, offset: int , limit: int) -> str:
        """Read file content with appropriate handler for file type."""
        # Validate that the path is actually relative, not absolute
        if os.path.isabs(relative_path):
            raise FileAccessError(f"Only relative paths are allowed. Got absolute path: {relative_path}")

        mime_type = self._get_mime_type(relative_path)
        try:
            # Handle different file types
            if mime_type.startswith('image/'):
                return await self._process_image_file(relative_path, mime_type)
            elif mime_type == 'application/pdf':
                return await self._process_pdf_file(relative_path)
            else:
                return await self._process_text_file(relative_path, offset, limit)
        except NotFoundError:
            raise NotFoundError("{relative_path} does not exist.") from None
        except RuntimeError as e:
            raise FileAccessError(e) from e
    
    async def _process_text_file(self, file_path: str, offset: int, limit: int) -> str:
        """Process text file with offset and limit support."""
        content = await self._read_text_file_lines(file_path, offset, limit)
        
        # Get total lines from full content
        full_content = await self._read_file_content(file_path)
        total_lines = len(full_content.splitlines())
        
        result = f"File: {file_path}\n"
        result += f"Size: {len(full_content)} characters\n"
        result += f"Lines: {total_lines}\n"
        
        if offset > 0 or limit > 0:
            start_line = offset if offset > 0 else 0
            end_line = start_line + (limit if limit > 0 else total_lines)
            result += f"Showing lines {start_line + 1}-{min(end_line, total_lines)}\n"
        
        result += f"\n{'-' * 50}\n"
        result += content
        
        return result
    
    async def _process_image_file(self, file_path: str, mime_type: str) -> str:
        """Process image file by encoding as base64."""
        try:
            # Read binary content through Runtime
            runtime = self._get_runtime()
            
            image_data = await runtime.read_bytes(file_path)
            
            # Encode as base64
            base64_data =  base64.b64encode(image_data).decode('utf-8')
            
            result = f"Image File: {file_path}\n"
            result += f"Type: {mime_type}\n"
            result += f"Size: {len(image_data)} bytes\n"
            result += "\nBase64 encoded content:\n"
            result += f"data:{mime_type};base64,{base64_data}"
            
            return result
            
        except Exception as e:
            raise FileAccessError(f"Cannot read image file {file_path}: {e}") from e
    
    async def _process_pdf_file(self, file_path: str) -> str:
        """Process PDF file (basic metadata only)."""
        result = f"PDF File: {file_path}\n"
        result += f"Size: {file_path} bytes\n"
        result += "\nNote: PDF content extraction requires additional libraries.\n"
        result += "This file contains a PDF document that would need specialized processing to extract text content."
        return result
