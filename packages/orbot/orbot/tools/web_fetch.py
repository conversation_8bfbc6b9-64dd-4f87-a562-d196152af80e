"""
Web Fetch Tool implementation using Lang<PERSON>hain BaseTool.
Based on the Gemini CLI web fetch tool pattern.
"""

import ipaddress
import re
from typing import ClassVar
from urllib.parse import urlparse

from bs4 import BeautifulSoup, Tag
from langchain_core.tools import BaseTool
from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field
import requests


class WebFetchInput(BaseModel):
    """Input schema for web fetch tool."""
    prompt: str = Field(
        description="A comprehensive prompt that includes the URL(s) (up to 20) to fetch and specific instructions on how to process their content (e.g., 'Summarize https://example.com/article and extract key points from https://another.com/data'). Must contain at least one URL starting with http:// or https://."
    )


class WebFetchTool(BaseTool):
    """Web fetch tool that fetches content from URLs and processes it."""
    
    name: str = "web_fetch"
    description: str = """Fetches content from web URLs and processes it according to instructions. Supports up to 20 URLs per request. Handles both public and private network addresses with appropriate security measures. This tool operates in a workspace. Use relative paths when possible."""
    
    args_schema: ArgsSchema | None = WebFetchInput
    
    # Constants
    URL_FETCH_TIMEOUT_SECONDS: ClassVar[int] = 10
    MAX_CONTENT_LENGTH: ClassVar[int] = 100000
    MAX_URLS: ClassVar[int] = 20
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
    
    def _run(self, prompt: str) -> str:
        """Execute web fetch synchronously."""
        try:
            return self._perform_fetch(prompt)
        except Exception as e:
            return f"Error performing web fetch: {str(e)}"
    
    async def _arun(self, prompt: str) -> str:
        """Execute web fetch asynchronously."""
        try:
            return self._perform_fetch(prompt)
        except Exception as e:
            return f"Error performing web fetch: {str(e)}"
    
    def _perform_fetch(self, prompt: str) -> str:
        """Perform the actual web fetch operation."""
        # Validate parameters
        validation_error = self._validate_params(prompt)
        if validation_error:
            return f"Validation error: {validation_error}"
        
        # Extract URLs from prompt
        urls = self._extract_urls(prompt)
        if not urls:
            return "Error: No valid URLs found in prompt"
        
        if len(urls) > self.MAX_URLS:
            return f"Error: Too many URLs. Maximum allowed is {self.MAX_URLS}, found {len(urls)}"
        
        # Fetch content from all URLs
        fetched_content = []
        sources = []
        
        for i, url in enumerate(urls):
            try:
                content, source_info = self._fetch_url_content(url)
                if content:
                    fetched_content.append(f"[{i+1}] Content from {url}:\n{content}")
                    sources.append(source_info)
                else:
                    fetched_content.append(f"[{i+1}] Failed to fetch content from {url}")
            except Exception as e:
                fetched_content.append(f"[{i+1}] Error fetching {url}: {str(e)}")
        
        # Combine all content
        combined_content = "\n\n".join(fetched_content)
        
        # Add source references
        if sources:
            source_list = "\n\nSources:\n" + "\n".join(
                f"[{i+1}] {source['title']} - {source['url']}" 
                for i, source in enumerate(sources)
            )
            combined_content += source_list
        
        # Add processing instructions
        result = f"Web content fetched successfully:\n\n{combined_content}\n\nOriginal prompt: {prompt}"
        
        return result
    
    def _validate_params(self, prompt: str) -> str | None:
        """Validate input parameters."""
        if not prompt or not prompt.strip():
            return "The 'prompt' parameter cannot be empty and must contain URL(s) and instructions."
        
        if 'http://' not in prompt and 'https://' not in prompt:
            return "The 'prompt' must contain at least one valid URL (starting with http:// or https://)."
        
        return None
    
    def _extract_urls(self, text: str) -> list[str]:
        """Extract URLs from text using regex."""
        url_regex = r'(https?://[^\s]+)'
        urls = re.findall(url_regex, text)
        
        # Clean up URLs (remove trailing punctuation)
        cleaned_urls = []
        for url in urls:
            # Remove common trailing punctuation
            url = re.sub(r'[.,;!?)]+$', '', url)
            cleaned_urls.append(url)
        
        return cleaned_urls
    
    def _fetch_url_content(self, url: str) -> tuple[str, dict]:
        """Fetch content from a single URL."""
        # Convert GitHub blob URLs to raw URLs
        processed_url = self._convert_github_url(url)
        
        # Check for private IP (basic security check)
        if self._is_private_ip(processed_url):
            raise ValueError(f"Access to private/local network addresses is not allowed: {processed_url}")
        
        # Fetch the content with better headers
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        response = requests.get(
            processed_url,
            headers=headers,
            timeout=self.URL_FETCH_TIMEOUT_SECONDS,
            allow_redirects=True
        )
        response.raise_for_status()
        
        # Extract text content from HTML
        content_type = response.headers.get('content-type', '').lower()
        
        if 'text/html' in content_type:
            text_content = self._extract_text_from_html(response.text)
            
            # Check if this appears to be a JavaScript-heavy site with minimal content
            if self._is_js_heavy_site(response.text, text_content):
                text_content = self._handle_js_heavy_site(processed_url, response.text, text_content)
        else:
            # For plain text, JSON, etc.
            text_content = response.text
        
        # Truncate if too long
        if len(text_content) > self.MAX_CONTENT_LENGTH:
            text_content = text_content[:self.MAX_CONTENT_LENGTH] + "\n\n[Content truncated...]"
        
        # Create source info
        source_info = {
            'url': url,
            'title': self._extract_title_from_html(response.text) if 'text/html' in content_type else url,
            'content_type': content_type
        }
        
        return text_content, source_info
    
    def _convert_github_url(self, url: str) -> str:
        """Convert GitHub blob URLs to raw content URLs."""
        if 'github.com' in url and '/blob/' in url:
            return url.replace('github.com', 'raw.githubusercontent.com').replace('/blob/', '/')
        return url
    
    def _is_private_ip(self, url: str) -> bool:
        """Check if URL points to a private/local network address."""
        try:
            parsed = urlparse(url)
            hostname = parsed.hostname
            
            if not hostname:
                return False
            
            # Check for localhost
            if hostname.lower() in ['localhost', '127.0.0.1', '::1']:
                return True
            
            # Check for private IP ranges
            try:
                ip = ipaddress.ip_address(hostname)
                return ip.is_private or ip.is_loopback or ip.is_link_local
            except ValueError:
                # Not an IP address, could be a domain name
                return False
                
        except Exception:
            return False
    
    def _extract_text_from_html(self, html: str) -> str:
        """Extract clean text content from HTML."""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style", "nav", "header", "footer", "aside"]):
                script.decompose()
            
            # Get text content
            text = soup.get_text()
            
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
            
        except Exception:
            # Fallback to simple text extraction
            return re.sub(r'<[^>]+>', '', html)
    
    def _extract_title_from_html(self, html: str) -> str:
        """Extract title from HTML."""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            title_tag = soup.find('title')
            if title_tag:
                return title_tag.get_text().strip()
        except Exception:
            pass
        
        return "Untitled"
    
    def _clean_text(self, text: str) -> str:
        """Clean text by removing excessive whitespace and special characters."""
        if not text:
            return ""
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove some problematic characters
        text = re.sub(r'[^\w\s\-.,!?:;()\'\"\/\[\]{}]', '', text)
        
        return text.strip()
    
    def _is_js_heavy_site(self, html: str, text_content: str) -> bool:
        """Detect if a site is JavaScript-heavy with minimal static content."""
        # Check for common SPA indicators
        spa_indicators = [
            'react', 'vue', 'angular', 'app.js', 'bundle.js', 'main.js',
            'id="app"', 'id="root"', 'class="app"', 'data-reactroot'
        ]
        
        has_spa_indicators = any(indicator in html.lower() for indicator in spa_indicators)
        
        # Check if extracted text is minimal compared to HTML size
        html_size = len(html)
        text_size = len(text_content.strip())
        
        # If HTML is substantial but text is minimal, likely JS-heavy
        minimal_content = html_size > 1000 and text_size < 200
        
        # Check for script tags without much other content
        script_count = html.lower().count('<script')
        content_indicators = html.lower().count('<p>') + html.lower().count('<div') + html.lower().count('<section')
        script_heavy = script_count > 3 and content_indicators < script_count
        
        return has_spa_indicators or minimal_content or script_heavy
    
    def _handle_js_heavy_site(self, url: str, html: str, current_content: str) -> str:
        """Handle JavaScript-heavy sites by providing enhanced information."""
        
        # Extract meta information that might be useful
        soup = BeautifulSoup(html, 'html.parser')
        
        # Get meta description
        meta_desc = ""
        desc_tag = soup.find('meta', attrs={'name': 'description'}) or soup.find('meta', attrs={'property': 'og:description'})
        if desc_tag and isinstance(desc_tag, Tag) and desc_tag.get('content'):
            meta_desc = desc_tag['content']
        
        # Get page title
        title = ""
        title_tag = soup.find('title')
        if title_tag:
            title = title_tag.get_text().strip()
        
        # Look for JSON-LD structured data
        structured_data = ""
        json_ld = soup.find('script', type='application/ld+json')
        if json_ld:
            structured_data = json_ld.get_text()[:500]  # Limit size
        
        # Enhanced content with available metadata
        enhanced_content = f"""JavaScript-Rendered Site Detected: {url}

Page Title: {title}

Meta Description: {meta_desc}

Available Static Content: {current_content}

Note: This appears to be a single-page application (SPA) or JavaScript-heavy website. The full content is dynamically loaded and may not be visible without JavaScript execution. The information above represents what could be extracted from the static HTML.

To get the complete content, this site would need to be accessed with a JavaScript-enabled browser."""

        if structured_data:
            enhanced_content += f"\n\nStructured Data Found: {structured_data}"
        
        return enhanced_content
