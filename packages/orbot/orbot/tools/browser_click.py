"""
Browser Click Tool implementation.
"""



from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.sandbox.base import RuntimeExecutionError

from .base_tool import RuntimeBaseTool


class BrowserClickInput(BaseModel):
    """Input schema for browser click tool."""
    index: int | None = Field(None, description="(Optional) Index number of the element to click")
    coordinate_x: float | None = Field(None, description="(Optional) X coordinate of click position")
    coordinate_y: float | None = Field(None, description="(Optional) Y coordinate of click position")


class BrowserClickTool(RuntimeBaseTool):
    """Tool for clicking elements in the current browser page."""
    
    name: str = "browser_click"
    description: str = "Click on elements in the current browser page. Use when clicking page elements is needed."
    
    args_schema: ArgsSchema | None = BrowserClickInput
    
    def _run(self, index: int | None = None, coordinate_x: float | None = None, coordinate_y: float | None = None) -> str:
        """Execute browser click synchronously."""
        import asyncio
        return asyncio.run(self._browser_click(index, coordinate_x, coordinate_y))
    
    async def _arun(self, index: int | None = None, coordinate_x: float | None = None, coordinate_y: float | None = None) -> str:
        """Execute browser click asynchronously."""
        return await self._browser_click(index, coordinate_x, coordinate_y)
    
    async def _browser_click(self, index: int | None = None, coordinate_x: float | None = None, coordinate_y: float | None = None) -> str:
        """Perform browser click operation."""
        # Ensure runtime is available
        if not await self._ensure_runtime():
            raise RuntimeExecutionError("Runtime not available for browser operations")
        
        runtime = self._get_runtime()
        
        try:
            # Determine click method based on provided parameters
            if index is not None:
                # Click by element index
                result = await runtime.browser_click(index=index)
                return f"Clicked element at index {index}. Result: {result}"
            elif coordinate_x is not None and coordinate_y is not None:
                # Click by coordinates
                result = await runtime.browser_click(coordinate_x=coordinate_x, coordinate_y=coordinate_y)
                return f"Clicked at coordinates ({coordinate_x}, {coordinate_y}). Result: {result}"
            else:
                raise ValueError("Either index or both coordinate_x and coordinate_y must be provided")
                
        except Exception as e:
            error_msg = f"Browser click operation failed: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeExecutionError(error_msg) from e
