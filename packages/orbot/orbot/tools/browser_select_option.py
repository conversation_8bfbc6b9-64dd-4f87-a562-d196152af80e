"""
Browser Select Option Tool implementation.
"""



from langchain_core.tools.base import ArgsSchema
from pydantic import BaseModel, Field

from common.sandbox.base import RuntimeExecutionError

from .base_tool import RuntimeBaseTool


class BrowserSelectOptionInput(BaseModel):
    """Input schema for browser select option tool."""
    index: int = Field(..., description="Index number of the dropdown list element")
    option: int = Field(..., description="Option number to select, starting from 0.")


class BrowserSelectOptionTool(RuntimeBaseTool):
    """Tool for selecting specified option from dropdown list element in the current browser page."""
    
    name: str = "browser_select_option"
    description: str = "Select specified option from dropdown list element in the current browser page. Use when selecting dropdown menu options."
    
    args_schema: ArgsSchema | None = BrowserSelectOptionInput
    
    def _run(self, index: int, option: int) -> str:
        """Execute browser select option synchronously."""
        import asyncio
        return asyncio.run(self._browser_select_option(index, option))
    
    async def _arun(self, index: int, option: int) -> str:
        """Execute browser select option asynchronously."""
        return await self._browser_select_option(index, option)
    
    async def _browser_select_option(self, index: int, option: int) -> str:
        """Perform browser select option operation."""
        # Ensure runtime is available
        if not await self._ensure_runtime():
            raise RuntimeExecutionError("Runtime not available for browser operations")
        
        runtime = self._get_runtime()
        
        try:
            result = await runtime.browser_select_option(index=index, option=option)
            return f"Selected option {option} from dropdown at index {index}. Result: {result}"
                
        except Exception as e:
            error_msg = f"Browser select option operation failed: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeExecutionError(error_msg) from e
