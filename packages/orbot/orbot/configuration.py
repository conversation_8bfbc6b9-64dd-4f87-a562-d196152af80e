"""
Configuration for turn-by-turn agent system.
"""

import os
from typing import Any

from pydantic import BaseModel, Field


class Configuration(BaseModel):
    """The configuration for the turn-by-turn agent."""

    model: str = Field(
        default="claude-sonnet-4-20250514",
        description="The name of the language model to use for the agent.",
    )

    api_key: str = Field(
        default="", description="Anthropic API key for the agent."
    )

    system_prompt: str = Field(
        default="You are a helpful AI assistant.",
        description="System prompt for the AI agent.",
    )

    max_turns: int = Field(
        default=100, description="Maximum number of turns in a conversation."
    )

    enable_thinking: bool = Field(
        default=True,
        description="Enable extended thinking mode for Claude models.",
    )

    thinking_budget_tokens: int = Field(
        default=8000, description="Token budget for Claude thinking mode."
    )

    max_tokens: int = Field(
        default=16384,
        description="Maximum tokens for model responses. Must be greater than thinking_budget_tokens when thinking is enabled.",
    )

    max_compression_tokens: int = Field(
        default=2000,
        description="Maximum tokens for compression responses.",
    )

    temperature: float = Field(
        default=1.0, description="Temperature setting for the language model."
    )

    max_tool_response: int = Field(
        default=10000,
        description="Maximum tokens for tools response."
    )
    
    speaker_selection_enabled: bool = Field(
        default=True,
        description="Enable automatic speaker selection for multi-turn conversations.",
    )

    conversation_timeout_minutes: int = Field(
        default=30, description="Timeout for conversations in minutes."
    )

    enable_logging: bool = Field(
        default=True, description="Enable conversation logging."
    )

    log_level: str = Field(
        default="INFO",
        description="Logging level (DEBUG, INFO, WARNING, ERROR).",
    )

    google_api_key: str = Field(
        default="", description="Google API key for web search functionality."
    )

    google_search_engine_id: str = Field(
        default="", description="Google Custom Search Engine ID for web search."
    )
    
    compression_enabled: bool = Field(
        default=True,
        description="Enable automatic context compression for long conversations.",
    )
    
    compression_token_threshold: float = Field(
        default=0.7,
        description="Compress when conversation exceeds this fraction of context window (0.0-1.0).",
    )
    
    compression_preserve_threshold: float = Field(
        default=0.3,
        description="Preserve this fraction of recent conversation when compressing (0.0-1.0).",
    )
    
    context_window_size: int = Field(
        default=200_000,
        description="Model context window size in tokens. Used for compression calculations.",
    )

    def validate_required_fields(self) -> None:
        """Validate that required fields are present and configuration is valid."""
        if not self.api_key and not os.environ.get("ANTHROPIC_API_KEY"):
            raise ValueError(
                "API key is required. Set ANTHROPIC_API_KEY environment variable "
                "or provide api_key in configuration."
            )

        # Validate thinking configuration
        if (
            self.enable_thinking
            and self.max_tokens <= self.thinking_budget_tokens
        ):
            raise ValueError(
                f"max_tokens ({self.max_tokens}) must be greater than "
                f"thinking_budget_tokens ({self.thinking_budget_tokens}) when thinking is enabled. "
                "See https://docs.anthropic.com/en/docs/build-with-claude/extended-thinking#max-tokens-and-context-window-size"
            )

    def model_post_init(self, __context: Any) -> None:
        """Post-initialization validation."""
        if not self.api_key:
            self.api_key = os.environ.get("ANTHROPIC_API_KEY", "")
        if not self.google_api_key:
            self.google_api_key = os.environ.get("GOOGLE_API_KEY", "")
        if not self.google_search_engine_id:
            self.google_search_engine_id = os.environ.get(
                "GOOGLE_SEARCH_ENGINE_ID", ""
            )
