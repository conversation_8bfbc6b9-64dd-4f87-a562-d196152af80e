import json
import os
import re
import shutil
from urllib.parse import parse_qs, urlparse

import chainlit as cl
from langchain.schema.runnable.config import RunnableConfig
from langchain_anthropic import <PERSON>tAnthropic
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.tools import StructuredTool
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import create_react_agent

from agent.tools import list_available_attachments, read_file


def format_string_alphanumeric_dash(input_string: str) -> str:
    """
    Formats a string to contain only alphanumeric characters and dashes.
    All spaces are first converted to dashes.

    This function first replaces all spaces with dashes, then uses a
    regular expression to remove any character that is not a letter
    (a-z, A-Z), a digit (0-9), or a dash (-).

    Args:
        input_string: The string to be formatted.

    Returns:
        A new string containing only the allowed characters, with spaces
        converted to dashes.
    """
    string_with_dashes = input_string.replace(" ", "-")
    formatted_string = re.sub(r"[^a-zA-Z0-9-]", "", string_with_dashes)
    return formatted_string


def load_tool(path: str):
    file = os.path.join(path, "manifest.json")
    if os.path.exists(file):
        with open(file) as f:
            manifest = json.load(f)

            def create_func(name):
                def execute_workflow(**kwargs):
                    command = f"VA_EXECUTION_ID=test VA_INPUT='{json.dumps(kwargs)}' python {path}/main.py"
                    print(command)
                    stream = os.popen(command)
                    stream.read()
                    stream.close()
                    return (
                        f"Workflow {name} executed with {kwargs} successfully"
                    )

                return execute_workflow

            return StructuredTool(
                name=format_string_alphanumeric_dash(manifest["name"]),
                description=manifest["description"],
                args_schema=manifest["parameters"],
                func=create_func(manifest["name"]),
            )
    raise Exception("Tool not found")


tools = [read_file, list_available_attachments]


@cl.on_message
async def on_message(message: cl.Message):
    # Handle file attachments
    temp_dir = f"temp/{cl.context.session.id}"
    new_attachments = []

    for element in message.elements:
        if element.name:
            os.makedirs(temp_dir, exist_ok=True)
            path = os.path.join(temp_dir, element.name)
            if element.path:
                shutil.copy(element.path, path)
            else:
                with open(path, "wb") as f:
                    f.write(element.content)
            new_attachments.append(path)

    if new_attachments:
        current_attachments = cl.user_session.get("attachments", [])
        cl.user_session.set(
            "attachments", current_attachments + new_attachments
        )
        file_names = [os.path.basename(path) for path in new_attachments]
        await cl.Message(
            content=f"📎 **Files uploaded:** {', '.join(file_names)}"
        ).send()

    # Get current state
    current_state = cl.user_session.get("state", {"messages": []})

    # Check if there are file attachments
    user_content = message.content
    if isinstance(user_content, list):
        user_content = " ".join(str(item) for item in user_content)

    attachments = cl.user_session.get("attachments", [])
    if attachments:
        file_info = []
        for attachment_path in attachments:
            filename = os.path.basename(attachment_path)
            file_info.append(
                f"- {filename} (path: {os.path.abspath(attachment_path)})"
            )
        user_content += "\n\n**Available files:**\n" + "\n".join(file_info)

    # Add the user message to the state
    user_message = HumanMessage(content=user_content)
    current_state["messages"].append(user_message)

    # Show a loading message
    loading_msg = cl.Message(
        content="🤔 Analyzing your request and starting the automation process..."
    )
    await loading_msg.send()

    try:
        config = {"configurable": {"thread_id": cl.context.session.id}}
        cb = cl.LangchainCallbackHandler()

        graph = cl.user_session.get("runnable")

        result = await cl.make_async(graph.invoke)(
            current_state, config=RunnableConfig(callbacks=[cb], **config)
        )

        cl.user_session.set("state", result)

        await loading_msg.remove()

        full_message_history = result.get("messages", [])

        user_facing_message = None
        for message_item in reversed(full_message_history):
            # Skip messages with tool calls or empty content
            if (
                isinstance(message_item, AIMessage)
                and not message_item.tool_calls
            ):
                if message_item.content:
                    if (
                        isinstance(message_item.content, list)
                        and not message_item.content
                    ):
                        continue
                    user_facing_message = message_item
                    break

        if user_facing_message and user_facing_message.content:
            final_content = user_facing_message.content
            display_text = ""
            if isinstance(final_content, list):
                for item in final_content:
                    if isinstance(item, dict) and item.get("type") == "text":
                        display_text += item.get("text", "")
            elif isinstance(final_content, str):
                display_text = final_content

            if display_text.strip():
                await cl.Message(content=display_text.strip()).send()
            else:
                await cl.Message(
                    content="✅ Processing complete, but the final message was empty."
                ).send()
        else:
            await cl.Message(
                content="✅ Processing complete, but no final user-facing message was found."
            ).send()

    except Exception as e:
        # Remove loading message on error
        await loading_msg.remove()
        await cl.Message(
            content=f"❌ Error processing request: {str(e)}"
        ).send()


@cl.on_chat_start
async def start():
    workflow_path = None
    try:
        if hasattr(cl.context, "session") and cl.context.session:
            environ = getattr(cl.context.session, "environ", {})
            referer = environ.get("HTTP_REFERER", "")

            if referer:
                parsed_url = urlparse(referer)
                query_params = parse_qs(parsed_url.query)

                if "workflow" in query_params:
                    workflow_path = query_params["workflow"][0]
                    print(
                        f"Found workflow_path in query params: {workflow_path}"
                    )

    except Exception as e:
        print(f"Error extracting workflow_path: {e}")

    # Store workflow_path in user session
    if workflow_path and workflow_path.strip():
        cl.user_session.set("workflow_path", workflow_path)

        graph = cl.user_session.get("runnable")
        print(graph)
        if graph is None:
            current_tools = tools + [load_tool(workflow_path)]
            model = ChatAnthropic(
                model_name="claude-sonnet-4-20250514",
                temperature=0,
                streaming=True,
            )
            model = model.bind_tools(current_tools, parallel_tool_calls=False)
            graph = create_react_agent(
                model, tools=current_tools, checkpointer=MemorySaver()
            )
            cl.user_session.set("runnable", graph)
