# React Agent with Chain<PERSON> and LangGraph

This is a React agent implementation using Chain<PERSON>, Lang<PERSON>raph, and Claude 4.0. The agent can perform web searches and calculations using tools.

NOTE: This package is used for ML testing

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up your Anthropic API key:
Create a `.env` file in the root directory and add:
```
ANTHROPIC_API_KEY=your-api-key-here
```

3. Run the Chainlit app:
```bash
chainlit run executor_demo.py
```

## Features

- Interactive chat interface using Chainlit
- React agent implementation using LangGraph
- Powered by Claude 4.0 (Claude 3 Opus)
- Tools:
  - Web search (placeholder implementation)
  - Mathematical calculations

## Usage

1. Start the application using the command above
2. Open your browser to the provided URL (usually http://localhost:8000)
3. Start chatting with the agent
4. The agent will use its tools when necessary to help answer your questions

## Architecture

The agent uses a state machine pattern implemented with LangGraph:
- Agent State: Maintains conversation history and next action
- Tools: Web search and calculator functions
- Workflow: Defines the agent's decision-making process

## Customization

You can customize the agent by:
1. Adding new tools in `executor_demo.py`
2. Modifying the system prompt
3. Adjusting the temperature and other LLM parameters
4. Adding new nodes to the workflow 