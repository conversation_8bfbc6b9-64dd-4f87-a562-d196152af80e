"""
Deprecated: You should follow the instructions here to run the chainlip app locally:
https://docs.google.com/document/d/13YmhUGjD_67yGt5zBL1zuyw6UDfaZEwrvWyCbmMkSGo/edit?tab=t.0
Chainlit Chat Interface for Multi-Agent System
"""

import asyncio
import concurrent.futures
import json
import logging
import os
import re
import shutil
import sys

import chainlit as cl
from langchain.schema.runnable.config import RunnableConfig
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.tools import StructuredTool

# Configure logging
from agent.configuration import Configuration
from agent.graph import create_graph
from common.sandbox.base import RuntimeType
from web_server.session.runtime_manager import get_runtime_session_manager

logger = logging.getLogger(__name__)

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# Initialize your existing graph
config = Configuration()
graph = create_graph(config)

# Override the runtime type to local for now.
session_manager = get_runtime_session_manager(RuntimeType.LOCAL)


def format_string_alphanumeric_dash(input_string: str) -> str:
    """
    Formats a string to contain only alphanumeric characters and dashes.

    This function is used to sanitize workflow names for safe usage as identifiers.
    All spaces are first converted to dashes, then non-alphanumeric characters
    are removed.

    Args:
        input_string: The input string to format

    Returns:
        A formatted string containing only alphanumeric characters and dashes
    """
    string_with_dashes = input_string.replace(" ", "-")
    formatted_string = re.sub(r"[^a-zA-Z0-9-]", "", string_with_dashes)
    return formatted_string


def load_tool(path: str):
    """
    Load a workflow tool from a given path.

    This function dynamically creates a StructuredTool from a workflow manifest file.
    It reads the manifest.json file from the specified path and creates an executable
    tool that can run the workflow with the provided parameters.

    Args:
        path: The file system path to the workflow directory containing manifest.json

    Returns:
        StructuredTool: A LangChain tool that can execute the workflow

    Raises:
        Exception: If the manifest file is not found or cannot be parsed
    """

    async def get_runtime():
        return await session_manager.get_runtime(cl.context.session.id)

    runtime = asyncio.run(get_runtime())

    async def read_file(path):
        return await runtime.read_file(path)

    file_content = asyncio.run(read_file(path + "/manifest.json"))

    if not file_content:
        logger.error(f"Manifest not found at path: {path}")
        raise Exception("Manifest not found")

    manifest = json.loads(file_content)

    def create_func(name):
        """Create the actual execution function for the workflow."""

        def execute_workflow(**kwargs):
            """
            Execute the workflow with provided parameters.

            Args:
                **kwargs: Workflow parameters as specified in the manifest

            Returns:
                str: Success message or error details
            """
            logger.info(f"Executing workflow '{name}'")

            # Prepare environment variables for the workflow execution
            envs = {"VA_EXECUTION_ID": "test", "VA_INPUT": json.dumps(kwargs)}

            def run_command():
                """Synchronous wrapper for running the workflow code."""
                result = runtime.run_code(path, "main.py", [], envs=envs)
                return result

            result = asyncio.run(run_command())

            if result.returncode != 0:
                logger.error(
                    f"Workflow '{name}' execution failed with return code {result.returncode}: {result.stderr}"
                )
                return f"Error executing workflow: {result.stderr}"

            logger.info(f"Workflow '{name}' executed successfully")
            return f"Workflow {name} executed with {kwargs} successfully"

        return execute_workflow

    return StructuredTool(
        name=format_string_alphanumeric_dash(manifest["name"]),
        description=manifest.get("description", "Auto-generated workflow"),
        args_schema=manifest["parameters"],
        func=create_func(manifest["name"]),
    )


async def handle_workflow_commands(message_content: str) -> bool:
    """
    Handle workflow-related commands and mode switching.

    This function processes special commands that control the application's behavior,
    including mode switching between BUILD and EXECUTION modes, and workflow-specific
    commands like listing and running workflows.

    Args:
        message_content: The user's message content to check for commands

    Returns:
        bool: True if a command was handled, False if normal processing should continue
    """
    content_lower = message_content.lower().strip()
    current_mode = cl.user_session.get(
        "app_mode", "build"
    )  # Default to build mode

    # Mode switching commands (available in both modes)
    if content_lower == "/build mode" or content_lower == "/build":
        logger.info("Switched to BUILD MODE")
        cl.user_session.set("app_mode", "build")
        cl.user_session.set("execution_mode", False)  # Reset execution state
        await cl.Message(
            content="🔨 **Switched to BUILD MODE**\n\nYou can now:\n• Describe automation needs to generate workflows\n• Create new workflows through conversation\n\nUse `/execution mode` to switch to execution mode when ready."
        ).send()
        return True

    elif content_lower == "/execution mode" or content_lower == "/execute":
        logger.info("Switched to EXECUTION MODE")
        cl.user_session.set("app_mode", "execution")
        await cl.Message(
            content="⚙️ **Switched to EXECUTION MODE**\n\nYou can now:\n• `/workflows` - List saved workflows\n• `/run <workflow_name>` - Execute workflows\n• Upload files and run workflows\n\nUse `/build mode` to create new workflows."
        ).send()
        return True

    # Build mode commands - prevent execution commands in build mode
    if current_mode == "build":
        if content_lower in ["/list workflows", "/workflows", "/run"]:
            await cl.Message(
                content="🔨 **You're in BUILD MODE**\n\nWorkflow execution commands are not available in build mode.\nUse `/execution mode` to switch to execution mode first."
            ).send()
            return True

        elif content_lower.startswith("/run "):
            await cl.Message(
                content="🔨 **You're in BUILD MODE**\n\nWorkflow execution is not available in build mode.\nUse `/execution mode` to switch to execution mode first."
            ).send()
            return True

    # Execution mode commands - handle workflow listing and execution
    elif current_mode == "execution":
        if content_lower == "/list workflows" or content_lower == "/workflows":
            workflows = cl.user_session.get("saved_workflows", [])
            if not workflows:
                await cl.Message(
                    content="📋 **No workflows saved yet.**\n\nSwitch to `/build mode` to create workflows first."
                ).send()
            else:
                workflow_list = "\n".join(
                    [
                        f"• **{w['name']}** - {w['description']}"
                        for w in workflows
                    ]
                )
                await cl.Message(
                    content=f"📋 **Saved Workflows:**\n\n{workflow_list}\n\n💡 **To execute a workflow:** Type `/run <workflow_name>` and provide input when prompted"
                ).send()
            return True

        elif content_lower.startswith("/run "):
            workflow_name = content_lower[5:].strip()
            workflows = cl.user_session.get("saved_workflows", [])
            workflow = next(
                (w for w in workflows if w["name"].lower() == workflow_name),
                None,
            )

            if not workflow:
                logger.warning(f"Workflow '{workflow_name}' not found")
                available_names = (
                    [w["name"] for w in workflows] if workflows else []
                )
                if not available_names:
                    await cl.Message(
                        content="❌ **No workflows available.**\n\nSwitch to `/build mode` to create workflows first."
                    ).send()
                else:
                    await cl.Message(
                        content=f"❌ **Workflow '{workflow_name}' not found.** Available workflows: {', '.join(available_names)}"
                    ).send()
                return True

            # Switch to execution mode and ask for input
            cl.user_session.set("execution_mode", True)
            cl.user_session.set("selected_workflow", workflow)

            # Ask user for workflow parameters
            await cl.Message(
                content=f"🚀 **Ready to execute '{workflow['name']}'**\n\n📝 **Please provide the input parameters for this workflow.**\nYou can describe what you want to process or provide specific parameter values."
            ).send()
            return True

    # Help command (available in both modes)
    if content_lower == "/help":
        if current_mode == "build":
            help_text = """
🔨 **BUILD MODE - Workflow Creation**

**Available Commands:**
• Describe your automation needs to generate workflows
• `/execution mode` - Switch to execution mode
• `/help` - Show this help message

**What you can do:**
• Create new workflows through conversation
• Generate automation scripts
• Build standard operating procedures
            """
        else:  # execution mode
            help_text = """
⚙️ **EXECUTION MODE - Workflow Running**

**Available Commands:**
• `/workflows` - List saved workflows
• `/run <workflow_name>` - Execute a workflow
• `/build mode` - Switch to build mode
• `/help` - Show this help message

**Execution Flow:**
1. Upload files if needed
2. Use `/run <workflow_name>` to start execution
3. Provide input parameters when prompted
4. View the workflow results
            """
        await cl.Message(content=help_text).send()
        return True

    return False


@cl.on_chat_start
async def start():
    """Initialize the chat session."""
    # Session attachments are managed by the app
    session_id = cl.context.session.id

    cl.user_session.set("is_runtime_initialized", False)
    cl.user_session.set("attachments", [])
    cl.user_session.set("saved_workflows", [])
    cl.user_session.set("execution_mode", False)
    cl.user_session.set("app_mode", "build")  # Start in build mode

    # Send initial welcome message
    welcome_msg = """
🤖 **Workflow Automation Assistant**

🔨 **BUILD MODE** - You're currently in workflow creation mode.

**What you can do:**
• Describe your automation needs to generate workflows
• Create standard operating procedures (SOPs)
• Generate automation scripts

**Mode Commands:**
• `/execution mode` - Switch to run existing workflows
• `/help` - Show available commands

🔄 **Initializing runtime environment...**

**What automation would you like to build today?**
"""
    initial_message = await cl.Message(content=welcome_msg).send()

    # Initialize runtime in separate thread to avoid blocking
    def get_runtime_sync():
        """Synchronous wrapper for runtime initialization."""
        import asyncio

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                session_manager.get_runtime(session_id)
            )
        finally:
            loop.close()

    async def initialize_runtime():
        try:
            # Run runtime initialization in a separate thread
            with concurrent.futures.ThreadPoolExecutor() as executor:
                # Show progress indicator
                initial_message.content = """
🤖 **Workflow Automation Assistant**

🔨 **BUILD MODE** - You're currently in workflow creation mode.

**What you can do:**
• Describe your automation needs to generate workflows
• Create standard operating procedures (SOPs)
• Generate automation scripts

**Mode Commands:**
• `/execution mode` - Switch to run existing workflows
• `/help` - Show available commands

⚡ **Setting up sandbox environment...**

**What automation would you like to build today?**
"""
                await initial_message.update()

                # Run the blocking operation in a thread
                runtime = await asyncio.get_event_loop().run_in_executor(
                    executor, get_runtime_sync
                )

            cl.user_session.set("is_runtime_initialized", True)

            # Update the welcome message with runtime info
            updated_welcome_msg = f"""
🤖 **Workflow Automation Assistant**

🔨 **BUILD MODE** - You're currently in workflow creation mode.

**What you can do:**
• Describe your automation needs to generate workflows
• Create standard operating procedures (SOPs)
• Generate automation scripts

**Mode Commands:**
• `/execution mode` - Switch to run existing workflows
• `/help` - Show available commands

✅ **Runtime environment ready!**
{f"🔗 **Browser session live URL:** {runtime.browser_session.debugger_url}" if runtime.browser_session and runtime.browser_session.debugger_url else ""}
{f"🔗 **Sandbox session ID:** {runtime.sandbox_session_id}" if runtime.sandbox_session_id else ""}

**What automation would you like to build today?**
"""
            initial_message.content = updated_welcome_msg
            await initial_message.update()

        except Exception as e:
            # Update message with error info
            error_welcome_msg = f"""
🤖 **Workflow Automation Assistant**

🔨 **BUILD MODE** - You're currently in workflow creation mode.

**What you can do:**
• Describe your automation needs to generate workflows
• Create standard operating procedures (SOPs)
• Generate automation scripts

**Mode Commands:**
• `/execution mode` - Switch to run existing workflows
• `/help` - Show available commands

❌ **Runtime initialization failed:** {str(e)}

**You can still chat, but some features may be limited.**
"""
            initial_message.content = error_welcome_msg
            await initial_message.update()

    # Start runtime initialization in background
    asyncio.create_task(initialize_runtime())


@cl.on_message
async def on_message(message: cl.Message):
    # Handle workflow commands first
    if await handle_workflow_commands(message.content):
        return

    # Handle file attachments
    session_id = cl.context.session.id
    temp_dir = f"local_storage/{session_id}"
    new_attachments = []

    # If the runtime is remote, we need to copy the files to the runtime.
    runtime = await session_manager.get_runtime(session_id)
    for element in message.elements:
        if element.name:
            os.makedirs(temp_dir, exist_ok=True)
            relative_path = os.path.join(temp_dir, element.name)

            file_content = None
            if element.path:
                with open(element.path, "rb") as src:
                    file_content = src.read()
                # Copy the file to the temp directory, later will will move it to gcs
                with open(relative_path, "wb") as f:
                    f.write(file_content)
            else:
                if element.content is None:
                    continue  # Skip this element
                file_content = element.content
                with open(relative_path, "wb") as f:
                    f.write(element.content)

            # We will also move the file to the runtime.
            await runtime.write_file(relative_path, file_content)
            new_attachments.append(relative_path)

    if new_attachments:
        current_attachments = cl.user_session.get("attachments", [])
        cl.user_session.set(
            "attachments", current_attachments + new_attachments
        )
        file_names = [os.path.basename(path) for path in new_attachments]

        current_mode = cl.user_session.get("app_mode", "build")
        if current_mode == "build":
            await cl.Message(
                content=f"📎 **Files uploaded:** {', '.join(file_names)}\n\n💡 These files will be available when you switch to execution mode."
            ).send()
        else:  # execution mode
            await cl.Message(
                content=f"📎 **Files uploaded:** {', '.join(file_names)}\n\n💡 Use `/workflows` to see available workflows or `/run <workflow_name>` to execute."
            ).send()

    # Get current state
    current_state = cl.user_session.get("state", {"messages": []})
    if not current_state.get("chainlit_session_id", ""):
        current_state["chainlit_session_id"] = session_id

    # Check if there are file attachments
    user_content = message.content
    if isinstance(user_content, list):
        user_content = " ".join(str(item) for item in user_content)

    attachments = cl.user_session.get("attachments", [])
    if attachments:
        file_info = []
        for attachment_path in attachments:
            filename = os.path.basename(attachment_path)
            file_info.append(f"- {filename} (path: {attachment_path})")
        user_content += "\n\n**Available files:**\n" + "\n".join(file_info)

    # Add the user message to the state
    user_message = HumanMessage(content=user_content)
    current_state["messages"].append(user_message)

    # Check if in execution mode
    execution_mode = cl.user_session.get("execution_mode", False)
    if execution_mode:
        selected_workflow = cl.user_session.get("selected_workflow")

        if selected_workflow:
            # Show a loading message
            loading_msg = cl.Message(
                content=f"⚙️ Running workflow '{selected_workflow['name']}' with your input..."
            )
            await loading_msg.send()

            try:
                # Load the workflow tool
                workflow_tool = load_tool(selected_workflow["path"])

                # Parse user input as parameters
                # For now, pass the raw user input - the workflow can handle parsing
                attachments = cl.user_session.get("attachments", [])
                workflow_params = {
                    "csv_file_path": (
                        f"{str(runtime.runtime_path)}/{attachments[-1]}"
                        if attachments
                        else None
                    ),
                }

                # Execute the workflow directly
                result = workflow_tool.func(**workflow_params)

                await loading_msg.remove()

                # Display results
                await cl.Message(
                    content=f"✅ **Workflow Results:**\n\n{result}"
                ).send()

                # Reset execution mode
                cl.user_session.set("execution_mode", False)
                cl.user_session.set("selected_workflow", None)

            except Exception as e:
                logger.error(f"Workflow execution error: {e}")
                await loading_msg.remove()
                await cl.Message(
                    content=f"❌ Error executing workflow: {str(e)}"
                ).send()
                cl.user_session.set("execution_mode", False)

            return

    current_mode = cl.user_session.get("app_mode", "build")
    if current_mode == "build":
        initial_msg = cl.Message(
            content="🔨 **Building your automation...** Starting workflow analysis."
        )
    else:
        initial_msg = cl.Message(
            content="🤔 **Analyzing your request...** (You're in execution mode - use workflow commands to run saved workflows)"
        )
    await initial_msg.send()

    try:
        config = {"configurable": {"thread_id": session_id}}
        cb = cl.LangchainCallbackHandler()

        # Process streaming chunks
        result = None
        status_msg = None

        async for stream_chunk in graph.astream(
            current_state,
            config=RunnableConfig(callbacks=[cb], **config),
            stream_mode=["custom", "values"],
            subgraphs=True,
        ):
            if isinstance(stream_chunk, tuple) and len(stream_chunk) == 3:
                namespace, mode, data = stream_chunk
                if (
                    mode == "custom"
                    and isinstance(data, dict)
                    and "status_update" in data
                ):
                    # Remove previous status message and display new one
                    if status_msg:
                        await status_msg.remove()
                    status_message = data["status_update"]
                    status_msg = cl.Message(content=status_message)
                    await status_msg.send()
                elif mode == "values":
                    result = data

        # Remove final status message
        if status_msg:
            await status_msg.remove()

        # Remove initial loading message
        await initial_msg.remove()

        if result is None:
            result = {"messages": []}
        elif "messages" not in result:
            result["messages"] = []

        cl.user_session.set("state", result)

        # Extract messages from the final state
        full_message_history = result.get("messages", [])

        # Find the final user-facing message (AIMessage without tool calls)
        user_facing_message = None
        for message_item in reversed(full_message_history):
            if (
                isinstance(message_item, AIMessage)
                and not message_item.tool_calls
                and message_item.content
            ):
                if not (
                    isinstance(message_item.content, list)
                    and not message_item.content
                ):
                    user_facing_message = message_item.content
                    break

        if user_facing_message and user_facing_message.strip():
            await cl.Message(content=user_facing_message.strip()).send()
        else:
            await cl.Message(
                content="✅ Processing complete, but no final message was generated."
            ).send()

        # Check if a workflow was generated (execution_service_url present)
        if result.get("execution_service_url"):
            execution_url = result["execution_service_url"]
            await cl.Message(
                content=f"🔗 **Execution Service URL:** {execution_url}"
            ).send()

            # Extract workflow information and save it
            workflow_path = execution_url

            if workflow_path:
                # Try to read manifest for workflow details
                try:
                    manifest_content = await runtime.read_file(
                        workflow_path + "/manifest.json"
                    )
                    if manifest_content:
                        manifest = json.loads(manifest_content)

                        workflow_info = {
                            "name": manifest.get("name", "Generated Workflow"),
                            "description": manifest.get(
                                "description", "Auto-generated workflow"
                            ),
                            "path": workflow_path,
                            "url": execution_url,
                            "created_at": str(asyncio.get_event_loop().time()),
                        }

                        # Save to session
                        saved_workflows = cl.user_session.get(
                            "saved_workflows", []
                        )
                        # Check if workflow already exists
                        if not any(
                            w["path"] == workflow_path for w in saved_workflows
                        ):
                            saved_workflows.append(workflow_info)
                            cl.user_session.set(
                                "saved_workflows", saved_workflows
                            )

                            await cl.Message(
                                content=f"💾 **Workflow Saved!** '{workflow_info['name']}' is now available for execution.\n\n💡 **Next steps:**\n• Use `/execution mode` to switch to execution mode\n• Upload files if needed\n• Use `/run {workflow_info['name']}` to execute"
                            ).send()

                except Exception as e:
                    await cl.Message(
                        content=f"❌ Error saving workflow: {str(e)}"
                    ).send()

    except Exception as e:
        logger.error(f"Error in message processing: {e}")
        await initial_msg.remove()
        await cl.Message(
            content=f"❌ **Error processing request:** {str(e)}"
        ).send()


@cl.on_chat_end
async def end():
    """Clean up on chat end."""
    # Clean up temp files
    temp_dir = f"local_storage/{cl.context.session.id}"
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)

    await cl.Message(content="👋 **Session ended**").send()

    if cl.user_session.get("is_runtime_initialized", False):
        await session_manager.close_runtime(cl.context.session.id)
