from automated_eval import MonitoredElement, URLState, WorkflowSpec

# Define the configuration using proper Pydantic models
config = WorkflowSpec(
    workflow_name="Camping Reservation Form Automation",
    url_states=[
        URLState(
            url="https://camp-4-booking-buddy.lovable.app/",
            description="Main camping reservation form page",
            expected_elements=[
                MonitoredElement(
                    selector="#date-in",
                    value="2024-07-15",
                    description="Check-in date field should be filled with selected date"
                ),
                MonitoredElement(
                    selector="#date-out",
                    value="2024-07-17",
                    description="Check-out date field should be filled with selected date"
                ),
                MonitoredElement(
                    selector="#number-of-people",
                    value="4",
                    description="Number of people field should be filled with selected count"
                ),
                MonitoredElement(
                    selector="#first-name",
                    value="John",
                    description="First name field should be filled with user's first name"
                ),
                MonitoredElement(
                    selector="#last-name",
                    value="Doe",
                    description="Last name field should be filled with user's last name"
                ),
                MonitoredElement(
                    selector="#phone",
                    value="(*************",
                    description="Phone number field should be filled with user's phone"
                ),
                MonitoredElement(
                    selector="#email",
                    value="<EMAIL>",
                    description="Email address field should be filled with user's email"
                )
            ]
        ),
        URLState(
            url="https://camp-4-booking-buddy.lovable.app/confirmation",
            description="Confirmation page after successful submission",
            expected_elements=[
            ]
        )
    ]
)
