{"use_cases": [{"id": 0, "name": "Camp 4 Booking", "description": "Generate a workflow to fill out this form: https://camp-4-booking-buddy.lovable.app\nA structured data will be provided in each execution. For example,\n    Check-in Date: 2024-1-1\n    Check-out Date:  2024-1-2\n    Number of People: 5\n    First Name: Joe\n    Last Name: Smith\n    Phone Number: **********\n    Email: <EMAIL>", "file_paths": [], "sop": null}, {"id": 1, "name": "Medallion Railroad", "description": "Fill out the form https://www4.palmettogba.com/ecx_rrPtanStatusv2/redirectToEntry.do?actionPath=select with csv data.\nYou have an example csv file that looks like this:\n\nPTAN,NPI,Tax ID,First Name,Last Name,Phone Number,Business Name,Email\nIN481080,**********,**********,<PERSON>,<PERSON>,**********,Seven Hills OBGYN,<EMAIL>\n... (more rows)\n\nNeed to make sure there is a one to one mapping between all of the required fields on the form\nThe phone number needs to be split into 3 parts and the Tax ID will have to extract the last 5 digits if it is not already 5 digits long.\nThe final code script should ask for human to handle the CAPTCHA before submitting (not needed in exploration)", "file_paths": ["railroad/railroad_creation.csv"], "sop": null}, {"id": 2, "name": "Add New Vendors for Payment in Airtable", "description": "Target link: https://airtable.com/app0OCkSrq3ML7wak/shrQNNZ2GKoDuXkSe\nFill out the form with the provided csv data that has fixed fields.\nNeed to make the data mapping correct.", "file_paths": ["airtable/vendors_workflow_creation.csv"], "sop": null}, {"id": 3, "name": "Wellcare", "description": "Fill out the form https://www.wellcare.com/Become-a-Provider to join the wellcare healthcare provider network. The input would be a structured csv file with fixed fields like the csv file you have.\nSpecial notes: The primary address will be the address 1 in the csv file; if there are multiple addresses, put other addresses in the additional information field in the form; The County is a required field, but the data will not have a value for it. It must be inferred from the zipcode, city, and country", "file_paths": ["wellcare_modified/data_workflow_creation.csv"], "sop": null}, {"id": 5, "name": "Bulk Upload Products in Shopify", "description": "Target link: (https://admin.shopify.com/)\nLog in to my account, go to my store, and add products one by one.\nYou have an example csv file that looks like this:\n\nTitle,Description,Price,SKU\nArtisan Leather Journal,\"Hand-stitched, full-grain leather journal with 200 lined pages. Perfect for notes and sketches.\",49.99,ALJ-001\nMinimalist Desk Lamp,Sleek aluminum design with 3 brightness settings and USB charging port.,75.5,MDL-002\n... (more rows)\n\nLog in information: <EMAIL>, password: yiningmao12", "file_paths": ["user_simulator_files/shopify_products.csv", "user_simulator_files/shopify_demo.mp4"], "sop": null}, {"id": 7, "name": "Bulk Upload Products in Shopify (with Video)", "description": "Target link: (https://admin.shopify.com/)\nLog in to my account, go to my store, and add products one by one.\nYou have an example csv file that looks like this:\n\nTitle,Description,Price,SKU\nArtisan Leather Journal,\"Hand-stitched, full-grain leather journal with 200 lined pages. Perfect for notes and sketches.\",49.99,ALJ-001\nMinimalist Desk Lamp,Sleek aluminum design with 3 brightness settings and USB charging port.,75.5,MDL-002\n... (more rows)\n\nLog in information: <EMAIL>, password: yiningmao12\nYou may also upload a video demonstration of the process.", "file_paths": ["user_simulator_files/shopify_products.csv", "user_simulator_files/shopify_demo.mp4"], "sop": null}, {"id": 8, "name": "Homeowners Insurance Quote Form", "description": "Fill out the form https://www.eatoninsuranceservices.com/homeowners/home_quote_form.aspx with csv data structured like this:\n\nFirst Name,Last Name,Street Name,City,State,ZIP Code,Email Address,Phone Number,Date of Birth,Non-Smokers,Occupancy Length,Do you run a business from home,Dwelling Location City\nJohn,Doe,123 Maple St,Detroit,MI,48201,<EMAIL>,************,04-12-1985,Yes,5 years,No,Detroit\nAlice,Smith,456 Oak Ave,Grand Rapids,MI,49503,<EMAIL>,************,11-05-1990,No,3 years,Yes,Grand Rapids\n... (more rows)\n\nThis form has a required CAPTCHA, but we should aim to create the workflow to fill out all form fields and avoid submitting for now.\nWhen executing the workflow, there can also be human interruption from the agent to proceed with the CAPTCHA if submission is needed.", "file_paths": [], "sop": null}, {"id": 9, "name": "Interest Form", "description": "Fill out the form https://friendly-info-gather.lovable.app/ with a structured csv file with fixed fields like this:\n\nFirst Name,Last Name,Date of Birth,Address,Phone Number\nJohn,<PERSON>e,04-12-1985,\"Apt 3B, 1247 Pinecrest Dr, Wayne County, MI 48226\",************\n<PERSON>,<PERSON>,11-05-1990,\"88 Cedar Brook Ln, Cook County, IL 60629\",************\n... (more rows)\n\nCorrect data mapping should be like this (used for verification, not directly exposed to the automation agent):\nFirst Name -> First Name\nLast Name -> Last Name\nDate of Birth (MM-DD-YYYY)\nMonth -> Month (dropdown)\nDate (DD) -> Date\nYear (YYYY) -> Year\nAddress:\nApt/Unit -> Apt/Unit\nStreet Address -> Street Address\nCounty -> County\nState -> State (dropdown)\nZIP Code -> ZIP Code\nPhone Number (XXX-XXX-XXXX)\nArea Code (XXX) -> Area Code (dropdown)\nPhone Number (XXX-XXXX) -> Phone Number\n\nPay attention to:\nAddress- have to extract the different entities e.g. apt, street, state, etc. some transformation will be needed.\nIn the raw data, the address is a full string with all of the information\nPhone Number- have to extract the area code and trim the other digits", "file_paths": [], "sop": null}, {"id": 10, "name": "Healthcare Form", "description": "Fill out the form https://utc-license-form-wizard.lovable.app/ with csv data structured like this:\n\nFirst Name,Last Name,Email Address,Phone Number,License Number,Date of Birth,Gender,Street Address,City,State,Postal Code,Emergency Contact,Emergency Contact Phone,Allergies,Current Medications\nAva,<PERSON>,<EMAIL>,(*************,123-4567,1987-08-14 17:00:00.000 -0700,Female,942 Lakeview Dr,Ann Arbor,MI,48103,<PERSON>,(*************,<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON>,<EMAIL>,(*************,789-2345,1990-03-22 17:00:00.000 -0700,Male,582 Oakwood Ave,Detroit,MI,48201,,,Peanuts,\n... (more rows)\n\nNeed to make sure that:\n- The Date of Birth field requires data to be converted into UTC time when selecting.\n  The raw data is in this format: 1987-08-14 17:00:00.000 -0700, and need to be converted into UTC MM/DD/YYYY\n- The license number requires 7 integer digits. However, the raw data has dashes, we will need to remove the dashes before entering these values", "file_paths": ["user_simulator_files/healthcare_form.csv"], "sop": null}, {"id": 11, "name": "Getting Quote for Renters Insurance Form", "description": "Submit the form to the target link https://purchase.allstate.com/onlineshopping/welcome. The input is a structured csv file with fixed fields.The form is Multipage, Website Navigation, Dynamic Fields.\nThings to note: by the end of the form, you are required to submit payment details, and we can stop there and let the agent handle the payment.\nWebsite Navigation required to begin the form: Need to select ‘renters’ and enter the ZIP Code. Need to handle pop up boxes.\nData Transformation Required: Inference of what type of property this is, inference needed for distance to the closest fire station, address needs to separated into street address and apartment", "file_paths": ["user_simulator_files/renters_insurance_mountain_view.csv"], "sop": null}, {"id": 12, "name": "<PERSON>'s Lovable Form", "description": "Fill out the multi-page healthcare provider form https://provider-form-flow.lovable.app/ with csv data with fixed fields.\nSpecial instructions: data inference needed for the State- raw data has state in abbreviations, but the form requires the full name ; License Number will need to remove dashes. The raw data has a license number with dashes but the form only accepts digits.; Given the mailing address and billing address, the model needs to infer whether they are the same address and check the “Same as mailing address” field correspondingly", "file_paths": ["user_simulator_files/lovable_form.csv"], "sop": null}, {"id": 13, "name": "Schedule Social Media Posts in Buffer", "description": "Target Link: https://publish.buffer.com/create?view=board\nUsername: <EMAIL>\nPassword: Orbyrocks1234\nFill out the form with the provided csv data that has fixed fields. Some special notes:\n- The scheduled date will have to be transformed from the raw data. The raw data is formatted in MM/DD/YYYY HH:MM:SS. We need to extract the date, time (in AM/PM) from the data to select individually.\n- Required login, website navigation; Need to click ‘+New’ -> Post to access the form; After filling in two fields, need to click the arrow next to ‘Add to Queue’ -> ‘Schedule Post’ to select the date and time we want", "file_paths": ["user_simulator_files/social_schedule.csv"], "sop": null}, {"id": 15, "name": "TriWest", "description": "Fill out target: https://joinournetwork.triwest.com/ with structured csv data with fixed fields.\nThings to note just in case:\nAddress in the form should be Primary Address in the csv file\nThe page times out after ~15 minutes\nTo submit a form, click on OK on the pop up box after the Submit button.", "file_paths": ["user_simulator_files/triwest.csv"], "sop": null}]}