import asyncio
import csv
import logging
from typing import Any

from pydantic import BaseModel
from va import step, workflow
from va.playwright import get_browser_context


class WorkflowInput(BaseModel):
    csv_file_path: str

def determine_email_type(email: str) -> str:
    """Determine if email is personal or work based on domain"""
    personal_domains = [
        'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 
        'aol.com', 'icloud.com', 'live.com', 'msn.com'
    ]
    
    domain = email.split('@')[1].lower()
    return "Personal" if domain in personal_domains else "Work"

def map_job_by_tags(tags: str) -> str:
    """Map candidate tags to appropriate job roles"""
    tags_lower = tags.lower()
    
    # Design-related keywords
    design_keywords = ['design', 'ux', 'ui', 'designer', 'visual', 'creative']
    
    # Engineering-related keywords  
    engineering_keywords = ['engineering', 'engineer', 'frontend', 'backend', 'developer', 'software']
    
    # Check for design roles first
    if any(keyword in tags_lower for keyword in design_keywords):
        return "Design"
    
    # Check for engineering roles
    if any(keyword in tags_lower for keyword in engineering_keywords):
        return "Engineering"
    
    # Default fallback to Product Manager
    return "Test"

def read_csv_data(file_path: str) -> list[dict[str, Any]]:
    """Read and parse CSV data"""
    candidates = []
    
    with open(file_path, encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            candidate = {
                'full_name': f"{row['first_name']} {row['last_name']}",
                'email': row['email'],
                'email_type': determine_email_type(row['email']),
                'phone': row['phone'],
                'current_company': row['current_company'],
                'current_title': row['current_title'],
                'location': row['location'],
                'source': row['source'],
                'tags': row['tags'],
                'suggested_job': map_job_by_tags(row['tags'])
            }
            candidates.append(candidate)
    
    return candidates

@workflow("Ashby Candidate Import Automation")
async def main(input: WorkflowInput, logger: logging.Logger):
    """Automate candidate import into Ashby ATS platform"""
    
    # Read CSV data
    candidates = read_csv_data(input.csv_file_path)
    logger.info(f"Processing {len(candidates)} candidates")
    
    async with get_browser_context(headless=False, slow_mo=500) as context:
        page = await context.new_page()
        
        with step("Navigate to Ashby platform"):
            logger.info("Navigating to Ashby platform")
            await page.goto("https://app.ashbyhq.com")
            
            # Wait for user to complete login manually
            logger.info("Please complete login and navigate to candidates section")
            await page.wait_for_url("**/home**", timeout=60000)
            
        for i, candidate in enumerate(candidates, 1):
            logger.info(f"Processing candidate {i}/{len(candidates)}: {candidate['full_name']}")
            
            with step(f"Add candidate {i}: {candidate['full_name']}"):
                
                # Step 1: Click Add > Candidate
                async with page.step("Click Add > Candidate"):
                    add_button = page.get_by_text("Add") | page.get_by_prompt("Add button in navigation")
                    await add_button.click()
                    await page.wait_for_timeout(500)
                    
                    candidate_option = page.get_by_text("Candidate") | page.get_by_prompt("Candidate option in dropdown")
                    await candidate_option.click()
                    await page.wait_for_timeout(1000)
                
                # Step 2: Fill basic candidate information
                async with page.step("Fill basic candidate information"):
                    # Fill name
                    name_field = page.get_by_label("Name") | page.get_by_prompt("Name input field")
                    await name_field.fill(candidate['full_name'])
                    await page.wait_for_timeout(500)
                    
                    # Fill email
                    email_field = page.get_by_label("Email Address") | page.get_by_prompt("Email address input field")
                    await email_field.fill(candidate['email'])
                    await page.wait_for_timeout(500)
                    
                    # Select email type
                    email_type_dropdown = page.get_by_label("Email Type") | page.get_by_prompt("Email type dropdown")
                    await email_type_dropdown.click()
                    await page.wait_for_timeout(500)
                    
                    email_type_option = page.get_by_text(candidate['email_type']) | page.get_by_prompt(f"Select {candidate['email_type']} email type")
                    await email_type_option.click()
                    await page.wait_for_timeout(500)
                    
                    # Fill phone number
                    phone_field = page.get_by_label("Phone Number") | page.get_by_prompt("Phone number input field")
                    await phone_field.fill(candidate['phone'])
                    await page.wait_for_timeout(500)
                    
                    # Select phone type as Personal
                    phone_type_dropdown = page.get_by_label("Phone Type") | page.get_by_prompt("Phone type dropdown")
                    await phone_type_dropdown.click()
                    await page.wait_for_timeout(500)
                    
                    personal_option = page.get_by_text("Personal") | page.get_by_prompt("Select Personal phone type")
                    await personal_option.click()
                    await page.wait_for_timeout(500)
                
                # Step 3: Set Attribution
                async with page.step("Set attribution to Sourced: Sourcing Form"):
                    # Find and click source dropdown
                    source_dropdown = page.get_by_text("Choose a source...") | page.get_by_prompt("Source dropdown for attribution")
                    await source_dropdown.click()
                    await page.wait_for_timeout(500)
                    
                    # Select "Sourced: Sourcing Form"
                    sourcing_form_option = page.get_by_text("Sourced: Sourcing Form") | page.get_by_prompt("Sourced: Sourcing Form option")
                    await sourcing_form_option.click()
                    await page.wait_for_timeout(500)
                
                # Step 4: Add Experience
                async with page.step("Add experience information"):
                    # Click +Add button next to Experience
                    add_experience_button = page.get_by_text("+Add") | page.get_by_prompt("Add button next to Experience section")
                    await add_experience_button.click()
                    await page.wait_for_timeout(1000)
                    
                    # Fill current role
                    role_field = page.get_by_placeholder("e.g. Mr. Manager") | page.get_by_prompt("Current position input field")
                    await role_field.fill(candidate['current_title'])
                    await page.wait_for_timeout(500)
                    
                    # Fill current company
                    company_field = page.get_by_label("Company") | page.get_by_prompt("Employer input field")
                    await company_field.fill(candidate['current_company'])
                    await page.wait_for_timeout(500)
                
                # Step 5: Click Next to proceed to job selection
                async with page.step("Proceed to job selection"):
                    next_button = page.get_by_text("Next") | page.get_by_prompt("Next button to proceed to job selection")
                    await next_button.click()
                    await page.wait_for_timeout(1000)
                
                # Step 6: Select appropriate job based on tags
                async with page.step(f"Select job based on tags: {candidate['tags']}"):
                    # Click job search dropdown
                    job_search_field = page.get_by_placeholder("Search by department or job title...") | page.get_by_prompt("Job search field")
                    await job_search_field.click()
                    await page.wait_for_timeout(500)
                    
                    # Type the suggested job category
                    await job_search_field.fill(candidate['suggested_job'])
                    await page.wait_for_timeout(1000)
                    
                    # Select the first matching job option
                    first_job_option = page.get_by_prompt("Pick job option Test in the dropdown")
                    await first_job_option.click()
                    await page.wait_for_timeout(500)
                
                # Step 7: Complete candidate creation
                async with page.step("Complete candidate creation"):
                    # Look for Submit, Save, or Create button
                    submit_button = (page.get_by_text("Add to Job") | page.get_by_prompt("Add to job button"))
                    await submit_button.click()
                    await page.wait_for_timeout(2000)
                
                logger.info(f"Successfully processed candidate: {candidate['full_name']}")
                
                # Brief pause between candidates
                await page.wait_for_timeout(1000)
        
        with step("Complete workflow"):
            logger.info(f"Successfully processed all {len(candidates)} candidates")
            logger.info("Ashby candidate import automation completed")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main(
        WorkflowInput(csv_file_path="Copy of ashby_candidates - workflow creation.csv"), 
        logger=logging.getLogger(__name__)
    ))
