"""
Video SOP Extractor - Extracts data entry SOPs from video recordings using Vertex AI.
"""

import argparse
import base64
from functools import wraps
import os
import sys
import time

from langchain_core.messages import HumanMessage
from langchain_google_vertexai import ChatVertexAI


def timer_decorator(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"⏱️ {func.__name__} took {execution_time:.2f} seconds to execute")
        return result

    return wrapper


class VideoSOPExtractor:
    """Extracts data entry SOPs from video recordings using Vertex AI."""

    def __init__(self):
        """Initialize the video SOP extractor."""

        # Initialize Vertex AI model for video analysis
        self.vertex_model = ChatVertexAI(
            model="gemini-2.5-pro",
            temperature=0.1,
            max_tokens=16384,
        )

    @timer_decorator
    def _extract_sop_with_gemini(self, video_path: str) -> str:
        """Extract SOP using Gemini's direct video understanding."""
        try:
            print(f"🎬 Analyzing video with Vertex AI: {video_path}")
            
            # Get video file size
            video_size = os.path.getsize(video_path)
            video_size_mb = video_size / (1024 * 1024)  # Convert to MB

            print(f"📹 Video info: {video_size_mb:.2f} MB")

            # Read video file
            with open(video_path, "rb") as video_file:
                video_data = video_file.read()

            # Create message with video
            message = HumanMessage(
                content=[
                    {"type": "text", "text": self._get_gemini_prompt()},
                    {
                        "type": "media",
                        "mime_type": self._get_mime_type(video_path),
                        "data": base64.b64encode(video_data).decode("utf-8"),
                    },
                ]
            )

            # Get response from Vertex AI
            response = self.vertex_model.invoke([message])
            print(
                f"Response from Vertex AI: {len(response.content)} characters"
            )
            print(f"Response metadata: {response.response_metadata}")
            print("✅ Successfully analyzed video with Gemini")
            return response.content

        except Exception as e:
            error_msg = f"❌ Error analyzing video with Vertex AI: {str(e)}"
            print(error_msg)
            return error_msg

    def _get_gemini_prompt(self) -> str:
        """Get prompt for Gemini video analysis."""
        return GEMINI_VIDEO_ANALYSIS_PROMPT

    def _get_mime_type(self, video_path: str) -> str:
        """Get MIME type based on video file extension."""
        ext = os.path.splitext(video_path)[1].lower()
        mime_types = {
            ".mp4": "video/mp4",
            ".avi": "video/x-msvideo",
            ".mov": "video/quicktime",
            ".mkv": "video/x-matroska",
            ".webm": "video/webm",
            ".flv": "video/x-flv",
            ".wmv": "video/x-ms-wmv",
        }
        return mime_types.get(ext, "video/mp4")


# =====================
# PROMPT STRINGS BELOW
# =====================

GEMINI_VIDEO_ANALYSIS_PROMPT = """You are an expert automation specialist analyzing a video recording of a data entry process.

Your task is to watch this video (or audio) and create a comprehensive Standard Operating Procedure (SOP) for automating this data entry process.

Please analyze the video (or audio) and provide a detailed and complete SOP that includes:

## 1. Source Schema
- Identify the source data format and structure
- List all data fields in the source
   - Double check that none of the fields are missed
- Note any data validation rules or patterns

## 2. Target Schema  
- Identify the target form/system structure
- List all required fields in the target and their type like textbox, dropdown, checkbox, datepicker, etc.
- Note any specific formatting requirements

## 3. Data Mapping
- Map source fields to target fields
    - Output mapping as "source_field_name: target_field_name : transformation_needed"
- Identify any data transformations needed
- Identify if some target fields are set to static values

## 4. Navigation Instructions
- Document how to navigate to the source data
- Document how to navigate to the target form
- Include any login/logout procedures (only if applicable from the video)

## 5. Steps Involved
- Identify the steps involved in the data entry process in order
- Note any specific actions or interactions required. 
    - Mainly when submitting a form, or going to a different page, or opening a different section, popups, etc.
- Document any conditional logic or decision points
"""


if __name__ == "__main__":
    
    """Main function to run video SOP extraction from command line."""
    parser = argparse.ArgumentParser(
        description="Extract SOP from video using Vertex AI Gemini"
    )
    parser.add_argument(
        "video_path",
        help="relative path to the video file to analyze"
    )
    
    args = parser.parse_args()
    
    # Check if video file exists
    if not os.path.exists(args.video_path):
        print(f"❌ Error: Video file '{args.video_path}' does not exist.")
        sys.exit(1)
    
    # Check if it's actually a file
    if not os.path.isfile(args.video_path):
        print(f"❌ Error: '{args.video_path}' is not a file.")
        sys.exit(1)
    
    try:
        # Create extractor and analyze video
        extractor = VideoSOPExtractor()
        sop_result = extractor._extract_sop_with_gemini(args.video_path)
        
        # Check for errors
        if "Error analyzing video with Vertex AI" in sop_result:
            print(f"❌ {sop_result}")
            sys.exit(1)
        
        # Output the result
        print("\n" + "="*50)
        print("SOP RESULT:")
        print("="*50)
        print(sop_result)

        # Write the result to SOP.md
        with open("SOP.md", "w", encoding="utf-8") as f:
            f.write(sop_result)
        print("✅ SOP saved to: SOP.md")
            
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
