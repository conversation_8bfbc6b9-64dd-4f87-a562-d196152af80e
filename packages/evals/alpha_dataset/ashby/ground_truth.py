from automated_eval import MonitoredElement, URLState, WorkflowSpec

# Define the configuration for processing 3 candidate imports into Ashby ATS
# Based on the CSV data: <PERSON>, <PERSON>, <PERSON>
config = WorkflowSpec(
    workflow_name="Ashby Candidate Import Automation",
    url_states=[
        # First candidate: <PERSON> (Design role)
        URLState(
            url="https://app.ashbyhq.com/home/<USER>/right-side/candidates/drafts/*",
            description="Ashby candidate form - <PERSON>",
            record_index=0,
            expected_elements=[
                # Basic candidate information
                MonitoredElement(
                    selector="span[data-cy='candidate.name']",
                    value="<PERSON>",
                    description="Candidate full name field"
                ),
                MonitoredElement(
                    selector="span[data-cy='candidate.primary_email_addressValue']",
                    value="<EMAIL>",
                    description="Email address field"
                ),
                MonitoredElement(
                    selector="input[placeholder='Home, work, other...']",
                    value="Work",
                    description="Email type selection (Work - company domain)"
                ),
                MonitoredElement(
                    selector="span[data-cy='candidate.primary_phone_numberValue']",
                    value="******-605-5815",
                    description="Phone number field"
                ),
                # MonitoredElement(
                #     selector="span[data-cy='candidate.primary_phone_numberType']",
                #     value="Personal",
                #     description="Phone type selection (always Personal)"
                # ),
                # Attribution
                MonitoredElement(
                    selector="span[aria-placeholder='Search for source...']",
                    value="Sourced: Sourcing Form",
                    description="Attribution source selection"
                ),
                
                # Experience information
                MonitoredElement(
                    selector="span[aria-placeholder='e.g. Mr. Manager']",
                    value="Account Executive",
                    description="Current role field"
                ),
                MonitoredElement(
                    selector="span[aria-placeholder='e.g. Gobias Industries']",
                    value="Summit Finance",
                    description="Current company field"
                )
                # # Job assignment
                # MonitoredElement(
                #     selector="input[placeholder*='Search by department'], input[placeholder*='job title'], .job-search-field",
                #     value="Design",
                #     description="Job search field with Design category"
                # )
            ]
        ),
        
        # Second candidate: Sophia White (Engineering role)
        URLState(
            url="https://app.ashbyhq.com/home/<USER>/right-side/candidates/drafts/*",
            description="Ashby candidate form - Sophia White",
            record_index=1,
            expected_elements=[
                # Basic candidate information
                MonitoredElement(
                    selector="span[data-cy='candidate.name']",
                    value="Sophia White",
                    description="Candidate full name field"
                ),
                MonitoredElement(
                    selector="span[data-cy='candidate.primary_email_addressValue']",
                    value="<EMAIL>",
                    description="Email address field"
                ),
                MonitoredElement(
                    selector="input[placeholder='Home, work, other...']",
                    value="Work",
                    description="Email type selection (Work - company domain)"
                ),
                MonitoredElement(
                    selector="span[data-cy='candidate.primary_phone_numberValue']",
                    value="******-676-6171",
                    description="Phone number field"
                ),
                MonitoredElement(
                    selector="span[data-cy='candidate.primary_phone_numberType']",
                    value="Personal",
                    description="Phone type selection (always Personal)"
                ),
                
                # Attribution
                MonitoredElement(
                    selector="span[aria-placeholder='Search for source...']",
                    value="Sourced: Sourcing Form",
                    description="Attribution source selection"
                ),
                
                # Experience information
                MonitoredElement(
                    selector="span[aria-placeholder='e.g. Mr. Manager']",
                    value="UX Designer",
                    description="Current role field"
                ),
                MonitoredElement(
                    selector="span[aria-placeholder='e.g. Gobias Industries']",
                    value="Skyward Ventures",
                    description="Current company field"
                ),
                
                # Job assignment
                MonitoredElement(
                    selector="input[placeholder*='Search by department'], input[placeholder*='job title'], .job-search-field",
                    value="Engineering",
                    description="Job search field with Engineering category"
                )
            ]
        ),
        
        # Third candidate: Amelia Clark (Product Manager role - fallback for "People" tag)
        URLState(
            url="https://app.ashbyhq.com/home/<USER>/right-side/candidates/drafts/*",
            description="Ashby candidate form - Amelia Clark",
            record_index=2,
            expected_elements=[
                # Basic candidate information
                MonitoredElement(
                    selector="span[data-cy='candidate.name']",
                    value="Amelia Clark",
                    description="Candidate full name field"
                ),
                MonitoredElement(
                    selector="span[data-cy='candidate.primary_email_addressValue']",
                    value="<EMAIL>",
                    description="Email address field"
                ),
                MonitoredElement(
                    selector="input[placeholder='Home, work, other...']",
                    value="Work",
                    description="Email type selection (Work - company domain)"
                ),
                MonitoredElement(
                    selector="span[data-cy='candidate.primary_phone_numberValue']",
                    value="******-783-6537",
                    description="Phone number field"
                ),
                MonitoredElement(
                    selector="span[data-cy='candidate.primary_phone_numberType']",
                    value="Personal",
                    description="Phone type selection (always Personal)"
                ),
                
                # Attribution
                MonitoredElement(
                    selector="span[aria-placeholder='Search for source...']",
                    value="Sourced: Sourcing Form",
                    description="Attribution source selection"
                ),
                
                # Experience information
                MonitoredElement(
                    selector="span[aria-placeholder='e.g. Mr. Manager']",
                    value="Data Scientist",
                    description="Current role field"
                ),
                MonitoredElement(
                    selector="span[aria-placeholder='e.g. Gobias Industries']",
                    value="Zenith Marketing",
                    description="Current company field"
                ),
                
                # Job assignment
                MonitoredElement(
                    selector="input[placeholder*='Search by department'], input[placeholder*='job title'], .job-search-field",
                    value="Product Manager",
                    description="Job search field with Product Manager category (fallback for People tag)"
                )
            ]
        ),
        
        # Success confirmation state
        URLState(
            url="https://app.ashbyhq.com/candidates/**",
            description="Candidate successfully created and visible in candidates list",
            record_index=0,
            expected_elements=[
                MonitoredElement(
                    selector=".candidate-card, .candidate-row, [data-testid*='candidate']",
                    value="candidate created",
                    description="Candidate entry appears in candidates list"
                ),
                MonitoredElement(
                    selector=".success-message, .notification, .toast",
                    value="success",
                    description="Success confirmation message"
                )
            ]
        )
    ]
) 
