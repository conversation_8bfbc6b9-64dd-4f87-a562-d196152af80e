"""
Runtime Manifest Generator

IMPORTANT: This file must be uploaded to the same directory as the generated code
to properly generate the manifest.json file.

Expected directory structure:
runtime_workspace/session_id/generated_code_dir/
├── main.py                    # Generated workflow code
├── requirements.txt           # Dependencies (if any)
├── runtime_generate_manifest.py  # This script
└── manifest.json             # Generated by this script

This script will:
1. Read the main.py file in the current directory
2. Extract the input class from the main function
3. Generate a manifest.json file with the workflow schema
"""

import ast
import json
import os
import sys


def get_input_class_from_source(module_path: str, module_name: str) -> type:
    """Extract the input class from the main function's source code."""
    print(f"🔍 Reading source from: {module_path}")
    with open(module_path) as f:
        source = f.read()

    # Parse the source code into an AST
    tree = ast.parse(source)

    # Find the main function (sync or async)
    for node in ast.walk(tree):
        if (
            isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef))  # noqa: UP038 (deprecated)
            and node.name == "main"
        ):
            print(f"🔍 Found main function: {node.name}")
            # Look at the function arguments
            for arg in node.args.args:
                if arg.arg == "input":
                    print(
                        f"🔍 Found input parameter with annotation: {ast.unparse(arg.annotation)}"
                    )
                    # Get the type annotation
                    if isinstance(arg.annotation, ast.Name):
                        # Import the main module directly from file path instead of by name
                        import importlib.util

                        spec = importlib.util.spec_from_file_location(
                            "main_module", module_path
                        )
                        main_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(main_module)
                        print(
                            f"🔍 Getting class {arg.annotation.id} from main module"
                        )
                        return getattr(main_module, arg.annotation.id)
                    elif isinstance(arg.annotation, ast.Attribute):
                        module_name = arg.annotation.value.id
                        class_name = arg.annotation.attr
                        print(
                            f"🔍 Getting class {class_name} from module {module_name}"
                        )
                        return getattr(sys.modules[module_name], class_name)

    raise ValueError("Could not find input class in main function")


def generate_manifest(workflow_name: str, workflow_description: str) -> str:
    """
    Generate a manifest.json for a workflow
    Args:
        workflow_name: The name of the workflow
        workflow_description: The description of the workflow
    """
    with open("manifest.json", "w") as f:
        # Get the actual current directory name
        current_dir = os.getcwd()
        module_name = os.path.basename(current_dir)
        print(f"🔍 Module name: {module_name}")

        # Get the input class from the source code
        main_path = os.path.join(".", "main.py")
        print(f"🔍 Main path: {main_path}")

        # Check if main.py exists
        if not os.path.exists(main_path):
            raise FileNotFoundError(
                f"Required file {main_path} not found. Cannot generate manifest without main.py."
            )

        try:
            input_class = get_input_class_from_source(main_path, module_name)
            manifest = {
                "name": workflow_name,
                "description": workflow_description,
                "parameters": input_class.model_json_schema(),
            }
        except Exception as e:
            raise RuntimeError(
                f"Could not extract input class from {main_path}: {e}"
            ) from e

        json.dump(manifest, f, indent=2)
    return f"Manifest for {workflow_name} has been generated and saved to manifest.json."


if __name__ == "__main__":
    if len(sys.argv) >= 3:
        workflow_name = sys.argv[1]
        workflow_description = sys.argv[2]
    else:
        # Use default values when no arguments provided (likely in runtime environment)
        workflow_name = "generated_workflow"
        workflow_description = "Auto-generated workflow"
        print(
            f"ℹ️ No arguments provided. Using defaults: name='{workflow_name}', description='{workflow_description}'"
        )

    result = generate_manifest(workflow_name, workflow_description)
