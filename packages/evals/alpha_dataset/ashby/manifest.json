{"name": "Ashby Candidate Import", "description": "Automate candidate import into Ashby ATS platform by reading CSV data and filling candidate forms with proper attribution and job assignment", "version": "1.0.0", "main": "main.py", "dependencies": ["vibe-automation", "pydantic", "playwright"], "input_schema": {"type": "object", "properties": {"csv_file_path": {"type": "string", "description": "Path to CSV file containing candidate data with columns: first_name, last_name, email, phone, current_company, current_title, location, source, tags"}}, "required": ["csv_file_path"]}, "tags": ["ATS", "Ashby", "Candidate Import", "Automation", "CSV Processing"], "features": ["Automated candidate data entry", "Email type detection (Personal vs Work)", "Experience section population", "Job assignment based on tags", "Source attribution to 'Sourced: Sourcing Form'", "Batch processing of multiple candidates"], "workflow_steps": ["Navigate to Ashby platform", "Add new candidate via Add > Candidate", "Fill basic information (name, email, phone)", "Set attribution to Sourced: Sourcing Form", "Add experience information", "Navigate to job assignment screen", "Select appropriate job based on tags", "Complete candidate creation"]}