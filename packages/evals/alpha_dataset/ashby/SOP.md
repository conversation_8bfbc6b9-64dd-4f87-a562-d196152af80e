# Ashby Candidate Import Automation SOP

## Workflow Input
- **Data Source**: CSV file with candidate information
- **CSV Fields**: first_name, last_name, email, phone, current_company, current_title, location, source, tags
- **Sample Data**: 3 candidate records (<PERSON>, <PERSON>, <PERSON>)

## High-level Workflow Steps
Navigate to Ashby ATS platform and automate candidate creation by clicking "Add > Candidate", filling out candidate details, adding experience information, and selecting appropriate job assignments based on tags.

## Detailed Workflow Steps
1. **Navigate to Ashby Platform**: Access https://app.ashbyhq.com and complete authentication
2. **Add New Candidate**: Click "Add > Candidate" to open candidate creation form
3. **Fill Basic Information**:
   - Name: Combine first_name and last_name from CSV (e.g., "<PERSON> Clark")
   - Email Address: Use email value from CSV
   - Email Type: Determine if personal or work email based on domain analysis
   - Phone Number: Use phone value from CSV
   - Phone Type: Always select "Personal"
4. **Set Attribution**:
   - Source: Select "Sourced: Sourcing Form" for all outbound sources from dropdown
5. **Add Experience Section**:
   - Click "+Add" button next to Experience
   - Fill current role using current_title from CSV
   - Fill current company using current_company from CSV
6. **Continue to Job Assignment**:
   - Click "Next" to proceed to job selection screen
7. **Select Appropriate Job**:
   - Use "Search by department or job title..." field
   - Enter job category based on tags analysis (Design, Engineering, or Product Manager)
   - Select first matching job option
   - If design role cannot be found, categorize under Product Manager with appropriate seniority
8. **Complete Candidate Creation**:
   - Click Submit/Save/Create button to finalize candidate entry

## Additional Rules/Requirements
- **Email Type Logic**: Check for company domains vs common personal domains (gmail, yahoo, etc.) to determine email type
- **Attribution Rule**: All outbound sources should be attributed to "Sourced: Sourcing Form"
- **Phone Type**: Always choose "Personal" for phone number type
- **Job Assignment Logic**: Match keywords from tags to available job roles; if design role cannot be found, categorize under Product Manager with appropriate seniority
- **Authentication**: Platform is already logged in
- **Data Validation**: Ensure all required fields are populated before submission