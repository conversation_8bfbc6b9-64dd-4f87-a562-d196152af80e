from automated_eval import MonitoredElement, URLState, WorkflowSpec

# Define the configuration for processing 3 Airtable vendor records
config = WorkflowSpec(
    workflow_name="Airtable Vendor Form Automation - 3 Vendors",
    url_states=[
        # First vendor: Office Supply Depot
        URLState(
            url="https://airtable.com/app0OCkSrq3ML7wak/shrQNNZ2GKoDuXkSe",
            description="Airtable vendor form page - Office Supply Depot",
            record_index=0,
            expected_elements=[
                MonitoredElement(
                    selector="input[type='text'][aria-required='true']",
                    value="Office Supply Depot",
                    description="Vendor name field"
                ),
                MonitoredElement(
                    selector="a[href^='mailto:']",
                    value="<EMAIL>",
                    description="Email address field (as mailto link)"
                ),
                MonitoredElement(
                    selector="input[type='text'][aria-required='false']",
                    value="123 Main St, Anytown, USA 12345",
                    description="Address field"
                )
            ]
        ),
        URLState(
            url="https://airtable.com/app0OCkSrq3ML7wak/shrQNNZ2GKoDuXkSe",
            description="Confirmation page after Office Supply Depot submission",
            record_index=0,
            expected_elements=[
                MonitoredElement(
                    selector="div.message[role='alert']",
                    value="Thank you for submitting the form!",
                    description="Thank you confirmation message"
                ),
                MonitoredElement(
                    selector="div[role='button'][class*='submit']",
                    value="Submit another response",
                    description="Submit another response button"
                )
            ]
        ),
        # Second vendor: Creative Ink LLC
        URLState(
            url="https://airtable.com/app0OCkSrq3ML7wak/shrQNNZ2GKoDuXkSe",
            description="Airtable vendor form page - Creative Ink LLC",
            record_index=1,
            expected_elements=[
                MonitoredElement(
                    selector="input[type='text'][aria-required='true']",
                    value="Creative Ink LLC",
                    description="Vendor name field"
                ),
                MonitoredElement(
                    selector="a[href^='mailto:']",
                    value="<EMAIL>",
                    description="Email address field (as mailto link)"
                ),
                MonitoredElement(
                    selector="input[type='text'][aria-required='false']",
                    value="456 Design Ave, Suite 200, Art City, USA 23456",
                    description="Address field"
                )
            ]
        ),
        URLState(
            url="https://airtable.com/app0OCkSrq3ML7wak/shrQNNZ2GKoDuXkSe",
            description="Confirmation page after Creative Ink LLC submission",
            record_index=1,
            expected_elements=[
                MonitoredElement(
                    selector="div.message[role='alert']",
                    value="Thank you for submitting the form!",
                    description="Thank you confirmation message"
                ),
                MonitoredElement(
                    selector="div[role='button'][class*='submit']",
                    value="Submit another response",
                    description="Submit another response button"
                )
            ]
        ),
        # Third vendor: Tech Solutions Group
        URLState(
            url="https://airtable.com/app0OCkSrq3ML7wak/shrQNNZ2GKoDuXkSe",
            description="Airtable vendor form page - Tech Solutions Group",
            record_index=2,
            expected_elements=[
                MonitoredElement(
                    selector="input[type='text'][aria-required='true']",
                    value="Tech Solutions Group",
                    description="Vendor name field"
                ),
                MonitoredElement(
                    selector="a[href^='mailto:']",
                    value="<EMAIL>",
                    description="Email address field (as mailto link)"
                ),
                MonitoredElement(
                    selector="input[type='text'][aria-required='false']",
                    value="789 Tech Park, Silicon Valley, USA 34567",
                    description="Address field"
                )
            ]
        ),
        URLState(
            url="https://airtable.com/app0OCkSrq3ML7wak/shrQNNZ2GKoDuXkSe",
            description="Final confirmation page after Tech Solutions Group submission",
            record_index=2,
            expected_elements=[
                MonitoredElement(
                    selector="div.message[role='alert']",
                    value="Thank you for submitting the form!",
                    description="Thank you confirmation message"
                )
            ]
        )
    ]
) 
