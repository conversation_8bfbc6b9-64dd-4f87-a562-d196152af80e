import asyncio
import csv
import logging

from pydantic import BaseModel
from va import step, workflow
from va.playwright import get_browser_context


class WorkflowInput(BaseModel):
    csv_file_path: str

@workflow("Airtable Vendor Form Automation")
async def main(input: WorkflowInput, logger: logging.Logger):
    """Implementation of the Airtable vendor form automation workflow"""
    
    # Read CSV data
    vendors = []
    with open(input.csv_file_path, newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            vendors.append(row)
    
    logger.info(f"Found {len(vendors)} vendors to process")
    
    async with get_browser_context(headless=False, slow_mo=500) as context:
        page = await context.new_page()
        
        for i, vendor in enumerate(vendors, 1):
            logger.info(f"Processing vendor {i}/{len(vendors)}: {vendor['VendorName']}")
            
            with step(f"Navigate to form for vendor {i}"):
                await page.goto("https://airtable.com/app0OCkSrq3ML7wak/shrQNNZ2GKoDuXkSe")
                
                # Close cookie banner if it appears (only on first visit)
                if i == 1:
                    try:
                        async with page.step("Close cookie banner"):
                            await page.get_by_role('button', name='Close').click()
                    except Exception:
                        logger.info("Cookie banner not found or already closed")
            
            with step(f"Fill vendor information for {vendor['VendorName']}"):
                async with page.step("Fill vendor name field"):
                    vendor_name_field = page.get_by_role('textbox', name='Vendor Name') | page.get_by_prompt("Vendor name input field")
                    await vendor_name_field.fill(vendor['VendorName'])
                
                async with page.step("Fill email address field"):
                    email_field = page.get_by_role('textbox', name='Your Email Address') | page.get_by_prompt("Email address input field")
                    await email_field.fill(vendor['ContactEmail'])
                
                async with page.step("Fill address field"):
                    address_field = page.get_by_role('textbox', name='Address', exact=True) | page.get_by_prompt("Address input field")
                    await address_field.fill(vendor['Address'])
            
            with step(f"Submit form for {vendor['VendorName']}"):
                async with page.step("Click submit button"):
                    submit_button = page.get_by_role('button', name='Submit') | page.get_by_prompt("Submit button")
                    await submit_button.click()
                
                # Wait for submission confirmation
                async with page.step("Wait for submission confirmation"):
                    await page.wait_for_selector('text=Thank you for submitting the form!', timeout=10000)
                    logger.info(f"Successfully submitted vendor: {vendor['VendorName']}")
                
                # Click "Submit another response" if not the last vendor
                if i < len(vendors):
                    async with page.step("Click submit another response"):
                        submit_another = page.get_by_role('button', name='Submit another response') | page.get_by_prompt("Submit another response button")
                        await submit_another.click()
    
    logger.info(f"Successfully processed all {len(vendors)} vendors")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main(WorkflowInput(csv_file_path="local_storage/e2230af5-f5db-44e5-8767-087b27ee30d0/vendors - workflow creation.csv"), logger=logging.getLogger(__name__)))
