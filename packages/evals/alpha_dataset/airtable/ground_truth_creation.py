from evals import MonitoredElement, URLState, WorkflowSpec

# Define the configuration for processing 1 Airtable vendor record (test version)
config = WorkflowSpec(
    workflow_name="Airtable Vendor Form Automation - Test (1 Vendor)",
    url_states=[
        # First vendor: Office Supply Depot
        URLState(
            url="https://airtable.com/app0OCkSrq3ML7wak/shrQNNZ2GKoDuXkSe",
            description="Airtable vendor form page - Office Supply Depot",
            record_index=0,
            expected_elements=[
                MonitoredElement(
                    selector="input[type='text'][aria-required='true']",
                    value="Office Supply Depot",
                    description="Vendor name field"
                ),
                MonitoredElement(
                    selector="a[href^='mailto:']",
                    value="<EMAIL>",
                    description="Email address field (as mailto link)"
                ),
                MonitoredElement(
                    selector="input[type='text'][aria-required='false']",
                    value="123 Main St, Anytown, USA 12345",
                    description="Address field"
                )
            ]
        ),
        URLState(
            url="https://airtable.com/app0OCkSrq3ML7wak/shrQNNZ2GKoDuXkSe",
            description="Confirmation page after Office Supply Depot submission",
            record_index=0,
            expected_elements=[
                MonitoredElement(
                    selector="div.message[role='alert']",
                    value="Thank you for submitting the form!",
                    description="Thank you confirmation message"
                ),
                MonitoredElement(
                    selector="div[role='button'][class*='submit']",
                    value="Submit another response",
                    description="Submit another response button"
                )
            ]
        )
    ]
) 
