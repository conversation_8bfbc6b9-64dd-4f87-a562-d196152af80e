from automated_eval import MonitoredElement, URLState, WorkflowSpec

# Define the configuration for processing WellCare Provider Network Application
# Based on the CSV data: <PERSON>, MD, HealthFirst Group, etc.
config = WorkflowSpec(
    workflow_name="WellCare Provider Network Application Automation",
    url_states=[
        # Initial form state with provider data filled
        URLState(
            url="https://www.wellcare.com/Become-a-Provider",
            description="WellCare provider application form - Danielle <PERSON>",
            record_index=0,
            expected_elements=[
                # Basic provider information
                MonitoredElement(
                    selector="input[data-anwserid='{C7A346AD-1278-405C-897F-C73697D8286A}']",
                    value="Danielle",
                    description="Provider First Name field"
                ),
                MonitoredElement(
                    selector="input[data-anwserid='{74EE88B6-632F-4E45-85ED-85C188665CE7}']",
                    value="Johnson",
                    description="Provider Last Name field"
                ),
                MonitoredElement(
                    selector="div[data-index='010'] p.sl-label",
                    value="MD",
                    description="Degree Type dropdown selection"
                ),
                
                # Group information
                MonitoredElement(
                    selector="input[data-anwser='Check to join as a group']:checked",
                    value="on",
                    description="Join as a group checkbox"
                ),
                MonitoredElement(
                    selector="input[data-anwserid='{6F00B6FA-741C-4FDD-86DB-64BF0F8B72BD}']",
                    value="HealthFirst Group",
                    description="Group Name field"
                ),
                MonitoredElement(
                    selector="input[data-anwserid='{59E9D164-2D22-43D9-B553-2045B8C6A9DB}']",
                    value="*********",
                    description="Tax Identification Number field"
                ),
                
                # Professional details
                MonitoredElement(
                    selector="div[data-index='050'] p.sl-label",
                    value="Orthopedic Surgery",
                    description="Specialty dropdown selection"
                ),
                MonitoredElement(
                    selector="div[data-index='051'] p.sl-label",
                    value="Medicaid",
                    description="Line of Business dropdown selection"
                ),
                
                # Practice location
                MonitoredElement(
                    selector="input[data-anwserid='{3E55D48D-FFDD-41C3-92DC-CE74064B0AF9}']",
                    value="21819 Johnson Course",
                    description="Street Address field"
                ),
                MonitoredElement(
                    selector="input[data-anwserid='{6EB1DFC3-7D40-493D-AA6C-19CFAE768A38}']",
                    value="East William",
                    description="City field"
                ),
                MonitoredElement(
                    selector="div[data-index='062'] p.sl-label",
                    value="Texas",
                    description="State dropdown selection"
                ),
                MonitoredElement(
                    selector="input[data-anwserid='{5C57BA1C-529F-4367-A3DA-D0C89B6B5A92}']",
                    value="39793",
                    description="ZIP Code field"
                ),
                MonitoredElement(
                    selector="div[data-index='071'] p.sl-label",
                    value="Brazoria",
                    description="County dropdown selection"
                ),
                
                # Facility information
                MonitoredElement(
                    selector="div[data-index='090'] p.sl-label",
                    value="Laboratory",
                    description="Facility type dropdown selection"
                ),
                
                # Credentials
                MonitoredElement(
                    selector="input[data-anwserid='{344DC16A-533F-46B5-9F4C-FC91069F7109}']",
                    value="**********",
                    description="NPI field"
                ),
                MonitoredElement(
                    selector="input[data-anwserid='{2CD127F5-0555-492A-8ABA-EF9B1D3CFBB5}']",
                    value="99585092",
                    description="CAQH Number field"
                ),
                MonitoredElement(
                    selector="input[data-anwserid='{CA1F900B-DFE3-4CB8-B348-981EDDC7EBB6}']",
                    value="**********",
                    description="Medicare Number field"
                ),
                MonitoredElement(
                    selector="input[data-anwserid='{A71022BE-B512-43B4-8871-75EA4D6A08EF}']",
                    value="**********",
                    description="Medicaid Number field"
                ),
                MonitoredElement(
                    selector="input[data-anwserid='{448D9105-191A-4C5A-AE56-BDA0D9DB5497}']",
                    value="Js78161",
                    description="License Number field"
                ),
                
                # Contact information
                MonitoredElement(
                    selector="input[data-anwserid='{86B227FA-DC75-422F-BED6-2F20A85340DB}']",
                    value="Maria Montgomery",
                    description="Primary Contact Name field"
                ),
                MonitoredElement(
                    selector="input[data-anwserid='{CCB0050A-B1F5-490F-B9E9-4F77A4B56BD9}']",
                    value="<EMAIL>",
                    description="Email field"
                ),
                MonitoredElement(
                    selector="input[data-anwserid='{352CDA91-85DB-494A-874F-2574BDD7DF94}']",
                    value="(*************",
                    description="Phone Number field"
                ),
                
                # Additional information with additional locations
                MonitoredElement(
                    selector="[id*='Pleaseprovideanyadditionalinformation'] textarea[data-anwserid='{B9720935-C0D5-4DF8-902B-715B05919F47}']",
                    value="Additional Location 2:\nAddress: 863 Lawrence Valleys\nCity: Port Lindachester, State: CA ZIP: 45098\nCounty: Los Angeles\n\n",
                    description="Additional information field with second location"
                ),
                
                # Submit button
                MonitoredElement(
                    selector="button#btnSubmit",
                    value="Submit",
                    description="Submit button"
                )
            ]
        ),
        
        # Success/confirmation state after form submission
        URLState(
            url="https://www.wellcare.com/en/successpage*",
            description="Success page after provider application submission",
            record_index=0,
            expected_elements=[]
        )
    ]
) 
