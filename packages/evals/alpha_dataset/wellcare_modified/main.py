import asyncio
import csv
import logging

from pydantic import BaseModel
from va import step, workflow
from va.playwright import get_browser_context

# County lookup based on ZIP code, city, and state (following Medallion data format)
COUNTY_LOOKUP = {
    # Texas counties
    "39793": "Brazoria County",
    "75001": "Collin County",
    "75002": "Collin County",
    "77001": "Harris County",
    "77002": "Harris County",
    "77003": "Harris County",
    "78701": "Travis County",
    "78702": "Travis County",
    "78703": "Travis County",
    "78704": "Travis County",
    "78705": "Travis County",
    "79901": "El Paso County",
    "79902": "El Paso County",
    "79903": "El Paso County",
    "79904": "El Paso County",
    "79905": "El Paso County",
    # California counties
    "45098": "Los Angeles County",
    "90001": "Los Angeles County",
    "90002": "Los Angeles County",
    "90003": "Los Angeles County",
    "90004": "Los Angeles County",
    "90005": "Los Angeles County",
    "90006": "Los Angeles County",
    "90007": "Los Angeles County",
    "90008": "Los Angeles County",
    "90009": "Los Angeles County",
    "90010": "Los Angeles County",
    "94102": "San Francisco County",
    "94103": "San Francisco County",
    "94104": "San Francisco County",
    "94105": "San Francisco County",
    "94106": "San Francisco County",
    "94107": "San Francisco County",
    "94108": "San Francisco County",
    "94109": "San Francisco County",
    "94110": "San Francisco County",
    "94111": "San Francisco County",
    "94112": "San Francisco County",
    # Add more ZIP codes as needed
}

def infer_county_from_zip(zip_code: str, city: str, state: str) -> str:
    """Infer county from ZIP code, city, and state following Medallion data format."""
    # First try direct ZIP code lookup
    if zip_code in COUNTY_LOOKUP:
        return COUNTY_LOOKUP[zip_code]
    
    # Fallback logic based on state and city patterns
    state_county_map = {
        "TX": {
            "Houston": "Harris County",
            "Dallas": "Dallas County",
            "Austin": "Travis County",
            "San Antonio": "Bexar County",
            "Fort Worth": "Tarrant County",
            "El Paso": "El Paso County",
            "East William": "Brazoria County",  # Based on the sample data
        },
        "CA": {
            "Los Angeles": "Los Angeles County",
            "San Francisco": "San Francisco County",
            "San Diego": "San Diego County",
            "Sacramento": "Sacramento County",
            "Oakland": "Alameda County",
            "Port Lindachester": "Los Angeles County",  # Based on the sample data
        },
        "FL": {
            "Miami": "Miami-Dade County",
            "Tampa": "Hillsborough County",
            "Orlando": "Orange County",
            "Jacksonville": "Duval County",
        },
        "NY": {
            "New York": "New York County",
            "Brooklyn": "Kings County",
            "Queens": "Queens County",
            "Bronx": "Bronx County",
            "Staten Island": "Richmond County",
        }
    }
    
    if state in state_county_map and city in state_county_map[state]:
        return state_county_map[state][city]
    
    # Default fallback - return a generic county name
    return f"{city} County"

class WorkflowInput(BaseModel):
    csv_file_path: str

@workflow("WellCare Provider Network Application Automation")
async def main(input: WorkflowInput, logger: logging.Logger):
    """Automate filling out WellCare provider network application forms."""
    
    logger.info("Starting WellCare Provider Network Application workflow")
    
    # Read CSV data
    providers = []
    with step("Step 1: Read CSV data"):
        logger.info(f"Reading CSV file: {input.csv_file_path}")
        with open(input.csv_file_path, encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                providers.append(row)
        logger.info(f"Found {len(providers)} providers to process")
    
    # Process each provider
    for i, provider in enumerate(providers, 1):
        logger.info(f"Processing provider {i}/{len(providers)}: {provider['Provider First Name']} {provider['Provider Last Name']}")
        
        async with get_browser_context(headless=False, slow_mo=500) as context:
            page = await context.new_page()
            
            with step(f"Step 2: Navigate to WellCare form for provider {i}"):
                logger.info("Navigating to WellCare provider form")
                async with page.step("Navigate to WellCare provider form"):
                    await page.goto("https://www.wellcare.com/Become-a-Provider")
                    await page.wait_for_load_state("networkidle")
                
                # Accept cookies if present
                try:
                    async with page.step("Accept cookies if present"):
                        await page.get_by_role("button", name="Accept").click()
                        await page.wait_for_timeout(1000)
                except Exception:
                    logger.info("No cookie consent dialog found")
            
            with step(f"Step 3: Fill basic provider information for {provider['Provider First Name']} {provider['Provider Last Name']}"):
                logger.info("Filling provider basic information")
                
                # Provider First Name
                async with page.step("Fill provider first name"):
                    first_name_field = page.get_by_label("Provider First Name") | page.get_by_prompt("Provider first name field")
                    await first_name_field.fill(provider['Provider First Name'])
                    await page.wait_for_timeout(500)
                
                # Provider Last Name
                async with page.step("Fill provider last name"):
                    last_name_field = page.get_by_label("Provider Last Name") | page.get_by_prompt("Provider last name field")
                    await last_name_field.fill(provider['Provider Last Name'])
                    await page.wait_for_timeout(500)
                
                # Degree Type - Use prompt-based selection
                async with page.step("Select degree type"):
                    degree_field = page.get_by_label("Degree Type") | page.get_by_prompt("Degree type dropdown")
                    await degree_field.select_option(provider['Degree Type'])
                    await page.wait_for_timeout(500)
            
            with step(f"Step 4: Handle group information for {provider['Provider First Name']} {provider['Provider Last Name']}"):
                logger.info("Handling group information")
                
                # Check "Join as a group" if group name exists
                if provider['Group Name'].strip():
                    async with page.step("Check join as a group"):
                        group_checkbox = page.get_by_label("Check to add provider to existing agreement") | page.get_by_prompt("add provider to existing agreement checkbox")
                        await group_checkbox.check()
                        await page.wait_for_timeout(500)
                    
                    # Fill Group Name
                    async with page.step("Fill group name"):
                        group_name_field = page.get_by_label("Group Name") | page.get_by_prompt("Group name field")
                        await group_name_field.fill(provider['Group Name'])
                        await page.wait_for_timeout(500)
                    
                    # Fill Tax Identification Number
                    async with page.step("Fill tax identification number"):
                        tax_id_field = page.get_by_label("Tax Identification Number") | page.get_by_prompt("Tax identification number field")
                        await tax_id_field.fill(provider['Tax Identification Number'])
                        await page.wait_for_timeout(500)
            
            with step(f"Step 5: Fill professional details for {provider['Provider First Name']} {provider['Provider Last Name']}"):
                logger.info("Filling professional details")
                
                # Specialty - Use prompt-based selection
                specialty_mapping = {
                    "Orthopedics": "Orthopedic Surgery",
                    "Cardiology": "Cardiology",
                    "Family Medicine": "Family Practice",
                    "Internal Medicine": "Internal Medicine",
                    "Pediatrics": "Pediatrics / Pediatrician",
                    "Dermatology": "Dermatology",
                    "Neurology": "Neurology",
                    "Psychiatry": "Psychiatry / Psychiatrist",
                    "Radiology": "Radiology",
                    "Anesthesiology": "Anesthesiology",
                    "Emergency Medicine": "Emergency Medicine",
                    "Pathology": "Pathology",
                    "Surgery": "General Surgery",
                    "Urology": "Urology",
                    "Ophthalmology": "Ophthalmology",
                    "Otolaryngology": "Otolaryngology",
                    "Gastroenterology": "Gastroenterology (Digestive Disorders)",
                    "Endocrinology": "Endocrinology",
                    "Nephrology": "Nephrology",
                    "Rheumatology": "Rheumatology",
                    "Oncology": "Oncology",
                    "Pulmonology": "Pulmonary Medicine",
                    "Infectious Disease": "Infectious Disease",
                    "Geriatrics": "Geriatric Medicine",
                    "Sports Medicine": "Sport Medicine",
                    "Pain Management": "Pain Management",
                    "Physical Therapy": "Physical Therapy",
                    "Occupational Therapy": "Occupational Therapy",
                    "Speech Therapy": "Speech Therapy"
                }
                specialty_value = specialty_mapping.get(provider['Specialty'], provider['Specialty'])
                async with page.step("Select specialty"):
                    specialty_field = page.get_by_label("Specialty") | page.get_by_prompt("Specialty dropdown")
                    await specialty_field.select_option(specialty_value)
                    await page.wait_for_timeout(500)
                
                # Line of Business - Use prompt-based selection
                async with page.step("Select line of business"):
                    line_of_business_field = page.get_by_label("Line of Business") | page.get_by_prompt("Line of business dropdown")
                    await line_of_business_field.select_option(provider['Line of Business'])
                    await page.wait_for_timeout(500)
            
            with step(f"Step 6: Fill practice location for {provider['Provider First Name']} {provider['Provider Last Name']}"):
                logger.info("Filling practice location")
                
                # Street Address (using Address 1 as primary)
                async with page.step("Fill street address"):
                    street_address_field = page.get_by_label("Street Address") | page.get_by_prompt("Street address field")
                    await street_address_field.fill(provider['Street Address 1'])
                    await page.wait_for_timeout(500)
                
                # City
                async with page.step("Fill city"):
                    city_field = page.get_by_label("City") | page.get_by_prompt("City field")
                    await city_field.fill(provider['City 1'])
                    await page.wait_for_timeout(500)
                
                # State - Use prompt-based selection
                async with page.step("Select state"):
                    state_field = page.get_by_label("State") | page.get_by_prompt("State dropdown")
                    await state_field.select_option(provider['State 1'])
                    await page.wait_for_timeout(1000)  # Wait for county dropdown to populate
                
                # ZIP Code
                async with page.step("Fill ZIP code"):
                    zip_field = page.get_by_label("ZIP Code") | page.get_by_prompt("ZIP code field")
                    await zip_field.fill(provider['Postal Code 1'])
                    await page.wait_for_timeout(500)
                
                # County (inferred from ZIP, city, state)
                inferred_county = infer_county_from_zip(provider['Postal Code 1'], provider['City 1'], provider['State 1'])
                logger.info(f"Inferred county: {inferred_county}")
                async with page.step("Select county"):
                    county_field = page.get_by_label("County") | page.get_by_prompt("County dropdown")
                    try:
                        await county_field.select_option(inferred_county)
                    except Exception:
                        logger.warning(f"Could not select county '{inferred_county}', trying first available option")
                        # Get all available options and select the first real county (not placeholder)
                        await county_field.select_option(index=1)
                    await page.wait_for_timeout(500)
            
            with step(f"Step 7: Fill facility information for {provider['Provider First Name']} {provider['Provider Last Name']}"):
                logger.info("Filling facility information")
                
                # Facility Type - Use prompt-based selection
                facility_mapping = {
                    "Laboratory": "Laboratory",
                    "Hospital": "Hospital",
                    "Urgent Care": "Urgent Care",
                    "Free Standing Facility": "Free Standing Facility",
                    "Clinic": "Walk in clinic",
                    "ASC": "ASC",
                    "DME": "DME",
                    "Home Health": "Home Health",
                    "Hospice": "Hospice",
                    "Skilled Nursing Facility": "Skilled Nursing Facility",
                    "Rehab Hospital": "Rehab Hospital"
                }
                facility_value = facility_mapping.get(provider['Facility'].strip(), provider['Facility'].strip())
                async with page.step("Select facility type"):
                    facility_field = page.get_by_label("Facility") | page.get_by_prompt("Facility type dropdown")
                    await facility_field.select_option(facility_value)
                    await page.wait_for_timeout(500)
            
            with step(f"Step 8: Fill credentials for {provider['Provider First Name']} {provider['Provider Last Name']}"):
                logger.info("Filling credentials and numbers")
                
                # NPI
                async with page.step("Fill NPI"):
                    npi_field = page.get_by_label("NPI") | page.get_by_prompt("NPI number field")
                    await npi_field.fill(provider['NPI'])
                    await page.wait_for_timeout(500)
                
                # CAQH Number
                async with page.step("Fill CAQH number"):
                    caqh_field = page.get_by_label("CAQH Number") | page.get_by_prompt("CAQH number field")
                    await caqh_field.fill(provider['CAQH Number'])
                    await page.wait_for_timeout(500)
                
                # Medicare Number
                async with page.step("Fill Medicare number"):
                    medicare_field = page.get_by_label("Medicare Number") | page.get_by_prompt("Medicare number field")
                    await medicare_field.fill(provider['Medicare Number'])
                    await page.wait_for_timeout(500)
                
                # Medicaid Number
                async with page.step("Fill Medicaid number"):
                    medicaid_field = page.get_by_label("Medicaid Number") | page.get_by_prompt("Medicaid number field")
                    await medicaid_field.fill(provider['Medicaid Number'])
                    await page.wait_for_timeout(500)
                
                # License Number
                async with page.step("Fill license number"):
                    license_field = page.get_by_label("License Number") | page.get_by_prompt("License number field")
                    await license_field.fill(provider['License Number'])
                    await page.wait_for_timeout(500)
            
            with step(f"Step 9: Fill contact information for {provider['Provider First Name']} {provider['Provider Last Name']}"):
                logger.info("Filling contact information")
                
                # Primary Contact Name
                async with page.step("Fill primary contact name"):
                    contact_name_field = page.get_by_label("Primary Contact Name") | page.get_by_prompt("Primary contact name field")
                    await contact_name_field.fill(provider['Primary Contact Names'])
                    await page.wait_for_timeout(500)
                
                # Email
                async with page.step("Fill email"):
                    email_field = page.get_by_label("Email") | page.get_by_prompt("Email field")
                    await email_field.fill(provider['Email'])
                    await page.wait_for_timeout(500)
                
                # Phone Number
                async with page.step("Fill phone number"):
                    phone_field = page.get_by_label("Phone Number") | page.get_by_prompt("Phone number field")
                    await phone_field.fill(provider['Phone Number'])
                    await page.wait_for_timeout(500)
                
                # Additional Information (include additional locations)
                additional_info = ""
                if provider['Street Address 2'].strip():
                    additional_info += "Additional Location 2:\n"
                    additional_info += f"Address: {provider['Street Address 2']}\n"
                    additional_info += f"City: {provider['City 2']}, State: {provider['State 2']} ZIP: {provider['Postal Code 2']}\n"
                    inferred_county_2 = infer_county_from_zip(provider['Postal Code 2'], provider['City 2'], provider['State 2'])
                    additional_info += f"County: {inferred_county_2}\n\n"
                
                if provider['Street Address 3'].strip():
                    additional_info += "Additional Location 3:\n"
                    additional_info += f"Address: {provider['Street Address 3']}\n"
                    additional_info += f"City: {provider['City 3']}, State: {provider['State 3']} ZIP: {provider['Postal Code 3']}\n"
                    inferred_county_3 = infer_county_from_zip(provider['Postal Code 3'], provider['City 3'], provider['State 3'])
                    additional_info += f"County: {inferred_county_3}\n\n"
                
                if additional_info:
                    async with page.step("Fill additional information"):
                        additional_info_field = page.get_by_label("Please provide any additional information") | page.get_by_prompt("Additional information field")
                        await additional_info_field.fill(additional_info)
                        await page.wait_for_timeout(500)
            
            with step(f"Step 10: Submit application for {provider['Provider First Name']} {provider['Provider Last Name']}"):
                logger.info("Submitting application")
                
                # Submit the form
                async with page.step("Click submit button"):
                    submit_button = page.get_by_role("button", name="Submit") | page.get_by_prompt("Submit button")
                    await submit_button.click()
                    await page.wait_for_timeout(2000)
                
                # Wait for submission confirmation or error
                try:
                    async with page.step("Wait for submission confirmation"):
                        # Check for success message or redirect
                        await page.wait_for_load_state("networkidle", timeout=10000)
                        logger.info(f"Application submitted successfully for {provider['Provider First Name']} {provider['Provider Last Name']}")
                except Exception:
                    logger.warning("Could not confirm submission - may need manual verification")
                
                # Wait between submissions
                await page.wait_for_timeout(3000)
    
    logger.info("Completed processing all providers")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main(WorkflowInput(csv_file_path="/Users/<USER>/projects/cursor/vibe-automation-server/packages/evals/alpha_dataset/wellcare_modified/data_workflow_creation.csv"), logger=logging.getLogger(__name__)))
