import asyncio
import csv
import logging

from pydantic import BaseModel
from va import step, workflow
from va.playwright import get_browser_context


class WorkflowInput(BaseModel):
    csv_file_path: str

@workflow("Shopify Product Addition Workflow")
async def main(input: WorkflowInput, logger: logging.Logger):
    """Implementation of the Shopify Product Addition SOP workflow"""
    
    # Read CSV data
    products = []
    try:
        with open(input.csv_file_path, encoding='utf-8') as file:
            reader = csv.DictReader(file)
            products = list(reader)
        logger.info(f"Loaded {len(products)} products from CSV")
    except Exception as e:
        logger.error(f"Error reading CSV file: {e}")
        return
    
    if not products:
        logger.error("No products found in CSV file")
        return
    
    async with get_browser_context(headless=False, slow_mo=500) as context:
        page = await context.new_page()
        
        with step("Step 1: Login to Shopify Admin"):
            logger.info("Navigating to Shopify admin login page")
            await page.goto('https://admin.shopify.com/store/e0veum-jf/products')
            await page.wait_for_timeout(3000)  # Wait for page to settle
            
            # Check if login is required
            if "login" in page.url.lower() or "accounts.shopify.com" in page.url:
                logger.info("Login required - filling credentials")
                
                # Fill email
                await page.get_by_role('textbox', name='Email').fill('<EMAIL>')
                await page.get_by_role('button', name='Continue with email').click()
                
                # Fill password
                await page.wait_for_selector('input[name="password"], [type="password"]', timeout=10000)
                await page.get_by_role('textbox', name='Password').fill('orby@2025')
                await page.get_by_role('button', name='Log in').click()
                
                # Wait for redirect to products page
                await page.wait_for_url('**/products**', timeout=30000)
                logger.info("Successfully logged in")
            else:
                logger.info("Already logged in")
        
        with step("Step 2: Process each product from CSV"):
            for i, product in enumerate(products, 1):
                logger.info(f"Processing product {i}/{len(products)}: {product['Title']}")
                
                with step(f"Step 2.{i}: Add product - {product['Title']}"):
                    # Navigate to add product page
                    await page.goto('https://admin.shopify.com/store/e0veum-jf/products/new')
                    await page.wait_for_timeout(3000)  # Wait for page to settle
                    
                    # Fill product title
                    async with page.step("Fill product title", context={"title": product['Title']}) as step_context:
                        await page.get_by_role('textbox', name='Title').fill(step_context["title"])
                    
                    # Fill product price
                    async with page.step("Fill product price", context={"price": product['Price']}) as step_context:
                        await page.get_by_role('textbox', name='Price $', exact=True).fill(step_context["price"])
                    
                    # Enable SKU field and fill SKU
                    async with page.step("Enable SKU field", context={"sku": product['SKU']}) as step_context:
                        # Check if SKU checkbox is already checked
                        sku_checkbox = page.get_by_role('checkbox', name='This product has a SKU or')
                        if not await sku_checkbox.is_checked():
                            await sku_checkbox.click()
                        
                        # Fill SKU
                        await page.get_by_role('textbox', name='SKU (Stock Keeping Unit)').fill(step_context["sku"])
                    
                    # Fill product description (if possible)
                    if product.get('Description'):
                        async with page.step("Fill product description", context={"description": product['Description']}) as step_context:
                            try:
                                # Try to focus on the description iframe and type
                                await page.locator('iframe[title="Rich Text Area"]').click()
                                await page.wait_for_timeout(1000)
                                await page.keyboard.type(step_context["description"])
                                logger.info("Successfully filled description")
                            except Exception as e:
                                logger.warning(f"Could not fill description: {e}")
                    
                    # Save the product
                    async with page.step("Save product", context={"title": product['Title']}) as step_context:
                        await page.locator('#AppFrameScrollable').get_by_role('button', name='Save').click()
                        
                        # Wait for save to complete
                        await page.wait_for_timeout(3000)
                        
                        # Check for success message or redirect
                        try:
                            await page.wait_for_selector('text="Product created"', timeout=10000)
                            logger.info(f"Successfully created product: {step_context['title']}")
                        except Exception:
                            # Alternative check - look for product URL pattern
                            if '/products/' in page.url and page.url != 'https://admin.shopify.com/store/e0veum-jf/products/new':
                                logger.info(f"Product created successfully (URL redirect): {step_context['title']}")
                            else:
                                logger.warning(f"Could not confirm product creation for: {step_context['title']}")
                    
                    # Wait between products
                    await page.wait_for_timeout(2000)
        
        with step("Step 3: Verify all products were created"):
            logger.info("Navigating to products list to verify creation")
            await page.goto('https://admin.shopify.com/store/e0veum-jf/products')
            await page.wait_for_timeout(3000)  # Wait for page to settle
            
            # Log completion
            logger.info(f"Workflow completed. Attempted to create {len(products)} products")
            
            # Take final screenshot for verification
            await page.screenshot(path="final_products_page.png")
            logger.info("Final screenshot saved as final_products_page.png")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    # Use the CSV file path from the runtime workspace
    csv_path = "/Users/<USER>/projects/cursor/vibe-automation-server/packages/evals/alpha_dataset/shopify/eval_data.csv"
    asyncio.run(main(WorkflowInput(csv_file_path=csv_path), logger=logging.getLogger(__name__)))
