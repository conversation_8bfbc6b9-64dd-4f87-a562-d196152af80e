from automated_eval import MonitoredElement, URLState, WorkflowSpec

# Define the configuration for processing 1 Shopify product (test version)
config = WorkflowSpec(
    workflow_name="Shopify Product Addition Workflow - Test (1 Product)",
    url_states=[
        # First product: Artisan Leather Journal
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/new",
            description="Shopify new product page - Artisan Leather Journal",
            record_index=0,
            expected_elements=[
                MonitoredElement(
                    selector="input[name='title']",
                    value="Artisan Leather Journal",
                    description="Product title field"
                ),
                MonitoredElement(
                    selector="input[name='price']",
                    value="49.99",
                    description="Product price field"
                ),
                MonitoredElement(
                    selector="input[name='sku']",
                    value="ALJ-001",
                    description="SKU field"
                ),
                MonitoredElement(
                    selector=".Polaris-Checkbox__Icon path",
                    value="Polaris-Checkbox--checked",
                    description="Enable SKU checkbox state"
                )
            ]
        ),
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/*",
            description="Product saved confirmation page - Artisan Leather Journal",
            record_index=0,
            expected_elements=[]
        )
    ]
) 
