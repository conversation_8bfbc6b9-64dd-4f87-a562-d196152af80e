from automated_eval import MonitoredElement, URLState, WorkflowSpec

# Define the configuration for processing 7 Shopify products
config = WorkflowSpec(
    workflow_name="Shopify Product Addition Workflow - 7 Products",
    url_states=[
        # First product: Artisan Leather Journal
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/new",
            description="Shopify new product page - Artisan Leather Journal",
            record_index=0,
            expected_elements=[
                MonitoredElement(
                    selector="input[name='title']",
                    value="Artisan Leather Journal",
                    description="Product title field"
                ),
                MonitoredElement(
                    selector="input[name='price']",
                    value="49.99",
                    description="Product price field"
                ),
                MonitoredElement(
                    selector="input[name='sku']",
                    value="ALJ-001",
                    description="SKU field"
                ),
                MonitoredElement(
                    selector=".Polaris-Checkbox__Icon path",
                    value="Polaris-Checkbox--checked",
                    description="Enable SKU checkbox state"
                )
            ]
        ),
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/*",
            description="Product saved confirmation page - Artisan Leather Journal",
            record_index=0,
            expected_elements=[]
        ),
        # Second product: Minimalist Desk Lamp
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/new",
            description="Shopify new product page - Minimalist Desk Lamp",
            record_index=1,
            expected_elements=[
                MonitoredElement(
                    selector="input[name='title']",
                    value="Minimalist Desk Lamp",
                    description="Product title field"
                ),
                MonitoredElement(
                    selector="input[name='price']",
                    value="75.50",
                    description="Product price field"
                ),
                MonitoredElement(
                    selector="input[name='sku']",
                    value="MDL-002",
                    description="SKU field"
                ),
                MonitoredElement(
                    selector=".Polaris-Checkbox__Icon path",
                    value="Polaris-Checkbox--checked",
                    description="Enable SKU checkbox state"
                )
            ]
        ),
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/*",
            description="Product saved confirmation page - Minimalist Desk Lamp",
            record_index=1,
            expected_elements=[]
        ),
        # Third product: Organic French Press Coffee
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/new",
            description="Shopify new product page - Organic French Press Coffee",
            record_index=2,
            expected_elements=[
                MonitoredElement(
                    selector="input[name='title']",
                    value="Organic French Press Coffee",
                    description="Product title field"
                ),
                MonitoredElement(
                    selector="input[name='price']",
                    value="22.00",
                    description="Product price field"
                ),
                MonitoredElement(
                    selector="input[name='sku']",
                    value="OFPC-003",
                    description="SKU field"
                ),
                MonitoredElement(
                    selector=".Polaris-Checkbox__Icon path",
                    value="Polaris-Checkbox--checked",
                    description="Enable SKU checkbox state"
                )
            ]
        ),
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/*",
            description="Product saved confirmation page - Organic French Press Coffee",
            record_index=2,
            expected_elements=[]
        ),
        # Fourth product: Stainless Steel Water Bottle
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/new",
            description="Shopify new product page - Stainless Steel Water Bottle",
            record_index=3,
            expected_elements=[
                MonitoredElement(
                    selector="input[name='title']",
                    value="Stainless Steel Water Bottle",
                    description="Product title field"
                ),
                MonitoredElement(
                    selector="input[name='price']",
                    value="35.00",
                    description="Product price field"
                ),
                MonitoredElement(
                    selector="input[name='sku']",
                    value="SSWB-004",
                    description="SKU field"
                ),
                MonitoredElement(
                    selector=".Polaris-Checkbox__Icon path",
                    value="Polaris-Checkbox--checked",
                    description="Enable SKU checkbox state"
                )
            ]
        ),
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/*",
            description="Product saved confirmation page - Stainless Steel Water Bottle",
            record_index=3,
            expected_elements=[]
        ),
        # Fifth product: Canvas Utility Backpack
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/new",
            description="Shopify new product page - Canvas Utility Backpack",
            record_index=4,
            expected_elements=[
                MonitoredElement(
                    selector="input[name='title']",
                    value="Canvas Utility Backpack",
                    description="Product title field"
                ),
                MonitoredElement(
                    selector="input[name='price']",
                    value="89.95",
                    description="Product price field"
                ),
                MonitoredElement(
                    selector="input[name='sku']",
                    value="CUBP-005",
                    description="SKU field"
                ),
                MonitoredElement(
                    selector=".Polaris-Checkbox__Icon path",
                    value="Polaris-Checkbox--checked",
                    description="Enable SKU checkbox state"
                )
            ]
        ),
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/*",
            description="Product saved confirmation page - Canvas Utility Backpack",
            record_index=4,
            expected_elements=[]
        ),
        # Sixth product: Smart WiFi Power Strip
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/new",
            description="Shopify new product page - Smart WiFi Power Strip",
            record_index=5,
            expected_elements=[
                MonitoredElement(
                    selector="input[name='title']",
                    value="Smart WiFi Power Strip",
                    description="Product title field"
                ),
                MonitoredElement(
                    selector="input[name='price']",
                    value="29.99",
                    description="Product price field"
                ),
                MonitoredElement(
                    selector="input[name='sku']",
                    value="SWPS-006",
                    description="SKU field"
                ),
                MonitoredElement(
                    selector=".Polaris-Checkbox__Icon path",
                    value="Polaris-Checkbox--checked",
                    description="Enable SKU checkbox state"
                )
            ]
        ),
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/*",
            description="Product saved confirmation page - Smart WiFi Power Strip",
            record_index=5,
            expected_elements=[]
        ),
        # Seventh product: Portable Bluetooth Speaker
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/new",
            description="Shopify new product page - Portable Bluetooth Speaker",
            record_index=6,
            expected_elements=[
                MonitoredElement(
                    selector="input[name='title']",
                    value="Portable Bluetooth Speaker",
                    description="Product title field"
                ),
                MonitoredElement(
                    selector="input[name='price']",
                    value="59.00",
                    description="Product price field"
                ),
                MonitoredElement(
                    selector="input[name='sku']",
                    value="PBS-007",
                    description="SKU field"
                ),
                MonitoredElement(
                    selector=".Polaris-Checkbox__Icon path",
                    value="Polaris-Checkbox--checked",
                    description="Enable SKU checkbox state"
                )
            ]
        ),
        URLState(
            url="https://admin.shopify.com/store/e0veum-jf/products/*",
            description="Product saved confirmation page - Portable Bluetooth Speaker",
            record_index=6,
            expected_elements=[]
        )
    ]
)
