from automated_eval import MonitoredElement, URLState, WorkflowSpec

# Define the configuration for processing 3 PTAN requests from railroad workflow
# Based on the CSV data: <PERSON>, <PERSON>, <PERSON> Roman
config = WorkflowSpec(
    workflow_name="Palmetto GBA PTAN Status Form Automation",
    url_states=[
        # First record: <PERSON>
        URLState(
            url="https://www4.palmettogba.com/ecx_rrPtanStatusv2/redirectToEntry.do?actionPath=select",
            description="PTAN request form page - <PERSON>",
            record_index=0,
            expected_elements=[
                MonitoredElement(
                    selector="#localPTAN",
                    value="IN481080",
                    description="Local Medicare Administrative Contractor PTAN field"
                ),
                MonitoredElement(
                    selector="#providerNPI",
                    value="**********",
                    description="Provider NPI field"
                ),
                MonitoredElement(
                    selector="#taxID",
                    value="51258",
                    description="Last 5 digits of Tax ID field"
                ),
                MonitoredElement(
                    selector="#firstName",
                    value="<PERSON>",
                    description="First name field"
                ),
                MonitoredElement(
                    selector="#lastName",
                    value="<PERSON>",
                    description="Last name field"
                ),
                MonitoredElement(
                    selector="#phoneNumberBox1",
                    value="332",
                    description="Phone number area code (first 3 digits)"
                ),
                MonitoredElement(
                    selector="#phoneNumberBox2",
                    value="181",
                    description="Phone number exchange (middle 3 digits)"
                ),
                MonitoredElement(
                    selector="#phoneNumberBox3",
                    value="9600",
                    description="Phone number last 4 digits"
                ),
                MonitoredElement(
                    selector="#businessName",
                    value="Seven Hills OBGYN",
                    description="Business name field"
                ),
                MonitoredElement(
                    selector="#email",
                    value="<EMAIL>",
                    description="Email address field"
                ),
                MonitoredElement(
                    selector="#confirmaEmail",
                    value="<EMAIL>",
                    description="Confirm email address field"
                )
            ]
        ),
        # Second record: Jacqueline Barnes
        URLState(
            url="https://www4.palmettogba.com/ecx_rrPtanStatusv2/redirectToEntry.do?actionPath=select",
            description="PTAN request form page - Jacqueline Barnes",
            record_index=1,
            expected_elements=[
                MonitoredElement(
                    selector="#localPTAN",
                    value="IN481087",
                    description="Local Medicare Administrative Contractor PTAN field"
                ),
                MonitoredElement(
                    selector="#providerNPI",
                    value="**********",
                    description="Provider NPI field"
                ),
                MonitoredElement(
                    selector="#taxID",
                    value="58964",
                    description="Last 5 digits of Tax ID field"
                ),
                MonitoredElement(
                    selector="#firstName",
                    value="Jacqueline",
                    description="First name field"
                ),
                MonitoredElement(
                    selector="#lastName",
                    value="Barnes",
                    description="Last name field"
                ),
                MonitoredElement(
                    selector="#phoneNumberBox1",
                    value="940",
                    description="Phone number area code (first 3 digits)"
                ),
                MonitoredElement(
                    selector="#phoneNumberBox2",
                    value="265",
                    description="Phone number exchange (middle 3 digits)"
                ),
                MonitoredElement(
                    selector="#phoneNumberBox3",
                    value="4235",
                    description="Phone number last 4 digits"
                ),
                MonitoredElement(
                    selector="#businessName",
                    value="Sunrise Family Clinic",
                    description="Business name field"
                ),
                MonitoredElement(
                    selector="#email",
                    value="<EMAIL>",
                    description="Email address field"
                ),
                MonitoredElement(
                    selector="#confirmaEmail",
                    value="<EMAIL>",
                    description="Confirm email address field"
                )
            ]
        ),
        # Third record: Cassandra Roman
        URLState(
            url="https://www4.palmettogba.com/ecx_rrPtanStatusv2/redirectToEntry.do?actionPath=select",
            description="PTAN request form page - Cassandra Roman",
            record_index=2,
            expected_elements=[
                MonitoredElement(
                    selector="#localPTAN",
                    value="IN481054",
                    description="Local Medicare Administrative Contractor PTAN field"
                ),
                MonitoredElement(
                    selector="#providerNPI",
                    value="**********",
                    description="Provider NPI field"
                ),
                MonitoredElement(
                    selector="#taxID",
                    value="21241",
                    description="Last 5 digits of Tax ID field"
                ),
                MonitoredElement(
                    selector="#firstName",
                    value="Cassandra",
                    description="First name field"
                ),
                MonitoredElement(
                    selector="#lastName",
                    value="Roman",
                    description="Last name field"
                ),
                MonitoredElement(
                    selector="#phoneNumberBox1",
                    value="816",
                    description="Phone number area code (first 3 digits)"
                ),
                MonitoredElement(
                    selector="#phoneNumberBox2",
                    value="184",
                    description="Phone number exchange (middle 3 digits)"
                ),
                MonitoredElement(
                    selector="#phoneNumberBox3",
                    value="9593",
                    description="Phone number last 4 digits"
                ),
                MonitoredElement(
                    selector="#businessName",
                    value="Bay Area Pediatrics",
                    description="Business name field"
                ),
                MonitoredElement(
                    selector="#email",
                    value="<EMAIL>",
                    description="Email address field"
                ),
                MonitoredElement(
                    selector="#confirmaEmail",
                    value="<EMAIL>",
                    description="Confirm email address field"
                )
            ]
        )
    ]
) 
