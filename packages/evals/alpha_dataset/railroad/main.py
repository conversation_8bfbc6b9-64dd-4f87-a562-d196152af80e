import asyncio
import csv
import logging

from pydantic import BaseModel
from va import step, workflow
from va.playwright import get_browser_context


class WorkflowInput(BaseModel):
    csv_file_path: str

@workflow("Palmetto GBA PTAN Status Form Automation")
async def main(input: WorkflowInput, logger: logging.Logger):
    """Implementation of the Palmetto GBA PTAN Status form automation workflow"""
    
    with step("Read CSV data"):
        logger.info("Reading CSV file from: %s", input.csv_file_path)
        providers = []
        try:
            with open(input.csv_file_path, encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    providers.append({
                        'ptan': row['PTAN'],
                        'npi': row['NPI'],
                        'tax_id': row['Tax ID'],
                        'first_name': row['First Name'],
                        'last_name': row['Last Name'],
                        'phone_number': row['Phone Number'],
                        'business_name': row['Business Name'],
                        'email': row['Email']
                    })
            logger.info("Successfully loaded %d provider records", len(providers))
        except Exception as e:
            logger.error("Error reading CSV file: %s", str(e))
            raise
    
    async with get_browser_context(headless=False, slow_mo=500) as context:
        page = await context.new_page()
        
        with step("Navigate to PTAN Status form"):
            await page.goto("https://www4.palmettogba.com/ecx_rrPtanStatusv2/redirectToEntry.do?actionPath=select")
            logger.info("Navigated to Palmetto GBA PTAN Status form")
        
        for i, provider in enumerate(providers, 1):
            logger.info("Processing provider %d/%d: %s %s", i, len(providers), provider['first_name'], provider['last_name'])
            
            with step(f"Fill Provider Information for {provider['first_name']} {provider['last_name']}"):
                
                async with page.step("Fill PTAN field"):
                    await page.get_by_role('textbox', name='Local Medicare Administrative').fill(provider['ptan'])
                
                async with page.step("Fill NPI field"):
                    await page.get_by_role('textbox', name='Provider NPI :').fill(provider['npi'])
                
                async with page.step("Fill Tax ID field (last 5 digits)"):
                    # Extract last 5 digits from tax ID
                    tax_id_last_5 = provider['tax_id'][-5:]
                    await page.get_by_role('textbox', name='Last 5 Digits of the Provider').fill(tax_id_last_5)
                
                logger.info("Completed provider information section")
            
            with step(f"Fill Contact Information for {provider['first_name']} {provider['last_name']}"):
                
                async with page.step("Fill First Name"):
                    await page.get_by_role('textbox', name='First Name :').fill(provider['first_name'])
                
                async with page.step("Fill Last Name"):
                    await page.get_by_role('textbox', name='Last Name :').fill(provider['last_name'])
                
                async with page.step("Fill Phone Number"):
                    # Split phone number into 3 parts (area code, exchange, number)
                    phone = provider['phone_number']
                    if len(phone) == 10:
                        area_code = phone[:3]
                        exchange = phone[3:6]
                        number = phone[6:]
                        
                        await page.get_by_role('textbox', name='Phone number :').fill(area_code)
                        await page.get_by_role('textbox', name='phone Number Box2').fill(exchange)
                        await page.get_by_role('textbox', name='phone Number Box3').fill(number)
                    else:
                        logger.warning("Invalid phone number format for %s: %s", provider['first_name'], phone)
                
                async with page.step("Fill Business Name"):
                    await page.get_by_role('textbox', name='Business Name :').fill(provider['business_name'])
                
                async with page.step("Fill Email Address"):
                    await page.get_by_role('textbox', name='Email Address :', exact=True).fill(provider['email'])
                
                async with page.step("Fill Confirm Email Address"):
                    await page.get_by_role('textbox', name='Confirm Email Address :').fill(provider['email'])
                
                logger.info("Completed contact information section")
            
            with step(f"Handle Captcha for {provider['first_name']} {provider['last_name']}"):
                logger.info("Form filled completely. Captcha is displayed for manual solving.")
                logger.info("Please solve the captcha manually and submit the form when ready.")
                logger.info("Form is ready for manual captcha solving and submission.")
                
                # Form is completely filled, ready for manual captcha solving
                # User can solve captcha and submit manually
            
            # If not the last provider, navigate to fresh form for next entry
            if i < len(providers):
                with step("Navigate to fresh form for next provider"):
                    await page.goto("https://www4.palmettogba.com/ecx_rrPtanStatusv2/redirectToEntry.do?actionPath=select")
                    logger.info("Navigated to fresh form for next provider")
        
        logger.info("All providers processed successfully")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_input = WorkflowInput(csv_file_path="local_storage/fad91c78-b515-4182-9dfb-a04931b1aa69/railroad_va_testing - workflow creation.csv")
    asyncio.run(main(test_input, logger=logging.getLogger(__name__)))
