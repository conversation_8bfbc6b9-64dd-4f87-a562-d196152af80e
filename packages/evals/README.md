# Automated Evaluation System

An automated evaluation system for VA (Vibe Automation) workflows that monitors DOM elements during execution and compares against expected specifications.

## How It Works

The system patches `page.step` function calls to inject monitoring capabilities:

1. **Step Monitoring**: Before and after each workflow step, captures specified DOM elements
2. **Element Tracking**: Monitors form fields, buttons, and other elements to verify workflow state
3. **State Detection**: Uses element vanishing detection to identify state transitions on same-URL pages
4. **Comparison**: Compares actual execution against expected workflow specifications
5. **Record-Level Analysis**: Provides success rates across multiple data records

## Data Model

The system uses three main Pydantic models to represent workflows:

### MonitoredElement
```python
class MonitoredElement(BaseModel):
    selector: str           # CSS selector (e.g., "#firstName", "button[role='combobox']")
    value: str = ""         # Expected value for the element
    description: Optional[str] = None  # Human-readable description
```

### URLState
```python
class URLState(BaseModel):
    url: str                           # Page URL
    expected_elements: List[MonitoredElement]  # Elements to monitor on this page
    description: Optional[str] = None  # Description of this state
    record_index: Optional[int] = None # Which CSV record this state belongs to
```

### WorkflowSpec
```python
class WorkflowSpec(BaseModel):
    workflow_name: str                 # Name of the workflow
    url_states: List[URLState]         # List of URL states in the workflow
```

## Creating Input Files

### Manual Creation
Create a Python file with a `config` variable containing a `WorkflowSpec`:

```python
from automated_eval import WorkflowSpec, URLState, MonitoredElement

config = WorkflowSpec(
    workflow_name="Contact Form Workflow",
    url_states=[
        URLState(
            url="https://example.com/form",
            expected_elements=[
                MonitoredElement(selector="#firstName", value="John", description="First name field"),
                MonitoredElement(selector="#lastName", value="Doe", description="Last name field"),
                MonitoredElement(selector="#email", value="<EMAIL>", description="Email field")
            ],
            description="Contact form page",
            record_index=0
        ),
        URLState(
            url="https://example.com/success",
            expected_elements=[
                MonitoredElement(selector="h1", value="Thank you!", description="Success message")
            ],
            description="Success confirmation page",
            record_index=0
        )
    ]
)
```

### CSV-Based Generation
Use `input_utils.py` to generate input files from CSV data and templates:

```python
from input_utils import create_input_from_csv_and_template

# Generate input file from CSV data and template
create_input_from_csv_and_template(
    csv_file_path="data.csv",
    template_file_path="template_workflow.py", 
    output_file_path="generated_input.py"
)
```

**Template File Format** (`template_workflow.py`):
```python
from automated_eval import WorkflowSpec, URLState, MonitoredElement

config = WorkflowSpec(
    workflow_name="Template Workflow",
    url_states=[
        URLState(
            url="https://example.com/form",
            expected_elements=[
                MonitoredElement(selector="#firstName", value="", description="First name"),
                MonitoredElement(selector="#lastName", value="", description="Last name")
            ],
            record_index=None  # Will be populated from CSV row index
        )
    ]
)
```

**CSV File Format**:
```csv
firstName,lastName,email
John,Doe,<EMAIL>
Jane,Smith,<EMAIL>
Bob,Johnson,<EMAIL>
```

The system automatically maps CSV columns to template values and creates separate workflow states for each CSV record using the `record_index` field.

## Running Automated Evaluations

### Command Line Usage
```bash
python automated_eval.py <workflow_file> <config_file> <csv_file_path> [target_url] [output_file]
```

**Arguments:**
- `workflow_file`: Path to the workflow's main.py file
- `config_file`: Path to the input specification file (generated or manual)
- `csv_file_path`: Path to the CSV data file for the workflow
- `target_url`: Optional target URL (workflow default will be used if not provided)
- `output_file`: Optional output file path (default: monitoring_output.jsonl)

### Example
```bash
# Run evaluation with CSV data
python automated_eval.py workflows/contact_form/main.py generated_input.py data.csv

# Run with custom target URL and output file
python automated_eval.py workflows/booking/main.py booking_input.py bookings.csv https://booking.example.com results.jsonl
```

## Output and Analysis

### Monitoring Output
The system writes detailed monitoring data to a JSONL file containing:
- Timestamp and event type (pre_step/post_step)
- Step description
- Current URL
- Captured element values

### Comparison Results
After execution, the system provides:

**URL State Comparison Table:**
```
═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
| #   | Expected URL                          | Actual URL                            | Status   | Notes                |
|-----|---------------------------------------|---------------------------------------|----------|----------------------|
| 1   | https://example.com/form              | https://example.com/form              | ✅       | Perfect match        |
| 2   | https://example.com/success           | https://example.com/success           | ✅       | Perfect match        |
═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
```

**Record-Level Summary:**
```
📊 RECORD SUMMARY:
   Total Records: 3
   Perfect Matches: 2
   Records with Issues: 1
   Success Rate: 66.7%
   📝 2/3 records matched correctly
```

## Key Features

### Element Vanishing Detection
Automatically detects state changes on same-URL pages by monitoring when previously filled elements become empty or disappear.

### Permissive Comparison
Expected elements must be present in actual results, but actual results can contain additional elements.

## Troubleshooting

### Common Issues
- **Selector Mismatch**: Ensure selectors match actual DOM structure (use browser dev tools)
