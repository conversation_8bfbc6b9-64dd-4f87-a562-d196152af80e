from automated_eval import MonitoredElement, URLState, WorkflowSpec

# Define the configuration for healthcare provider registration form
config = WorkflowSpec(
    workflow_name="Healthcare Provider Registration Form Automation",
    url_states=[
        # Record 0: Provider from CSV (<PERSON>)
        URLState(
            url="https://provider-form-flow.lovable.app/",
            description="Main form page - Basic Information step",
            record_index=0,
            expected_elements=[
                MonitoredElement(
                    selector="#firstName",
                    value="John",
                    description="First name field should be filled"
                ),
                MonitoredElement(
                    selector="#lastName", 
                    value="Smith",
                    description="Last name field should be filled"
                ),
                MonitoredElement(
                    selector="#practiceName",
                    value="WellCare Clinic",
                    description="Practice name field should be filled"
                ),
                MonitoredElement(
                    selector="button[role='combobox']",
                    value="Nurse Practitioner",
                    description="Specialty dropdown should be selected"
                )
            ]
        ),
        URLState( 
            url="https://provider-form-flow.lovable.app/",
            description="Main form page - Licensing Information step",
            record_index=0,
            expected_elements=[
                MonitoredElement(
                    selector="button[role='combobox']",
                    value="Oregon",
                    description="State licensed dropdown should be selected"
                ),
                MonitoredElement(
                    selector="#licenseNumber",
                    value="9382555",
                    description="License number field should be filled"
                )
            ]
        ),
        URLState(
            url="https://provider-form-flow.lovable.app/",
            description="Main form page - Address Information step",
            record_index=0,
            expected_elements=[
                MonitoredElement(
                    selector="#mailingAddressLine1",
                    value="6578 Maple Dr",
                    description="Address line 1 field should be filled"
                ),
                MonitoredElement(
                    selector="#mailingAddressLine2",
                    value="",
                    description="Address line 2 field should be empty"
                ),
                MonitoredElement(
                    selector="button[role='combobox']",
                    value="Indiana",
                    description="Mailing state dropdown should be selected"
                ),
                MonitoredElement(
                    selector="#mailingZipCode",
                    value="28465",
                    description="Zip code field should be filled"
                ),
                MonitoredElement(
                    selector="#sameAsMailing",
                    value="on",
                    description="Same as mailing address checkbox should be checked"
                )
            ]
        ),
        URLState(
            url="https://provider-form-flow.lovable.app/",
            description="Main form page - Attestation step",
            record_index=0,
            expected_elements=[
                MonitoredElement(
                    selector="#acceptTerms-agree",
                    value="agree",
                    description="Terms and conditions should be accepted"
                ),
                MonitoredElement(
                    selector="#confirmAccuracy-agree",
                    value="agree", 
                    description="Information accuracy should be confirmed"
                )
            ]
        ),
        URLState(
            url="https://provider-form-flow.lovable.app/",
            description="Success page after form submission",
            record_index=0,
            expected_elements=[
                MonitoredElement(
                    selector="h2",
                    value="Submission Successful!",
                    description="Success heading should be displayed"
                )
            ]
        )
    ]
) 
