import asyncio
import csv
import logging

from pydantic import BaseModel
from step import step
from va import workflow
from va.playwright import get_browser_context


class WorkflowInput(BaseModel):
    csv_file_path: str

@workflow("Healthcare Provider Registration Form Automation")
async def main(input: WorkflowInput, logger: logging.Logger):
    """Automate filling healthcare provider registration forms with CSV data"""
    
    # Read CSV data
    providers = []
    with open(input.csv_file_path, newline='', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            providers.append(row)
    
    logger.info(f"Found {len(providers)} providers in CSV file")
    
    async with get_browser_context(headless=False, slow_mo=1000) as context:
        for i, provider in enumerate(providers):
            logger.info(f"Processing provider {i+1}/{len(providers)}: {provider['First name']} {provider['Last name']}")
            
            page = await context.new_page()
            
            async with step(f"Navigate to form for {provider['First name']} {provider['Last name']}"):
                await page.goto("https://provider-form-flow.lovable.app/")
                await page.wait_for_timeout(1000)  # Wait to see the change
                logger.info("Navigated to provider registration form")
            
            async with step("Fill Basic Information"):
                # First Name
                first_name_field = page.get_by_role('textbox', name='First Name') | page.get_by_prompt("First name field")
                await first_name_field.fill(provider['First name'])
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                # Last Name
                last_name_field = page.get_by_role('textbox', name='Last Name') | page.get_by_prompt("Last name field")
                await last_name_field.fill(provider['Last name'])
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                # Practice Name
                practice_field = page.get_by_role('textbox', name='Practice Name') | page.get_by_prompt("Practice name field")
                await practice_field.fill(provider['Practice name'])
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                # Specialty dropdown
                specialty_dropdown = page.get_by_role('combobox') | page.get_by_prompt("Specialty dropdown")
                await specialty_dropdown.click()
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                # Select specialty option
                specialty_option = page.get_by_role('option', name=provider['Specialty']) | page.get_by_prompt(f"Select {provider['Specialty']} specialty option")
                await specialty_option.click()
                await page.wait_for_timeout(1000)  # Wait to see the change

            async with step("Click Next"):
                # Click Next
                next_button = page.get_by_role('button', name='Next') | page.get_by_prompt("Next button")
                await next_button.click()
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                logger.info("Filled basic information section")
            
            async with step("Fill Licensing Information"):
                # State Licensed dropdown
                state_dropdown = page.get_by_role('combobox') | page.get_by_prompt("State licensed dropdown")
                await state_dropdown.click()
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                # Convert state abbreviation to full name for selection
                state_mapping = {
                    'AL': 'Alabama', 'AK': 'Alaska', 'AZ': 'Arizona', 'AR': 'Arkansas',
                    'CA': 'California', 'CO': 'Colorado', 'CT': 'Connecticut', 'DE': 'Delaware',
                    'FL': 'Florida', 'GA': 'Georgia', 'HI': 'Hawaii', 'ID': 'Idaho',
                    'IL': 'Illinois', 'IN': 'Indiana', 'IA': 'Iowa', 'KS': 'Kansas',
                    'KY': 'Kentucky', 'LA': 'Louisiana', 'ME': 'Maine', 'MD': 'Maryland',
                    'MA': 'Massachusetts', 'MI': 'Michigan', 'MN': 'Minnesota', 'MS': 'Mississippi',
                    'MO': 'Missouri', 'MT': 'Montana', 'NE': 'Nebraska', 'NV': 'Nevada',
                    'NH': 'New Hampshire', 'NJ': 'New Jersey', 'NM': 'New Mexico', 'NY': 'New York',
                    'NC': 'North Carolina', 'ND': 'North Dakota', 'OH': 'Ohio', 'OK': 'Oklahoma',
                    'OR': 'Oregon', 'PA': 'Pennsylvania', 'RI': 'Rhode Island', 'SC': 'South Carolina',
                    'SD': 'South Dakota', 'TN': 'Tennessee', 'TX': 'Texas', 'UT': 'Utah',
                    'VT': 'Vermont', 'VA': 'Virginia', 'WA': 'Washington', 'WV': 'West Virginia',
                    'WI': 'Wisconsin', 'WY': 'Wyoming'
                }
                
                state_full_name = state_mapping.get(provider['State licensed'], provider['State licensed'])
                state_option = page.get_by_role('option', name=state_full_name) | page.get_by_prompt(f"Select {state_full_name} state option")
                await state_option.click()
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                # License Number
                license_field = page.get_by_role('textbox', name='License Number (7 digits, without dashes)') | page.get_by_prompt("License number field")
                await license_field.fill(provider['License number'])
                await page.wait_for_timeout(1000)  # Wait to see the change
                
            # Click Next
            async with step("Click Next"):
                next_button = page.get_by_role('button', name='Next') | page.get_by_prompt("Next button")
                await next_button.click()
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                logger.info("Filled licensing information section")
            
            async with step("Fill Address Information"):
                # Mailing Address Line 1
                address1_field = page.get_by_role('textbox', name='Address Line 1') | page.get_by_prompt("Address line 1 field")
                await address1_field.fill(provider['Mailing address 1'])
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                # Mailing Address Line 2 (if not empty)
                if provider['Mailing address 2'].strip():
                    address2_field = page.get_by_role('textbox', name='Address Line 2') | page.get_by_prompt("Address line 2 field")
                    await address2_field.fill(provider['Mailing address 2'])
                    await page.wait_for_timeout(1000)  # Wait to see the change
                
                # Mailing State dropdown
                state_dropdown = page.get_by_role('combobox') | page.get_by_prompt("Mailing address state dropdown")
                await state_dropdown.click()
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                mailing_state_full_name = state_mapping.get(provider['Mailing address State'], provider['Mailing address State'])
                mailing_state_option = page.get_by_role('option', name=mailing_state_full_name) | page.get_by_prompt(f"Select {mailing_state_full_name} mailing state option")
                await mailing_state_option.click()
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                # Mailing Zip Code
                zip_field = page.get_by_role('textbox', name='Zip Code') | page.get_by_prompt("Zip code field")
                await zip_field.fill(provider['Mailing address ZIP'])
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                # Check if billing address is same as mailing address
                mailing_same_as_billing = (
                    provider['Mailing address 1'] == provider['Billing address 1'] and
                    provider['Mailing address 2'] == provider['Billing address 2'] and
                    provider['Mailing address State'] == provider['Billing address State'] and
                    provider['Mailing address ZIP'] == provider['Billing address ZIP']
                )
                
                if mailing_same_as_billing:
                    # The checkbox should already be checked by default, but let's ensure it
                    same_address_checkbox = page.get_by_role('checkbox', name='Same as mailing address') | page.get_by_prompt("Same as mailing address checkbox")
                    if not await same_address_checkbox.is_checked():
                        await same_address_checkbox.click()
                        await page.wait_for_timeout(1000)  # Wait to see the change
                else:
                    # If billing address is different, uncheck the checkbox and fill billing fields
                    same_address_checkbox = page.get_by_role('checkbox', name='Same as mailing address') | page.get_by_prompt("Same as mailing address checkbox")
                    if await same_address_checkbox.is_checked():
                        await same_address_checkbox.click()
                        await page.wait_for_timeout(1000)  # Wait to see the change
                    
                    # Fill billing address fields (implementation would go here if needed)
                    # For this form, we'll assume addresses are the same as per the CSV data
                
            # Click Next
            async with step("Click Next"):
                next_button = page.get_by_role('button', name='Next') | page.get_by_prompt("Next button")
                await next_button.click()
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                logger.info("Filled address information section")
            
            async with step("Complete Attestation"):
                # Accept Terms and Conditions
                terms_agree = page.locator('#acceptTerms-agree') | page.get_by_prompt("Terms and conditions agree radio button")
                await terms_agree.click()
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                # Confirm accuracy
                accuracy_agree = page.locator('#confirmAccuracy-agree') | page.get_by_prompt("Information accuracy agree radio button")
                await accuracy_agree.click()
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                # Submit Form
            async with step("Submit Form"):
                submit_button = page.get_by_role('button', name='Submit Form') | page.get_by_prompt("Submit form button")
                await submit_button.click()
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                logger.info("Completed attestation and submitted form")
            
            async with step("Verify Submission"):
                # Wait for success message
                success_heading = page.get_by_role('heading', name='Submission Successful!') | page.get_by_prompt("Submission successful heading")
                await success_heading.wait_for()
                await page.wait_for_timeout(1000)  # Wait to see the change
                
                logger.info(f"Successfully submitted form for {provider['First name']} {provider['Last name']}")
            
            # Close the page before processing next provider
            await page.close()
            
            # If not the last provider, wait a bit before processing the next one
            if i < len(providers) - 1:
                await asyncio.sleep(2)
    
    logger.info(f"Successfully processed all {len(providers)} providers")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main(WorkflowInput(csv_file_path="/Users/<USER>/projects/cursor/vibe-automation-server/packages/evals/multi_page_form/provider-data-short.csv"), logger=logging.getLogger(__name__)))
