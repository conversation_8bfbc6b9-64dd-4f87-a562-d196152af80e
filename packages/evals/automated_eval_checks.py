#!/usr/bin/env python3
"""
Unit tests for automated_eval.py
"""
import json
import os

# Import the module under test
import sys
from unittest.mock import AsyncMock, Mock, mock_open, patch

import pytest

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from automated_eval import (
    MonitoredElement,
    URLState,
    WorkflowSpec,
    build_url_to_elements_map,
    capture_elements_async,
    compare_workflow_specs,
    compare_workflow_specs_with_details,
    get_elements_for_url,
    load_workflow_spec,
    log_monitoring_data,
    monitor_and_log_page_state,
    parse_monitoring_logs_to_spec,
)


class TestMonitoredElement:
    """Test the MonitoredElement model"""
    
    def test_monitored_element_creation(self):
        element = MonitoredElement(
            selector="#test",
            value="test_value",
            description="Test element"
        )
        assert element.selector == "#test"
        assert element.value == "test_value"
        assert element.description == "Test element"
    
    def test_monitored_element_defaults(self):
        element = MonitoredElement(
            selector="#test"
        )
        assert element.value == ""
        assert element.description is None
    
    def test_monitored_element_values_equal_same(self):
        element1 = MonitoredElement(
            selector="#test",
            value="test_value",
            description="Description 1"
        )
        element2 = MonitoredElement(
            selector="#test",
            value="test_value",
            description="Description 2"  # Different description should be ignored
        )
        assert element1.values_equal(element2)
    
    def test_monitored_element_values_equal_different_value(self):
        element1 = MonitoredElement(selector="#test", value="value1")
        element2 = MonitoredElement(selector="#test", value="value2")
        assert not element1.values_equal(element2)
    
    def test_monitored_element_values_equal_different_selector(self):
        element1 = MonitoredElement(selector="#test1", value="value")
        element2 = MonitoredElement(selector="#test2", value="value")
        assert not element1.values_equal(element2)
    
    def test_monitored_element_values_equal_whitespace_stripped(self):
        element1 = MonitoredElement(selector="#test", value="  value  ")
        element2 = MonitoredElement(selector="#test", value="value")
        assert element1.values_equal(element2)


class TestURLState:
    """Test the URLState model"""
    
    def test_url_state_creation(self):
        element = MonitoredElement(selector="#test", value="test_value")
        state = URLState(
            url="https://example.com",
            expected_elements=[element],
            description="Test page"
        )
        assert state.url == "https://example.com"
        assert len(state.expected_elements) == 1
        assert state.description == "Test page"
    
    def test_url_state_values_equal_same(self):
        element1 = MonitoredElement(selector="#test", value="value")
        element2 = MonitoredElement(selector="#test", value="value")
        
        state1 = URLState(
            url="https://example.com",
            expected_elements=[element1],
            description="Description 1"
        )
        state2 = URLState(
            url="https://example.com", 
            expected_elements=[element2],
            description="Description 2"  # Different description should be ignored
        )
        assert state1.values_equal(state2)
    
    def test_url_state_values_equal_different_url(self):
        element = MonitoredElement(selector="#test", value="value")
        state1 = URLState(url="https://example1.com", expected_elements=[element])
        state2 = URLState(url="https://example2.com", expected_elements=[element])
        assert not state1.values_equal(state2)
    
    def test_url_state_values_equal_different_elements(self):
        element1 = MonitoredElement(selector="#test1", value="value")
        element2 = MonitoredElement(selector="#test2", value="value")
        
        state1 = URLState(url="https://example.com", expected_elements=[element1])
        state2 = URLState(url="https://example.com", expected_elements=[element2])
        assert not state1.values_equal(state2)


class TestWorkflowSpec:
    """Test the WorkflowSpec model"""
    
    def test_workflow_spec_creation(self):
        element = MonitoredElement(selector="#test", value="value")
        state = URLState(
            url="https://example.com",
            expected_elements=[element]
        )
        spec = WorkflowSpec(
            workflow_name="Test workflow",
            url_states=[state]  # Changed to list format
        )
        assert spec.workflow_name == "Test workflow"
        assert len(spec.url_states) == 1
        assert spec.url_states[0].url == "https://example.com"  # Access by index
    
    def test_workflow_spec_values_equal_same(self):
        element1 = MonitoredElement(selector="#test", value="value")
        element2 = MonitoredElement(selector="#test", value="value")
        
        state1 = URLState(url="https://example.com", expected_elements=[element1])
        state2 = URLState(url="https://example.com", expected_elements=[element2])
        
        spec1 = WorkflowSpec(
            workflow_name="Name 1",
            url_states=[state1]  # Changed to list format
        )
        spec2 = WorkflowSpec(
            workflow_name="Name 2",  # Different name should be ignored
            url_states=[state2]  # Changed to list format
        )
        assert spec1.values_equal(spec2)
    
    def test_workflow_spec_values_equal_different_urls(self):
        element = MonitoredElement(selector="#test", value="value")
        state1 = URLState(url="https://example1.com", expected_elements=[element])
        state2 = URLState(url="https://example2.com", expected_elements=[element])
        
        spec1 = WorkflowSpec(workflow_name="Test", url_states=[state1])  # Changed to list format
        spec2 = WorkflowSpec(workflow_name="Test", url_states=[state2])  # Changed to list format
        assert not spec1.values_equal(spec2)


class TestLoadWorkflowSpec:
    """Test load_workflow_spec function"""
    
    def _find_state_by_url(self, spec, url):
        """Helper function to find a URLState by URL in the list"""
        for state in spec.url_states:
            if state.url == url:
                return state
        return None
    
    def test_load_workflow_spec_success(self):
        # Mock a Python module with a config variable
        mock_config = WorkflowSpec(
            workflow_name="Test Workflow",
            url_states=[  # Changed to list format
                URLState(
                    url="https://example.com",
                    expected_elements=[
                        MonitoredElement(
                            selector="#name",
                            value="John",
                            description="Name field"
                        )
                    ]
                )
            ]
        )
        
        with patch("importlib.util.spec_from_file_location") as mock_spec_from_file:
            with patch("importlib.util.module_from_spec") as mock_module_from_spec:
                # Mock the module loading process
                mock_spec = Mock()
                mock_spec_from_file.return_value = mock_spec
                
                mock_module = Mock()
                mock_module.config = mock_config
                mock_module_from_spec.return_value = mock_module
                
                mock_spec.loader = Mock()
                
                result = load_workflow_spec("test_config.py")
                
        assert result.workflow_name == "Test Workflow"
        assert len(result.url_states) == 1
        
        # Find the state by URL
        state = self._find_state_by_url(result, "https://example.com")
        assert state is not None
        assert len(state.expected_elements) == 1
        assert state.expected_elements[0].selector == "#name"
    
    def test_load_workflow_spec_file_not_found(self):
        with patch("importlib.util.spec_from_file_location", side_effect=FileNotFoundError):
            with pytest.raises(FileNotFoundError):
                load_workflow_spec("nonexistent.py")


class TestCaptureElementsAsync:
    """Test capture_elements_async function"""
    
    @pytest.mark.asyncio
    async def test_capture_elements_async_success(self):
        # Mock page and elements
        mock_page = Mock()
        mock_locator = Mock()
        mock_element = Mock()
        
        mock_page.locator.return_value = mock_locator
        mock_locator.first = mock_element
        mock_element.input_value = AsyncMock(return_value="test_value")
        
        # Create test locators
        locators = [
            MonitoredElement(
                selector="#test",
                value="expected"
            )
        ]
        
        result = await capture_elements_async(mock_page, locators)
        
        assert len(result) == 1
        assert result[0].selector == "#test"
        assert result[0].value == "test_value"
    
    @pytest.mark.asyncio
    async def test_capture_elements_async_fallback_to_text_content(self):
        mock_page = Mock()
        mock_locator = Mock()
        mock_element = Mock()
        
        mock_page.locator.return_value = mock_locator
        mock_locator.first = mock_element
        mock_element.input_value = AsyncMock(side_effect=Exception("No input_value"))
        mock_element.text_content = AsyncMock(return_value="text_value")
        
        locators = [
            MonitoredElement(
                selector="#test"
            )
        ]
        
        result = await capture_elements_async(mock_page, locators)
        
        assert len(result) == 1
        assert result[0].value == "text_value"
    
    @pytest.mark.asyncio
    async def test_capture_elements_async_element_not_found(self):
        mock_page = Mock()
        mock_locator = Mock()
        
        mock_page.locator.return_value = mock_locator
        mock_locator.first = Mock(side_effect=Exception("Element not found"))
        
        locators = [
            MonitoredElement(
                selector="#missing"
            )
        ]
        
        result = await capture_elements_async(mock_page, locators)
        
        assert len(result) == 1
        assert result[0].value == ""
        assert result[0].selector == "#missing"


class TestLogMonitoringData:
    """Test log_monitoring_data function"""
    
    def test_log_monitoring_data_success(self):
        test_element = MonitoredElement(selector="#test", value="test_value")
        
        with patch("builtins.open", mock_open()) as mock_file:
            with patch("os.makedirs"):
                with patch("os.path.dirname", return_value="test_dir"):
                    log_monitoring_data(
                        "test_output.jsonl",
                        "test_event",
                        "test_description",
                        "test_url",
                        [test_element.model_dump()]
                    )
        
        # Verify the file was opened correctly
        mock_file.assert_called_once_with("test_dir/test_output.jsonl", 'a')
        
        # Verify data was written
        written_data = "".join(call.args[0] for call in mock_file().write.call_args_list)
        parsed_data = json.loads(written_data.rstrip('\n'))
        assert parsed_data['event'] == "test_event"
        assert parsed_data['step_description'] == "test_description"
        assert parsed_data['url'] == "test_url"
        assert parsed_data['elements'] == [test_element.model_dump()]
        assert 'timestamp' in parsed_data


class TestBuildUrlToElementsMap:
    """Test build_url_to_elements_map function"""
    
    def test_build_url_to_elements_map_single_url(self):
        locator1 = MonitoredElement(selector="#field1")
        locator2 = MonitoredElement(selector="#field2")
        
        state = URLState(
            url="https://example.com",
            expected_elements=[locator1, locator2]
        )
        
        spec = WorkflowSpec(workflow_name="Test", url_states=[state])  # Changed to list format
        
        result = build_url_to_elements_map(spec)
        
        assert "https://example.com" in result
        assert len(result["https://example.com"]) == 2
        assert result["https://example.com"][0].selector == "#field1"
        assert result["https://example.com"][1].selector == "#field2"
    
    def test_build_url_to_elements_map_multiple_urls(self):
        locator1 = MonitoredElement(selector="#field1")
        locator2 = MonitoredElement(selector="#field2")
        
        state1 = URLState(
            url="https://example.com/page1",
            expected_elements=[locator1]
        )
        
        state2 = URLState(
            url="https://example.com/page2",
            expected_elements=[locator2]
        )
        
        spec = WorkflowSpec(workflow_name="Test", url_states=[state1, state2])  # Changed to list format
        
        result = build_url_to_elements_map(spec)
        
        assert len(result) == 2
        assert "https://example.com/page1" in result
        assert "https://example.com/page2" in result
        assert len(result["https://example.com/page1"]) == 1
        assert len(result["https://example.com/page2"]) == 1
    
    def test_build_url_to_elements_map_merged_urls(self):
        locator1 = MonitoredElement(selector="#field1")
        locator2 = MonitoredElement(selector="#field2")
        
        # Create a single URLState with both elements to simulate merging
        state = URLState(
            url="https://example.com",
            expected_elements=[locator1, locator2]
        )
        
        spec = WorkflowSpec(workflow_name="Test", url_states=[state])  # Changed to list format
        
        result = build_url_to_elements_map(spec)
        
        assert len(result) == 1
        assert "https://example.com" in result
        assert len(result["https://example.com"]) == 2  # Both elements present


class TestGetElementsForUrl:
    """Test get_elements_for_url function"""
    
    def test_get_elements_for_url_found(self):
        locator = MonitoredElement(selector="#test")
        url_map = {"https://example.com": [locator]}
        
        result = get_elements_for_url(url_map, "https://example.com")
        
        assert len(result) == 1
        assert result[0].selector == "#test"
    
    def test_get_elements_for_url_not_found(self):
        url_map = {"https://example.com": []}
        
        result = get_elements_for_url(url_map, "https://other.com")
        
        assert len(result) == 0


class TestMonitorAndLogPageStateAsync:
    """Test monitor_and_log_page_state function"""
    
    @pytest.mark.asyncio
    async def test_monitor_and_log_page_state_no_page(self, capsys):
        await monitor_and_log_page_state(
            "pre_step", 
            "Test step", 
            None,  # No page
            {},
            "output.jsonl"
        )
        
        captured = capsys.readouterr()
        assert "No page object - skipping pre_step monitoring" in captured.out
    
    @pytest.mark.asyncio
    async def test_monitor_and_log_page_state_success(self):
        # Mock page object
        mock_page = Mock()
        mock_page.is_closed.return_value = False
        mock_page.url = "https://example.com"
        
        # Mock elements
        mock_element = MonitoredElement(selector="#test")
        url_map = {"https://example.com": [mock_element]}
        
        with patch("automated_eval.capture_elements_async") as mock_capture:
            with patch("automated_eval.log_monitoring_data") as mock_log:
                mock_capture.return_value = [mock_element]
                
                await monitor_and_log_page_state(
                    "pre_step",
                    "Test step", 
                    mock_page,
                    url_map,
                    "output.jsonl"
                )
                
                mock_capture.assert_called_once()
                mock_log.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_monitor_and_log_page_state_unknown_url(self, capsys):
        mock_page = Mock()
        mock_page.is_closed.return_value = False
        mock_page.url = "https://unknown.com"
        
        await monitor_and_log_page_state(
            "pre_step",
            "Test step",
            mock_page,
            {},  # Empty url map
            "output.jsonl"
        )
        
        captured = capsys.readouterr()
        assert "Found 0 elements to monitor for URL" in captured.out  # Updated message
    
    @pytest.mark.asyncio
    async def test_monitor_and_log_page_state_page_closed(self, capsys):
        mock_page = Mock()
        mock_page.is_closed.return_value = True
        
        await monitor_and_log_page_state(
            "pre_step",
            "Test step",
            mock_page,
            {},
            "output.jsonl"
        )
        
        captured = capsys.readouterr()
        assert "Page is closed" in captured.out


class TestParseMonitoringLogsToSpec:
    """Test parse_monitoring_logs_to_spec function"""
    
    def _find_state_by_url(self, spec, url):
        """Helper function to find a URLState by URL in the list"""
        for state in spec.url_states:
            if state.url == url:
                return state
        return None
    
    def test_parse_monitoring_logs_success(self):
        # Create test JSONL data
        test_data = [
            {
                "timestamp": "2024-01-01T12:00:00",
                "event": "pre_step",
                "step_description": "Fill form",
                "url": "https://example.com",
                "elements": [
                    {"selector": "#name", "value": "", "description": "Name field"}
                ]
            },
            {
                "timestamp": "2024-01-01T12:00:01",
                "event": "post_step",
                "step_description": "Fill form",
                "url": "https://example.com",
                "elements": [
                    {"selector": "#name", "value": "John", "description": "Name field"},
                    {"selector": "#email", "value": "<EMAIL>", "description": "Email field"}
                ]
            }
        ]
        
        # Mock file reading
        with patch("builtins.open", mock_open(read_data="\n".join(json.dumps(entry) for entry in test_data) + "\n")):
            result = parse_monitoring_logs_to_spec("test.jsonl", "Test Workflow")
        
        assert result.workflow_name == "Test Workflow"
        assert len(result.url_states) == 1
        
        # Find the state by URL
        state = self._find_state_by_url(result, "https://example.com")
        assert state is not None
        assert len(state.expected_elements) == 2
        
        # Check elements were captured with final values
        elements_by_selector = {elem.selector: elem for elem in state.expected_elements}
        assert elements_by_selector["#name"].value == "John"
        assert elements_by_selector["#email"].value == "<EMAIL>"
    
    def test_parse_monitoring_logs_ignores_pre_step(self):
        test_data = [
            {
                "event": "pre_step",
                "url": "https://example.com",
                "elements": [{"selector": "#name", "value": "ignored"}]
            }
        ]
        
        with patch("builtins.open", mock_open(read_data=json.dumps(test_data[0]))):
            result = parse_monitoring_logs_to_spec("test.jsonl")
        
        # Should have empty url_states since pre_step events are ignored
        assert len(result.url_states) == 0
    
    def test_parse_monitoring_logs_filters_empty_values(self):
        test_data = [
            {
                "event": "post_step",
                "url": "https://example.com",
                "elements": [
                    {"selector": "#filled", "value": "value"},
                    {"selector": "#empty", "value": ""},
                    {"selector": "#whitespace", "value": "   "}
                ]
            }
        ]
        
        with patch("builtins.open", mock_open(read_data=json.dumps(test_data[0]))):
            result = parse_monitoring_logs_to_spec("test.jsonl")
        
        state = self._find_state_by_url(result, "https://example.com")
        assert state is not None
        assert len(state.expected_elements) == 1
        assert state.expected_elements[0].selector == "#filled"
        assert state.expected_elements[0].value == "value"
    
    def test_parse_monitoring_logs_updates_existing_elements(self):
        test_data = [
            {
                "event": "post_step",
                "url": "https://example.com",
                "elements": [{"selector": "#name", "value": "first_value"}]
            },
            {
                "event": "post_step", 
                "url": "https://example.com",
                "elements": [{"selector": "#name", "value": "final_value"}]
            }
        ]
        
        with patch("builtins.open", mock_open(read_data="\n".join(json.dumps(entry) for entry in test_data))):
            result = parse_monitoring_logs_to_spec("test.jsonl")
        
        state = self._find_state_by_url(result, "https://example.com")
        assert state is not None
        assert len(state.expected_elements) == 1
        assert state.expected_elements[0].value == "final_value"


class TestCompareWorkflowSpecs:
    """Test compare_workflow_specs function"""
    
    def test_compare_workflow_specs_same(self):
        element1 = MonitoredElement(selector="#test", value="test_value")
        element2 = MonitoredElement(selector="#test", value="test_value")
        
        state1 = URLState(url="https://example.com", expected_elements=[element1])
        state2 = URLState(url="https://example.com", expected_elements=[element2])
        
        spec1 = WorkflowSpec(workflow_name="Test", url_states=[state1])  # Changed to list format
        spec2 = WorkflowSpec(workflow_name="Test2", url_states=[state2])  # Changed to list format
        
        assert compare_workflow_specs(spec1, spec2)
    
    def test_compare_workflow_specs_different(self):
        element1 = MonitoredElement(selector="#test", value="test_value")
        element2 = MonitoredElement(selector="#test", value="different_value")
        
        state1 = URLState(url="https://example.com", expected_elements=[element1])
        state2 = URLState(url="https://example.com", expected_elements=[element2])
        
        spec1 = WorkflowSpec(workflow_name="Test", url_states=[state1])  # Changed to list format
        spec2 = WorkflowSpec(workflow_name="Test", url_states=[state2])  # Changed to list format
        
        assert not compare_workflow_specs(spec1, spec2)
    
    def test_compare_workflow_specs_with_details_same(self):
        element1 = MonitoredElement(selector="#test", value="test_value")
        element2 = MonitoredElement(selector="#test", value="test_value")
        
        state1 = URLState(url="https://example.com", expected_elements=[element1])
        state2 = URLState(url="https://example.com", expected_elements=[element2])
        
        spec1 = WorkflowSpec(workflow_name="Test", url_states=[state1])
        spec2 = WorkflowSpec(workflow_name="Test", url_states=[state2])
        
        result = compare_workflow_specs_with_details(spec1, spec2)
        
        assert result["perfect_match"]
        
        # Check that the table output shows perfect match
        # (This would be captured in the console output)
    
    def test_compare_workflow_specs_with_details_url_mismatch(self):
        element1 = MonitoredElement(selector="#test", value="test_value")
        element2 = MonitoredElement(selector="#test", value="test_value")
        
        state1 = URLState(url="https://example.com", expected_elements=[element1])
        state2 = URLState(url="https://different.com", expected_elements=[element2])
        
        spec1 = WorkflowSpec(workflow_name="Test", url_states=[state1])
        spec2 = WorkflowSpec(workflow_name="Test", url_states=[state2])
        
        result = compare_workflow_specs_with_details(spec1, spec2)
        
        assert not result["perfect_match"]
        
        # Check that the table output shows URL mismatch
        # (This would be captured in the console output)
    
    def test_compare_workflow_specs_with_details_element_mismatch(self):
        element1 = MonitoredElement(selector="#test", value="test_value")
        element2 = MonitoredElement(selector="#test", value="different_value")
        
        state1 = URLState(url="https://example.com", expected_elements=[element1])
        state2 = URLState(url="https://example.com", expected_elements=[element2])
        
        spec1 = WorkflowSpec(workflow_name="Test", url_states=[state1])
        spec2 = WorkflowSpec(workflow_name="Test", url_states=[state2])
        
        result = compare_workflow_specs_with_details(spec1, spec2)
        
        assert not result["perfect_match"]
        
        # Check that the table output shows elements differ
        # (This would be captured in the console output)
    
    def test_compare_workflow_specs_with_details_value_mismatch(self):
        element1 = MonitoredElement(selector="#test", value="test_value")
        element2 = MonitoredElement(selector="#test", value="different_value")
        
        state1 = URLState(url="https://example.com", expected_elements=[element1])
        state2 = URLState(url="https://example.com", expected_elements=[element2])
        
        spec1 = WorkflowSpec(workflow_name="Test", url_states=[state1])
        spec2 = WorkflowSpec(workflow_name="Test", url_states=[state2])
        
        result = compare_workflow_specs_with_details(spec1, spec2)
        
        assert not result["perfect_match"]
        
        # Check that the table output shows value mismatch
        # (This would be captured in the console output)
    
    def test_compare_workflow_specs_with_details_multiple_mismatches(self):
        element1 = MonitoredElement(selector="#test", value="test_value")
        element2 = MonitoredElement(selector="#different", value="different_value")
        
        state1 = URLState(url="https://example.com", expected_elements=[element1])
        state2 = URLState(url="https://different.com", expected_elements=[element2])
        
        spec1 = WorkflowSpec(workflow_name="Test", url_states=[state1])
        spec2 = WorkflowSpec(workflow_name="Test", url_states=[state2])
        
        result = compare_workflow_specs_with_details(spec1, spec2)
        
        assert not result["perfect_match"]
        
        # Check that the table output shows multiple mismatches
        # (This would be captured in the console output)


if __name__ == "__main__":
    pytest.main([__file__]) 
