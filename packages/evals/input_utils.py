#!/usr/bin/env python3
"""
Utility functions for creating input files from CSV data and templates.

This module provides functions to generate long input files by combining
CSV data with template configurations, making it easier to handle large
datasets without manually creating extensive input files.
"""

import csv
import importlib.util
from pathlib import Path

from automated_eval import MonitoredElement, URLState, WorkflowSpec


def create_input_from_csv_and_template(
    template_file_path: str,
    csv_file_path: str,
    output_file_path: str,
    workflow_name: str | None = None
) -> None:
    """
    Create a complete input file by combining a template with CSV data.
    
    Args:
        template_file_path: Path to the template input file (Python file with 'config' variable)
        csv_file_path: Path to the CSV file containing the data
        output_file_path: Path where the generated input file should be saved
        workflow_name: Optional custom workflow name (defaults to template name + CSV info)
    
    The template file should contain a WorkflowSpec with URLState entries that have
    empty values in their MonitoredElement objects. This function will:
    1. Read the template configuration
    2. Read the CSV data
    3. For each CSV row, create copies of the template URLStates
    4. Fill in the MonitoredElement values using CSV data
    5. Set the record_index field to track which CSV row each URLState corresponds to
    6. Write the complete configuration to the output file
    """
    # Load template configuration
    spec = importlib.util.spec_from_file_location("template_config", template_file_path)
    template_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(template_module)
    template_config: WorkflowSpec = template_module.config
    
    # Read CSV data
    csv_data = []
    with open(csv_file_path, newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        csv_data = list(reader)
    
    # Create column name mapping (handle variations in CSV headers)
    column_mapping = _create_column_mapping(csv_data[0].keys() if csv_data else [])
    
    # Generate URLStates for all CSV rows
    all_url_states = []
    
    for row_index, row_data in enumerate(csv_data):
        # Create URLStates for this CSV row
        for template_state in template_config.url_states:
            # Create a copy of the template state
            new_state = URLState(
                url=template_state.url,
                description=f"{template_state.description} - Record {row_index + 1}",
                record_index=row_index,
                expected_elements=[]
            )
            
            # Fill in the element values using CSV data
            for template_element in template_state.expected_elements:
                filled_element = MonitoredElement(
                    selector=template_element.selector,
                    value=_get_value_for_selector(template_element.selector, row_data, column_mapping),
                    description=template_element.description
                )
                new_state.expected_elements.append(filled_element)
            
            all_url_states.append(new_state)
    
    # Create the final WorkflowSpec
    final_workflow_name = workflow_name or f"{template_config.workflow_name} - {len(csv_data)} Records"
    final_config = WorkflowSpec(
        workflow_name=final_workflow_name,
        url_states=all_url_states
    )
    
    # Write the output file
    _write_config_file(final_config, output_file_path)
    
    print(f"✅ Generated input file with {len(csv_data)} records and {len(all_url_states)} URL states")
    print(f"   Template: {template_file_path}")
    print(f"   CSV: {csv_file_path}")
    print(f"   Output: {output_file_path}")


def _create_column_mapping(csv_headers: list[str]) -> dict[str, str]:
    """
    Create a mapping from standard field names to actual CSV column names.
    This handles variations in CSV header naming.
    """
    mapping = {}
    
    # Normalize headers for comparison (lowercase, no spaces/dashes)
    normalized_headers = {header.lower().replace(' ', '').replace('-', ''): header for header in csv_headers}
    
    # Define standard mappings
    standard_mappings = {
        'checkindate': ['checkindate', 'checkdate', 'datein', 'arrival', 'startdate'],
        'checkoutdate': ['checkoutdate', 'checkout', 'dateout', 'departure', 'enddate'],
        'numberofpeople': ['numberofpeople', 'people', 'guests', 'occupancy', 'pax'],
        'firstname': ['firstname', 'fname', 'givenname'],
        'lastname': ['lastname', 'lname', 'surname', 'familyname'],
        'phonenumber': ['phonenumber', 'phone', 'mobile', 'telephone', 'tel'],
        'emailaddress': ['emailaddress', 'email', 'mail', 'emailaddr']
    }
    
    # Create the mapping
    for standard_name, variations in standard_mappings.items():
        for variation in variations:
            if variation in normalized_headers:
                mapping[standard_name] = normalized_headers[variation]
                break
    
    return mapping


def _get_value_for_selector(selector: str, row_data: dict[str, str], column_mapping: dict[str, str]) -> str:
    """
    Get the appropriate value from CSV row data for a given selector.
    Maps CSS selectors to CSV column data.
    """
    # Define selector to field mapping
    selector_mappings = {
        '#date-in': 'checkindate',
        '#date-out': 'checkoutdate',
        '#number-of-people': 'numberofpeople',
        '#first-name': 'firstname',
        '#last-name': 'lastname',
        '#phone': 'phonenumber',
        '#email': 'emailaddress'
    }
    
    # Get the field name for this selector
    field_name = selector_mappings.get(selector)
    if not field_name:
        # If we don't recognize the selector, return empty string
        return ""
    
    # Get the CSV column name for this field
    csv_column = column_mapping.get(field_name)
    if not csv_column:
        # If we can't map to a CSV column, return empty string
        return ""
    
    # Return the value from the CSV row
    return row_data.get(csv_column, "").strip()


def _write_config_file(config: WorkflowSpec, output_file_path: str) -> None:
    """
    Write a WorkflowSpec to a Python file as a config variable.
    """
    # Create the directory if it doesn't exist
    output_path = Path(output_file_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Generate the Python file content
    content = []
    content.append("from automated_eval import MonitoredElement, URLState, WorkflowSpec")
    content.append("")
    content.append("# Auto-generated configuration file")
    content.append("config = WorkflowSpec(")
    content.append(f'    workflow_name="{config.workflow_name}",')
    content.append("    url_states=[")
    
    for i, state in enumerate(config.url_states):
        content.append("        URLState(")
        content.append(f'            url="{state.url}",')
        content.append(f'            description="{state.description}",')
        if state.record_index is not None:
            content.append(f'            record_index={state.record_index},')
        content.append("            expected_elements=[")
        
        for j, element in enumerate(state.expected_elements):
            content.append("                MonitoredElement(")
            content.append(f'                    selector="{element.selector}",')
            content.append(f'                    value="{element.value}",')
            if element.description:
                content.append(f'                    description="{element.description}"')
            content.append("                )" + ("," if j < len(state.expected_elements) - 1 else ""))
        
        content.append("            ]")
        content.append("        )" + ("," if i < len(config.url_states) - 1 else ""))
    
    content.append("    ]")
    content.append(")")
    
    # Write the file
    with open(output_file_path, 'w', encoding='utf-8') as f:
        f.write("\n".join(content))


def create_template_from_existing_config(
    existing_config_path: str,
    template_output_path: str,
    keep_first_record: bool = True
) -> None:
    """
    Create a template file from an existing configuration by emptying values.
    
    Args:
        existing_config_path: Path to existing configuration file
        template_output_path: Path where template should be saved
        keep_first_record: If True, keeps only the first record's URLStates as template
    """
    # Load existing configuration
    spec = importlib.util.spec_from_file_location("existing_config", existing_config_path)
    existing_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(existing_module)
    existing_config: WorkflowSpec = existing_module.config
    
    # Create template by emptying values
    template_states = []
    
    if keep_first_record:
        # Find all URLStates for the first record (record_index = 0)
        first_record_states = [state for state in existing_config.url_states if state.record_index == 0]
        if not first_record_states:
            # If no record_index found, take first few states (assume they're one record)
            unique_urls = []
            for state in existing_config.url_states:
                if state.url not in unique_urls:
                    unique_urls.append(state.url)
                    first_record_states.append(state)
                if len(unique_urls) >= 2:  # Assume form + confirmation pages
                    break
    else:
        first_record_states = existing_config.url_states
    
    for state in first_record_states:
        template_state = URLState(
            url=state.url,
            description=state.description.replace(" - Record 1", "").replace(" - Benjamin Chen", ""),
            record_index=None,  # Remove record index in template
            expected_elements=[]
        )
        
        # Empty the values in elements
        for element in state.expected_elements:
            template_element = MonitoredElement(
                selector=element.selector,
                value="",  # Empty value for template
                description=element.description
            )
            template_state.expected_elements.append(template_element)
        
        template_states.append(template_state)
    
    # Create template config
    template_config = WorkflowSpec(
        workflow_name=existing_config.workflow_name.replace(" - 3 Records", "").replace(" - Multiple Records", "") + " - Template",
        url_states=template_states
    )
    
    # Write template file
    _write_config_file(template_config, template_output_path)
    
    print(f"✅ Created template file with {len(template_states)} URL states")
    print(f"   Source: {existing_config_path}")
    print(f"   Template: {template_output_path}")


if __name__ == "__main__":
    # Example usage
    import sys
    
    if len(sys.argv) < 4:
        print("Usage: python input_utils.py <template_file> <csv_file> <output_file> [workflow_name]")
        print("  template_file: Path to template input file")
        print("  csv_file: Path to CSV data file")
        print("  output_file: Path for generated input file")
        print("  workflow_name: Optional custom workflow name")
        sys.exit(1)
    
    template_file = sys.argv[1]
    csv_file = sys.argv[2]
    output_file = sys.argv[3]
    workflow_name = sys.argv[4] if len(sys.argv) > 4 else None
    
    create_input_from_csv_and_template(template_file, csv_file, output_file, workflow_name) 
