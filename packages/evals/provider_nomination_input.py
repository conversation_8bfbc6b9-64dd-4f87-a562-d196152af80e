from automated_eval import MonitoredElement, URLState, WorkflowSpec

# Define the configuration using proper Pydantic models
config = WorkflowSpec(
    workflow_name="Provider Nomination Form Automation",
    url_states=[
        URLState(
            url="https://provider-profile-pal.lovable.app/",
            description="Provider nomination form page",
            expected_elements=[
                MonitoredElement(
                    selector="#firstName",
                    value="Dr. <PERSON>",
                    description="Provider's first name field"
                ),
                MonitoredElement(
                    selector="#lastName", 
                    value="Johnson",
                    description="Provider's last name field"
                ),
                MonitoredElement(
                    selector="#email",
                    value="<EMAIL>",
                    description="Provider's email address field"
                ),
                MonitoredElement(
                    selector="#phone",
                    value="(*************",
                    description="Provider's phone number field"
                ),
                MonitoredElement(
                    selector="#npi",
                    value="**********",
                    description="Provider's NPI number field"
                ),
                MonitoredElement(
                    selector="#specialty",
                    value="Family Medicine",
                    description="Provider's primary specialty dropdown"
                ),
                MonitoredElement(
                    selector="#practiceName",
                    value="Central Health Medical Center",
                    description="Practice name field"
                ),
                MonitoredElement(
                    selector="#practiceAddress",
                    value="123 Main Street",
                    description="Practice address field"
                ),
                MonitoredElement(
                    selector="#city",
                    value="Springfield",
                    description="Practice city field"
                ),
                MonitoredElement(
                    selector="#state",
                    value="CA",
                    description="Practice state dropdown"
                ),
                MonitoredElement(
                    selector="#zipCode",
                    value="90210",
                    description="Practice ZIP code field"
                ),
                MonitoredElement(
                    selector="#comments",
                    value="Experienced family physician with 15+ years of practice. Specializes in preventive care and chronic disease management.",
                    description="Additional comments field (optional)"
                )
            ]
        )
    ]
) 
