#!/usr/bin/env python3
"""
Simple automated evaluation for VA workflows
- Patches va.step to monitor DOM elements
- Uses frame inspection to find page object
- Writes monitoring results to JSONL file
- Compares actual execution against expected workflow specifications

Usage:
    python automated_eval.py <workflow_file> <config_file> <csv_file_path> [target_url] [output_file] [--auth] [--log-file]
    
Arguments:
    workflow_file: Path to the workflow's main.py file
    config_file: Path to the validation specification file  
    csv_file_path: Path to the CSV data file for the workflow
    target_url: Optional target URL (workflow default will be used if not provided)
    output_file: Optional output file path (default: monitoring_output.jsonl)
    --auth: Optional flag to enable manual authentication mode
    --log-file: Optional path to write workflow logs (default: workflow_logs.txt)
"""
import asyncio
from datetime import datetime
import fnmatch
import importlib.util
import json
import logging
import os
from pathlib import Path
import re
import sys
import threading
import time
import typing

from pydantic import BaseModel
import va.playwright.page

# Global monitoring event loop and thread
monitoring_loop = None
monitoring_loop_thread = None
MONITORING_PAUSE_SECONDS = 1

def run_monitoring_loop_in_thread(loop):
    """Run the monitoring event loop in a dedicated thread"""
    asyncio.set_event_loop(loop)
    print(f"🔍 MONITORING: Loop started in thread {threading.get_ident()}")
    loop.run_forever()
    print(f"🔍 MONITORING: Loop stopped in thread {threading.get_ident()}")

def start_monitoring_loop():
    """Start the monitoring event loop in a background thread"""
    global monitoring_loop, monitoring_loop_thread
    
    if monitoring_loop is not None:
        print("🔍 MONITORING: Loop already running")
        return
    
    monitoring_loop = asyncio.new_event_loop()
    monitoring_loop_thread = threading.Thread(
        target=run_monitoring_loop_in_thread, 
        args=(monitoring_loop,),
        daemon=True  # Thread will die when main program exits
    )
    monitoring_loop_thread.start()
    
    # Give the loop a moment to start up
    time.sleep(0.1)
    print(f"🔍 MONITORING: Loop started in background thread {monitoring_loop_thread.ident}")

def stop_monitoring_loop():
    """Stop the monitoring event loop and clean up the thread"""
    global monitoring_loop, monitoring_loop_thread
    
    if monitoring_loop and monitoring_loop.is_running():
        print(f"🔍 MONITORING: Stopping loop in background thread {monitoring_loop_thread.ident}")
        # Schedule the stop on the event loop itself
        monitoring_loop.call_soon_threadsafe(monitoring_loop.stop)
        monitoring_loop_thread.join(timeout=5)  # Wait up to 5 seconds for thread to finish
        print("🔍 MONITORING: Loop thread joined")
    
    monitoring_loop = None
    monitoring_loop_thread = None

def run_async_on_monitoring_loop(coro):
    """
    Run an async coroutine on the dedicated monitoring event loop from sync context.
    Blocks until the coroutine completes.
    """
    global monitoring_loop
    
    if monitoring_loop is None:
        raise RuntimeError("Monitoring loop not started. Call start_monitoring_loop() first.")
    
    # Submit the coroutine to the monitoring event loop
    future = asyncio.run_coroutine_threadsafe(coro, monitoring_loop)
    
    try:
        # Block this thread until the async operation completes
        return future.result(timeout=10)  # 10 second timeout
    except Exception as e:
        print(f"🔍 MONITORING: Error running async operation: {e}")
        raise
    finally:
        # Clean up if the future was cancelled or not done
        if not future.done():
            future.cancel()

def urls_match(pattern_url: str, actual_url: str) -> bool:
    """Check if actual_url matches pattern_url, supporting * wildcards using fnmatch"""
    # If no wildcards, do exact comparison
    if '*' not in pattern_url and '?' not in pattern_url and '[' not in pattern_url:
        return pattern_url == actual_url
    
    # Use fnmatch for wildcard pattern matching
    return fnmatch.fnmatch(actual_url, pattern_url)

class MonitoredElement(BaseModel):
    selector: str
    value: str = ""
    description: str | None = None
    
    def values_equal(self, other: 'MonitoredElement') -> bool:
        """Compare only selector and value, ignoring descriptions"""
        return (
            self.selector == other.selector and 
            self.value.strip() == other.value.strip()
        )

# TODO: Consider using WorkflowState instead of WorkflowStep, since steps can have arbitrary order
# (fill name, fill email, fill date) is equivalent to (fill name, fill date, fill email)
# using something like WorkflowState at the end of a "Submit" or "Next page" step could be helpful
class URLState(BaseModel):
    url: str
    expected_elements: list[MonitoredElement]
    description: str | None = None
    record_index: int | None = None
    
    def values_equal(self, other: 'URLState') -> bool:
        """Compare only URL and element values, ignoring descriptions.
        Permissive comparison: expected elements must be present in actual, but actual can have extra elements.
        Supports wildcard matching for URLs using * patterns."""
        if not urls_match(self.url, other.url):
            return False
            
        # Create lookup dict for other elements by selector
        other_elements_by_selector = {elem.selector: elem for elem in other.expected_elements}
        
        # Check if each expected element exists in actual and values match
        for element in self.expected_elements:
            if element.selector not in other_elements_by_selector:
                return False  # Expected element is missing in actual
            
            other_element = other_elements_by_selector[element.selector]
            if not element.values_equal(other_element):
                return False  # Expected element value doesn't match actual
                
        return True  # All expected elements are present and match (ignore extras in actual)

class WorkflowSpec(BaseModel):
    workflow_name: str
    url_states: list[URLState]
    
    def values_equal(self, other: 'WorkflowSpec') -> bool:
        """Compare only URL states and element values, ignoring names and descriptions.
        Supports wildcard matching for URLs using * patterns."""
        
        # For each expected state, find a matching actual state
        for expected_state in self.url_states:
            # Find a matching actual state using pattern matching
            matching_actual_state = None
            for actual_state in other.url_states:
                if urls_match(expected_state.url, actual_state.url):
                    matching_actual_state = actual_state
                    break
            
            # If no matching actual state found, or if the state doesn't match
            if matching_actual_state is None or not expected_state.values_equal(matching_actual_state):
                return False
        
        # All expected states have matching actual states
        return True

def load_workflow_spec(spec_file_path: str) -> WorkflowSpec:
    """Load monitoring configuration from Python file with 'config' variable"""
    import importlib.util
    spec = importlib.util.spec_from_file_location("workflow_config", spec_file_path)
    config_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(config_module)
    return config_module.config


async def capture_elements_async(page: va.playwright.page.Page, locators: list[MonitoredElement]) -> list[MonitoredElement]:
    """Capture DOM elements and return updated MonitoredElement objects"""
    captured_elements = []
    
    print(f"🔍 CAPTURE DEBUG: Starting capture for {len(locators)} elements")
        
    for i, locator in enumerate(locators):
        print(f"🔍 CAPTURE DEBUG: [{i+1}/{len(locators)}] Capturing selector: {locator.selector}")
        
        try:
            element = page.locator(locator.selector)
            print(f"🔍 CAPTURE DEBUG: Created locator for {locator.selector}")
            
            # Try to get the first element and extract value
            try:
                first_element = element.first
                print(f"🔍 CAPTURE DEBUG: Got first element for {locator.selector}")
                
                # Check if element actually exists
                try:
                    count = await element.count()
                    print(f"🔍 CAPTURE DEBUG: Element count for {locator.selector}: {count}")
                    
                    if count == 0:
                        print(f"🔍 CAPTURE DEBUG: No elements found for {locator.selector}")
                        captured_elements.append(MonitoredElement(
                            selector=locator.selector,
                            value='',
                            description=locator.description
                        ))
                        continue
                        
                except Exception as e:
                    print(f"🔍 CAPTURE DEBUG: Error counting elements for {locator.selector}: {e}")
                
                if count > 0:
                    # DEBUG: Print the actual element HTML structure
                    try:
                        element_html = await first_element.evaluate("el => el.outerHTML")
                        print(f"🔍 CAPTURE DEBUG: Element HTML for {locator.selector}: {element_html}")
                    except Exception as e:
                        print(f"🔍 CAPTURE DEBUG: Could not get element HTML for {locator.selector}: {e}")
                    
                    # Try different value extraction methods in order of preference
                    captured_value = None
                    extraction_methods = ['input_value', 'text_content', 'inner_text', 'get_attribute']
                    
                    for method_name in extraction_methods:
                        try:
                            print(f"🔍 CAPTURE DEBUG: Trying {method_name} for {locator.selector}")
                            
                            if method_name == 'get_attribute':
                                # Try common attributes for form elements
                                for attr in ['value', 'data-state', 'aria-checked', 'checked', 'class']:
                                    try:
                                        method = getattr(first_element, method_name)
                                        captured_value = await method(attr)
                                        if captured_value:
                                            print(f"🔍 CAPTURE DEBUG: Got value '{captured_value}' from {method_name}('{attr}') for {locator.selector}")
                                            break
                                    except Exception as attr_e:
                                        print(f"🔍 CAPTURE DEBUG: {method_name}('{attr}') failed for {locator.selector}: {attr_e}")
                            else:
                                method = getattr(first_element, method_name)
                                captured_value = await method()
                                print(f"🔍 CAPTURE DEBUG: Got value '{captured_value}' from {method_name} for {locator.selector}")
                            
                            if captured_value:  # Stop at first non-empty value
                                print(f"🔍 CAPTURE DEBUG: SUCCESS - Using value '{captured_value}' for {locator.selector}")
                                break
                            else:
                                print(f"🔍 CAPTURE DEBUG: {method_name} returned empty/null for {locator.selector}")
                                
                        except Exception as method_e:
                            print(f"🔍 CAPTURE DEBUG: {method_name} failed for {locator.selector}: {method_e}")
                            continue
                    
                    # Element exists - create new MonitoredElement with captured value
                    final_value = captured_value.strip() if captured_value else ''
                    print(f"🔍 CAPTURE DEBUG: Final value for {locator.selector}: '{final_value}'")
                    
                    captured_elements.append(MonitoredElement(
                        selector=locator.selector,
                        value=final_value,
                        description=locator.description
                    ))
                    
            except Exception as e:
                print(f"🔍 CAPTURE DEBUG: Error accessing first element for {locator.selector}: {e}")
                # Element not found - create MonitoredElement with empty value
                captured_elements.append(MonitoredElement(
                    selector=locator.selector,
                    value='',
                    description=locator.description
                ))
        except Exception as e:
            print(f"🔍 CAPTURE DEBUG: Error creating locator for {locator.selector}: {e}")
            # Catch any Playwright errors (TargetClosedError, etc.) and create empty element
            captured_elements.append(MonitoredElement(
                selector=locator.selector,
                value='',
                description=locator.description
            ))

    print(f"🔍 CAPTURE DEBUG: Completed capture, returning {len(captured_elements)} elements")
    return captured_elements


def capture_elements_sync(page: va.playwright.page.Page, locators: list[MonitoredElement]) -> list[MonitoredElement]:
    """Synchronous wrapper for capture_elements_async using run_coroutine_threadsafe"""
    try:
        # Get the event loop from the main thread
        loop = asyncio.get_running_loop()
        print(f"🔍 Got event loop: {loop}")
        
        # Run the async function in the event loop from this thread
        future = asyncio.run_coroutine_threadsafe(capture_elements_async(page, locators), loop)
        print(f"🔍 Created future: {future}")
        
        # Wait for completion (with timeout)
        result = future.result(timeout=10)
        print(f"🔍 Future completed successfully with {len(result)} elements")
        return result
        
    except Exception as e:
        print(f"⚠️  Sync capture failed: {type(e).__name__}: {str(e)}")
        import traceback
        traceback.print_exc()
        # Return empty elements as fallback
        return [MonitoredElement(selector=loc.selector, value='') for loc in locators]


def log_monitoring_data(output_file: str, event_type: str, step_description: str, url: str, elements: list[MonitoredElement]):
    """Write monitoring data to JSONL file"""
    entry = {
        'timestamp': datetime.now().isoformat(),
        'event': event_type,
        'step_description': step_description,
        'url': url,
        'elements': elements
    }
    
    # Only create directory if output_file has a directory component
    output_dir = os.path.dirname(output_file)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    with open(output_file, 'a') as f:
        f.write(json.dumps(entry) + '\n')


async def monitor_and_log_page_state(event_type: str, step_description: str, page: va.playwright.page.Page | None, url_to_elements_map: dict[str, list[MonitoredElement]], output_file_path: str):
    """Monitor page state and log elements to file"""
    try:
        if page is None:
            print(f"⚠️  No page object - skipping {event_type} monitoring")
            return
        
        # Check if page is closed before proceeding
        if page.is_closed():
            print(f"⚠️  Page is closed - skipping {event_type} monitoring")
            return
            
        # Get current page URL
        elements_to_monitor = []
        try:
            current_url = getattr(page, 'url', 'unknown') or 'unknown'
        except (AttributeError, RuntimeError):
            current_url = 'unknown'

        if current_url == 'unknown':
            print("⚠️  Could not determine page URL - using placeholder 'unknown'")
        else:
            # Get elements to monitor for this URL
            elements_to_monitor = get_elements_for_url(url_to_elements_map, current_url)
            print(f"🔍 Found {len(elements_to_monitor)} elements to monitor for URL: {current_url}")

        # Always log data for the URL, even if no elements to monitor
        try:
            if elements_to_monitor:
                elements = await capture_elements_async(page, elements_to_monitor)
                # Convert MonitoredElement objects to dictionaries for JSON serialization
                elements_dict = [element.model_dump() for element in elements]
                log_monitoring_data(output_file_path, event_type, step_description, current_url, elements_dict)
                print(f"✅ Monitored {event_type}: {step_description} ({len(elements)} elements)")
            else:
                # Log entry with empty elements list
                log_monitoring_data(output_file_path, event_type, step_description, current_url, [])
                print(f"✅ Monitored {event_type}: {step_description} (0 elements)")
        except Exception:
            print(f"⚠️  {event_type} monitoring interrupted (likely page closed)")
    except Exception:
        # Silently handle any remaining Playwright exceptions to avoid noise
        print(f"⚠️  {event_type} monitoring skipped (page unavailable)")

class MonitoredAsyncStepContextManager:
    """Custom context manager that wraps the original AsyncStepContextManager with monitoring"""
    
    def __init__(self, page, original_context_manager, command: str, url_to_elements_map: dict[str, list[MonitoredElement]], output_file_path: str):
        self.page = page
        self.original_context_manager = original_context_manager
        self.command = command
        self.url_to_elements_map = url_to_elements_map
        self.output_file_path = output_file_path
    
    async def __aenter__(self):
        print(f"🔍 Step called: {self.command}")
        
        # Pre-step monitoring
        try:
            print(f"🔍 Starting pre-step monitoring for: {self.command}")
            time.sleep(MONITORING_PAUSE_SECONDS)
            await monitor_and_log_page_state('pre_step', self.command, self.page, self.url_to_elements_map, self.output_file_path)
            print(f"🔍 Completed pre-step monitoring for: {self.command}")
        except Exception as e:
            print(f"❌ Could not run pre-step monitoring: {e}")
        
        # Enter the original context manager
        result = await self.original_context_manager.__aenter__()
        return result
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Exit the original context manager first
        try:
            result = await self.original_context_manager.__aexit__(exc_type, exc_val, exc_tb)
        except Exception as e:
            print(f"❌ Error in original context manager exit: {e}")
            result = False
        
        # Post-step monitoring
        try:
            print(f"🔍 Starting post-step monitoring for: {self.command}")
            time.sleep(MONITORING_PAUSE_SECONDS)
            await monitor_and_log_page_state('post_step', self.command, self.page, self.url_to_elements_map, self.output_file_path)
            print(f"🔍 Completed post-step monitoring for: {self.command}")
        except Exception as e:
            print(f"❌ Could not run post-step monitoring: {e}")
        
        return result


def create_monitored_step_method(original_step_method, url_to_elements_map: dict[str, list[MonitoredElement]], output_file_path: str):
    """Create monitored version of the page.step method"""
    
    def monitored_step(self, command: str, context: dict[str, typing.Any] | None = None, max_retries: int = 3):
        """
        Monitored version of page.step that wraps the original method with monitoring
        
        Parameters match the original page.step method:
        - command: Natural language description of the action
        - context: Context variables available to the generated script
        - max_retries: Maximum number of retry attempts
        """
        print(f"🔍 Monitored step called: {command}")
        
        # Call the original step method to get the original context manager
        original_context_manager = original_step_method(self, command, context, max_retries)
        
        # Return our custom context manager that wraps the original
        return MonitoredAsyncStepContextManager(
            page=self,
            original_context_manager=original_context_manager,
            command=command,
            url_to_elements_map=url_to_elements_map,
            output_file_path=output_file_path
        )
    
    return monitored_step


def patch_page_step(url_to_elements_map: dict[str, list[MonitoredElement]], output_file_path: str):
    """Patch page.step with monitoring version"""
    try:
        # Start the dedicated monitoring event loop
        start_monitoring_loop()
        
        # Get the original step method from the Page class
        import va.playwright.page
        original_step_method = va.playwright.page.Page.step
        
        # Create the monitored version
        monitored_step_method = create_monitored_step_method(original_step_method, url_to_elements_map, output_file_path)
        
        # Replace the step method on the Page class
        va.playwright.page.Page.step = monitored_step_method
        
        print("✓ Patched page.step with monitoring")
        print("✓ Started dedicated monitoring event loop")
        return True
        
    except Exception as e:
        print(f"✗ Failed to patch page.step: {e}")
        import traceback
        traceback.print_exc()
        return False


def build_url_to_elements_map(workflow_spec: WorkflowSpec) -> dict[str, list[MonitoredElement]]:
    """Build a dictionary mapping URLs to lists of elements to monitor"""
    url_to_elements: dict[str, list[MonitoredElement]] = {}
    
    for state in workflow_spec.url_states:
        if state.url not in url_to_elements:
            url_to_elements[state.url] = []
        
        # Add elements from this state, avoiding duplicates by selector
        existing_selectors = {elem.selector for elem in url_to_elements[state.url]}
        for elem in state.expected_elements:
            if elem.selector not in existing_selectors:
                url_to_elements[state.url].append(MonitoredElement(selector=elem.selector, value=elem.value))
                existing_selectors.add(elem.selector)
    
    return url_to_elements


def get_elements_for_url(url_to_elements_map, current_url):
    for pattern_url, elements in url_to_elements_map.items():
        if urls_match(pattern_url, current_url):
            return elements
    return []


def any_element_vanished(current_entry: dict, previous_entry: dict | None) -> bool:
    """Check if any element that was present in previous_entry is now missing or empty in current_entry"""
    if previous_entry is None:
        return False
    
    # Create lookup dictionaries for easy comparison
    previous_elements = {elem['selector']: elem['value'] for elem in previous_entry.get('elements', [])}
    current_elements = {elem['selector']: elem['value'] for elem in current_entry.get('elements', [])}
    
    # Check if any previously present element is now missing or empty
    for selector, previous_value in previous_elements.items():
        # Skip if previous value was already empty
        if not previous_value or not previous_value.strip():
            continue
            
        # Element vanished if:
        # 1. Selector no longer exists in current elements
        # 2. Selector exists but value is now empty
        current_value = current_elements.get(selector, '')
        if not current_value or not current_value.strip():
            return True
    
    return False


def parse_monitoring_logs_to_spec(jsonl_file: str, workflow_name: str = "parsed_execution") -> WorkflowSpec:
    """Parse JSONL monitoring data into a WorkflowSpec for comparison"""
    url_states_list: list[URLState] = []
    previous_entry = None
    
    with open(jsonl_file) as f:
        for line in f:
            entry = json.loads(line)
            url = entry['url']
            
            # Only process post_step events (final state of elements)
            if entry['event'] != 'post_step':
                continue
                
            # Initialize URL state if list is empty or if it's different from the last URL state
            if not url_states_list or url_states_list[-1].url != url or any_element_vanished(entry, previous_entry):
                url_states_list.append(URLState(
                    url=url,
                    expected_elements=[],
                    description="Parsed from execution logs"
                ))
            
            # Update the last URL state with the new elements
            url_states_list[-1].expected_elements = [MonitoredElement(**elem) for elem in entry['elements']]
            
            # Track previous entry for next iteration
            previous_entry = entry

    spec = WorkflowSpec(workflow_name=workflow_name, url_states=url_states_list)
    with open("parsed_workflow_spec.json", "w") as f:
        json.dump(spec.model_dump(), f, indent=2)
    return spec


def compare_workflow_specs(expected: WorkflowSpec, actual: WorkflowSpec) -> bool:
    """Compare WorkflowSpecs focusing only on URLs and element values"""
    return expected.values_equal(actual)


def _group_states_by_record(url_states: list[URLState]) -> dict[int, list[URLState]]:
    """Group URL states by their record_index"""
    grouped = {}
    
    for state in url_states:
        record_idx = state.record_index if state.record_index is not None else 0
        if record_idx not in grouped:
            grouped[record_idx] = []
        grouped[record_idx].append(state)
    
    return grouped


def _show_record_summary(expected: WorkflowSpec, actual: WorkflowSpec) -> None:
    """Show record-level summary statistics if record_index is available"""
    
    # Check if we have record_index information
    expected_has_records = any(state.record_index is not None for state in expected.url_states)

    if not expected_has_records:
        return  # Skip record summary if no record_index

    from comparison_utils import infer_record_indices_with_llm
    actual = infer_record_indices_with_llm(expected, actual)
    
    # Group by records
    expected_records = _group_states_by_record(expected.url_states)
    actual_records = _group_states_by_record(actual.url_states)
    
    # Calculate record-level matches
    all_record_indices = set(expected_records.keys()) | set(actual_records.keys())
    perfect_records = 0
    
    for record_idx in all_record_indices:
        expected_states = expected_records.get(record_idx, [])
        actual_states = actual_records.get(record_idx, [])
        
        # Check if this record matches perfectly
        if len(expected_states) == len(actual_states):
            record_matches = True
            for exp_state in expected_states:
                # Find matching actual state by URL
                matching_actual = None
                for act_state in actual_states:
                    if act_state.url == exp_state.url:
                        matching_actual = act_state
                        break
                
                if matching_actual is None or not exp_state.values_equal(matching_actual):
                    record_matches = False
                    break
            
            if record_matches:
                perfect_records += 1
    
    # Show summary
    total_records = len(all_record_indices)
    print("\n📊 RECORD SUMMARY:")
    print(f"   Total Records: {total_records}")
    print(f"   Perfect Matches: {perfect_records}")
    print(f"   Records with Issues: {total_records - perfect_records}")
    print(f"   Success Rate: {(perfect_records / total_records * 100):.1f}%" if total_records > 0 else "   Success Rate: 0%")
    
    if perfect_records == total_records:
        print(f"   🎉 All {total_records} records matched perfectly!")
    else:
        print(f"   📝 {perfect_records}/{total_records} records matched correctly")


def compare_workflow_specs_with_details(expected: WorkflowSpec, actual: WorkflowSpec) -> dict[str, typing.Any]:
    """Compare and display workflow specs with ordered table format"""
    
    print("\nPage State Comparison:")
    print("═" * 120)
    print(f"| {'#':<3} | {'Expected URL':<45} | {'Actual URL':<45} | {'Status':<9} | {'Notes':<20} |")
    print("|-----|-----------------------------------------------|-----------------------------------------------|-----------|----------------------|")
    
    # Get max length for comparison
    max_len = max(len(expected.url_states), len(actual.url_states))
    
    perfect_match = True
    element_differences = []
    url_differences = []
    expected_urls = []
    actual_urls = []
    
    for i in range(max_len):
        row_num = i + 1
        
        # Get expected and actual states for this position
        expected_state = expected.url_states[i] if i < len(expected.url_states) else None
        actual_state = actual.url_states[i] if i < len(actual.url_states) else None
        
        # Determine status
        if expected_state is None:
            # Only in actual
            status = "❌"
            notes = "Only in actual"
            expected_url = "(missing)"
            actual_url = actual_state.url
            perfect_match = False
            url_differences.append({
                'row': row_num,
                'type': 'only_in_actual',
                'expected_url': expected_url,
                'actual_url': actual_url
            })
        elif actual_state is None:
            # Only in expected
            status = "❌"
            notes = "Only in expected"
            expected_url = expected_state.url
            actual_url = "(missing)"
            perfect_match = False
            url_differences.append({
                'row': row_num,
                'type': 'only_in_expected',
                'expected_url': expected_url,
                'actual_url': actual_url
            })
        elif not urls_match(expected_state.url, actual_state.url):
            # URL mismatch
            status = "❌"
            notes = "URL mismatch"
            expected_url = expected_state.url
            actual_url = actual_state.url
            perfect_match = False
            url_differences.append({
                'row': row_num,
                'type': 'url_mismatch',
                'expected_url': expected_url,
                'actual_url': actual_url
            })
        else:
            # URLs match, check elements
            if expected_state.values_equal(actual_state):
                status = "✅"
                notes = "Perfect match"
                expected_url = expected_state.url
                actual_url = actual_state.url
            else:
                status = "⚠️"
                notes = "Elements differ"
                expected_url = expected_state.url
                actual_url = actual_state.url
                perfect_match = False
                
                # Collect element differences for this row
                element_differences.append({
                    'row': row_num,
                    'url': expected_state.url,
                    'expected_state': expected_state,
                    'actual_state': actual_state
                })
        
        expected_urls.append(expected_url)
        actual_urls.append(actual_url)
        
        # Truncate URLs if too long
        expected_display = expected_url[:45] if len(expected_url) <= 45 else expected_url[:42] + "..."
        actual_display = actual_url[:45] if len(actual_url) <= 45 else actual_url[:42] + "..."

        # Add indices if URLs repeat
        if expected_url in expected_urls[:row_num-1]:
            expected_display = f"{expected_display} (x{expected_urls.count(expected_url)})"
        if actual_url in actual_urls[:row_num-1]:
            actual_display = f"{actual_display} (x{actual_urls.count(actual_url)})"
        
        print(f"| {row_num:<3} | {expected_display:<45} | {actual_display:<45} | {status:<8} | {notes:<20} |")
    
    print("═" * 120)
    print("\nLegend:")
    print("✅ Perfect match (URL and all elements match)")
    print("⚠️  URL matches but elements differ")
    print("❌ URL mismatch or missing")
    
    # Show URL differences first (more fundamental)
    if url_differences:
        print("\nURL Differences:")
        for diff in url_differences:
            print(f"\nRow {diff['row']} - {diff['type'].replace('_', ' ').title()}:")
            if diff['type'] == 'only_in_actual':
                print(f"  Expected: {diff['expected_url']}")
                print(f"  Actual:   {diff['actual_url']}")
            elif diff['type'] == 'only_in_expected':
                print(f"  Expected: {diff['expected_url']}")
                print(f"  Actual:   {diff['actual_url']}")
            elif diff['type'] == 'url_mismatch':
                print(f"  Expected: {diff['expected_url']}")
                print(f"  Actual:   {diff['actual_url']}")
                # Show if it's a pattern match issue
                if '*' in diff['expected_url'] or '?' in diff['expected_url']:
                    print("  Note: Expected URL contains wildcards - check if pattern should match")
    
    # Show element differences
    if element_differences:
        print("\nElement Differences:")
        for diff in element_differences:
            print(f"\nRow {diff['row']} - {diff['url']}:")
            
            # Create lookup dicts for easier comparison
            expected_elements = {elem.selector: elem for elem in diff['expected_state'].expected_elements}
            actual_elements = {elem.selector: elem for elem in diff['actual_state'].expected_elements}
            
            # Check each expected element
            for selector, expected_elem in expected_elements.items():
                if selector not in actual_elements:
                    print(f"  Missing selector: {selector}")
                else:
                    actual_elem = actual_elements[selector]
                    if expected_elem.value.strip() != actual_elem.value.strip():
                        print(f"  Selector: {selector}")
                        print(f"    Expected: \"{expected_elem.value.strip()}\"")
                        print(f"    Actual:   \"{actual_elem.value.strip()}\"")
            
            # Check for unexpected elements
            for selector in actual_elements:
                if selector not in expected_elements:
                    print(f"  Unexpected selector: {selector} = \"{actual_elements[selector].value.strip()}\"")
    
    if perfect_match:
        print("\n✅ OVERALL RESULT: Perfect match!")
    else:
        print("\n❌ OVERALL RESULT: Differences found")
    
    # Show record-level summary if available
    # _show_record_summary(expected, actual)
    
    # Return simple result for backward compatibility
    return {"perfect_match": perfect_match}


def extract_cdp_url_from_browser(browser):
    """
    Extract CDP URL from the VA browser instance.
    
    Since VA uses Stagehand which abstracts away Playwright, we need to probe
    for active CDP endpoints that were launched with remote debugging enabled.
    
    Raises RuntimeError if CDP URL cannot be extracted.
    """
    print(f"🔍 DEBUG: VA browser type: {type(browser)}")
    print(f"🔍 DEBUG: VA browser attributes: {[attr for attr in dir(browser) if not attr.startswith('_')]}")
    
    # Since Stagehand abstracts away Playwright, we need to find active CDP endpoints
    # by probing common ports - but only if we can actually verify it's active
    import requests
    
    print("🔍 Probing for active CDP endpoints...")
    for port in [9222, 9223, 9224, 9225, 9226, 9227, 9228, 9229]:
        try:
            response = requests.get(f"http://localhost:{port}/json/version", timeout=1)
            if response.status_code == 200:
                data = response.json()
                browser_info = data.get('Browser', 'Unknown')
                print(f"🔍 Found active CDP endpoint at port {port}: {browser_info}")
                
                # Verify this is actually a Chrome/Chromium browser
                if 'chrome' in browser_info.lower() or 'chromium' in browser_info.lower():
                    return f"http://localhost:{port}"
        except Exception as e:
            print(f"🔍 Port {port} not available: {e}")
            continue
    
    # If we get here, we couldn't find the CDP URL
    raise RuntimeError(
        "Could not find active CDP endpoint. "
        "The authentication browser might not be launched with remote debugging enabled. "
        "Check the debug output above for more details."
    )


async def create_authenticated_browser_session():
    """
    Launch browser for authentication using VA's get_browser with remote debugging enabled.
    Uses Stagehand's configuration system to enable CDP.
    """
    print("🔐 Starting authentication phase...")
    print("🚀 Launching authentication browser with remote debugging enabled...")
    
    # We need to temporarily modify the VA browser launch to enable remote debugging
    # by setting environment variables that Stagehand will pick up
    
    # Find an available port for CDP
    import socket
    def find_available_port():
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            return s.getsockname()[1]
    
    cdp_port = find_available_port()
    print(f"🔧 Using CDP port: {cdp_port}")
    
    # Temporarily patch the get_browser function to add remote debugging args
    import os
    
    # Save original environment
    original_connection_url = os.environ.get("CONNECTION_URL")
    
    # Ensure CONNECTION_URL is not set (so we create a new browser)
    if "CONNECTION_URL" in os.environ:
        del os.environ["CONNECTION_URL"]
    
    # We need to modify the Stagehand config to include remote debugging args
    # This is tricky because VA doesn't expose this directly
    # Let's try a different approach - launch with custom args
    
    # Import the underlying Stagehand classes to configure remote debugging
    from stagehand import Stagehand, StagehandConfig
    
    # Create a custom config with remote debugging enabled
    config = StagehandConfig(
        env="LOCAL",
        model_name="claude-3-7-sonnet-20250219",
        model_client_options={"apiKey": os.getenv("ANTHROPIC_API_KEY")},
        local_browser_launch_options={
            "headless": False,
            "slow_mo": 1000,
            "args": [
                f"--remote-debugging-port={cdp_port}",
                "--remote-debugging-address=0.0.0.0",
                "--no-first-run",
                "--no-default-browser-check"
            ]
        }
    )
    
    # Now create the browser using Stagehand directly    
    stagehand = Stagehand(config)
    
    try:
        # Initialize Stagehand
        await stagehand.init()
        
        # Get the context and create a wrapped context
        stagehand_pages = await stagehand.context.pages()
        
        # Import the WrappedContext from VA
        from va.playwright.playwright import WrappedContext
        auth_browser = WrappedContext(stagehand.context, stagehand_pages)
        
        # Create a page for user authentication
        await auth_browser.new_page()
        
        print("🔐 Authentication browser launched! Please:")
        print("   1. Use the browser window to navigate to the target site")
        print("   2. Complete authentication (login, solve captcha, etc.)")
        print("   3. Fill any initial form data if needed")
        print("   4. Press Enter when ready to continue with automation...")
        
        # Wait for user to complete authentication
        input()
        
        # Now extract the CDP URL
        cdp_url = f"http://localhost:{cdp_port}"
        print(f"✅ Authentication complete! CDP URL: {cdp_url}")
        
        # Verify the CDP endpoint is actually working
        import requests
        try:
            response = requests.get(f"{cdp_url}/json/version", timeout=2)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ CDP endpoint verified: {data.get('Browser', 'Unknown')}")
            else:
                raise RuntimeError(f"CDP endpoint not responding: {response.status_code}")
        except Exception as e:
            raise RuntimeError(f"CDP endpoint verification failed: {e}") from e
        
        # Return the stagehand instance (for cleanup) and CDP URL
        return stagehand, cdp_url
        
    except Exception as e:
        # Clean up on error
        try:
            await stagehand.close()
        except Exception:
            pass
        # Restore original environment
        if original_connection_url:
            os.environ["CONNECTION_URL"] = original_connection_url
        raise RuntimeError(f"Authentication failed: {e}") from e
        
    # Don't close stagehand here - it needs to stay alive for workflow connection!


async def close_stagehand_with_timeout(stagehand, timeout_seconds=10):
    """Close Stagehand instance with a timeout to prevent hanging"""
    try:
        await asyncio.wait_for(stagehand.close(), timeout=timeout_seconds)
    except TimeoutError:
        print(f"⚠️  Stagehand close operation timed out after {timeout_seconds} seconds")
        raise
    except Exception as e:
        print(f"⚠️  Error during Stagehand close: {e}")
        raise


def check_va_sdk_compliance(workflow_file_path: str) -> bool:
    """
    Check if the workflow code uses proper VA SDK patterns, specifically page.step() context managers.
    
    Args:
        workflow_file_path: Path to the workflow's main.py file
        
    Returns:
        bool: True if compliant, False otherwise
        
    Raises:
        SystemExit: If the workflow code is not compliant with VA SDK requirements
    """
    try:
        with open(workflow_file_path, encoding='utf-8') as f:
            workflow_code = f.read()
    except Exception as e:
        print(f"❌ VA SDK COMPLIANCE ERROR: Failed to read workflow file: {e}")
        return False
    
    # Check for page.step() context manager usage
    page_step_pattern = r'async\s+with\s+page\.step\s*\('
    page_step_matches = re.findall(page_step_pattern, workflow_code)
    
    # Check for page interactions without step context managers
    page_interaction_patterns = [
        r'await\s+page\.goto\s*\(',
        r'await\s+page\.click\s*\(',
        r'await\s+page\.fill\s*\(',
        r'await\s+page\.select_option\s*\(',
        r'await\s+page\.check\s*\(',
        r'await\s+page\.uncheck\s*\(',
        r'await\s+page\.get_by_\w+\([^)]*\)\.fill\s*\(',
        r'await\s+page\.get_by_\w+\([^)]*\)\.click\s*\(',
        r'await\s+page\.get_by_\w+\([^)]*\)\.check\s*\(',
        r'await\s+page\.get_by_\w+\([^)]*\)\.select_option\s*\(',
        r'await\s+[^.]*\.fill\s*\(',
        r'await\s+[^.]*\.click\s*\(',
        r'await\s+[^.]*\.check\s*\(',
        r'await\s+[^.]*\.select_option\s*\(',
    ]
    
    page_interactions = []
    for pattern in page_interaction_patterns:
        matches = re.findall(pattern, workflow_code)
        page_interactions.extend(matches)
    
    # Analyze compliance
    has_page_steps = len(page_step_matches) > 0
    has_page_interactions = len(page_interactions) > 0
    
    print("🔍 VA SDK COMPLIANCE CHECK:")
    print(f"   Workflow file: {workflow_file_path}")
    print(f"   Found {len(page_step_matches)} page.step() context managers")
    print(f"   Found {len(page_interactions)} page interactions")
    
    if not has_page_interactions:
        print("⚠️  No page interactions found - this might be a non-browser workflow")
        return True
    
    if not has_page_steps:
        print("❌ VA SDK COMPLIANCE ERROR: Workflow contains page interactions but no page.step() context managers!")
        print(f"   Page interactions found: {page_interactions[:5]}...")  # Show first 5
        print("   ")
        print("   REQUIRED: All page interactions must be wrapped in 'async with page.step():' context managers")
        print("   ")
        print("   Example:")
        print("   async with page.step('Fill username field'):")
        print("       await page.get_by_label('Username').fill('john_doe')")
        print("   ")
        print("   This is required for proper evaluation and monitoring of workflow steps.")
        return False
    
    # Check if there are enough page.step() calls relative to page interactions
    step_to_interaction_ratio = len(page_step_matches) / len(page_interactions)
    if step_to_interaction_ratio < 0.3:  # At least 30% of interactions should be in steps
        print(f"⚠️  VA SDK COMPLIANCE WARNING: Low page.step() usage ratio ({step_to_interaction_ratio:.2%})")
        print("   Consider wrapping more page interactions in page.step() context managers")
        print("   This will improve evaluation accuracy and debugging capability")
    
    print("✓ VA SDK compliance check passed")
    return True


def main():
    import asyncio
    """Main entry point for automated evaluation"""
    # Disable noisy asyncio error logging to silence TargetClosedError messages
    logging.getLogger('asyncio').setLevel(logging.CRITICAL)
    
    if len(sys.argv) < 4:
        print("Usage: python automated_eval.py <workflow_file> <config_file> <csv_file_path> [target_url] [output_file] [--auth] [--log-file <log_file_path>]")
        print("  workflow_file: Path to the workflow's main.py file")
        print("  config_file: Path to the validation specification file")  
        print("  csv_file_path: Path to the CSV data file for the workflow")
        print("  target_url: Optional target URL (workflow default will be used if not provided)")
        print("  output_file: Optional output file path (default: monitoring_output.jsonl)")
        print("  --auth: Optional flag to enable manual authentication mode")
        print("  --log-file <log_file_path>: Optional path to write workflow logs (default: workflow_logs.txt)")
        sys.exit(1)
    
    workflow_file = sys.argv[1]
    test_input_file = sys.argv[2]
    csv_file_path = sys.argv[3]
    
    # Parse remaining arguments
    remaining_args = sys.argv[4:]
    target_url = None
    output_file_path = "monitoring_output.jsonl"
    auth_mode = False
    workflow_log_file = "workflow_logs.txt"
    
    i = 0
    while i < len(remaining_args):
        arg = remaining_args[i]
        if arg == "--auth":
            auth_mode = True
        elif arg == "--log-file":
            # Next argument should be the log file path
            if i + 1 < len(remaining_args):
                workflow_log_file = remaining_args[i + 1]
                i += 1  # Skip the next argument since we used it
            else:
                print("Error: --log-file requires a file path")
                sys.exit(1)
        elif arg.endswith('.jsonl'):
            output_file_path = arg
        elif arg.endswith('.txt'):
            workflow_log_file = arg
        elif target_url is None and not arg.startswith('--'):
            target_url = arg
        i += 1
    
    # Convert output file path to absolute path to ensure we can find it later
    output_file_path = os.path.abspath(output_file_path)
    
    # Convert workflow log file path to absolute path
    workflow_log_file = os.path.abspath(workflow_log_file)
    
    # Remove existing output file to ensure clean run
    if os.path.exists(output_file_path):
        os.remove(output_file_path)
        print("🗑️  Removed existing output file for clean run")
    
    # Remove existing workflow log file to ensure clean run
    if os.path.exists(workflow_log_file):
        os.remove(workflow_log_file)
        print("🗑️  Removed existing workflow log file for clean run")
    
    print("🚀 Starting automated evaluation")
    print(f"   Workflow: {workflow_file}")
    print(f"   Test input: {test_input_file}")
    print(f"   CSV file: {csv_file_path}")
    if target_url:
        print(f"   Target URL: {target_url}")
    print(f"   Output: {output_file_path}")
    if auth_mode:
        print("   Authentication: Manual mode enabled")
    print(f"   Workflow Logs: {workflow_log_file}")
    
    auth_browser = None
    
    try:
        # PHASE 0: VA SDK Compliance Check
        print("\n🔍 Checking VA SDK compliance...")
        if not check_va_sdk_compliance(workflow_file):
            print("❌ VA SDK compliance check failed. Please fix the workflow and try again.")
            sys.exit(1)
        
        # Load configuration
        try:
            test_input = load_workflow_spec(test_input_file)
            print(f"✓ Loaded test input with {len(test_input.url_states)} URLs")
        except Exception as e:
            print(f"✗ Failed to load test input: {e}")
            sys.exit(1)
        
        # PHASE 1: Authentication (if enabled)
        if auth_mode:
            try:
                auth_browser, cdp_url = asyncio.run(create_authenticated_browser_session())
                
                # Set CONNECTION_URL for workflow to use same session
                os.environ["CONNECTION_URL"] = cdp_url
                print(f"🔧 Set CONNECTION_URL to: {cdp_url}")
                
            except Exception as e:
                print(f"✗ Authentication failed: {e}")
                sys.exit(1)
        
        # PHASE 2: Monitoring Setup
        # CRITICAL: Patch page.step BEFORE the workflow imports it
        if not patch_page_step(build_url_to_elements_map(test_input), output_file_path):
            print("✗ Failed to patch page.step")
            sys.exit(1)
        
        # PHASE 3: Workflow Execution
        # Prepare workflow path for use in multiple places
        workflow_path = Path(workflow_file).resolve()
        original_cwd = os.getcwd()
        
        # Now import and run workflow (which will use the patched step)
        try:
            # Change to workflow directory so imports work correctly
            os.chdir(workflow_path.parent)
            
            # Import workflow module
            spec = importlib.util.spec_from_file_location("workflow", workflow_path)
            workflow_module = importlib.util.module_from_spec(spec)
            sys.modules["workflow"] = workflow_module
            spec.loader.exec_module(workflow_module)
            
            # No need to patch workflow module's step reference since we patched page.step at the class level
            print("✓ Workflow module imported successfully")
            
            # Now actually run the workflow's main function
            if hasattr(workflow_module, 'main') and hasattr(workflow_module, 'WorkflowInput'):
                print("🚀 Running workflow main function...")
                
                # Create simple input using command line arguments
                input_data = {"csv_file_path": csv_file_path}
                if target_url:
                    input_data["target_url"] = target_url
                    
                sample_input = workflow_module.WorkflowInput(**input_data)
                
                # Setup logging with both console and file output
                # Clear any existing handlers
                root_logger = logging.getLogger()
                for handler in root_logger.handlers[:]:
                    root_logger.removeHandler(handler)
                
                # Setup file handler for workflow logs
                file_handler = logging.FileHandler(workflow_log_file, mode='w')
                file_handler.setLevel(logging.DEBUG)
                file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
                file_handler.setFormatter(file_formatter)
                
                # Setup console handler for workflow logs
                console_handler = logging.StreamHandler()
                console_handler.setLevel(logging.INFO)
                console_formatter = logging.Formatter('%(levelname)s - %(message)s')
                console_handler.setFormatter(console_formatter)
                
                # Configure root logger
                root_logger.addHandler(file_handler)
                root_logger.addHandler(console_handler)
                root_logger.setLevel(logging.DEBUG)
                
                # Create workflow logger
                logger = logging.getLogger("workflow")
                logger.info(f"Starting workflow execution - logs will be written to {workflow_log_file}")
                
                print(f"📝 Workflow logs will be written to: {workflow_log_file}")

                # Run the workflow - it will connect to auth session if CONNECTION_URL is set
                asyncio.run(workflow_module.main(sample_input, logger))
                print("✓ Workflow execution completed")
                logger.info("Workflow execution completed successfully")
            else:
                print("⚠ Workflow module missing main function or WorkflowInput class")
            
            # Restore original directory
            os.chdir(original_cwd)
            
            print("✓ Executed workflow successfully")
            
        except Exception as e:
            print(f"✗ Workflow execution failed: {e}")
            # Restore directory on error
            try:
                os.chdir(original_cwd)
            except Exception:
                pass
            raise  # Re-raise to trigger cleanup
        
        print(f"✓ Monitoring completed. Results written to {output_file_path}")
        
        # PHASE 4: Results Analysis
        # Parse monitoring logs and compare with expected spec
        try:
            print("🔍 Parsing monitoring logs for comparison...")
            
            # Check if output file exists and provide helpful error message
            if not os.path.exists(output_file_path):
                print(f"❌ EVALUATION ERROR: Monitoring output file not found at: {output_file_path}")
                
                # Try to find the file in common locations
                possible_locations = [
                    os.path.join(original_cwd, os.path.basename(output_file_path)),
                    os.path.join(workflow_path.parent, os.path.basename(output_file_path))
                ]
                
                found_file = None
                for location in possible_locations:
                    if os.path.exists(location):
                        found_file = location
                        break
                
                if found_file:
                    print(f"   Found monitoring file at: {found_file}")
                    print("   Using found file for comparison...")
                    output_file_path = found_file
                else:
                    print(f"   Searched in: {possible_locations}")
                    return False
            
            actual_spec = parse_monitoring_logs_to_spec(output_file_path, test_input.workflow_name)
            compare_workflow_specs_with_details(test_input, actual_spec)
            return True
                        
        except Exception as e:
            print(f"❌ EVALUATION ERROR: Failed to parse monitoring logs or perform comparison: {e}")
            return False
    
    finally:
        # CLEANUP: Always clean up resources
        print("🧹 Cleaning up resources...")
        
        # Clean up authentication browser if it was created
        if auth_browser is not None:
            try:
                # auth_browser is the Stagehand instance directly (returned from create_authenticated_browser_session)
                print(f"🔍 DEBUG: Stagehand instance type: {type(auth_browser)}")
                print(f"🔍 DEBUG: Stagehand instance methods: {[method for method in dir(auth_browser) if not method.startswith('_')]}")
                
                # Close the Stagehand instance directly
                asyncio.run(close_stagehand_with_timeout(auth_browser, timeout_seconds=10))
                print("✓ Browser cleaned up successfully")
                
            except Exception as e:
                print(f"⚠️  Warning during auth browser cleanup: {e}")
                import traceback
                traceback.print_exc()
        
        # Clean up environment variable
        if "CONNECTION_URL" in os.environ:
            del os.environ["CONNECTION_URL"]
            print("✓ CONNECTION_URL environment variable cleaned up")
        
        # Clean up monitoring loop
        stop_monitoring_loop()
        print("✓ Cleanup completed")


if __name__ == "__main__":
    main() 
