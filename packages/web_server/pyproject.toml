[project]
name = "web_server"
version = "0.1.0"
description = "Vibe Automation Server"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "agent",
    "beanie>=1.29.0",
    "chainlit>=2.5.5",
    "fastapi>=0.115.12",
    "fastapi-jwt>=0.3.0",
    "motor>=3.7.1",
    "prometheus-client>=0.17.0",
    "python-multipart>=0.0.7",
    "uvicorn>=0.34.3",
    "langchain-anthropic>=0.3.15",
    "playwright>=1.52.0",
    "aiofiles>=24.1.0",
    "pygithub>=2.6.1",
    "mongomock-motor>=0.0.36",
    "PyYAML>=6.0.0",
    "common",
    "orbot",
]

[tool.uv.sources]
common = { workspace = true }
orbot = { workspace = true }
agent = { workspace = true }

[project.scripts]
web_server = "web_server.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["web_server"]
