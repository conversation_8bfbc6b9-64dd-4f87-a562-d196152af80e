version: 1
disable_existing_loggers: False
formatters:
  default:
    '()': uvicorn.logging.DefaultFormatter
    format: '%(levelprefix)s %(message)s'
filters:
  socketio_filter:
    '()': web_server.utils.logger.SocketIOFilter
  health_check_filter:
    '()': web_server.utils.logger.HealthCheckFilter
handlers:
  split:
    class: web_server.utils.logger.InfoToStdoutErrorToStderr
    formatter: default
    filters: [socketio_filter, health_check_filter]

loggers:
  uvicorn.error:
    level: INFO
    handlers: [split]
    propagate: no
  uvicorn.access:
    level: INFO
    handlers: [split]
    propagate: no

root:
  level: INFO
  handlers: [split]
