"""
Tests for MCPConfigManager class using pytest.
"""

import json
from pathlib import Path
import tempfile
from unittest.mock import mock_open, patch

import pytest

from web_server.runtime_init.mcp_config.config_loader import MCPConfigManager


@pytest.fixture
def mock_yaml_config():
    """Mock YAML configuration content."""
    return {
        "default": {
            "playwright": {
                "command": "npx",
                "args": ["@playwright/mcp@latest", "--isolated"],
            }
        }
    }


@pytest.fixture
def config_manager(mock_yaml_config):
    """Create a test MCPConfigManager instance."""
    mock_yaml_content = """
default:
  playwright:
    command: "npx"
    args:
      - "@playwright/mcp@latest"
      - "--isolated"
"""

    with patch("builtins.open", mock_open(read_data=mock_yaml_content)):
        with patch("yaml.safe_load", return_value=mock_yaml_config):
            return MCPConfigManager()


class TestMCPConfigManager:
    """Test cases for MCPConfigManager."""

    def test_initial_default_server_loaded(self, config_manager):
        """Test that default servers are loaded from YAML."""
        servers = config_manager.list_servers()
        assert "playwright" in servers

        playwright_config = config_manager.get_server_config("playwright")
        expected_config = {
            "command": "npx",
            "args": ["@playwright/mcp@latest", "--isolated"],
        }
        assert playwright_config == expected_config

    def test_add_new_server(self, config_manager):
        """Test adding a new server."""
        # Add a new server
        config_manager.add_server("my_server", "python", ["/path/to/server.py"])

        # Verify it's in the list
        servers = config_manager.list_servers()
        assert "my_server" in servers

        # Verify its configuration
        config = config_manager.get_server_config("my_server")
        expected_config = {"command": "python", "args": ["/path/to/server.py"]}
        assert config == expected_config

    def test_override_existing_server(self, config_manager):
        """Test overriding an existing server."""
        # Override playwright server
        config_manager.add_server(
            "playwright", "node", ["custom-playwright.js"]
        )

        # Verify it's overridden
        config = config_manager.get_server_config("playwright")
        expected_config = {"command": "node", "args": ["custom-playwright.js"]}
        assert config == expected_config

    def test_add_server_args(self, config_manager):
        """Test adding args to an existing server."""
        # Add args to playwright
        config_manager.add_server_args("playwright", ["--headless", "--debug"])

        # Verify args were added
        config = config_manager.get_server_config("playwright")
        expected_config = {
            "command": "npx",
            "args": [
                "@playwright/mcp@latest",
                "--isolated",
                "--headless",
                "--debug",
            ],
        }
        assert config == expected_config

    def test_add_server_args_to_nonexistent_server(self, config_manager):
        """Test adding args to a server that doesn't exist."""
        # Should not raise an error, just do nothing
        config_manager.add_server_args("nonexistent", ["--test"])

        # Verify it doesn't exist
        config = config_manager.get_server_config("nonexistent")
        assert config == {}

    def test_get_server_config_with_additional_args(self, config_manager):
        """Test getting server config with additional args."""
        config = config_manager.get_server_config(
            "playwright", args=["--cdp-endpoint=ws://localhost:9222"]
        )

        expected_config = {
            "command": "npx",
            "args": [
                "@playwright/mcp@latest",
                "--isolated",
                "--cdp-endpoint=ws://localhost:9222",
            ],
        }
        assert config == expected_config

    def test_get_nonexistent_server_config(self, config_manager):
        """Test getting config for a server that doesn't exist."""
        config = config_manager.get_server_config("nonexistent")
        assert config == {}

    def test_export_to_json_no_file_path(self, config_manager):
        """Test exporting to JSON without file path."""
        # Add a custom server for more interesting output
        config_manager.add_server(
            "custom_server", "python", ["/path/to/custom.py"]
        )

        # Export to JSON string
        json_str = config_manager.export_to_json()

        # Parse and verify
        parsed = json.loads(json_str)

        expected_structure = {
            "mcpServers": {
                "playwright": {
                    "command": "npx",
                    "args": ["@playwright/mcp@latest", "--isolated"],
                },
                "custom_server": {
                    "command": "python",
                    "args": ["/path/to/custom.py"],
                },
            }
        }

        assert parsed == expected_structure

    def test_export_to_json_with_file_path(self, config_manager):
        """Test exporting to JSON with file path."""
        # Add a custom server
        config_manager.add_server("test_server", "node", ["test.js"])

        with tempfile.NamedTemporaryFile(
            mode="w", suffix=".json", delete=False
        ) as temp_file:
            temp_path = Path(temp_file.name)

            try:
                # Export to file
                json_str = config_manager.export_to_json(temp_path)

                # Read the file and verify
                with open(temp_path) as f:
                    file_content = f.read()

                # Both the returned string and file content should be the same
                assert json_str == file_content

                # Parse and verify structure
                parsed = json.loads(file_content)
                assert "mcpServers" in parsed
                assert "playwright" in parsed["mcpServers"]
                assert "test_server" in parsed["mcpServers"]

            finally:
                # Clean up
                temp_path.unlink(missing_ok=True)

    def test_get_mcp_servers_dict(self, config_manager):
        """Test getting the MCP servers dictionary."""
        # Add a custom server
        config_manager.add_server("dict_test", "python", ["dict_test.py"])

        servers_dict = config_manager.get_mcp_servers_dict()

        expected_dict = {
            "playwright": {
                "command": "npx",
                "args": ["@playwright/mcp@latest", "--isolated"],
            },
            "dict_test": {"command": "python", "args": ["dict_test.py"]},
        }

        assert servers_dict == expected_dict

    def test_complex_workflow(self, config_manager):
        """Test a complex workflow with multiple operations."""
        # 1. Start with default playwright
        initial_servers = config_manager.list_servers()
        assert initial_servers == ["playwright"]

        # 2. Add multiple servers
        config_manager.add_server("server1", "python", ["/path/to/server1.py"])
        config_manager.add_server(
            "server2", "node", ["server2.js", "--port", "3000"]
        )

        # 3. Modify existing server
        config_manager.add_server_args("playwright", ["--headless"])

        # 4. Override a server
        config_manager.add_server(
            "server1", "python3", ["/new/path/server1.py", "--debug"]
        )

        # 5. Verify final state
        final_servers = set(config_manager.list_servers())
        assert final_servers == {"playwright", "server1", "server2"}

        # 6. Export and verify JSON structure
        json_str = config_manager.export_to_json()
        parsed = json.loads(json_str)

        expected_structure = {
            "mcpServers": {
                "playwright": {
                    "command": "npx",
                    "args": [
                        "@playwright/mcp@latest",
                        "--isolated",
                        "--headless",
                    ],
                },
                "server1": {
                    "command": "python3",
                    "args": ["/new/path/server1.py", "--debug"],
                },
                "server2": {
                    "command": "node",
                    "args": ["server2.js", "--port", "3000"],
                },
            }
        }

        assert parsed == expected_structure

    def test_add_server_with_empty_args(self, config_manager):
        """Test adding a server with no args."""
        config_manager.add_server("simple_server", "node")

        config = config_manager.get_server_config("simple_server")
        expected_config = {"command": "node", "args": []}
        assert config == expected_config

    def test_json_export_formatting(self, config_manager):
        """Test that JSON export is properly formatted."""
        config_manager.add_server("format_test", "python", ["test.py"])

        json_str = config_manager.export_to_json()

        # Verify it's valid JSON
        parsed = json.loads(json_str)

        # Verify it's properly formatted (has indentation)
        assert "\n" in json_str
        assert "  " in json_str  # Should have indentation

        # Verify structure
        assert "mcpServers" in parsed
        assert len(parsed["mcpServers"]) == 2  # playwright + format_test
