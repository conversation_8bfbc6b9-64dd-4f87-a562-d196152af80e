"""Tests for UserFileService."""

from unittest.mock import AsyncMock, patch

from bson import ObjectId
import pytest

from common.models.organization import Organization, TenantInfo, TenantType


@pytest.fixture
def tenant_info():
    """Sample tenant info for testing."""
    org = Organization(
        id=ObjectId("507f1f77bcf86cd799439012"),
        display_name="Test Org",
        tenant_type=TenantType.SHARED_TENANT,
    )
    return TenantInfo(org=org)


@pytest.fixture
def user_file_service():
    """UserFileService instance with mocked dependencies."""
    with patch("common.storage.gcs.get_gcs_client") as mock_gcs_func:
        mock_gcs_instance = AsyncMock()
        mock_gcs_func.return_value = mock_gcs_instance

        from web_server.services.user_file_service import UserFileService

        service = UserFileService()
        service._user_file_repo = AsyncMock()
        service._gcs_client = mock_gcs_instance
        return service


@pytest.mark.asyncio
async def test_create_user_file_success(user_file_service, tenant_info):
    """Test successful user file creation."""
    # Arrange
    content = b"test file content"
    filename = "manifest.json"
    file_type = "manifest"
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    creator_id = ObjectId("507f1f77bcf86cd799439013")
    expected_user_file_id = ObjectId("507f1f77bcf86cd799439014")

    # Mock GCS upload and metadata creation
    user_file_service._gcs_client.upload = AsyncMock()
    user_file_service._user_file_repo.create_user_file.return_value = (
        expected_user_file_id
    )

    with patch("web_server.core.config.server_config") as mock_config:
        mock_config.chainlit_default_bucket = "test-bucket"

        # Act
        result = await user_file_service.create_user_file(
            content=content,
            filename=filename,
            file_type=file_type,
            workflow_id=workflow_id,
            creator_id=creator_id,
            tenant_info=tenant_info,
        )

    # Assert
    assert result == expected_user_file_id
    user_file_service._gcs_client.upload.assert_called_once()
    user_file_service._user_file_repo.create_user_file.assert_called_once()


@pytest.mark.asyncio
async def test_create_user_file_with_different_workflow(
    user_file_service, tenant_info
):
    """Test user file creation with different workflow."""
    # Arrange
    content = b"sop file content"
    filename = "sop.md"
    file_type = "sop"
    workflow_id = ObjectId("507f1f77bcf86cd799439099")  # Different workflow
    creator_id = ObjectId("507f1f77bcf86cd799439013")
    expected_user_file_id = ObjectId("507f1f77bcf86cd799439014")

    # Mock GCS upload and metadata creation
    user_file_service._gcs_client.upload = AsyncMock()
    user_file_service._user_file_repo.create_user_file.return_value = (
        expected_user_file_id
    )

    with patch("web_server.core.config.server_config") as mock_config:
        mock_config.chainlit_default_bucket = "test-bucket"

        # Act
        result = await user_file_service.create_user_file(
            content=content,
            filename=filename,
            file_type=file_type,
            workflow_id=workflow_id,
            creator_id=creator_id,
            tenant_info=tenant_info,
        )

    # Assert
    assert result == expected_user_file_id
    user_file_service._gcs_client.upload.assert_called_once()
    user_file_service._user_file_repo.create_user_file.assert_called_once()


@pytest.mark.asyncio
async def test_create_user_file_failure(user_file_service, tenant_info):
    """Test user file creation failure."""
    # Arrange
    content = b"test file content"
    filename = "manifest.json"
    file_type = "manifest"
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    creator_id = ObjectId("507f1f77bcf86cd799439013")

    # Mock successful upload but failed metadata creation
    user_file_service._gcs_client.upload = AsyncMock()
    user_file_service._user_file_repo.create_user_file.return_value = None

    with patch("web_server.core.config.server_config") as mock_config:
        mock_config.chainlit_default_bucket = "test-bucket"

        # Act & Assert
        with pytest.raises(Exception, match="Failed to create user file"):
            await user_file_service.create_user_file(
                content=content,
                filename=filename,
                file_type=file_type,
                workflow_id=workflow_id,
                creator_id=creator_id,
                tenant_info=tenant_info,
            )


@pytest.mark.asyncio
async def test_create_user_file_repo_exception(user_file_service, tenant_info):
    """Test user file creation with repository exception."""
    # Arrange
    content = b"test file content"
    filename = "manifest.json"
    file_type = "manifest"
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    creator_id = ObjectId("507f1f77bcf86cd799439013")

    # Mock successful upload but repository exception
    user_file_service._gcs_client.upload = AsyncMock()
    user_file_service._user_file_repo.create_user_file.side_effect = Exception(
        "Database error"
    )

    with patch("web_server.core.config.server_config") as mock_config:
        mock_config.chainlit_default_bucket = "test-bucket"

        # Act & Assert
        with pytest.raises(Exception, match="Database error"):
            await user_file_service.create_user_file(
                content=content,
                filename=filename,
                file_type=file_type,
                workflow_id=workflow_id,
                creator_id=creator_id,
                tenant_info=tenant_info,
            )


@pytest.mark.asyncio
async def test_generate_gcs_path(user_file_service):
    """Test GCS path generation."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    filename = "manifest.json"
    file_type = "manifest"

    # Act
    result = user_file_service._generate_gcs_path(
        workflow_id, filename, file_type
    )

    # Assert
    expected_path = f"va_workflows/{workflow_id}/manifest/manifest.json"
    assert result == expected_path
