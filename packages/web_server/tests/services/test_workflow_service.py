"""Tests for WorkflowService."""

from datetime import UTC, datetime
from unittest.mock import AsyncMock

from bson import ObjectId
import pytest

from common.models.organization import Organization, TenantInfo, TenantType
from common.models.workflow import (
    Workflow,
    WorkflowGeneratedFiles,
    WorkflowGitRepository,
)


@pytest.fixture
def sample_git_repository():
    """Sample WorkflowGitRepository for testing."""
    return WorkflowGitRepository(
        repo_url="https://github.com/test/repo",
        commit_hash="abc123",
        branch="main",
    )


@pytest.fixture
def sample_generated_files():
    """Sample WorkflowGeneratedFiles for testing."""
    return WorkflowGeneratedFiles(
        sop_file_id=ObjectId(), manifest_file_id=ObjectId()
    )


@pytest.fixture
def tenant_info():
    """Sample tenant info for testing."""
    org = Organization(
        id=ObjectId("507f1f77bcf86cd799439012"),
        display_name="Test Org",
        tenant_type=TenantType.SHARED_TENANT,
    )
    return TenantInfo(org=org)


@pytest.fixture
def workflow_service():
    """WorkflowService instance with mocked dependencies."""
    # Import and create service after all patches are in place
    from web_server.services.workflow_service import WorkflowService

    service = WorkflowService()
    service._workflow_repo = AsyncMock()
    return service


@pytest.mark.asyncio
async def test_create_workflow_success(workflow_service, tenant_info):
    """Test successful workflow creation."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    creator_id = ObjectId("507f1f77bcf86cd799439013")

    created_workflow = Workflow(
        id=workflow_id,
        thread_id="test_thread_123",
        org_id=tenant_info.org.id,
        creator_id=creator_id,
        display_name="Test Workflow",
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    # Set up the mock repository
    workflow_service._workflow_repo.create_workflow.return_value = workflow_id
    workflow_service._workflow_repo.get_workflow_by_id.return_value = (
        created_workflow
    )

    # Act
    result = await workflow_service.create_workflow(
        creator_id=creator_id,
        tenant_info=tenant_info,
        thread_id="test_thread_123",
        display_name="Test Workflow",
    )

    # Assert - Check that result is a Workflow domain model
    assert isinstance(result, Workflow)
    assert (
        result == created_workflow
    )  # Should return the exact workflow from get_workflow_by_id
    assert result.org_id == tenant_info.org.id
    assert result.creator_id == creator_id
    assert result.display_name == "Test Workflow"
    workflow_service._workflow_repo.create_workflow.assert_called_once_with(
        creator_id=creator_id,
        tenant_info=tenant_info,
        thread_id="test_thread_123",
        display_name="Test Workflow",
    )


@pytest.mark.asyncio
async def test_create_workflow_failure(workflow_service, tenant_info):
    """Test workflow creation failure."""
    # Arrange
    creator_id = ObjectId("507f1f77bcf86cd799439013")
    workflow_service._workflow_repo.create_workflow.return_value = None

    # Act
    result = await workflow_service.create_workflow(
        creator_id=creator_id,
        tenant_info=tenant_info,
        thread_id="test_thread_123",
    )

    # Assert
    assert result is None


@pytest.mark.asyncio
async def test_create_workflow_repo_exception(workflow_service, tenant_info):
    """Test workflow creation with repository exception."""
    # Arrange
    creator_id = ObjectId("507f1f77bcf86cd799439013")
    workflow_service._workflow_repo.create_workflow.side_effect = Exception(
        "Database error"
    )

    # Act & Assert
    with pytest.raises(Exception, match="Database error"):
        await workflow_service.create_workflow(
            creator_id=creator_id,
            tenant_info=tenant_info,
            thread_id="test_thread_123",
        )


@pytest.mark.asyncio
async def test_update_workflow_success(
    workflow_service, sample_git_repository, sample_generated_files, tenant_info
):
    """Test successful workflow update."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    updated_workflow = Workflow(
        id=workflow_id,
        thread_id="test_thread_123",
        display_name="Updated Workflow",
        org_id=ObjectId("507f1f77bcf86cd799439012"),
        creator_id=ObjectId("507f1f77bcf86cd799439013"),
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    # Configure the mock repository
    workflow_service._workflow_repo.update_workflow.return_value = True
    workflow_service._workflow_repo.get_workflow_by_id.return_value = (
        updated_workflow
    )

    # Act
    result = await workflow_service.update_workflow(
        workflow_id=workflow_id,
        tenant_info=tenant_info,
        display_name="Updated Workflow",
        git_repository=sample_git_repository,
        generated_files=sample_generated_files,
    )

    # Assert - Check that result is a Workflow domain model
    assert isinstance(result, Workflow)
    assert (
        result == updated_workflow
    )  # Should return the exact workflow from get_workflow_by_id
    assert result.display_name == "Updated Workflow"
    # Verify both update and get methods were called
    workflow_service._workflow_repo.update_workflow.assert_called_once()
    workflow_service._workflow_repo.get_workflow_by_id.assert_called_once_with(
        workflow_id, tenant_info
    )


@pytest.mark.asyncio
async def test_update_workflow_minimal_request(workflow_service, tenant_info):
    """Test workflow update with minimal parameters (no optional fields)."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    updated_workflow = Workflow(
        id=workflow_id,
        thread_id="test_thread_123",
        display_name="Updated Workflow",
        org_id=ObjectId(),
        creator_id=ObjectId(),
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    # Configure the mock repository
    workflow_service._workflow_repo.update_workflow.return_value = True
    workflow_service._workflow_repo.get_workflow_by_id.return_value = (
        updated_workflow
    )

    # Act
    result = await workflow_service.update_workflow(
        workflow_id=workflow_id,
        tenant_info=tenant_info,
        display_name="Updated Workflow",
        # No optional fields set
    )

    # Assert - Check that result is a Workflow domain model
    assert isinstance(result, Workflow)
    assert (
        result == updated_workflow
    )  # Should return the exact workflow from get_workflow_by_id
    assert result.display_name == "Updated Workflow"
    # Verify both update and get methods were called
    workflow_service._workflow_repo.update_workflow.assert_called_once()
    workflow_service._workflow_repo.get_workflow_by_id.assert_called_once_with(
        workflow_id, tenant_info
    )


@pytest.mark.asyncio
async def test_update_workflow_update_failure(workflow_service, tenant_info):
    """Test workflow update failure in update step."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    workflow_service._workflow_repo.update_workflow.return_value = False

    # Act
    result = await workflow_service.update_workflow(
        workflow_id=workflow_id,
        tenant_info=tenant_info,
        display_name="Updated Workflow",
    )

    # Assert
    assert result is None


@pytest.mark.asyncio
async def test_update_workflow_get_failure(workflow_service, tenant_info):
    """Test workflow update failure in get step."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    # First call (update) succeeds, second call (get) returns None
    workflow_service._workflow_repo.update_workflow.return_value = True
    workflow_service._workflow_repo.get_workflow_by_id.return_value = None

    # Act
    result = await workflow_service.update_workflow(
        workflow_id=workflow_id,
        tenant_info=tenant_info,
        display_name="Updated Workflow",
    )

    # Assert
    assert result is None


@pytest.mark.asyncio
async def test_update_workflow_exception(workflow_service, tenant_info):
    """Test workflow update with exception."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    workflow_service._workflow_repo.update_workflow.side_effect = Exception(
        "Database error"
    )

    # Act & Assert
    with pytest.raises(Exception, match="Database error"):
        await workflow_service.update_workflow(
            workflow_id=workflow_id,
            tenant_info=tenant_info,
            display_name="Updated Workflow",
        )


@pytest.mark.asyncio
async def test_list_workflows_with_pagination(workflow_service, tenant_info):
    """Test workflow listing with custom pagination parameters."""
    # Arrange
    org_id = tenant_info.org.id
    mock_workflows = [
        Workflow(
            id=ObjectId("507f1f77bcf86cd799439013"),
            thread_id="test_thread_1",
            display_name="Workflow 1",
            org_id=org_id,
            creator_id=ObjectId("507f1f77bcf86cd799439012"),
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC),
        ),
        Workflow(
            id=ObjectId("507f1f77bcf86cd799439014"),
            thread_id="test_thread_2",
            display_name="Workflow 2",
            org_id=org_id,
            creator_id=ObjectId("507f1f77bcf86cd799439013"),
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC),
        ),
    ]

    workflow_service._workflow_repo.list_workflows.return_value = mock_workflows
    workflow_service._workflow_repo.count_workflows.return_value = 10

    # Act
    workflows, total_count = await workflow_service.list_workflows(
        tenant_info=tenant_info, page_size=5, page_number=1
    )

    # Assert
    assert isinstance(workflows, list)
    assert len(workflows) == 2
    assert all(isinstance(w, Workflow) for w in workflows)
    assert total_count == 10

    # Verify repository calls
    workflow_service._workflow_repo.list_workflows.assert_called_once_with(
        tenant_info=tenant_info,
        limit=5,
        offset=0,
        display_name_prefix=None,
        creator_ids=None,
    )
    workflow_service._workflow_repo.count_workflows.assert_called_once_with(
        tenant_info=tenant_info,
        display_name_prefix=None,
        creator_ids=None,
    )


@pytest.mark.asyncio
async def test_list_workflows_default_pagination(workflow_service, tenant_info):
    """Test workflow listing with default pagination parameters."""
    # Arrange
    workflow_service._workflow_repo.list_workflows.return_value = []
    workflow_service._workflow_repo.count_workflows.return_value = 0

    # Act
    workflows, total_count = await workflow_service.list_workflows(
        tenant_info=tenant_info
    )

    # Assert
    assert isinstance(workflows, list)
    assert len(workflows) == 0
    assert total_count == 0

    # Verify repository calls with defaults
    workflow_service._workflow_repo.list_workflows.assert_called_once_with(
        tenant_info=tenant_info,
        limit=10,  # default
        offset=0,
        display_name_prefix=None,
        creator_ids=None,
    )


@pytest.mark.asyncio
async def test_list_workflows_with_display_name_filter(
    workflow_service, tenant_info
):
    """Test workflow listing with display name prefix filter."""
    # Arrange
    workflow_service._workflow_repo.list_workflows.return_value = []
    workflow_service._workflow_repo.count_workflows.return_value = 0

    # Act
    workflows, total_count = await workflow_service.list_workflows(
        tenant_info=tenant_info, display_name_prefix="Test"
    )

    # Assert
    workflow_service._workflow_repo.list_workflows.assert_called_once_with(
        tenant_info=tenant_info,
        limit=10,
        offset=0,
        display_name_prefix="Test",
        creator_ids=None,
    )


@pytest.mark.asyncio
async def test_list_workflows_with_creator_ids_filter(
    workflow_service, tenant_info
):
    """Test workflow listing with creator IDs filter."""
    # Arrange
    creator_ids = [ObjectId("507f1f77bcf86cd799439012")]
    workflow_service._workflow_repo.list_workflows.return_value = []
    workflow_service._workflow_repo.count_workflows.return_value = 0

    # Act
    workflows, total_count = await workflow_service.list_workflows(
        tenant_info=tenant_info, creator_ids=creator_ids
    )

    # Assert
    workflow_service._workflow_repo.list_workflows.assert_called_once_with(
        tenant_info=tenant_info,
        limit=10,
        offset=0,
        display_name_prefix=None,
        creator_ids=creator_ids,
    )


@pytest.mark.asyncio
async def test_list_workflows_invalid_page_size(workflow_service, tenant_info):
    """Test workflow listing with invalid page size."""
    # Act & Assert - page_size too small
    with pytest.raises(ValueError, match="page_size must be between 1 and 20"):
        await workflow_service.list_workflows(
            tenant_info=tenant_info, page_size=0
        )

    # Act & Assert - page_size too large
    with pytest.raises(ValueError, match="page_size must be between 1 and 20"):
        await workflow_service.list_workflows(
            tenant_info=tenant_info, page_size=25
        )


@pytest.mark.asyncio
async def test_list_workflows_invalid_page_number(
    workflow_service, tenant_info
):
    """Test workflow listing with invalid page number."""
    # Act & Assert
    with pytest.raises(ValueError, match="page_number must be >= 1"):
        await workflow_service.list_workflows(
            tenant_info=tenant_info, page_number=0
        )


@pytest.mark.asyncio
async def test_list_workflows_pagination_calculation(
    workflow_service, tenant_info
):
    """Test that pagination offset is calculated correctly."""
    # Arrange
    workflow_service._workflow_repo.list_workflows.return_value = []
    workflow_service._workflow_repo.count_workflows.return_value = 0

    # Act - page 3 with size 5 should have offset 10
    await workflow_service.list_workflows(
        tenant_info=tenant_info, page_size=5, page_number=3
    )

    # Assert
    workflow_service._workflow_repo.list_workflows.assert_called_once_with(
        tenant_info=tenant_info,
        limit=5,
        offset=10,  # (3-1) * 5 = 10
        display_name_prefix=None,
        creator_ids=None,
    )


@pytest.mark.asyncio
async def test_list_workflows_exception(workflow_service, tenant_info):
    """Test workflow listing with repository exception."""
    # Arrange
    workflow_service._workflow_repo.list_workflows.side_effect = Exception(
        "Database error"
    )

    # Act & Assert
    with pytest.raises(Exception, match="Database error"):
        await workflow_service.list_workflows(tenant_info=tenant_info)


@pytest.mark.asyncio
async def test_delete_workflow_success(workflow_service, tenant_info):
    """Test successful workflow deletion."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    workflow_service._workflow_repo.delete_workflow.return_value = True

    # Act
    result = await workflow_service.delete_workflow(
        workflow_id=workflow_id, tenant_info=tenant_info
    )

    # Assert
    assert result is True

    # Verify repository call
    workflow_service._workflow_repo.delete_workflow.assert_called_once_with(
        workflow_id=workflow_id,
        tenant_info=tenant_info,
    )


@pytest.mark.asyncio
async def test_delete_workflow_not_found(workflow_service, tenant_info):
    """Test workflow deletion when workflow not found."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    workflow_service._workflow_repo.delete_workflow.return_value = False

    # Act
    result = await workflow_service.delete_workflow(
        workflow_id=workflow_id, tenant_info=tenant_info
    )

    # Assert
    assert result is False


@pytest.mark.asyncio
async def test_delete_workflow_exception(workflow_service, tenant_info):
    """Test workflow deletion with repository exception."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    workflow_service._workflow_repo.delete_workflow.side_effect = Exception(
        "Database error"
    )

    # Act & Assert
    with pytest.raises(Exception, match="Database error"):
        await workflow_service.delete_workflow(
            workflow_id=workflow_id, tenant_info=tenant_info
        )


@pytest.mark.asyncio
async def test_get_workflow_by_id_success(workflow_service, tenant_info):
    """Test successful workflow retrieval by ID."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    expected_workflow = Workflow(
        id=workflow_id,
        thread_id="test_thread_123",
        display_name="Test Workflow",
        org_id=tenant_info.org.id,
        creator_id=ObjectId("507f1f77bcf86cd799439013"),
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    workflow_service._workflow_repo.get_workflow_by_id.return_value = (
        expected_workflow
    )

    # Act
    result = await workflow_service.get_workflow_by_id(
        workflow_id=workflow_id, tenant_info=tenant_info
    )

    # Assert
    assert isinstance(result, Workflow)
    assert (
        result == expected_workflow
    )  # Should return the exact workflow from get_workflow_by_id
    assert result.display_name == "Test Workflow"
    workflow_service._workflow_repo.get_workflow_by_id.assert_called_once_with(
        workflow_id, tenant_info
    )


@pytest.mark.asyncio
async def test_get_workflow_by_id_not_found(workflow_service, tenant_info):
    """Test workflow retrieval when workflow not found."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    workflow_service._workflow_repo.get_workflow_by_id.return_value = None

    # Act
    result = await workflow_service.get_workflow_by_id(
        workflow_id=workflow_id, tenant_info=tenant_info
    )

    # Assert
    assert result is None
    workflow_service._workflow_repo.get_workflow_by_id.assert_called_once_with(
        workflow_id, tenant_info
    )


@pytest.mark.asyncio
async def test_get_workflow_by_id_exception(workflow_service, tenant_info):
    """Test workflow retrieval with repository exception."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    workflow_service._workflow_repo.get_workflow_by_id.side_effect = Exception(
        "Database error"
    )

    # Act & Assert
    with pytest.raises(Exception, match="Database error"):
        await workflow_service.get_workflow_by_id(
            workflow_id=workflow_id, tenant_info=tenant_info
        )
