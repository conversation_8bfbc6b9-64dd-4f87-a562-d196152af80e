import asyncio
import os
from unittest.mock import AsyncMock, patch

import pytest

from common.sandbox.base import Runtime, RuntimeType
from web_server.session.runtime_manager import (
    RuntimeSessionManager,
    get_runtime_session_manager,
)


@pytest.fixture
def reset_singleton():
    """Reset singleton instance before and after each test."""
    RuntimeSessionManager.reset_instance()
    yield
    RuntimeSessionManager.reset_instance()


@pytest.fixture
def mock_local_runtime():
    """Mock LocalRuntime class."""
    with patch("web_server.session.runtime_manager.LocalRuntime") as mock:
        mock_instance = AsyncMock(spec=Runtime)
        mock.return_value = mock_instance
        yield mock, mock_instance


@pytest.fixture
def mock_e2b_runtime():
    """Mock E2BRuntime class."""
    with patch("web_server.session.runtime_manager.E2BRuntime") as mock:
        mock_instance = AsyncMock(spec=Runtime)
        mock.return_value = mock_instance
        yield mock, mock_instance


@pytest.fixture
def runtime_manager(reset_singleton, mock_local_runtime):
    """Create RuntimeSessionManager instance with mocked runtime."""
    return RuntimeSessionManager(runtime_type=RuntimeType.LOCAL)


def test_singleton_pattern():
    """Test that RuntimeSessionManager implements singleton pattern correctly."""
    manager1 = RuntimeSessionManager()
    manager2 = RuntimeSessionManager()

    assert manager1 is manager2
    assert RuntimeSessionManager.get_instance() is manager1


@pytest.mark.parametrize(
    "env_var_value, expected_runtime_type",
    [
        (None, RuntimeType.LOCAL),
        ("test_key", RuntimeType.E2B),
    ],
    ids=["no_api_key", "valid_api_key"],
)
def test_detect_runtime_type(
    reset_singleton, env_var_value, expected_runtime_type
):
    """Test runtime type detection based on environment variables."""
    # Save original value if it exists
    original_value = os.environ.get("E2B_API_KEY")
    
    try:
        if env_var_value is not None:
            # Set the environment variable
            os.environ["E2B_API_KEY"] = env_var_value
        else:
            # Remove the environment variable if it exists
            if "E2B_API_KEY" in os.environ:
                del os.environ["E2B_API_KEY"]
        
        manager = RuntimeSessionManager()
        assert manager.runtime_type == expected_runtime_type
    finally:
        # Restore original state
        if original_value is not None:
            os.environ["E2B_API_KEY"] = original_value
        elif "E2B_API_KEY" in os.environ:
            del os.environ["E2B_API_KEY"]


def test_get_runtime_session_manager_factory(reset_singleton):
    """Test the factory function creates and returns manager."""
    manager = get_runtime_session_manager(RuntimeType.LOCAL)

    assert isinstance(manager, RuntimeSessionManager)
    assert manager.runtime_type == RuntimeType.LOCAL

    # Second call should return same instance
    manager2 = get_runtime_session_manager()
    assert manager is manager2


def test_get_runtime_creates_new_session(runtime_manager, mock_local_runtime):
    """Test that get_runtime creates a new session correctly."""
    mock_class, mock_instance = mock_local_runtime

    async def run_test():
        runtime = await runtime_manager.get_runtime("test_session")

        assert runtime is mock_instance

        # Verify runtime was created with correct config
        mock_class.assert_called_once()
        config = mock_class.call_args[0][0]
        assert config.chainlit_session_id == "test_session"
        assert config.run_type == RuntimeType.LOCAL

        # Verify initialize was called
        mock_instance.initialize.assert_called_once()

    asyncio.run(run_test())


def test_get_runtime_returns_existing_session(
    runtime_manager, mock_local_runtime
):
    """Test that get_runtime returns existing session when called multiple times."""
    mock_class, mock_instance = mock_local_runtime

    async def run_test():
        # First call creates the runtime
        runtime1 = await runtime_manager.get_runtime("test_session")
        # Second call should return the same runtime
        runtime2 = await runtime_manager.get_runtime("test_session")

        assert runtime1 is runtime2
        assert runtime1 is mock_instance

        # Runtime should only be created once
        mock_class.assert_called_once()
        mock_instance.initialize.assert_called_once()

    asyncio.run(run_test())


def test_get_runtime_multiple_sessions(runtime_manager, mock_local_runtime):
    """Test creating multiple runtime sessions."""
    mock_class, mock_instance = mock_local_runtime
    session_ids = ["session1", "session2"]

    async def run_test():
        runtimes = []
        for session_id in session_ids:
            runtime = await runtime_manager.get_runtime(session_id)
            runtimes.append(runtime)

        # All runtimes should be the same mock instance (since we're mocking)
        assert all(r is mock_instance for r in runtimes)

        # Verify all sessions are tracked
        get_stats = await runtime_manager.get_session_states()
        assert get_stats["total_runtime_sessions"] == len(session_ids)

        # Runtime should be created for each session
        assert mock_class.call_count == len(session_ids)
        assert mock_instance.initialize.call_count == len(session_ids)

    asyncio.run(run_test())


def test_get_runtime_initialization_failure(
    runtime_manager, mock_local_runtime
):
    """Test handling of runtime initialization failure."""
    mock_class, mock_instance = mock_local_runtime
    mock_instance.initialize.side_effect = Exception("Initialization failed")

    async def run_test():
        with pytest.raises(Exception) as exc_info:
            await runtime_manager.get_runtime("test_session")

        assert "Initialization failed" in str(exc_info.value)

    asyncio.run(run_test())


def test_close_runtime_existing_session(runtime_manager, mock_local_runtime):
    """Test closing an existing runtime session."""
    mock_class, mock_instance = mock_local_runtime

    async def run_test():
        # Create a session first
        await runtime_manager.get_runtime("test_session")
        stats = await runtime_manager.get_session_states()
        assert stats["total_runtime_sessions"] == 1

        # Close the session
        await runtime_manager.close_runtime("test_session")
        stats = await runtime_manager.get_session_states()
        # Verify session is removed
        assert stats["total_runtime_sessions"] == 0
        # Verify stop was called
        mock_instance.stop.assert_called_once()

    asyncio.run(run_test())


def test_close_runtime_nonexistent_session(runtime_manager):
    """Test closing a non-existent runtime session."""

    async def run_test():
        await runtime_manager.get_runtime("session1")
        # Verify session is created
        stats = await runtime_manager.get_session_states()
        assert stats["total_runtime_sessions"] == 1
        # Close a non-existent session
        await runtime_manager.close_runtime("nonexistent_session")
        # Verify no sessions are affected
        stats = await runtime_manager.get_session_states()
        assert stats["total_runtime_sessions"] == 1

    asyncio.run(run_test())
