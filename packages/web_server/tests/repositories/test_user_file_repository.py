"""Tests for UserFileRepository."""

from unittest.mock import AsyncMock, Mock, patch

from bson import ObjectId
import pytest

from common.models.organization import Organization, TenantInfo, TenantType
from web_server.repositories.user_file_repository import UserFileRepository


@pytest.fixture
def user_file_repo():
    """UserFileRepository instance."""
    return UserFileRepository()


@pytest.fixture
def tenant_info():
    """Sample tenant info for testing."""
    org = Organization(
        id=ObjectId("507f1f77bcf86cd799439012"),
        display_name="Test Org",
        tenant_type=TenantType.SHARED_TENANT,
    )
    return TenantInfo(org=org)


@pytest.mark.asyncio
async def test_create_user_file_success(user_file_repo, tenant_info):
    """Test successful user file creation."""
    # Arrange
    creator_id = ObjectId("507f1f77bcf86cd799439013")
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    path = "gs://bucket/manifest.json"
    expected_user_file_id = ObjectId("507f1f77bcf86cd799439014")

    mock_collection = AsyncMock()
    mock_result = Mock()
    mock_result.inserted_id = expected_user_file_id
    mock_collection.insert_one = AsyncMock(return_value=mock_result)

    with patch.object(
        user_file_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await user_file_repo.create_user_file(
            creator_id=creator_id,
            path=path,
            workflow_id=workflow_id,
            tenant_info=tenant_info,
        )

        # Assert
        assert result == expected_user_file_id
        mock_collection.insert_one.assert_called_once()

        # Verify the insert data
        call_args = mock_collection.insert_one.call_args[0][0]
        assert call_args["creator_id"] == creator_id
        assert (
            call_args["org_id"] == tenant_info.org.id
        )  # org_id extracted from tenant_info
        assert call_args["path"] == path
        assert call_args["workflow_id"] == workflow_id
        assert "created_time" in call_args


@pytest.mark.asyncio
async def test_create_user_file_with_different_workflow(
    user_file_repo, tenant_info
):
    """Test user file creation with different workflow ID."""
    # Arrange
    creator_id = ObjectId("507f1f77bcf86cd799439013")
    workflow_id = ObjectId("507f1f77bcf86cd799439099")  # Different workflow
    path = "gs://bucket/sop.md"
    expected_user_file_id = ObjectId("507f1f77bcf86cd799439014")

    mock_collection = AsyncMock()
    mock_result = Mock()
    mock_result.inserted_id = expected_user_file_id
    mock_collection.insert_one = AsyncMock(return_value=mock_result)

    with patch.object(
        user_file_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await user_file_repo.create_user_file(
            creator_id=creator_id,
            path=path,
            workflow_id=workflow_id,
            tenant_info=tenant_info,
        )

        # Assert
        assert result == expected_user_file_id

        # Verify workflow_id is set correctly
        call_args = mock_collection.insert_one.call_args[0][0]
        assert call_args["workflow_id"] == workflow_id


@pytest.mark.asyncio
async def test_create_user_file_failure(user_file_repo, tenant_info):
    """Test user file creation failure."""
    # Arrange
    creator_id = ObjectId("507f1f77bcf86cd799439013")
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    path = "gs://bucket/manifest.json"

    mock_collection = AsyncMock()
    mock_collection.insert_one = AsyncMock(
        side_effect=Exception("Database error")
    )

    with patch.object(
        user_file_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await user_file_repo.create_user_file(
            creator_id=creator_id,
            path=path,
            workflow_id=workflow_id,
            tenant_info=tenant_info,
        )

        # Assert
        assert result is None
