"""Tests for WorkflowRepository."""

from datetime import UTC, datetime
from unittest.mock import AsyncMock, Mock, patch

from bson import ObjectId
import pytest

from common.models.organization import Organization, TenantInfo, TenantType
from common.models.workflow import (
    Workflow,
    WorkflowGeneratedFiles,
    WorkflowGitRepository,
)
from web_server.repositories.workflow_repository import WorkflowRepository


@pytest.fixture
def mock_collection():
    """Mock MongoDB collection."""
    return Mock()


@pytest.fixture
def workflow_repo():
    """WorkflowRepository instance with mocked dependencies."""
    return WorkflowRepository()


@pytest.fixture
def tenant_info():
    """Sample tenant info for testing."""
    org = Organization(
        id=ObjectId("507f1f77bcf86cd799439012"),
        display_name="Test Org",
        tenant_type=TenantType.SHARED_TENANT,
    )
    return TenantInfo(org=org)


@pytest.mark.asyncio
async def test_create_workflow_success(
    workflow_repo, mock_collection, tenant_info
):
    """Test successful workflow creation."""
    # Arrange
    creator_id = ObjectId("507f1f77bcf86cd799439013")
    expected_workflow_id = ObjectId("507f1f77bcf86cd799439011")

    mock_result = Mock()
    mock_result.inserted_id = expected_workflow_id
    mock_collection.insert_one = AsyncMock(return_value=mock_result)

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.create_workflow(creator_id, tenant_info, thread_id="test_thread_123")

        # Assert
        assert result == expected_workflow_id
        mock_collection.insert_one.assert_called_once()

        # Verify the insert data
        call_args = mock_collection.insert_one.call_args[0][0]
        assert (
            call_args["org_id"] == tenant_info.org.id
        )  # org_id extracted from tenant_info
        assert call_args["creator_id"] == creator_id
        assert "created_at" in call_args
        assert "updated_at" in call_args


@pytest.mark.asyncio
async def test_create_workflow_failure(
    workflow_repo, mock_collection, tenant_info
):
    """Test workflow creation failure."""
    # Arrange
    creator_id = ObjectId()

    mock_collection.insert_one = AsyncMock(
        side_effect=Exception("Database error")
    )

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.create_workflow(creator_id, tenant_info, thread_id="test_thread_123")

        # Assert
        assert result is None


@pytest.mark.asyncio
async def test_update_workflow_success(
    workflow_repo, mock_collection, tenant_info
):
    """Test successful workflow update."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    display_name = "Test Workflow"
    git_repo = WorkflowGitRepository(
        repo_url="https://github.com/test/repo",
        commit_hash="abc123",
        branch="main",
    )
    generated_files = WorkflowGeneratedFiles(
        sop_file_id=ObjectId(), manifest_file_id=ObjectId()
    )

    mock_result = Mock()
    mock_result.modified_count = 1
    mock_collection.update_one = AsyncMock(return_value=mock_result)

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.update_workflow(
            workflow_id=workflow_id,
            tenant_info=tenant_info,
            display_name=display_name,
            git_repository=git_repo,
            generated_files=generated_files,
        )

        # Assert
        assert result is True
        mock_collection.update_one.assert_called_once()

        # Verify the update call
        call_args = mock_collection.update_one.call_args
        assert call_args[0][0] == {
            "_id": workflow_id,
            "org_id": tenant_info.org.id,
        }

        update_data = call_args[0][1]["$set"]
        assert update_data["display_name"] == display_name
        assert "updated_at" in update_data


@pytest.mark.asyncio
async def test_update_workflow_minimal(
    workflow_repo, mock_collection, tenant_info
):
    """Test workflow update with minimal data."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    display_name = "Test Workflow"

    mock_result = Mock()
    mock_result.modified_count = 1
    mock_collection.update_one = AsyncMock(return_value=mock_result)

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.update_workflow(
            workflow_id=workflow_id,
            tenant_info=tenant_info,
            display_name=display_name,
        )

        # Assert
        assert result is True

        # Verify the update call
        call_args = mock_collection.update_one.call_args
        update_data = call_args[0][1]["$set"]
        assert update_data["display_name"] == display_name
        assert "git_repository" not in update_data
        assert "generated_files" not in update_data


@pytest.mark.asyncio
async def test_update_workflow_failure(
    workflow_repo, mock_collection, tenant_info
):
    """Test workflow update failure."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    display_name = "Test Workflow"

    mock_collection.update_one = AsyncMock(
        side_effect=Exception("Database error")
    )

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.update_workflow(
            workflow_id=workflow_id,
            tenant_info=tenant_info,
            display_name=display_name,
        )

        # Assert
        assert result is False


@pytest.mark.asyncio
async def test_update_workflow_success_with_all_fields(
    workflow_repo, mock_collection, tenant_info
):
    """Test successful workflow update with all optional fields."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    display_name = "Test Workflow"
    git_repo = WorkflowGitRepository(
        repo_url="https://github.com/test/repo",
        commit_hash="abc123",
        branch="main",
    )
    generated_files = WorkflowGeneratedFiles(
        sop_file_id=ObjectId(), manifest_file_id=ObjectId()
    )

    mock_result = Mock()
    mock_result.modified_count = 1
    mock_collection.update_one = AsyncMock(return_value=mock_result)

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.update_workflow(
            workflow_id=workflow_id,
            tenant_info=tenant_info,
            display_name=display_name,
            git_repository=git_repo,
            generated_files=generated_files,
        )

        # Assert
        assert result is True
        mock_collection.update_one.assert_called_once()

        # Verify the update call parameters match the model structure
        call_args = mock_collection.update_one.call_args
        assert call_args[0][0] == {
            "_id": workflow_id,
            "org_id": tenant_info.org.id,
        }

        update_data = call_args[0][1]["$set"]
        assert update_data["display_name"] == display_name
        assert "updated_at" in update_data
        # Verify that fields that should not be modified are not present
        assert "created_at" not in update_data
        assert "org_id" not in update_data
        assert "creator_id" not in update_data


@pytest.mark.asyncio
async def test_get_workflow_by_id_success(
    workflow_repo, mock_collection, tenant_info
):
    """Test successful workflow retrieval by mocking the collection."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    org_id = tenant_info.org.id
    expected_doc = {
        "_id": workflow_id,
        "thread_id": "test_thread_123",
        "display_name": "Test Workflow",
        "org_id": org_id,
        "creator_id": ObjectId("507f1f77bcf86cd799439013"),
        "created_at": datetime.now(UTC),
        "updated_at": datetime.now(UTC),
    }
    mock_collection.find_one = AsyncMock(return_value=expected_doc)

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.get_workflow_by_id(
            workflow_id, tenant_info
        )

        # Assert
        assert result is not None
        assert isinstance(result, Workflow)
        assert result.id == workflow_id
        assert result.display_name == "Test Workflow"
        mock_collection.find_one.assert_called_once_with(
            {"_id": workflow_id, "org_id": org_id}
        )


@pytest.mark.asyncio
async def test_get_workflow_by_id_not_found(
    workflow_repo, mock_collection, tenant_info
):
    """Test workflow retrieval failure when document is not found."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    org_id = tenant_info.org.id
    mock_collection.find_one = AsyncMock(return_value=None)

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.get_workflow_by_id(
            workflow_id, tenant_info
        )

        # Assert
        assert result is None
        mock_collection.find_one.assert_called_once_with(
            {"_id": workflow_id, "org_id": org_id}
        )


@pytest.mark.asyncio
async def test_list_and_count_workflows_success(
    workflow_repo, mock_collection, tenant_info
):
    """Test successful workflow listing and counting."""
    # Arrange
    org_id = tenant_info.org.id
    mock_workflow_docs = [
        {
            "_id": ObjectId(),
            "thread_id": "test_thread_1",
            "display_name": "Workflow 1",
            "org_id": org_id,
            "creator_id": ObjectId(),
            "created_at": datetime.now(UTC),
            "updated_at": datetime.now(UTC),
        },
        {
            "_id": ObjectId(),
            "thread_id": "test_thread_2",
            "display_name": "Workflow 2",
            "org_id": org_id,
            "creator_id": ObjectId(),
            "created_at": datetime.now(UTC),
            "updated_at": datetime.now(UTC),
        },
    ]

    mock_cursor = Mock()
    mock_cursor.skip.return_value = mock_cursor
    mock_cursor.limit.return_value = mock_cursor
    mock_cursor.sort.return_value = mock_cursor
    mock_cursor.to_list = AsyncMock(return_value=mock_workflow_docs)
    mock_collection.find.return_value = mock_cursor

    mock_collection.count_documents = AsyncMock(return_value=15)

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        workflows = await workflow_repo.list_workflows(
            tenant_info, limit=10, offset=5
        )
        total_count = await workflow_repo.count_workflows(tenant_info)

        # Assert List
        assert len(workflows) == 2
        assert all(isinstance(w, Workflow) for w in workflows)
        assert workflows[0].display_name == "Workflow 1"

        query_filter = {"org_id": org_id}
        mock_collection.find.assert_called_once_with(query_filter)
        mock_cursor.skip.assert_called_once_with(5)
        mock_cursor.limit.assert_called_once_with(10)
        mock_cursor.sort.assert_called_once_with("created_at", -1)
        mock_cursor.to_list.assert_called_once_with(length=10)

        # Assert Count
        assert total_count == 15
        mock_collection.count_documents.assert_called_once_with(query_filter)


@pytest.mark.asyncio
async def test_list_workflows_with_display_name_filter(
    workflow_repo, mock_collection, tenant_info
):
    """Test workflow listing with display name prefix filter."""
    # Arrange
    org_id = tenant_info.org.id
    mock_cursor = Mock()
    mock_cursor.skip.return_value = mock_cursor
    mock_cursor.limit.return_value = mock_cursor
    mock_cursor.sort.return_value = mock_cursor
    mock_cursor.to_list = AsyncMock(return_value=[])
    mock_collection.find.return_value = mock_cursor

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.list_workflows(
            tenant_info=tenant_info,
            display_name_prefix="Test",
            limit=10,
            offset=0
        )

        # Assert
        assert result == []
        expected_filter = {
            "org_id": org_id,
            "display_name": {"$regex": "^Test", "$options": "i"}
        }
        mock_collection.find.assert_called_once_with(expected_filter)


@pytest.mark.asyncio
async def test_list_workflows_with_creator_ids_filter(
    workflow_repo, mock_collection, tenant_info
):
    """Test workflow listing with creator IDs filter."""
    # Arrange
    org_id = tenant_info.org.id
    creator_ids = [ObjectId("507f1f77bcf86cd799439013"), ObjectId("507f1f77bcf86cd799439014")]
    mock_cursor = Mock()
    mock_cursor.skip.return_value = mock_cursor
    mock_cursor.limit.return_value = mock_cursor
    mock_cursor.sort.return_value = mock_cursor
    mock_cursor.to_list = AsyncMock(return_value=[])
    mock_collection.find.return_value = mock_cursor

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.list_workflows(
            tenant_info=tenant_info,
            creator_ids=creator_ids,
            limit=10,
            offset=0
        )

        # Assert
        assert result == []
        expected_filter = {
            "org_id": org_id,
            "creator_id": {"$in": creator_ids}
        }
        mock_collection.find.assert_called_once_with(expected_filter)


@pytest.mark.asyncio
async def test_list_workflows_with_combined_filters(
    workflow_repo, mock_collection, tenant_info
):
    """Test workflow listing with both display name and creator IDs filters."""
    # Arrange
    org_id = tenant_info.org.id
    creator_ids = [ObjectId("507f1f77bcf86cd799439013")]
    mock_cursor = Mock()
    mock_cursor.skip.return_value = mock_cursor
    mock_cursor.limit.return_value = mock_cursor
    mock_cursor.sort.return_value = mock_cursor
    mock_cursor.to_list = AsyncMock(return_value=[])
    mock_collection.find.return_value = mock_cursor

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.list_workflows(
            tenant_info=tenant_info,
            display_name_prefix="MyWorkflow",
            creator_ids=creator_ids,
            limit=5,
            offset=10
        )

        # Assert
        assert result == []
        expected_filter = {
            "org_id": org_id,
            "display_name": {"$regex": "^MyWorkflow", "$options": "i"},
            "creator_id": {"$in": creator_ids}
        }
        mock_collection.find.assert_called_once_with(expected_filter)
        mock_cursor.skip.assert_called_once_with(10)
        mock_cursor.limit.assert_called_once_with(5)


@pytest.mark.asyncio
async def test_count_workflows_with_display_name_filter(
    workflow_repo, mock_collection, tenant_info
):
    """Test workflow counting with display name prefix filter."""
    # Arrange
    org_id = tenant_info.org.id
    mock_collection.count_documents = AsyncMock(return_value=5)

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.count_workflows(
            tenant_info=tenant_info,
            display_name_prefix="Test"
        )

        # Assert
        assert result == 5
        expected_filter = {
            "org_id": org_id,
            "display_name": {"$regex": "^Test", "$options": "i"}
        }
        mock_collection.count_documents.assert_called_once_with(expected_filter)


@pytest.mark.asyncio
async def test_count_workflows_with_creator_ids_filter(
    workflow_repo, mock_collection, tenant_info
):
    """Test workflow counting with creator IDs filter."""
    # Arrange
    org_id = tenant_info.org.id
    creator_ids = [ObjectId("507f1f77bcf86cd799439013")]
    mock_collection.count_documents = AsyncMock(return_value=3)

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.count_workflows(
            tenant_info=tenant_info,
            creator_ids=creator_ids
        )

        # Assert
        assert result == 3
        expected_filter = {
            "org_id": org_id,
            "creator_id": {"$in": creator_ids}
        }
        mock_collection.count_documents.assert_called_once_with(expected_filter)


@pytest.mark.asyncio
async def test_list_workflows_empty(
    workflow_repo, mock_collection, tenant_info
):
    """Test workflow listing returns empty list when no documents found."""
    # Arrange
    mock_cursor = Mock()
    mock_cursor.skip.return_value = mock_cursor
    mock_cursor.limit.return_value = mock_cursor
    mock_cursor.sort.return_value = mock_cursor
    mock_cursor.to_list = AsyncMock(return_value=[])  # No documents
    mock_collection.find.return_value = mock_cursor

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.list_workflows(tenant_info=tenant_info)

        # Assert
        assert result == []


@pytest.mark.asyncio
async def test_list_workflows_failure(
    workflow_repo, mock_collection, tenant_info
):
    """Test workflow listing handles database exceptions gracefully."""
    # Arrange
    mock_collection.find.side_effect = Exception("Database connection error")

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.list_workflows(tenant_info=tenant_info)

        # Assert (implementation returns empty list on error)
        assert result == []


@pytest.mark.asyncio
async def test_delete_workflow_success(
    workflow_repo, mock_collection, tenant_info
):
    """Test successful workflow deletion."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    org_id = tenant_info.org.id

    mock_result = Mock()
    mock_result.deleted_count = 1
    mock_collection.delete_one = AsyncMock(return_value=mock_result)

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.delete_workflow(
            workflow_id=workflow_id, tenant_info=tenant_info
        )

        # Assert
        assert result is True
        mock_collection.delete_one.assert_called_once_with(
            {"_id": workflow_id, "org_id": org_id}
        )


@pytest.mark.asyncio
async def test_delete_workflow_not_found(
    workflow_repo, mock_collection, tenant_info
):
    """Test workflow deletion when workflow is not found."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")

    mock_result = Mock()
    mock_result.deleted_count = 0  # Simulate no document found
    mock_collection.delete_one = AsyncMock(return_value=mock_result)

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.delete_workflow(
            workflow_id=workflow_id, tenant_info=tenant_info
        )

        # Assert
        assert result is False


@pytest.mark.asyncio
async def test_delete_workflow_failure(
    workflow_repo, mock_collection, tenant_info
):
    """Test workflow deletion handles database exceptions gracefully."""
    # Arrange
    workflow_id = ObjectId("507f1f77bcf86cd799439011")
    mock_collection.delete_one = AsyncMock(
        side_effect=Exception("Database connection error")
    )

    with patch.object(
        workflow_repo, "_get_collection", return_value=mock_collection
    ):
        # Act
        result = await workflow_repo.delete_workflow(
            workflow_id=workflow_id, tenant_info=tenant_info
        )

        # Assert (implementation returns False on error)
        assert result is False
