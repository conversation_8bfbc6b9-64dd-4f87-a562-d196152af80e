"""
Message processing framework for Chainlit chat applications.

This package provides a Chain of Responsibility pattern for handling different
types of messages and user interactions in a scalable, extensible way.
"""

from .base import (
    MessageContext,
    MessageHandler,
    MessageResult,
    ProcessingResult,
)
from .processor import MessageProcessor

__all__ = [
    "MessageContext",
    "MessageResult",
    "ProcessingResult",
    "MessageHandler",
    "MessageProcessor",
]
