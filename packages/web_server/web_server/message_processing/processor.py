"""
Main message processor that orchestrates handlers.
"""

import asyncio
import json

import chainlit as cl

from common.log import debug, error, info
from common.log.log import warn
from common.sandbox.base import Runtime, RuntimeType
from common.sandbox.file_watcher import ProcessedEvent
from common.utils.gcs_utils import convert_signed_to_gcs
from web_server.constants.chat import WindowMessageType
from web_server.session.runtime_manager import RuntimeSessionManager
from web_server.session.state_manager import get_state_manager

from .base import (
    MessageContext,
    MessageHandler,
    MessageResult,
    ProcessingResult,
)


class MessageProcessor:
    """Main message processor that orchestrates handlers"""

    def __init__(self, session_manager: RuntimeSessionManager):
        self.session_manager = session_manager
        self.handlers: list[MessageHandler] = []
        self.state_manager = get_state_manager()

    def register_handlers(self, handlers: list[MessageHandler]) -> None:
        """Register multiple handlers at once"""
        for handler in handlers:
            self.handlers.append(handler)
        info(f"Registered {len(handlers)} handlers")

    async def process_message(self, message: cl.Message) -> ProcessingResult:
        """Process a message through the handler chain"""

        # Build context
        session_id = cl.context.session.id
        try:
            runtime = await self._get_runtime(message, session_id)
            # Start file watcher for E2B runtime if not already active
            if (
                runtime
                and runtime.runtime_type == RuntimeType.E2B
                and not runtime.file_watcher.is_active()
            ):
                runtime.start_file_watcher(self._create_file_event_callback())

        except Exception as e:
            error(f"Failed to get runtime for session {session_id}: {e}")
            await cl.Message(
                content="❌ Failed to initialize runtime environment. Please try again."
            ).send()
            return ProcessingResult.error(e)

        context = MessageContext(
            message=message,
            session_id=session_id,
            runtime=runtime,
        )

        info(
            f"Processing message in {self.state_manager.get_app_mode()} mode with {len(self.handlers)} handlers"
        )

        # Process through handler chain
        for handler in self.handlers:
            try:
                if await handler.can_handle(context):
                    info(f"Processing message with {handler.name}")
                    result = await handler.handle(context)

                    if result.result == MessageResult.HANDLED:
                        info(f"Message handled by {handler.name}")
                        return result
                    elif result.result == MessageResult.ERROR:
                        error(f"Error in {handler.name}: {result.exception}")
                        if result.exception:
                            await self._handle_error(context, result.exception)
                        return result
                    elif result.result == MessageResult.CONTINUE:
                        debug(
                            f"{handler.name} returned CONTINUE, trying next handler"
                        )
                        # Continue to next handler
                        continue

            except Exception as e:
                error(f"Unhandled error in {handler.name}: {e}")
                await self._handle_error(context, e)
                return ProcessingResult.error(e)

        # No handler processed the message
        info("No handler processed the message")
        await cl.Message(
            content="🤔 I'm not sure how to handle that message. Type `/help` for available commands."
        ).send()

        return ProcessingResult.handled("No handler found")

    async def _handle_error(
        self, context: MessageContext, exception: Exception
    ) -> None:
        """Handle processing errors"""
        error_message = f"❌ Error processing your message: {str(exception)}"
        error(
            f"Error processing message for session {context.session_id}: {exception}"
        )

        await cl.Message(content=error_message).send()

    async def _get_runtime(
        self, message: cl.Message, session_id: str
    ) -> Runtime | None:
        """Get the runtime for the context"""
        content = message.content.lower().strip()
        # Debug or Command messages don't need a runtime (except for /run to execute workflows)
        if (
            content.startswith("/")
            and not content.startswith("/run")
            and not content.startswith("/build")
        ):
            return None
        # Blocking call - wait for runtime to be ready
        loading_msg = cl.Message(content="🤔 **Processing your message...**")
        await loading_msg.send()
        try:
            # Send window message that runtime initialization has started
            state_manager = get_state_manager()
            await state_manager.construct_and_send_window_message(
                type=WindowMessageType.RUNTIME_INITIALIZATION_STARTED,
            )
            runtime = await self.session_manager.get_runtime(session_id)
            await state_manager.construct_and_send_window_message(
                type=WindowMessageType.RUNTIME_INITIALIZED,
            )
            return runtime
        except Exception as e:
            raise RuntimeError(f"Failed to get runtime: {e}") from e
        finally:
            await loading_msg.remove()

    def _create_file_event_callback(self):
        """Create a callback function for file events."""

        def on_file_event(processed_event: ProcessedEvent):
            """Handle file system events and send to client."""
            try:
                # Send ProcessedEvent to client via window message
                main_loop = asyncio.get_running_loop()

                async def send_file_event():
                    try:
                        # Convert ProcessedEvent to JSON-serializable format
                        if processed_event.signed_url:
                            # Add the temp file path to the state workflow
                            self.state_manager.add_temp_file_path(
                                processed_event.name,
                                convert_signed_to_gcs(
                                    processed_event.signed_url
                                ),
                            )
                        event_data = processed_event.to_dict()
                        await self.state_manager.construct_and_send_window_message(
                            type=WindowMessageType.FILE_CHANGE_EVENT,
                            event_data=event_data,
                        )

                        # Check if todos.json was modified and render the task list
                        if processed_event.name == "todos.json":
                            info(
                                "todos.json detected in file event, rendering task list"
                            )
                            # Create a dummy message for the task list
                            dummy_msg = cl.Message(content="")
                            await self._render_todo_tasklist(dummy_msg)

                    except Exception as e:
                        warn(f"Error sending file event window message: {e}")

                # Schedule in main event loop
                asyncio.run_coroutine_threadsafe(send_file_event(), main_loop)

            except Exception as e:
                warn(f"Error handling file event: {e}")

        return on_file_event

    async def _render_todo_tasklist(self, msg: cl.Message) -> None:
        """Render the todo list from todos.json as a Chainlit TaskList."""
        try:
            runtime = await self.session_manager.get_runtime(
                session_id=self.state_manager.get_session_id()
            )

            # Read the todos.json file
            try:
                todos_json = await runtime.read_file("todos.json")
                todos_data = json.loads(todos_json)
            except Exception as e:
                error(f"No todos.json file found or error reading: {e}")
                return

            # Create Chainlit TaskList
            task_list = cl.TaskList()

            # Add tasks from todos.json
            for todo_item in todos_data:
                # Map our status to Chainlit TaskStatus
                status_mapping = {
                    "Not Started": cl.TaskStatus.READY,
                    "In Progress": cl.TaskStatus.RUNNING,
                    "Completed": cl.TaskStatus.DONE,
                }

                task = cl.Task(
                    title=todo_item["name"],
                    status=status_mapping.get(
                        todo_item["status"], cl.TaskStatus.READY
                    ),
                )
                if task.status == cl.TaskStatus.RUNNING:
                    task.forId = msg.id
                await task_list.add_task(task)

            # Calculate completion percentage
            total_tasks = len(todos_data)
            completed_tasks = sum(
                1 for todo in todos_data if todo["status"] == "Completed"
            )

            if total_tasks > 0:
                completion_percentage = int(
                    (completed_tasks / total_tasks) * 100
                )
                if completion_percentage < 100:
                    task_list.status = f"Progress: ({completion_percentage}%)"
                else:
                    task_list.status = "Complete"
                await task_list.send()

        except Exception as e:
            error(f"Error rendering todo tasklist: {e}")
