"""
Handler for normal conversation in build mode.
"""

import os

import chainlit as cl
from langchain.schema.runnable.config import RunnableConfig
from langchain_core.messages import AIMessage, HumanMessage

from common.log import error
from web_server.constants.env import DEV_MODE, LOCAL_MODE
from web_server.core.config import server_config
from web_server.session.state_manager import AppMode

from ..base import MessageContext, MessageHandler, ProcessingResult


class BuildModeHandler(MessageHandler):
    """Handles normal conversation in build mode"""

    def __init__(self, graph):
        """Initialize with the LangGraph instance"""
        super().__init__()
        self.graph = graph

    async def can_handle(self, context: MessageContext) -> bool:
        return self.state_manager.get_app_mode() == AppMode.BUILD

    async def handle(self, context: MessageContext) -> ProcessingResult:
        """Handle build mode conversation"""
        # Show loading message
        loading_msg = cl.Message(
            content="🔨 **Building your automation...** Analyzing your request and generating the workflow."
        )
        await loading_msg.send()

        try:
            # Process the request through the graph
            result = await self._process_build_request(context)
            await loading_msg.remove()

            # Send SOP and completion flag if available
            await self._send_sop_messages(context, result)

            # Send response to user
            await self._send_build_response(context, result)

            return ProcessingResult.handled()

        except Exception as e:
            error(f"Error in build mode processing: {e}")
            await loading_msg.remove()
            await cl.Message(
                content=f"❌ Error processing request: {str(e)}"
            ).send()
            return ProcessingResult.error(e)

    async def _process_build_request(self, context: MessageContext) -> dict:
        """Process the build request through the LangGraph with streaming"""
        # Prepare the conversation state
        # NOTE: we don't need to store graph state in cl.user_session. As the checkpointer in graph is already storing the state.
        browser_session = getattr(context.runtime, "browser_session", None)
        browser_connection_url = (
            browser_session.connect_url if browser_session else ""
        )
        current_state = {
            "chainlit_session_id": context.session_id,
            "browser_connection_url": browser_connection_url or "",
        }

        # Add file information to user content if attachments exist
        user_content = context.content
        if isinstance(user_content, list):
            user_content = " ".join(str(item) for item in user_content)

        # Add file information to user content if attachments exist
        attachments = self.state_manager.get_attachments()
        if attachments:
            file_info = []
            for attachment in attachments:
                filename = os.path.basename(attachment.path)
                file_info.append(f"- {filename} (path: {attachment.path})")
            user_content += "\n\n**Available files:**\n" + "\n".join(file_info)

        # Add the user message to the state
        user_message = HumanMessage(content=user_content)
        # Add the user message to the state, this append to the messages stored in graph checkpointer
        current_state["messages"] = [user_message]

        # Process through the graph with streaming
        config = {"thread_id": context.session_id}
        runnable_config = RunnableConfig(configurable=config)
        # We will have the loading messages that we build in our on_message handler
        # So we will only add the langchain callback handler for local or dev mode
        if server_config.mode == LOCAL_MODE or server_config.mode == DEV_MODE:
            # Add the langchain callback handler for local or dev mode
            # This can be used for debugging
            runnable_config = RunnableConfig(
                configurable=config, callbacks=[cl.LangchainCallbackHandler()]
            )

        # Process streaming chunks
        result = None
        status_msg = None

        async for stream_chunk in self.graph.astream(
            current_state,
            config=runnable_config,
            stream_mode=["custom", "values"],
            subgraphs=True,
        ):
            if isinstance(stream_chunk, tuple) and len(stream_chunk) == 3:
                namespace, mode, data = stream_chunk
                if (
                    mode == "custom"
                    and isinstance(data, dict)
                    and "status_update" in data
                ):
                    # Remove previous status message and display new one
                    if status_msg:
                        await status_msg.remove()
                    status_message = data["status_update"]
                    status_msg = cl.Message(content=status_message)
                    await status_msg.send()
                elif mode == "values":
                    result = data

        # Remove final status message
        if status_msg:
            await status_msg.remove()

        if result is None:
            result = {"messages": []}
        elif "messages" not in result:
            result["messages"] = []

        return result

    async def _send_sop_messages(
        self, context: MessageContext, result: dict
    ) -> None:
        """Send SOP and completion flag messages"""
        # Send the SOP to the user
        sop = result.get("automation_plan_sop")
        if sop:
            await cl.Message(content=sop, tags=["sop"]).send()

        # Send SOP completion flag
        is_sop_complete = result.get("is_sop_complete")
        if is_sop_complete:
            await cl.Message(
                content="SOP is complete", tags=["sop.complete_flag"]
            ).send()

    async def _send_build_response(
        self, context: MessageContext, result: dict
    ) -> None:
        """Send the build response to the user"""
        full_message_history = result.get("messages", [])

        # Find the last user-facing message
        user_facing_message = None
        for message_item in reversed(full_message_history):
            # Skip messages with tool calls or empty content
            if (
                isinstance(message_item, AIMessage)
                and not message_item.tool_calls
            ):
                if message_item.content:
                    if (
                        isinstance(message_item.content, list)
                        and not message_item.content
                    ):
                        continue
                    user_facing_message = message_item
                    break

        if user_facing_message and user_facing_message.content:
            final_content = user_facing_message.content
            display_text = ""
            if isinstance(final_content, list):
                for item in final_content:
                    if isinstance(item, dict) and item.get("type") == "text":
                        display_text += item.get("text", "")
            elif isinstance(final_content, str):
                display_text = final_content

            if display_text.strip():
                await cl.Message(content=display_text.strip()).send()
            else:
                await cl.Message(
                    content="✅ Processing complete, but the final message was empty."
                ).send()
        else:
            await cl.Message(
                content="✅ Processing complete, but no final user-facing message was found."
            ).send()
