"""
Conditional build handler that switches between standard and Orbot handlers.
"""

from agent.claude_code_agent import ClaudeC<PERSON><PERSON><PERSON>
from common.log import info, warn
from web_server.session.state_manager import (
    AppMode,
    StateKey,
)

from ..base import MessageContext, MessageHandler, ProcessingResult
from .build_mode_handler_with_orbot import BuildMode<PERSON><PERSON>lerWithOrbot
from .flow import Flow
from .single_agent_mode_handler import SingleAgentModeHandler


class ConditionalBuildHandler(MessageHandler):
    """Handler that conditionally uses either standard or Orbot build handler."""

    def __init__(self, standard_agent: ClaudeCliAgent):
        """Initialize with both handlers."""
        super().__init__()
        self.standard_handler = SingleAgentModeHandler(standard_agent)
        self.orbot_handler = BuildModeHandlerWithOrbot()
        self.flow = Flow()

    async def can_handle(self, context: MessageContext) -> bool:
        """Can handle if we're in build mode."""
        return self.state_manager.get_app_mode() == AppMode.BUILD

    async def handle(self, context: MessageContext) -> ProcessingResult:
        """Handle the message by choosing the appropriate handler"""
        use_orbot = self.state_manager.get_state_value(
            StateKey.USE_ORBOT, False
        )

        use_flow = self.state_manager.get_state_value(
            StateKey.USE_FLOW, False
        )

        # Use Orbot handler if enabled and available
        if use_orbot:
            info("Using Orbot build handler")
            return await self.orbot_handler.handle(context)
        elif use_flow:
            info("Using Flow build handler")
            return await self.flow.handle(context)
        else:
            # Fall back to standard handler
            if use_orbot:
                warn(
                    "Orbot requested but not available, using standard handler"
                )
            else:
                info("Using standard build handler")
            return await self.standard_handler.handle(context)


# Export the handler
__all__ = ["ConditionalBuildHandler"]
