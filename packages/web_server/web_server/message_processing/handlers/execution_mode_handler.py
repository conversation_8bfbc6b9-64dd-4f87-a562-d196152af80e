"""
Handler for messages in execution mode (when not executing a specific workflow).
"""

import chainlit as cl

from web_server.session.state_manager import AppMode

from ..base import MessageContext, MessageHandler, ProcessingResult


class ExecutionModeHandler(MessageHandler):
    """Handles messages in execution mode when no workflow is selected"""

    def __init__(self):
        """Initialize the execution mode handler."""
        super().__init__()

    async def can_handle(self, context: MessageContext) -> bool:
        return (
            self.state_manager.get_app_mode() == AppMode.EXECUTION
            and not self.state_manager.is_execution_mode()
        )  # Not in active workflow execution

    async def handle(self, context: MessageContext) -> ProcessingResult:
        """Handle execution mode messages"""
        await cl.Message(
            content="⚙️ **You're in execution mode.** Use `/run` to execute a workflow.\n\n💡 **Available commands:**\n• `/run` - Execute a workflow\n• `/build mode` - Switch to build mode\n• `/single agent mode` - Switch to single agent mode"
        ).send()

        return ProcessingResult.continue_processing()
