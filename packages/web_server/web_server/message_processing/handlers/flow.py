"""
Build mode handler using turn-by-turn agent (Orbot) for intelligent automation building.
"""

import asyncio
import json
from typing import cast
import uuid

from bson import ObjectId
import chainlit as cl
from orbot.turn_by_turn_agent import TurnByTurnAgent, TurnResult, TurnType

from common.constants.e2b_constants import REPO_NAME
from common.log import debug, error, info, warn
from common.models.workflow import (
    WorkflowGeneratedFiles,
    WorkflowGitRepository,
    WorkflowStatus,
)
from common.sandbox.base import Runtime
from web_server.constants.chat import WindowMessageType
from web_server.constants.env import GENERATED_MANIFEST_FILE_NAME
from web_server.message_processing.handlers.file_upload_handler import (
    LOCAL_STORAGE_PATH,
)
from web_server.services.user_file_service import UserFileService
from web_server.services.workflow_service import WorkflowService
from web_server.session.runtime_manager import get_runtime_session_manager
from web_server.session.state_manager import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Workflow
from web_server.utils.execution_log_parser import parse_sdk_review_event

from ..base import <PERSON><PERSON><PERSON><PERSON><PERSON>, MessageHandler, ProcessingResult


class Flow(MessageHandler):
    """Handler for build mode using turn-by-turn conversation approach with Orbot."""

    async def can_handle(self, context: MessageContext) -> bool:
        return self.state_manager.get_app_mode() == AppMode.BUILD

    async def handle(self, context: MessageContext) -> ProcessingResult:
        """Handle build mode conversation using flow agent."""
        try:
            info(
                f"Processing build mode message with Orbot: {context.content[:100]}{'...' if len(context.content) > 100 else ''}"
            )

            # If no workflow exists for this session, create an initial workflow
            existing_workflow = await self.state_manager.get_workflow(
                cl.context.session.thread_id
            )
            if not existing_workflow:
                # Check if this is a shared workflow access
                shared_workflow_id = (
                    await self._check_for_shared_workflow_access(context)
                )
                if shared_workflow_id:
                    await self._handle_shared_workflow_access(
                        context, shared_workflow_id
                    )
                    # Don't process the special message further - just return
                    return ProcessingResult.handled()
                else:
                    await self._create_initial_workflow(context)

            # Get or create Orbot agent for this session
            agent = cast(
                TurnByTurnAgent,
                self.state_manager.get_state_value(StateKey.FLOW),
            )

            # Append uploaded elements information to the content
            enriched_content = await self._enrich_content_with_uploads(context)

            # Process message through turn-by-turn agent
            execution_id = f"test-{cl.context.session.thread_id}-{uuid.uuid4()}"
            main_loop = asyncio.get_running_loop()

            # Register the execution ID with the bash tool
            await agent.tool_registry.reregister_bash_tool_with_envs(
                envs={"VA_EXECUTION_ID": execution_id}
            )

            # Async callback for real-time log streaming generated from redis stream for this execution or from runtime
            def on_log_received(log_entry):
                """Callback for real-time log streaming into step"""
                # Only handle stdout logs (no stderr as requested)
                if log_entry.stream_type == "stdout":
                    content = log_entry.content
                    parsed_log = parse_sdk_review_event(
                        log_entry.content, execution_id
                    )
                    if not parsed_log:
                        return
                    content = f"**{parsed_log.get('user_message', content)}**"
                    web_app_message = json.dumps(parsed_log, ensure_ascii=False)

                    # If the log entry is a review request, send it as a window message
                    async def send_window_message_with_error_handling():
                        """Send the log line to the browser window via state manager"""
                        try:
                            await self.state_manager.send_window_message(
                                web_app_message
                            )
                        except Exception as e:
                            warn(f"Error sending window message: {e}")

                    try:
                        asyncio.run_coroutine_threadsafe(
                            send_window_message_with_error_handling(),
                            main_loop,
                        )
                    except Exception as e:
                        warn(f"Error scheduling task: {e}")

            # Wrap the action inside the log_streamer to capture the logs
            if context.log_streamer:
                context.log_streamer.start(
                    on_log_callback=on_log_received,
                    execution_id=execution_id,
                )
            async for turn_result in agent.process_user_message(
                enriched_content,
            ):
                await self._handle_turn_result(turn_result)
            if context.log_streamer:
                context.log_streamer.stop()

            # Check if the workflow is complete
            await self._handle_workflow_completion(context)

            return ProcessingResult.handled()

        except Exception as e:
            error(f"Error in build mode handler: {e}")
            await cl.Message(content=f"❌ Error: {str(e)}").send()
            return ProcessingResult.error(e)

    async def _handle_turn_result(self, turn_result: TurnResult) -> None:
        """Handle individual turn results as Chainlit steps."""

        debug(
            f"Processing turn result - type: {turn_result.turn_type.value}, tool: {turn_result.tool_name}"
        )

        if turn_result.turn_type in (
            TurnType.AI_THINKING,
            TurnType.AI_RESPONSE,
        ):
            # Create thinking step
            async with cl.Step(name="thinking", type="llm") as thinking_step:
                thinking_step.output = turn_result.content
        elif turn_result.turn_type == TurnType.TOOL_CALL:
            if turn_result.tool_name == "message_notify_user":
                # message already been sent in the tool
                return
            elif turn_result.tool_name == "message_ask_user":
                await self._handle_ask_user_tool(turn_result)
            else:
                # Handle file-related tools with special element creation
                await self._handle_tools_turn_result(turn_result)

        elif turn_result.turn_type == TurnType.SPEAKER_SELECTION:
            return

        elif turn_result.turn_type == TurnType.ERROR:
            raise Exception(f"{str(turn_result.content)}")

    async def _handle_ask_user_tool(self, turn_result: TurnResult) -> None:
        """Handle message_ask_user tool with file elements."""
        try:
            # Parse tool call input to extract attachments
            tool_input = turn_result.metadata.get("tool_call_input", {})
            if isinstance(tool_input, str):
                try:
                    tool_input = json.loads(tool_input)
                except json.JSONDecodeError:
                    tool_input = {}

            # Extract the question text and attachments
            question_text = tool_input.get("text", turn_result.content)
            attachments = tool_input.get("attachments", [])

            # Ensure attachments is a list
            if isinstance(attachments, str):
                attachments = [attachments]
            elif not isinstance(attachments, list):
                attachments = []

            # Create file elements for attachments
            elements = []
            if attachments:
                runtime = await get_runtime_session_manager().get_runtime(
                    session_id=self.state_manager.get_session_id()
                )

                for attachment in attachments:
                    try:
                        element = await self._create_attachment_element(
                            attachment, runtime
                        )
                        if element:
                            elements.append(element)
                    except Exception as e:
                        debug(f"Failed to create element for {attachment}: {e}")

            # The actual AskUserMessage is handled by the tool itself,
            # but we create a step to show the tool execution
            await cl.Message(
                content=question_text, elements=elements if elements else None
            ).send()

        except Exception as e:
            error(f"Error handling ask_user tool: {e}")
            # Fall back to standard tool handling
            async with cl.Step(
                name="message_ask_user", type="tool"
            ) as tool_step:
                tool_step.output = turn_result.content

    async def _handle_tools_turn_result(self, turn_result: TurnResult) -> None:
        """Handle tool calls that may involve files and create appropriate elements."""

        if turn_result.tool_name == "update_todos":
            # Send progress updates to user.
            await cl.Message(content=turn_result.content).send()
            # Task list will be automatically rendered by file watcher when todos.json changes
        else:
            async with cl.Step(
                name=turn_result.tool_name or "tool", type="tool"
            ) as tool_step:
                tool_step.input = turn_result.metadata.get(
                    "tool_call_input", {}
                )
                tool_step.output = f"{turn_result.content[:1000]}{'...' if len(turn_result.content) > 1000 else ''}"
                # Check if this is a file-related tool and create elements if needed
                file_tools = ["edit", "write_file", "read_file"]
                if turn_result.tool_name in file_tools:
                    try:
                        # Parse tool input to get file path
                        tool_input = turn_result.metadata.get(
                            "tool_call_input", {}
                        )
                        if isinstance(tool_input, str):
                            try:
                                tool_input = json.loads(tool_input)
                            except json.JSONDecodeError:
                                tool_input = {}

                        # Extract file path based on tool type
                        file_path = tool_input.get("relative_path")

                        # Create file element if we have a valid file path and the operation was successful
                        if file_path and "success" in turn_result.metadata.get(
                            "status", ""
                        ):
                            runtime = await get_runtime_session_manager().get_runtime(
                                session_id=self.state_manager.get_session_id()
                            )

                            element = await self._create_attachment_element(
                                file_path, runtime
                            )
                            if element:
                                tool_step.elements = [element]
                    except Exception as e:
                        warn(
                            f"Failed to create file element for {turn_result}: {e}"
                        )

    async def _create_attachment_element(self, attachment: str, runtime):
        """
        Create a Chainlit element based on the attachment type.

        Args:
            attachment: The attachment path or URL
            runtime: The runtime instance for reading files

        Returns:
            A Chainlit element (Text for URLs, File for files) or None if creation fails
        """
        try:
            runtime = await get_runtime_session_manager().get_runtime(
                session_id=self.state_manager.get_session_id()
            )
            if attachment.startswith(("http://", "https://")):
                # Create a text element for URLs
                return cl.Text(
                    name=attachment.split("/")[-1] or "Link",
                    content=attachment,
                    display="inline",
                )
            else:
                # Read file content using runtime
                try:
                    file_content = await runtime.read_bytes(attachment)
                except Exception:
                    # Try reading as text and encode
                    file_text = await runtime.read_file(attachment)
                    file_content = file_text.encode("utf-8")

                # Create file element
                return cl.File(
                    name=attachment.split("/")[-1],
                    content=file_content,
                    display="inline",
                )
        except Exception as e:
            debug(f"Error creating element for {attachment}: {e}")
            return None

    async def _enrich_content_with_uploads(
        self, context: MessageContext
    ) -> str:
        """Append information about uploaded files to the message content.

        Also saves uploaded files to the runtime workspace so they can be accessed by the agent.

        Args:
            context: The message context containing the original message and elements

        Returns:
            The enriched content with file upload information appended
        """
        enriched_content = context.content

        # Check if there are any uploaded elements
        if context.elements:
            file_info_lines = ["\n\n[Uploaded Files:]"]

            for element in context.elements:
                if hasattr(element, "name") and hasattr(element, "content"):
                    # Handle file elements
                    file_name = getattr(element, "name", "Unknown")

                    # Build file info string
                    file_info = f"- {file_name}"
                    file_info += (
                        f" - Saved to: {LOCAL_STORAGE_PATH + file_name}"
                    )
                    file_info_lines.append(file_info)

            # Append file information to the content
            enriched_content += "\n".join(file_info_lines)
            info(
                f"Enriched message with {len(context.elements)} uploaded element(s)"
            )

        return enriched_content

    async def _create_initial_workflow(self, context: MessageContext) -> None:
        """Create an initial workflow entry at the beginning of the session."""
        try:
            tenant_info = await self._get_tenant_info()
            if not (
                hasattr(cl.context.session, "user")
                and cl.context.session.user
                and hasattr(cl.context.session.user, "id")
            ):
                error("User session not available for workflow creation")
                return
            user_id = ObjectId(str(cl.context.session.user.id))
            workflow_service = WorkflowService()

            # Use the first user message (truncated) as the initial display name
            initial_display_name = (
                context.content[:75] if context.content else "New Workflow"
            )

            created_workflow = await workflow_service.create_workflow(
                creator_id=user_id,
                tenant_info=tenant_info,
                display_name=initial_display_name,
                thread_id=cl.context.session.thread_id,
            )

            if created_workflow:
                # Create initial workflow view model and set UI state
                initial_workflow = Workflow(
                    name=initial_display_name,
                    mongo_id=created_workflow.id,
                    thread_id=cl.context.session.thread_id,
                    is_executable=False,  # Not executable until completion
                    commit_hash="",  # No commit hash yet
                    bb_context_id=context.runtime.browser_session.context_id
                    if context.runtime and context.runtime.browser_session
                    else "",
                )
                await self.state_manager.set_workflow(initial_workflow)

                # TODO: clean up logging in the future
                info(f"Initial workflow created with ID: {created_workflow.id}")
            else:
                warn("Failed to create initial workflow entry.")

        except Exception as e:
            error(f"Error creating initial workflow: {e}")

    async def _check_for_shared_workflow_access(
        self, context: MessageContext
    ) -> str | None:
        """Check if the user is trying to access a shared workflow.

        This is detected by looking for a special marker in the first message
        that indicates the workflow ID to load.
        """
        # Check if the message contains a workflow ID marker
        if context.content.startswith(
            "[LOAD_WORKFLOW:"
        ) and context.content.endswith("]"):
            # Extract workflow ID from the marker
            workflow_id = context.content[len("[LOAD_WORKFLOW:") : -1]
            return workflow_id
        return None

    async def _handle_shared_workflow_access(
        self, context: MessageContext, workflow_id: str
    ) -> None:
        """Handle when a user accesses a shared workflow."""
        try:
            tenant_info = await self._get_tenant_info()
            if not (
                hasattr(cl.context.session, "user")
                and cl.context.session.user
                and hasattr(cl.context.session.user, "id")
            ):
                error("User session not available for shared workflow access")
                return

            user_id = ObjectId(str(cl.context.session.user.id))
            workflow_service = WorkflowService()

            # Access the workflow and update user_thread_mapping
            updated_workflow = await workflow_service.access_workflow(
                workflow_id=ObjectId(workflow_id),
                user_id=user_id,
                thread_id=cl.context.session.thread_id,
                tenant_info=tenant_info,
            )

            if updated_workflow:
                # Create workflow view model and set UI state
                workflow_model = Workflow(
                    name=updated_workflow.display_name or "Shared Workflow",
                    mongo_id=updated_workflow.id,
                    thread_id=cl.context.session.thread_id,
                    is_executable=updated_workflow.status
                    == WorkflowStatus.COMPLETED,
                    commit_hash=updated_workflow.git_repository.commit_hash
                    if updated_workflow.git_repository
                    else "",
                    bb_context_id=updated_workflow.bb_context_id or "",
                )
                await self.state_manager.set_workflow(workflow_model)

                # Silently link the workflow to this thread - no message sent
                info(f"User {user_id} silently accessed shared workflow {workflow_id}")
            else:
                await cl.Message(
                    content="❌ Unable to load the requested workflow. It may not exist or you may not have access to it."
                ).send()

        except Exception as e:
            error(f"Error handling shared workflow access: {e}")
            await cl.Message(
                content="❌ An error occurred while loading the workflow. Please try again."
            ).send()

    async def _handle_workflow_completion(
        self, context: MessageContext
    ) -> None:
        """Process the workflow completion"""
        # Check if the workflow is complete
        if not context.runtime:
            return

        async def check_for_workflow_completion(
            runtime: Runtime,
        ) -> bytes | None:
            """Check if the workflow is complete"""
            # Check if the MANIFEST.JSON file exists in the runtime_workspace/session_id/ directory
            try:
                file_bytes = await runtime.read_bytes(
                    GENERATED_MANIFEST_FILE_NAME
                )
                if file_bytes:
                    info("Workflow manifest found, hence saving the workflow")
                    return file_bytes
                else:
                    return None
            except Exception:
                return None

        manifest_bytes = await check_for_workflow_completion(context.runtime)
        if not manifest_bytes:
            return

        commit_hash = await context.runtime.push_workspace()
        # Parse manifest json to get workflow name
        manifest_json = json.loads(manifest_bytes)
        workflow_name = manifest_json.get("name", "Unknown Workflow Name")

        # Get tenant info and user info from chainlit context
        tenant_info = await self._get_tenant_info()
        if not (
            hasattr(cl.context.session, "user")
            and cl.context.session.user
            and hasattr(cl.context.session.user, "id")
        ):
            error("User session not available for workflow completion")
            return
        user_id = ObjectId(str(cl.context.session.user.id))
        workflow_service = WorkflowService()
        user_file_service = UserFileService()

        # Retrieve the workflow object that was created and saved in the session state
        session_workflow = await self.state_manager.get_workflow(
            cl.context.session.thread_id
        )
        if not session_workflow:
            await cl.Message(
                content=f"⚠️ Failed to update workflow **{workflow_name}**. Please try again."
            ).send()
            return

        workflow_id = session_workflow.mongo_id

        # Save manifest file to user_files
        manifest_file_id = await user_file_service.create_user_file(
            content=manifest_bytes,
            filename="manifest.json",
            file_type="manifest",
            workflow_id=workflow_id,
            creator_id=user_id,
            tenant_info=tenant_info,
        )

        # Save SOP file if it exists
        sop_file_id = None
        sop_bytes = await context.runtime.read_bytes("SOP.md")
        if sop_bytes:
            sop_file_id = await user_file_service.create_user_file(
                content=sop_bytes,
                filename="SOP.md",
                file_type="sop",
                workflow_id=workflow_id,
                creator_id=user_id,
                tenant_info=tenant_info,
            )

        # Create generated files reference
        generated_files = WorkflowGeneratedFiles(
            sop_file_id=sop_file_id,
            manifest_file_id=manifest_file_id,
        )

        # Update workflow with git repository and generated files
        git_repository = WorkflowGitRepository(
            repo_url=REPO_NAME,
            commit_hash=commit_hash,
        )

        updated_workflow = await workflow_service.update_workflow(
            workflow_id=workflow_id,
            tenant_info=tenant_info,
            display_name=workflow_name,
            git_repository=git_repository,
            generated_files=generated_files,
            status=WorkflowStatus.COMPLETED,
            bb_context_id=context.runtime.browser_session.context_id
            if context.runtime.browser_session
            else "",
        )
        if not updated_workflow:
            await cl.Message(
                content=f"⚠️ Failed to update workflow **{workflow_name}**. Please try again."
            ).send()
            return

        # Create session workflow object
        workflow = Workflow(
            name=workflow_name,
            mongo_id=updated_workflow.id,
            thread_id=cl.context.session.thread_id,
            is_executable=True,
            commit_hash=commit_hash,
            bb_context_id=context.runtime.browser_session.context_id
            if context.runtime.browser_session
            else "",
        )

        # Set the workflow in the chainlit state manager
        await self.state_manager.set_workflow(workflow)
        await self.state_manager.construct_and_send_window_message(
            type=WindowMessageType.WORKFLOW_READY,
        )
        await cl.Message(
            content=f"💾 **Workflow Saved!** '{workflow_name}' is now available for execution.\n\n💡"
        ).send()

        # TODO: Also update the thread name to the workflow name


# Export the handler
__all__ = ["Flow"]
