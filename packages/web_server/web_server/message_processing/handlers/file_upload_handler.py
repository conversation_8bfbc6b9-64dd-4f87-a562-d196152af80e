"""
Handler for file attachments and uploads.
"""

import os

import chainlit as cl

from common.log import error, info
from web_server.session.runtime_manager import get_runtime_session_manager
from web_server.session.state_manager import Attachment

from ..base import Message<PERSON>ontext, MessageHand<PERSON>, ProcessingResult

LOCAL_STORAGE_PATH = 'local_storage/'

class FileUploadHandler(MessageHandler):
    """Handles file attachments and uploads"""

    def __init__(self):
        """Initialize the file upload handler."""
        super().__init__()

    async def can_handle(self, context: MessageContext) -> bool:
        return len(context.elements) > 0

    async def handle(self, context: MessageContext) -> ProcessingResult:
        """Handle file uploads"""
        try:
            new_attachments = await self._process_file_uploads(context)
            if new_attachments:
                await self._send_upload_confirmation(context, new_attachments)
            return (
                ProcessingResult.continue_processing()
            )  # Continue processing the message
        except Exception as e:
            error(f"Error processing file uploads: {e}")
            return ProcessingResult.error(e)

    async def _process_file_uploads(
        self, context: MessageContext
    ) -> list[Attachment]:
        """Process file uploads and return list of new attachment paths"""
        new_attachments: list[Attachment] = []

        for element in context.elements:
            if element.name:
                # Create the temp directory if it doesn't exist
                relative_path = LOCAL_STORAGE_PATH + element.name
                file_content = None
                if element.path:
                    with open(element.path, "rb") as src:
                        file_content = src.read()
                else:
                    if element.content is None:
                        continue  # Skip this element
                    file_content = element.content

                # Copy the file to the runtime
                try:
                    runtime = await get_runtime_session_manager().get_runtime(
                        session_id=context.session_id
                    )
                    await runtime.write_file(relative_path, file_content)
                    new_attachments.append(
                        Attachment(
                            name=element.name,
                            path=relative_path,
                            size=len(file_content),
                            mime=element.mime,
                        )
                    )
                    info(f"Uploaded file to runtime: {element.name}")
                except Exception as e:
                    error(f"Failed to write file to runtime: {e}")

        if new_attachments:
            # Update session attachments using state manager
            for attachment in new_attachments:
                self.state_manager.add_attachment(attachment)

        return new_attachments

    async def _send_upload_confirmation(
        self, context: MessageContext, new_attachments: list[Attachment]
    ) -> None:
        """Send confirmation message for uploaded files"""
        file_names = [
            os.path.basename(attachment.path) for attachment in new_attachments
        ]

        await cl.Message(
            content=f"📎 **Files uploaded to runtime:** {', '.join(file_names)}"
        ).send()
