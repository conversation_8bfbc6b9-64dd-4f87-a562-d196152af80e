"""
Handler for workflow execution when in execution mode with a selected workflow.
"""

import asyncio
from datetime import UTC, datetime
import json
import os
import re
from typing import Any
import uuid

from bson import ObjectId
import chainlit as cl
import httpx

from common.log import error, info, warn
from common.models.execution import Execution as ExecutionModel
from common.models.execution import (
    ExecutionBrowserService,
    ExecutionIdentifierType,
    ExecutionStatus,
    ExecutionTimestamps,
    ExecutionTriggeredBy,
    ExecutionWorkflowContext,
)
from common.models.execution import ExecutionLog as ExecutionLogModel
from common.sandbox.base import Runtime
from common.sandbox.log_streamer import FilterType
from common.services.execution_log_service import ExecutionLogService
from common.storage.gcs import get_gcs_client
from execution.v1.services.execution_service import ExecutionService
from web_server.constants.chat import WindowMessageType
from web_server.core.config import server_config
from web_server.session.state_manager import StateKey, Workflow
from web_server.utils.execution_log_parser import create_execution_log_filter

from ..base import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ProcessingResult


def format_string_alphanumeric_dash(input_string: str) -> str:
    """
    Format a string to contain only alphanumeric characters and dashes.
    Args:
        input_string: The string to format
    Returns:
        A formatted string containing only alphanumeric characters and dashes
    """
    string_with_dashes = input_string.replace(" ", "-")
    formatted_string = re.sub(r"[^a-zA-Z0-9-]", "", string_with_dashes)
    return formatted_string


async def execute_workflow_async(
    runtime: Runtime,
    execution_id: str,
    **kwargs: Any,
) -> str:
    """Execute a workflow located at *path* inside *runtime*.

    This helper encapsulates the whole lifecycle: it reads the workflow's
    ``manifest.json`` to obtain metadata, prepares the execution environment
    variables, runs the workflow's ``main.py`` with the provided *kwargs*, and
    returns a human-readable result string.

    Args:
        path: Directory that contains the workflow (incl. ``manifest.json``).
        runtime: Runtime instance capable of `read_file` and `run_code`.
        **kwargs: Arbitrary parameters that will be JSON-encoded and exposed to
                  the workflow via the ``VA_INPUT`` environment variable.

    Returns:
        A result string indicating success or formatted error details.
    """

    # Load and validate manifest
    file_content = await runtime.read_file("manifest.json")

    if not file_content:
        error("Manifest not found in the root of the runtime")
        raise FileNotFoundError("manifest.json not found in workflow directory")

    manifest = json.loads(file_content)
    workflow_name = manifest.get("name", "Unnamed Workflow")

    # Prepare environment variables for the workflow run
    envs = {
        "VA_EXECUTION_ID": execution_id,
        "VA_INPUT": json.dumps(kwargs),
    }

    # Propagate Browserbase connection URL if a browser session is active
    if getattr(runtime, "browser_session", None):
        bs = runtime.browser_session  # type: ignore[attr-defined]
        if bs and getattr(bs, "connect_url", None):  # Safeguard for typing
            envs["CONNECTION_URL"] = bs.connect_url

    # Run the workflow within the provided runtime
    try:
        result = await runtime.run_code(None, "main.py", None, envs=envs)
    except Exception as exc:  # pragma: no cover – runtime exceptions
        error(f"Workflow '{workflow_name}' execution failed: {exc}")
        return f"Error executing workflow: {str(exc)}"

    # Handle non-zero exit status
    if result.returncode != 0:
        error(
            f"Workflow '{workflow_name}' execution failed with return code {result.returncode}: {result.stderr}"
        )
        return f"Error executing workflow: Exit code {result.returncode}\n{result.stderr}"

    info(f"Workflow '{workflow_name}' executed successfully")

    # Close the dedicated browser session
    try:
        runtime.release_browser_session()
    except Exception as exc:  # pragma: no cover
        warn(f"Failed to release browser session: {exc}")

    return f"Workflow {workflow_name} executed with {kwargs} successfully!\n"


class WorkflowExecutionHandler(MessageHandler):
    """Handles workflow execution when in execution mode with a selected workflow"""

    def __init__(self):
        """Initialize the workflow execution handler."""
        super().__init__()
        # No external recording processor – we handle rrweb upload directly here.

    async def can_handle(self, context: MessageContext) -> bool:
        return (
            self.state_manager.get_state_value(StateKey.EXECUTION_MODE, False)
            and await self.state_manager.get_workflow(
                cl.context.session.thread_id
            )
            is not None
        )

    async def handle(self, context: MessageContext) -> ProcessingResult:
        """Execute the selected workflow"""
        selected_workflow = await self.state_manager.get_workflow(
            cl.context.session.thread_id
        )

        tenant_info = None
        try:
            tenant_info = await self._get_tenant_info()
            org_id: str | None = str(tenant_info.org.id)
        except Exception:
            org_id = None

        # Safety check - should not happen given can_handle logic
        if not selected_workflow:
            return ProcessingResult.error(Exception("No workflow selected"))

        attachments = self.state_manager.get_attachments()
        if not len(attachments):
            await cl.Message(
                content="❌ No attachments found. Please upload a file to run the workflow."
            ).send()
            return ProcessingResult.handled()

        # Ensure a browser session is available before creating execution
        try:
            if context.runtime and context.runtime.config.use_browser_session:
                # No existing browser session – create one without BB context id
                await context.runtime.create_browser_session(
                    selected_workflow.bb_context_id
                )

                # Update state manager with live view and runtime info
                if context.runtime.browser_session:
                    new_live_view_url = (
                        context.runtime.browser_session.debugger_url
                    )
                    await self.state_manager.construct_and_send_window_message(
                        type=WindowMessageType.BB_SESSION_LIVE_VIEW_URL,
                        url=new_live_view_url,
                    )
                self.state_manager.update_runtime_info(context.runtime)

        except Exception as exc:
            warn(
                f"Failed to prepare browser session before execution creation: {exc}"
            )

        try:
            execution_id = await self._create_execution_record(
                selected_workflow, context.runtime
            )
        except Exception as exc:
            warn(f"Could not create execution record: {exc}")
            execution_id = f"chat_session_execution_{uuid.uuid4()}"

        self._update_execution_id_state(execution_id)

        # Show loading message
        loading_msg = cl.Message(
            content=f"⚙️ Running workflow '{selected_workflow.name}' with your input..."
        )
        await loading_msg.send()

        try:
            # Capture the session ID (if any) BEFORE running the workflow because
            # execute_workflow_async() releases the session which sets
            # runtime.browser_session to None. We still need the ID later to fetch
            # the recording.
            session_id_for_recording: str | None = None
            if context.runtime and context.runtime.browser_session is not None:
                session_id_for_recording = context.runtime.browser_session.id

            async with cl.Step(
                name="Running workflow...", type="run", default_open=True
            ) as step:
                log_content = []
                # Get the current event loop for thread-safe scheduling
                main_loop = asyncio.get_running_loop()

                def on_log_received(log_entry):
                    """Callback for real-time log streaming into step"""
                    if log_entry.stream_type == "stderr":
                        log_content.append(log_entry.content)

                        async def send_window_message_with_error_handling():
                            """Send the log line to the browser window via state manager"""
                            try:
                                await self.state_manager.send_window_message(
                                    log_entry.content
                                )
                            except Exception as e:
                                warn(f"Error sending window message: {e}")

                        async def append_execution_log_if_event():
                            """Append execution log to DB for EXECUTION_EVENT lines."""
                            try:
                                data = json.loads(log_entry.content)
                                if (
                                    isinstance(data, dict)
                                    and data.get("type")
                                    == WindowMessageType.EXECUTION_EVENT.value
                                ):
                                    status_txt = data.get("status", "")
                                    desc_txt = data.get("description", "")
                                    concat_desc = (
                                        f"{status_txt}: {desc_txt}".strip(": ")
                                    )

                                    if not org_id:
                                        return

                                    exec_log = ExecutionLogModel(
                                        execution_id=ObjectId(execution_id),
                                        workflow_id=ObjectId(
                                            selected_workflow.mongo_id
                                        ),
                                        org_id=ObjectId(org_id),
                                        description=concat_desc,
                                    )

                                    service = ExecutionLogService()
                                    await service.append_execution_log(exec_log)
                            except Exception as exc:
                                warn(f"Error appending execution log: {exc}")

                        # Schedule coroutine in main event loop from different thread
                        try:
                            asyncio.run_coroutine_threadsafe(
                                send_window_message_with_error_handling(),
                                main_loop,
                            )
                            asyncio.run_coroutine_threadsafe(
                                append_execution_log_if_event(), main_loop
                            )
                        except Exception as e:
                            warn(f"Error scheduling task: {e}")

                # Start log streaming with callback and custom execution-log filter
                if context.log_streamer is not None:
                    context.log_streamer.start(
                        on_log_callback=on_log_received,
                        filter_type=FilterType.CUSTOM,
                        custom_filter_func=create_execution_log_filter(),
                        execution_id=execution_id,
                    )

                result = await self._execute_workflow(
                    context, selected_workflow, execution_id
                )
                # Reset execution state
                self._reset_execution_state()
                # Stop log streaming
                if context.log_streamer is not None:
                    context.log_streamer.stop()

                # Mark execution as completed
                try:
                    await self._update_execution_status(
                        execution_id, ExecutionStatus.COMPLETED
                    )
                except Exception as exc:
                    warn(f"Failed to update execution status: {exc}")

                # Set final step output
                if log_content:
                    step.output = "\n".join(log_content)
                else:
                    step.output = "No logs captured"

                # Kick-off background task that fetches rrweb recording, uploads
                # it to GCS and updates the execution record with the GCS url.
                if session_id_for_recording:
                    info(
                        f"Kicking off background task to process rrweb recording for execution {execution_id}, session {session_id_for_recording}"
                    )
                    asyncio.create_task(
                        self._process_rrweb_recording(
                            execution_id=execution_id,
                            session_id=session_id_for_recording,
                            tenant_info=tenant_info,
                        )
                    )

            # Display results
            await cl.Message(
                content=f"✅ **Workflow Results:**\n\n{result}"
            ).send()

            await loading_msg.remove()

            return ProcessingResult.handled()

        except Exception as e:
            error(f"Workflow execution error: {e}")

            # Mark execution as failed
            try:
                await self._update_execution_status(
                    execution_id, ExecutionStatus.FAILED
                )
            except Exception as exc:
                warn(f"Failed to update execution status on error: {exc}")

            await loading_msg.remove()
            await cl.Message(
                content=f"❌ Error executing workflow: {str(e)}"
            ).send()
            self._reset_execution_state()
            return ProcessingResult.error(e)

    async def _execute_workflow(
        self, context: MessageContext, workflow: Workflow, execution_id: str
    ) -> str:
        """Execute the workflow inside the runtime and return (result, session_id)."""

        if context.runtime is None:
            raise RuntimeError("Runtime is not initialized")

        # Currently we only support passing the last uploaded CSV path.
        attachments = self.state_manager.get_attachments()
        workflow_params = {
            "csv_file_path": (
                f"{str(context.runtime.runtime_path)}/{attachments[-1].path}"
                if attachments
                else None
            ),
        }

        return await execute_workflow_async(
            context.runtime, execution_id, **workflow_params
        )

    async def _create_execution_record(
        self, workflow: Workflow, runtime: Runtime | None = None
    ) -> str:
        """Create a new execution record in the database and return its id."""

        try:
            tenant_info = await self._get_tenant_info()

            execution_service = ExecutionService()

            browser_service = None
            if runtime and runtime.browser_session is not None:
                browser_service = ExecutionBrowserService(
                    session_id=runtime.browser_session.id,
                    context_id=getattr(
                        runtime.browser_session, "context_id", None
                    ),
                )

            execution = ExecutionModel(
                org_id=tenant_info.org.id,
                workflow_id=ObjectId(workflow.mongo_id),
                workflow_context=ExecutionWorkflowContext(
                    commit_hash=workflow.commit_hash
                ),
                triggered_by=ExecutionTriggeredBy(
                    type=ExecutionIdentifierType.USER,
                    identifier=str(cl.context.session.user.identifier)
                    if hasattr(cl.context.session, "user")
                    and cl.context.session.user
                    else "anonymous",
                ),
                status=ExecutionStatus.RUNNING,
                timestamps=ExecutionTimestamps(started_at=datetime.now(UTC)),
                browser_service=browser_service,
            )

            created_execution = await execution_service.create_execution(
                execution, tenant_info
            )

            return str(created_execution.id)

        except Exception as exc:
            raise RuntimeError(
                f"Failed to create execution record: {exc}"
            ) from exc

    def _update_execution_id_state(self, execution_id: str | None) -> None:
        """Update execution ID state"""
        self.state_manager.set_execution_id(execution_id)

    def _reset_execution_state(self) -> None:
        """Reset execution mode state"""
        self.state_manager.update_state(StateKey.EXECUTION_MODE, False)
        self._update_execution_id_state(None)

    async def _update_execution_status(
        self, execution_id: str, status: ExecutionStatus
    ) -> None:
        """Update execution status in DB."""

        try:
            tenant_info = await self._get_tenant_info()
        except Exception:
            return

        execution_service = ExecutionService()
        await execution_service.update_execution_status(
            execution_id=str(execution_id),
            tenant_info=tenant_info,
            status=status.value,
        )

    # ------------------------------------------------------------------
    # rrweb recording handling methods
    # ------------------------------------------------------------------

    async def _process_rrweb_recording(
        self,
        *,
        execution_id: str,
        session_id: str,
        tenant_info,
        max_wait_sec: int = 30,
        poll_every: float = 5.0,
    ) -> None:
        """Fetch Browserbase rrweb recording, upload to GCS, update DB & notify FE."""

        if tenant_info is None:
            warn(
                "[WorkflowExecutionHandler] Cannot process rrweb recording – tenant_info is None."
            )
            return

        api_key = os.getenv("BB_API_KEY", "")
        if not api_key:
            warn(
                "[WorkflowExecutionHandler] BB_API_KEY not set – skipping rrweb recording upload"
            )
            return

        url = f"https://api.browserbase.com/v1/sessions/{session_id}/recording"
        headers = {"X-BB-API-Key": api_key}

        async def _fetch_recording() -> str | None:
            try:
                async with httpx.AsyncClient(timeout=30) as client:
                    resp = await client.get(url, headers=headers)
                    if resp.status_code == 200:
                        txt = resp.text
                        if txt and txt.strip() not in ["", "{}", "[]"]:
                            return txt
                    return None
            except Exception as exc:  # pragma: no cover
                warn(
                    f"[WorkflowExecutionHandler] Failed to fetch rrweb recording: {exc}"
                )
                return None

        # Wait until recording ready
        deadline = asyncio.get_event_loop().time() + max_wait_sec
        attempt = 1
        recording_json: str | None = None
        while asyncio.get_event_loop().time() < deadline:
            recording_json = await _fetch_recording()
            if recording_json is not None:
                break
            await asyncio.sleep(poll_every)
            attempt += 1

        if recording_json is None:
            error(
                f"[WorkflowExecutionHandler] rrweb recording not available for session {session_id} after timeout"
            )
            return

        # Upload to GCS
        base_bucket = server_config.chainlit_default_bucket
        bucket_name = tenant_info.get_bucket_name(base_bucket)
        gcs_path = f"executions/{execution_id}/rrweb.json"

        try:
            gcs_client = get_gcs_client()
            await gcs_client.upload(
                bucket=bucket_name,
                path=gcs_path,
                content=recording_json.encode("utf-8"),
                metadata={"Content-Type": "application/json"},
            )
            gcs_url = f"gs://{bucket_name}/{gcs_path}"
        except Exception as exc:
            error(
                f"[WorkflowExecutionHandler] Failed to upload rrweb recording: {exc}"
            )
            return

        # Update execution record
        try:
            exec_service = ExecutionService()
            await exec_service.update_execution_recording_url(
                execution_id=str(execution_id),
                tenant_info=tenant_info,
                recording_url=gcs_url,
            )
        except Exception as exc:
            error(
                f"[WorkflowExecutionHandler] Failed to update execution with rrweb url: {exc}"
            )
            return

        # Notify frontend
        await self.state_manager.construct_and_send_window_message(
            type=WindowMessageType.RECORDING_READY,
            execution_id=execution_id,
        )
