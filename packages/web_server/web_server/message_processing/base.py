"""
Base classes for the message processing framework.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

import chainlit as cl

from common.sandbox.base import Runtime
from web_server.data.orby_chainlit_data_layer import TenantInfoMixin
from web_server.session.state_manager import get_state_manager


@dataclass
class MessageContext:
    """Encapsulates all message processing context"""

    message: cl.Message
    session_id: str
    runtime: Runtime | None = None

    @property
    def content(self) -> str:
        """Get message content as string"""
        return self.message.content or ""

    @property
    def elements(self) -> list:
        """Get message elements (files, etc.)"""
        return self.message.elements or []

    @property
    def log_streamer(self):
        if self.runtime is None:
            return None
        return self.runtime.log_streamer


class MessageResult(Enum):
    """Result of message processing"""

    HANDLED = "handled"  # Message was handled, stop processing
    CONTINUE = "continue"  # Continue to next handler
    ERROR = "error"  # Error occurred


@dataclass
class ProcessingResult:
    """Result of processing a message"""

    result: MessageResult
    message: str | None = None
    exception: Exception | None = None

    @classmethod
    def handled(cls, message: str | None = None) -> "ProcessingResult":
        """Create a handled result"""
        return cls(MessageResult.HANDLED, message)

    @classmethod
    def continue_processing(
        cls, message: str | None = None
    ) -> "ProcessingResult":
        """Create a continue result"""
        return cls(MessageResult.CONTINUE, message)

    @classmethod
    def error(
        cls, exception: Exception, message: str | None = None
    ) -> "ProcessingResult":
        """Create an error result"""
        return cls(MessageResult.ERROR, message, exception)


class MessageHandler(TenantInfoMixin, ABC):
    """Abstract base class for message handlers"""

    def __init__(self):
        TenantInfoMixin.__init__(self)
        self.state_manager = get_state_manager()

    @abstractmethod
    async def can_handle(self, context: MessageContext) -> bool:
        """Check if this handler can process the message"""
        pass

    @abstractmethod
    async def handle(self, context: MessageContext) -> ProcessingResult:
        """Process the message"""
        pass

    @property
    def name(self) -> str:
        """Get handler name for logging"""
        return self.__class__.__name__
