"""
Factory for creating and configuring message processors.
"""

from agent.claude_code_agent import <PERSON><PERSON><PERSON><PERSON><PERSON>
from web_server.message_processing.handlers.command_handler import (
    ChainlitCommandHandler,
)
from web_server.session.runtime_manager import RuntimeSessionManager

from .handlers import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ExecutionModeHandler,
    FileUploadHandler,
    WorkflowExecutionHandler,
)
from .processor import MessageProcessor


def create_message_processor(
    session_manager: RuntimeSessionManager, single_agent: ClaudeCliAgent
) -> MessageProcessor:
    """
    Create a message processor with all default handlers registered.

    Args:
        session_manager: The runtime session manager
        graph: The LangGraph instance for build mode processing

    Returns:
        Configured MessageProcessor instance
    """
    processor = MessageProcessor(session_manager)

    # Register default handlers in priority order
    handlers = [
        <PERSON>lit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(),
        FileUploadHandler(),
        WorkflowExecutionHand<PERSON>(),
        Conditional<PERSON><PERSON><PERSON><PERSON><PERSON>(single_agent),
        ExecutionModeHandler(),
    ]

    processor.register_handlers(handlers)

    return processor
