import json

from common.models.user import User
from common.utils.auth import AuthPayload, get_auth_payload_from_http_headers
from web_server.constants.env import LOCAL_MODE
from web_server.core.config import server_config


def get_auth_payload_wrapper(headers: dict[str, str]) -> AuthPayload:
    """
    Wrapper to get the auth payload from the request.

    This is a workaround to to login to the system when we are running in local mode.

    We will fetch the user from the local_token cookie and return the auth payload instead
    of extracting it from the headers which are set by the auth middleware.
    """
    cookie_str = headers.get("cookie")
    if server_config.mode == LOCAL_MODE and is_local_token_present(cookie_str):
        if cookie_str:
            cookies = cookie_str.split("; ")
            for cookie in cookies:
                if "local_token=" in cookie:
                    local_token = cookie.split("local_token=")[1]
                    user_json = json.loads(local_token)
                    dbUser = User(**user_json)

                    org_id = None
                    if len(dbUser.org_ids) > 0:
                        org_id = dbUser.org_ids[0]
                    return AuthPayload(
                        user_email=dbUser.email,
                        user_id=dbUser.id,
                        org_id=org_id,
                    )
        raise ValueError("Local token not found in the cookie")
    return get_auth_payload_from_http_headers(headers)


def is_local_token_present(cookieString: str | None) -> bool:
    """
    Check if the local_token is present in the cookie string.
    """
    return cookieString and "local_token=" in cookieString
