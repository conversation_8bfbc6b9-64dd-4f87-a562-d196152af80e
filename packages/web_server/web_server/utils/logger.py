import logging
import sys


class SocketIOFilter(logging.Filter):
    """
    Filter to suppress repetitive Socket.IO polling logs while keeping other logs.
    """

    def filter(self, record):
        # Filter out Socket.IO polling requests
        if hasattr(record, "getMessage"):
            message = record.getMessage()
            # Check for Socket.IO patterns
            if any(
                pattern in message
                for pattern in [
                    "/socket.io/?EIO=4&transport=polling",
                    "socket.io",
                    "transport=polling",
                    "transport=websocket",
                ]
            ):
                return False
        return True


class HealthCheckFilter(logging.Filter):
    """
    Filter to suppress repetitive health check logs while keeping other logs.
    """

    def filter(self, record):
        # Filter out health check requests
        if hasattr(record, "getMessage"):
            message = record.getMessage()
            # Check for health check patterns
            if any(
                pattern in message
                for pattern in [
                    "GET /api/v1/va/health HTTP/1.1\" 200",
                    "/api/v1/va/health",
                    "/healthcheck",
                    "health_check",
                    "\"GET /health ",
                    "\"GET /healthz ",
                    "\"GET /healthcheck ",
                    "\" 200 OK",
                ]
            ):
                # Additional check to avoid false positives on 200 OK
                # Only filter if it contains health-related keywords along with 200 OK
                if "\" 200 OK" in message:
                    return not any(
                        health_keyword in message.lower()
                        for health_keyword in ["health", "healthz", "healthcheck"]
                    )
                return False
        return True


class InfoToStdoutErrorToStderr(logging.StreamHandler):
    """
    A logging handler that sends INFO, WARNING, and DEBUG messages to stdout and ERROR and CRITICAL messages to stderr.

    In GKE logs by default are sent to stderr which is not what we want for INFO and WARNING messages.
    """

    def emit(self, record):
        if record.levelno >= logging.ERROR:
            self.stream = sys.stderr
        else:
            self.stream = sys.stdout
        super().emit(record)
