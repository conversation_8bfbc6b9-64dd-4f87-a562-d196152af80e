"""
Execution log parsing utilities for workflow execution handlers.

This module contains parsing logic for runtime execution logs, converting raw
stdout lines into structured JSON events for the UI.
"""

import json
import re

from web_server.constants.chat import WindowMessageType

# ---------------------------------------------------------------------------
# Unified regex (match + parse)
# ---------------------------------------------------------------------------

# Unified regex: the trailing ": description" segment is now mandatory for all statuses.
EXEC_EVENT_REGEX = re.compile(
    r"^(?P<timestamp>\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})(?:,\d{3})?"  # Timestamp
    r"\s+-\s+"
    r"(?P<level>INFO|ERROR|WARN|WARNING|DEBUG)"  # Log level
    r"\s+-\s+"
    r"(?P<status>STARTING|PROGRESS|SUCCESS|COMPLETED|FAILED|WARNING|TASK_COMPLETED_SUCCESSFULLY|TASK_FAILED)"  # Status
    r"\s*:\s*(?P<description>.+)$",  # Mandatory ": desc" segment
)

# regex for identfying messages with "review_requested" from VA SDK
REVIEW_REQUESTED_REGEX = re.compile(
    r"\breview_requested\s+(?P<execution_id>[^\s]+)\s+(?P<review_id>[^\s]+)(?P<user_message>.*)"
)

# regex for capturing execution lifecycle markers (mark_start / mark_stop)
LIFECYCLE_MARK_REGEX = re.compile(r"\bmark_(start|stop)\s+(?P<exec_id>\S+)")

# ---------------------------------------------------------------------------
# Parsing helpers
# ---------------------------------------------------------------------------


def parse_execution_event(
    line: str, execution_id: str
) -> dict[str, str] | None:
    """Return parsed execution-event fields if *line* matches the expected
    format; otherwise return ``None``.

    Output keys: ``timestamp`` (YYYY-MM-DD HH:MM:SS), ``status`` (lower-level
    execution status), and ``description`` (free-text).
    """

    match = EXEC_EVENT_REGEX.match(line)
    if not match:
        return None

    ts = match.group("timestamp")
    level = match.group("level")
    status = match.group("status")
    description_group = match.group("description")

    # Ignore lines without a description.
    if description_group is None:
        return None

    desc = description_group.strip()

    return {
        "execution_id": execution_id,
        "type": WindowMessageType.EXECUTION_EVENT.value,
        "timestamp": ts,
        "level": level,
        "status": status,
        "description": desc,
    }


def parse_sdk_review_event(
    line: str, execution_id: str
) -> dict[str, str] | None:
    """Return parsed SDK logs which are displayed as part of execution-event,
    but contain specific keyword "review_requested".
    """
    match = REVIEW_REQUESTED_REGEX.search(line)

    if not match:
        return None

    review_id = match.group("review_id").strip()
    user_message = match.group("user_message").strip()

    return {
        "execution_id": execution_id,
        "type": WindowMessageType.REVIEW_REQUEST.value,
        "review_id": review_id,
        "user_message": user_message,
    }


def parse_lifecycle_event(line: str) -> dict[str, str] | None:
    """Detect logs like 'mark_start <id>' or 'mark_stop <id>'."""

    match = LIFECYCLE_MARK_REGEX.search(line)
    if not match:
        return None

    event_raw = match.group(1).upper()  # START or STOP
    exec_id = match.group("exec_id").strip()

    return {
        "type": WindowMessageType.EXECUTION_LIFECYCLE.value,
        "execution_id": exec_id,
        "event": event_raw,
    }


def create_execution_log_filter(execution_id: str = ""):
    """Create a custom filter function for LogStreamer that parses execution events.

    Args:
        execution_id: The execution ID to use for parsed events

    Returns:
        A filter function that can be used with LogStreamer's custom filtering
    """

    def execution_log_filter(stream_type: str, content: str):
        """Transform raw runtime log lines into structured JSON strings.

        Only stdout is considered; all parsing helpers are applied in
        order until the first match. If the line does not correspond to
        a recognised execution event, return ``None`` to skip it.
        """
        if stream_type != "stderr":
            return None

        parsed = (
            parse_execution_event(content, execution_id)
            or parse_sdk_review_event(content, execution_id)
            or parse_lifecycle_event(content)
        )

        if parsed is None:
            return None

        # Return a JSON-encoded string; this will be forwarded to the
        # callback and, subsequently, to the UI.
        return json.dumps(parsed, ensure_ascii=False)

    return execution_log_filter
