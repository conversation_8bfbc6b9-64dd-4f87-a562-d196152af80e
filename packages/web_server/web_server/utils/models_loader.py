from collections.abc import Callable, Iterator
from importlib import import_module
from inspect import isclass
from pathlib import Path
from typing import Any

from beanie import Document

APPLICATION_DIR = Path(__file__).parent.parent  # App's root directory


def get_modules(module: str) -> Iterator[str]:
    """Returns all .py modules in given file_dir as
    a generator of dot separated string values.
    """
    file_dir = Path(APPLICATION_DIR / module)
    idx_app_root = len(APPLICATION_DIR.parts) - 1
    modules = [
        f for f in list(file_dir.rglob("*.py")) if not f.stem == "__init__"
    ]
    for filepath in modules:
        yield (".".join(filepath.parts[idx_app_root:])[0:-3])


def dynamic_loader(module: str, compare: Callable[[object], bool]) -> list[Any]:
    """Iterates over all .py files in `module` directory,
    finding all classes that match `compare` function.
    """
    items = []
    for mod in get_modules(module):
        imported_module = import_module(mod)
        if hasattr(imported_module, "__all__"):
            objs = [
                getattr(imported_module, obj) for obj in imported_module.__all__
            ]
            items += [o for o in objs if compare(o) and o not in items]
    return items


def is_beanie_model(item: object) -> bool:
    """Determines if item is a Beanie Document object."""
    return isclass(item) and issubclass(item, Document)


def get_beanie_models() -> list[Any]:
    """Dynamic Beanie model finder."""
    return dynamic_loader("models", is_beanie_model)
