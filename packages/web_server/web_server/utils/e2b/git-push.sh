#!/bin/bash

# Git push script that takes repo name, work_dir, and branch
# Usage: git-push <repo_name> <work_dir> <branch> [--no-sudo]
# Note: Use --no-sudo only for local development.
# Returns: commit hash to stdout, all other messages to stderr
# 
# Authentication: SSH Keys only
# - SSH keys should be available in ~/.ssh/
# - Use SSH URLs: **************:user/repo.git
# 
# This script is available globally and can be run from any directory

set -e

# Parse arguments
USE_SUDO=true
ARGS=()

while [[ $# -gt 0 ]]; do
    case $1 in
        --no-sudo)
            USE_SUDO=false
            shift
            ;;
        *)
            ARGS+=("$1")
            shift
            ;;
    esac
done

# Check if all required arguments are provided
if [ ${#ARGS[@]} -ne 3 ]; then
    echo "Usage: $0 <repo_name> <work_dir> <branch> [--no-sudo]" >&2
    echo "Example: $0 **************:user/repo.git /path/to/work main" >&2
    echo "Example: $0 **************:user/repo.git /path/to/work main --no-sudo" >&2
    echo "" >&2
    echo "Authentication: SSH Keys only" >&2
    echo "- SSH keys should be available in ~/.ssh/" >&2
    echo "- Use SSH URLs: **************:user/repo.git" >&2
    echo "" >&2
    exit 1
fi

REPO_NAME="${ARGS[0]}"
WORK_DIR="${ARGS[1]}"
BRANCH="${ARGS[2]}"
GIT_EMAIL="<EMAIL>"
GIT_NAME="orby-bhavesh"

# Check if work directory exists
if [ ! -d "$WORK_DIR" ]; then
    echo "Error: Work directory '$WORK_DIR' does not exist" >&2
    exit 1
fi

# Check if SSH URL is provided
if [[ ! "$REPO_NAME" =~ ^git@ ]]; then
    echo "Error: Only SSH URLs are supported (**************:user/repo.git)" >&2
    exit 1
fi

# Change to work directory
cd "$WORK_DIR"

# SSH authentication is pre-configured in the Docker image
echo "Using SSH authentication..." >&2

# Use sudo for git operations based on configuration and user privileges
if [ "$USE_SUDO" = false ] || [ "$EUID" -eq 0 ]; then
    GIT_CMD="git"
else
    GIT_CMD="sudo git"
fi

# Initialize git repo if it doesn't exist
if [ ! -d ".git" ]; then
    $GIT_CMD init >&2
    $GIT_CMD config user.name "$GIT_NAME" >&2
    $GIT_CMD config user.email "$GIT_EMAIL" >&2
fi

# Fix ownership and safe directory issues
$GIT_CMD config --global --add safe.directory "$PWD" >&2

# Add remote if it doesn't exist
if ! $GIT_CMD remote get-url origin &>/dev/null; then
    $GIT_CMD remote add origin "$REPO_NAME" >&2
fi

# Add all files
$GIT_CMD add . >&2

# Check if there are changes to commit
if $GIT_CMD diff --staged --quiet; then
    echo "No changes to commit" >&2
    # If no changes, return the current commit hash if it exists
    if $GIT_CMD rev-parse HEAD &>/dev/null; then
        $GIT_CMD rev-parse HEAD
    else
        echo "No commits found" >&2
        exit 1
    fi
else
    # Commit with timestamp
    COMMIT_MSG="Auto-commit from E2B sandbox - $(date '+%Y-%m-%d %H:%M:%S')"
    $GIT_CMD commit -m "$COMMIT_MSG" >&2

    # Create and checkout branch if it doesn't exist locally
    if ! $GIT_CMD show-ref --verify --quiet "refs/heads/$BRANCH" 2>/dev/null; then
        echo "Creating new branch '$BRANCH'..." >&2
        $GIT_CMD checkout -b "$BRANCH" >&2
    else
        echo "Switching to existing branch '$BRANCH'..." >&2
        $GIT_CMD checkout "$BRANCH" >&2
    fi

    # Force push to branch (creates remote branch if it doesn't exist)
    $GIT_CMD push --force -u origin "$BRANCH" >&2

    # Return commit hash
    $GIT_CMD rev-parse HEAD
fi 
