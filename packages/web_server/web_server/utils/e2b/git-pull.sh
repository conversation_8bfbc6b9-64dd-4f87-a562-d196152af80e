#!/bin/bash

# Git pull script that takes repo name, commit/branch, and work_dir
# Usage: git-pull <repo_name> <commit_or_branch> <work_dir>
# Returns: success message to stdout
# 
# Authentication: SSH Keys only
# - SSH keys should be available in /root/.ssh/
# - Use SSH URLs: **************:user/repo.git
# 
# This script is available globally and can be run from any directory

set -e

# Check if all required arguments are provided
if [ $# -ne 3 ]; then
    echo "Usage: $0 <repo_name> <commit_or_branch> <work_dir>" >&2
    echo "Example: $0 **************:user/repo.git main /path/to/work" >&2
    echo "Example: $0 **************:user/repo.git abc123def /path/to/work" >&2
    echo "" >&2
    echo "Authentication: SSH Keys only" >&2
    echo "- SSH keys should be available in ~/.ssh/" >&2
    echo "- Use SSH URLs: **************:user/repo.git" >&2
    echo "" >&2
    exit 1
fi

REPO_NAME="$1"
COMMIT_OR_BRANCH="$2"
WORK_DIR="$3"
# TODO: Create a dummy user for git operations
GIT_EMAIL="<EMAIL>"
GIT_NAME="orby-bhavesh"

# Check if SSH URL is provided
if [[ ! "$REPO_NAME" =~ ^git@ ]]; then
    echo "Error: Only SSH URLs are supported (**************:user/repo.git)" >&2
    exit 1
fi

# SSH authentication is pre-configured in the Docker image
echo "Using SSH authentication..." >&2

# Use sudo for git operations if not running as root
if [ "$EUID" -ne 0 ]; then
    GIT_CMD="sudo git"
else
    GIT_CMD="git"
fi

# Create work directory if it doesn't exist
mkdir -p "$WORK_DIR"

# Change to work directory
cd "$WORK_DIR"

# Clone or pull the repository
if [ ! -d ".git" ]; then
    echo "Cloning repository..." >&2
    $GIT_CMD clone "$REPO_NAME" .

    # Set up git config
    $GIT_CMD config user.name "$GIT_NAME"
    $GIT_CMD config user.email "$GIT_EMAIL"
else
    echo "Repository exists, fetching latest changes..." >&2
    $GIT_CMD fetch origin
fi

# Fix ownership and safe directory issues
$GIT_CMD config --global --add safe.directory "$PWD"

# Checkout the specified commit or branch
echo "Checking out $COMMIT_OR_BRANCH..." >&2
$GIT_CMD checkout "$COMMIT_OR_BRANCH"

# If it's a branch, pull the latest changes
if $GIT_CMD show-ref --verify --quiet "refs/heads/$COMMIT_OR_BRANCH" 2>/dev/null; then
    echo "Pulling latest changes for branch $COMMIT_OR_BRANCH..." >&2
    $GIT_CMD pull origin "$COMMIT_OR_BRANCH"
fi

# Get the current commit hash
CURRENT_COMMIT=$($GIT_CMD rev-parse HEAD)

echo "Successfully pulled repository to $WORK_DIR"
echo "Current commit: $CURRENT_COMMIT" 
