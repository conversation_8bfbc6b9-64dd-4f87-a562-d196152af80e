"""
Cleanup utilities for the web server.
Centralizes all cleanup operations for graceful shutdown.
"""

import asyncio

from common.log import error, info


class CleanupManager:
    """Manages cleanup operations for the web server."""

    def __init__(self):
        self._cleanup_tasks: list[asyncio.Task] = []

    async def cleanup_all(self) -> None:
        """Perform all cleanup operations."""
        info("🧹 Starting comprehensive cleanup...")

        cleanup_operations = [
            self._cleanup_runtime_sessions(),
        ]

        # Run all cleanup operations concurrently
        results = await asyncio.gather(
            *cleanup_operations, return_exceptions=True
        )

        # Log any cleanup errors
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                error(f"Cleanup operation {i} failed: {result}")

        info("✅ Cleanup completed")

    async def _cleanup_runtime_sessions(self) -> None:
        """Clean up runtime sessions."""
        try:
            from web_server.session.runtime_manager import (
                get_runtime_session_manager,
            )

            runtime_manager = get_runtime_session_manager()
            if runtime_manager:
                await runtime_manager.cleanup_all_runtimes()
            else:
                info("No runtime manager to clean up")
        except Exception as e:
            error(f"Error during runtime cleanup: {e}")


# Global cleanup manager instance
_cleanup_manager: CleanupManager | None = None


def get_cleanup_manager() -> CleanupManager:
    """Get the global cleanup manager instance."""
    global _cleanup_manager
    if _cleanup_manager is None:
        _cleanup_manager = CleanupManager()
    return _cleanup_manager


async def perform_cleanup() -> None:
    """Perform all cleanup operations."""
    cleanup_manager = get_cleanup_manager()
    await cleanup_manager.cleanup_all()
