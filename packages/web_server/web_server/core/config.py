"""Configuration settings for the web server."""

from dataclasses import dataclass
import os

from dotenv import load_dotenv

from web_server.constants.env import LOCAL_MODE

# override=False is used to prevent the .env file from being overridden by the environment variables
load_dotenv(override=False)


@dataclass
class ServerConfig:
    """Server configuration settings."""

    mongodb_uri: str = os.getenv("MONGODB_URI", "")
    github_access_token: str = os.getenv("GITHUB_ACCESS_TOKEN", "")
    port: int = int(os.getenv("UVICORN_PORT", "8080"))
    mode: str = os.getenv("MODE", LOCAL_MODE).lower()
    browserbase_api_key: str = os.getenv("BB_API_KEY", "")
    orby_base_url: str = os.getenv("ORBY_BASE_URL", "https://dev2-grpc.orby.ai")

    # LangSmith settings
    langsmith_api_key: str = os.getenv("LANGSMITH_API_KEY", "")
    langsmith_project: str = os.getenv(
        "LANGSMITH_PROJECT", "vibe-automation-server"
    )
    langsmith_endpoint: str = os.getenv(
        "LANGSMITH_ENDPOINT", "https://api.smith.langchain.com"
    )
    langsmith_tracing: str = os.getenv("LANGSMITH_TRACING", "false")
    postgres_url: str = os.getenv("POSTGRES_URI", "")
    chainlit_default_bucket: str = os.getenv("CHAINLIT_DEFAULT_BUCKET", "")
    default_db_name: str = os.getenv("DEFAULT_DB_NAME", "dev")
    default_service_account: str = os.getenv("DEFAULT_SERVICE_ACCOUNT", "")


# Global config instance
server_config = ServerConfig()
