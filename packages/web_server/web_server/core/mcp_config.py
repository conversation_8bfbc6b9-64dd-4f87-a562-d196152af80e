"""
MCP (Model Control Protocol) configuration for system-level MCP servers.
"""

from dataclasses import dataclass, field
import json
import os
import re
from typing import Any

from common.log import error, info


@dataclass
class MCPServerConfig:
    """Configuration for a single MCP server."""
    name: str
    type: str  # "sse" or "stdio"
    url: str | None = None  # For SSE servers
    command: str | None = None  # For stdio servers
    args: list[str] = field(default_factory=list)  # For stdio servers
    env: dict[str, str] = field(default_factory=dict)  # Environment variables
    metadata: dict[str, Any] = field(default_factory=dict)  # Additional metadata


class MCPSystemConfig:
    """System-level MCP configuration manager."""
    
    def __init__(self):
        self.servers: list[MCPServerConfig] = []
        self._load_from_file()
        self._load_from_env()
    
    def _substitute_env_vars(self, value: Any) -> Any:
        """
        Recursively substitute environment variables in configuration values.
        Supports ${VAR_NAME} syntax.
        """
        if isinstance(value, str):
            # Find all ${VAR_NAME} patterns
            pattern = re.compile(r'\$\{([^}]+)\}')
            
            def replacer(match):
                var_name = match.group(1)
                return os.getenv(var_name, match.group(0))
            
            return pattern.sub(replacer, value)
        elif isinstance(value, dict):
            return {k: self._substitute_env_vars(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [self._substitute_env_vars(item) for item in value]
        else:
            return value
    
    def _load_from_env(self):
        """Load MCP server configurations from environment variables."""
        # Example env vars:
        # MCP_SERVERS='[{"name": "filesystem", "type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "/tmp"]}]'
        mcp_servers_json = os.getenv("MCP_SERVERS")
        if mcp_servers_json:
            try:
                servers_data = json.loads(mcp_servers_json)
                for server_data in servers_data:
                    # Substitute environment variables
                    server_data = self._substitute_env_vars(server_data)
                    server = MCPServerConfig(**server_data)
                    self.servers.append(server)
                    info(f"Loaded MCP server from env: {server.name}")
            except Exception as e:
                error(f"Error loading MCP servers from environment: {e}")
    
    def _load_from_file(self):
        """Load MCP server configurations from a JSON file."""
        # Look for mcp_servers.json in various locations
        config_paths = [
            "mcp_servers.json"
        ]
        
        for config_path in config_paths:
            if os.path.exists(config_path):
                try:
                    with open(config_path) as f:
                        servers_data = json.load(f)
                    
                    for server_data in servers_data:
                        # Skip if server with same name already exists
                        if any(s.name == server_data.get("name") for s in self.servers):
                            continue
                        
                        # Substitute environment variables
                        server_data = self._substitute_env_vars(server_data)
                        server = MCPServerConfig(**server_data)
                        self.servers.append(server)
                        info(f"Loaded MCP server from {config_path}: {server.name}")
                    
                    break  # Stop after first successful load
                except Exception as e:
                    error(f"Error loading MCP servers from {config_path}: {e}")
    
    def get_mcp_servers(self) -> list[MCPServerConfig]:
        """Get list of servers that should be auto-connected."""
        return self.servers
    
    def get_server_by_name(self, name: str) -> MCPServerConfig | None:
        """Get a server configuration by name."""
        for server in self.servers:
            if server.name == name:
                return server
        return None
    
    def add_server(self, server: MCPServerConfig):
        """Add a server configuration."""
        # Remove existing server with same name
        self.servers = [s for s in self.servers if s.name != server.name]
        self.servers.append(server)
    
    def remove_server(self, name: str):
        """Remove a server configuration by name."""
        self.servers = [s for s in self.servers if s.name != name]


# Global MCP system configuration
mcp_system_config = MCPSystemConfig()


def get_mcp_system_config() -> MCPSystemConfig:
    """Get the global MCP system configuration."""
    return mcp_system_config
