"""Business service for user file operations."""

from bson import ObjectId

from common.log import error
from common.models.organization import TenantInfo
from common.storage.gcs import get_gcs_client

from ..repositories.user_file_repository import UserFileRepository


class UserFileService:
    def __init__(self):
        """Initialize user file service"""
        self._user_file_repo = UserFileRepository()
        self._gcs_client = get_gcs_client()

    async def create_user_file(
        self,
        content: bytes,
        filename: str,
        file_type: str,
        workflow_id: ObjectId,
        creator_id: ObjectId,
        tenant_info: TenantInfo,
    ) -> ObjectId:
        """Upload file to GCS and create metadata record

        Args:
            content: File content as bytes
            filename: Original filename
            file_type: File type (manifest, sop, etc.)
            workflow_id: Associated workflow ID
            creator_id: ID of user creating the file
            tenant_info: Tenant context

        Returns:
            ObjectId: ID of created user file record

        Raises:
            Exception: If upload fails or metadata creation fails
        """
        gcs_path = self._generate_gcs_path(workflow_id, filename, file_type)
        user_file_id = None

        try:
            # Get bucket name using tenant info
            from ..core.config import server_config

            bucket_name = tenant_info.get_bucket_name(
                server_config.chainlit_default_bucket
            )

            # Upload to GCS first
            await self._gcs_client.upload(
                bucket=bucket_name,
                path=gcs_path,
                content=content,
            )

            # Create metadata record
            user_file_id = await self._user_file_repo.create_user_file(
                creator_id=creator_id,
                path=f"gs://{bucket_name}/{gcs_path}",
                workflow_id=workflow_id,
                tenant_info=tenant_info,
            )

            if not user_file_id:
                raise Exception("Failed to create user file")

            return user_file_id

        except Exception as e:
            error(f"Error creating user file: {e}")

            # Rollback: delete GCS file if metadata creation failed
            if user_file_id is None:
                try:
                    from ..core.config import server_config

                    bucket_name = tenant_info.get_bucket_name(
                        server_config.chainlit_default_bucket
                    )

                    # Check if file exists before attempting deletion
                    if await self._gcs_client.exists(
                        bucket=bucket_name, path=gcs_path
                    ):
                        # Note: GCSClient doesn't have delete method, would need to be added
                        # For now, log the orphaned file path for manual cleanup
                        error(
                            f"Orphaned GCS file at {bucket_name}/{gcs_path} - manual cleanup required"
                        )
                except Exception as cleanup_error:
                    error(
                        f"Failed to cleanup orphaned GCS file: {cleanup_error}"
                    )

            raise e

    def _generate_gcs_path(
        self, workflow_id: ObjectId, filename: str, file_type: str
    ) -> str:
        """Generate standardized GCS paths: va_workflows/{workflow_id}/{type}/{filename}"""
        return f"va_workflows/{workflow_id}/{file_type}/{filename}"
