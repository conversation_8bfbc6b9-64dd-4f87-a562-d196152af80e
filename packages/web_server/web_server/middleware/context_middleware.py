from contextvars import <PERSON><PERSON><PERSON><PERSON>

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

from common.utils.auth import _ORG_ID_HEADER_KEY
from web_server.constants.env import LOCAL_MODE
from web_server.core.config import server_config

# Define context variable for cookies
headers_var: ContextVar[dict[str, str] | None] = ContextVar(
    "headers", default=None
)


class RequestContextMiddleware(BaseHTTPMiddleware):
    """
    Middleware to capture and store request context (headers, cookies) for use throughout the application.

    This middleware extracts cookies and headers from incoming HTTP requests and stores them in context variables
    that can be accessed from anywhere in the application during request processing.
    """

    async def dispatch(self, request: Request, call_next):
        # Extract headers from request
        headers = dict(request.headers) if request.headers else {}
        # In local mode, we use cookies instead of headers because the authentication server that normally converts cookies to headers is bypassed.
        if server_config.mode == LOCAL_MODE:
            headers = dict(request.cookies) if request.cookies else {}

        # Set headers context variable
        headers_token = headers_var.set(headers)
        try:
            response = await call_next(request)
        finally:
            # Cleanup context variable to avoid leaking context
            headers_var.reset(headers_token)
        return response


# Utility function to get the Orby org ID from the current request context.
def get_orby_org_header_id() -> str | None:
    """Get the Orby org ID from the current request context."""
    headers = headers_var.get()
    if headers is None:
        return None

    org_id = headers.get(_ORG_ID_HEADER_KEY) or headers.get("org_id")
    return org_id  # Fine to use direct key as this is for local mode
