import os
import uuid

from github import Auth, Github, GithubException, InputGitTreeElement

from common.log import error
from web_server.clients.code_management.base import (
    BaseCodeManager,
    File,
    Repository,
)


class GithubCodeManager(BaseCodeManager):
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if hasattr(self, "_initialized") and self._initialized:
            return  # Avoid reinitializing
        access_token = os.getenv("GITHUB_TOKEN")
        if access_token is None:
            raise RuntimeError(
                "GITHUB_TOKEN is not set in the environment variables."
            )

        auth = Auth.Token(access_token)
        self.github_client = Github(auth=auth)
        self._initialized = True

    @classmethod
    def get_instance(cls):
        return cls()

    def create_repo(
        self,
        name: str,
        description: str = "",
        private: bool = True,
    ) -> Repository:
        """
        Create a new repository using the GitHub API.
        Args:
            name (str): The name of the repository to create.
            description (str, optional): Optional description for the repository. Defaults to "".
            private (bool, optional): Whether the repository should be private. Defaults to True.
        """
        if not isinstance(name, str) or not name:
            raise ValueError("Repository name must be a non-empty string.")
        # Create a new repository using the GitHub API
        repo = self.github_client.get_user().create_repo(
            name=name,
            description=description,
            private=private,
            auto_init=True,
        )
        # Return a Repository object containing the full name (owner/repo)
        return Repository(full_name=repo.full_name)

    def delete_repo(self, repo: Repository) -> None:
        """
        Delete a repository using the GitHub API.
        Args:
            repo (Repository): The repository object to delete.
        """
        if repo is None or not hasattr(repo, "full_name") or not repo.full_name:
            raise ValueError(
                "Repository object must have a valid 'full_name' attribute."
            )
        # Get the repository object using the full name
        gh_repo = self.github_client.get_repo(repo.full_name)
        # Call the delete method to remove the repository from GitHub
        gh_repo.delete()

    def push_code_to_new_branch(
        self,
        repo: Repository,
        local_directory_path: str,
        branch_name: str,
        base_branch_name: str = "main",
    ) -> str:
        """
        Force push files from a local path to a branch in the given GitHub repo.
        Args:
            repo (Repository): Target GitHub repository.
            branch_name (str): Name of the branch to create or overwrite.
            local_path (str): Local folder path to read files from.
        Returns:
            str: SHA of the pushed commit.
        """

        if not repo or not repo.full_name:
            raise ValueError("Repository must have a valid 'full_name'.")
        if not branch_name or not isinstance(branch_name, str):
            raise ValueError("Branch name must be a non-empty string.")
        if not os.path.isdir(local_directory_path):
            raise ValueError(f"Invalid local path: {local_directory_path}")

        gh_repo = self.github_client.get_repo(repo.full_name)

        # Use base_branch_name as base commit
        base_ref = gh_repo.get_git_ref(f"heads/{base_branch_name}")
        base_commit = gh_repo.get_commit(base_ref.object.sha)
        base_tree = base_commit.commit.tree

        # Recursively read files from local_path
        tree_elements = []
        for root, _, files in os.walk(local_directory_path):
            for file_name in files:
                full_path = os.path.join(root, file_name)
                relative_path = os.path.relpath(
                    full_path, local_directory_path
                ).replace("\\", "/")
                with open(full_path, encoding="utf-8") as f:
                    content = f.read()
                tree_elements.append(
                    InputGitTreeElement(
                        path=relative_path,
                        mode="100644",
                        type="blob",
                        content=content,
                    )
                )

        if not tree_elements:
            raise ValueError(
                f"No files found in directory: {local_directory_path}"
            )

        # Create new tree and commit
        new_tree = gh_repo.create_git_tree(
            tree=tree_elements, base_tree=base_tree
        )
        new_commit = gh_repo.create_git_commit(
            message=f"Force push code from {local_directory_path} to {branch_name}",
            tree=new_tree,
            parents=[base_commit.commit],
        )

        try:
            branch_ref = gh_repo.get_git_ref(f"heads/{branch_name}")
            branch_ref.edit(sha=new_commit.sha, force=True)
        except Exception:
            # Create new branch if it doesn't exist
            gh_repo.create_git_ref(
                ref=f"refs/heads/{branch_name}", sha=new_commit.sha
            )

        return new_commit.sha

    def update_files(
        self,
        repo: Repository,
        files: list[File],
        message: str,
        base_branch_name: str = "main",
        target_branch_name: str = "main",
    ) -> str:
        """
        Update or create multiple files in a GitHub repository and commit the changes.
        Args:
            repo (Repository): Repository object with full_name.
            files (list[File]): List of File objects with path and content. e.g. [File(path="main.py", content="print('Hello, world!')")]
            message (str): Commit message.
            base_branch_name (str, optional): Base Branch to update. Defaults to "main".
            target_branch_name (str, optional): Branch to update. Defaults to "main".

        Returns:
            str: SHA of the new commit.
        """
        if not isinstance(files, list) or not files:
            raise ValueError(
                "Parameter 'files' must be a non-empty list of file paths to contents."
            )
        for file in files:
            if not isinstance(file.path, str) or not file.path.strip():
                raise ValueError(
                    f"File path '{file.path}' must be a non-empty string."
                )
            if not isinstance(file.content, str):
                raise ValueError(
                    f"Content for file '{file.path}' must be a string."
                )
        if not isinstance(message, str) or not message.strip():
            raise ValueError("Parameter 'message' must be a non-empty string.")
        if repo is None or not hasattr(repo, "full_name") or not repo.full_name:
            raise ValueError(
                "Parameter 'repo' must be a Repository object with a valid 'full_name'."
            )
        if (
            not isinstance(base_branch_name, str)
            or not base_branch_name.strip()
        ):
            raise ValueError(
                "Parameter 'base_branch_name' must be a non-empty string."
            )
        if (
            not isinstance(target_branch_name, str)
            or not target_branch_name.strip()
        ):
            raise ValueError(
                "Parameter 'target_branch_name' must be a non-empty string."
            )

        # Get the repository object using the full name
        repo = self.github_client.get_repo(full_name_or_id=repo.full_name)
        # Get the base branch object
        base_branch = repo.get_branch(base_branch_name)
        # Get the SHA of the latest commit on the base branch
        base_commit = repo.get_git_ref(f"heads/{base_branch.name}").object.sha
        # Get the tree of the base commit
        base_tree = repo.get_git_tree(repo.get_commit(base_commit).sha)

        # Create tree elements for each file to be updated
        tree_elements = []
        for file in files:
            tree_elements.append(
                InputGitTreeElement(
                    path=file.path,
                    mode="100644",
                    type="blob",
                    content=file.content,
                )
            )
        # Create a new tree with the updated files
        new_tree = repo.create_git_tree(tree=tree_elements, base_tree=base_tree)
        # Get the parent commit object
        parent_commit = repo.get_git_commit(base_commit)
        # Create a new commit with the updated tree
        new_commit = repo.create_git_commit(
            message=message, tree=new_tree, parents=[parent_commit]
        )

        # Update the branch reference to point to the new commit
        try:
            ref = repo.get_git_ref(f"heads/{target_branch_name}")
            ref.edit(sha=new_commit.sha)
        except GithubException as e:
            if e.status == 404:
                # Create new branch if it doesn't exist
                repo.create_git_ref(
                    ref=f"refs/heads/{target_branch_name}", sha=new_commit.sha
                )
            else:
                raise e

        return new_commit.sha

    def get_files(
        self,
        repo: Repository,
        ref: str = "main",
        path: str = "",
    ) -> list[File]:
        """
        Get all files from a GitHub repository and branch.

        Args:
            repo (Repository): Repository object with full_name.
            path (str, optional): Path to get files from. Defaults to "".
            ref (str, optional): Branch or commit SHA to get files from. Defaults to "main".
        """
        if not isinstance(repo, Repository) or not repo.full_name:
            raise ValueError(
                "Parameter 'repo' must be a Repository object with a valid 'full_name'."
            )
        # Get the repository object using the full name
        gh_repo = self.github_client.get_repo(repo.full_name)
        file_contents = gh_repo.get_contents(path=path, ref=ref)
        files = []
        while file_contents:
            file_content = file_contents.pop(0)
            if file_content.type == "dir":
                file_contents.extend(
                    gh_repo.get_contents(file_content.path, ref=ref)
                )
            else:
                files.append(
                    File(
                        name=file_content.name,
                        path=file_content.path,
                        content=file_content.content,
                        decoded_content=file_content.decoded_content,
                        encoding=file_content.encoding,
                        size=file_content.size,
                        sha=file_content.sha,
                    )
                )
        return files


# Helper function to get the GithubCodeManager instance
def get_github_client():
    return GithubCodeManager.get_instance()


# This is a util function, it's only for temporary use where we will push the code into https://github.com/orby-bhavesh/code-repository repository to avoid authentication issues.
# TODO: Remove this once the code management is implemented.
def push_code_to_new_branch(
    local_directory_path: str,
) -> str:
    try:
        branch_name = str(uuid.uuid4())
        # Hardcoded repository for temporary use
        repo = Repository(full_name="orby-bhavesh/code-repository")
        return get_github_client().push_code_to_new_branch(
            repo=repo,
            local_directory_path=local_directory_path,
            branch_name=branch_name,
        )
    except Exception as e:
        error(f"Failed to push code to new branch: {e}")
