from abc import abstractmethod


class Repository:
    def __init__(self, full_name: str):
        self.full_name = full_name


class File:
    def __init__(
        self,
        name: str,
        path: str,
        content: str,
        decoded_content: bytes,
        encoding: str,
        size: int,
        sha: str,
    ):
        self.name = name
        self.path = path
        self.content = content
        self.decoded_content = decoded_content
        self.encoding = encoding
        self.size = size
        self.sha = sha


class BaseCodeManager:
    @abstractmethod
    def create_repo(
        self,
        name: str,
        description: str = "",
        private: bool = True,
    ) -> Repository:
        pass

    @abstractmethod
    def delete_repo(self, repo: Repository) -> None:
        pass

    @abstractmethod
    def update_files(
        self,
        repo: Repository,
        files: list[File],
        message: str,
        target_branch_name: str = "main",
        base_branch_name: str = "main",
    ) -> str:
        pass

    @abstractmethod
    def get_files(
        self,
        repo: Repository,
        path: str = "",
        ref: str = "main",
    ) -> list[File]:
        pass
