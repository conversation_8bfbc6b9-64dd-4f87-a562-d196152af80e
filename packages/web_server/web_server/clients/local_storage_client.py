from pathlib import Path
from typing import Any

import aiofiles  # type: ignore[import-untyped]
from chainlit.data.storage_clients.base import BaseStorageClient


class LocalStorageClient(BaseStorageClient):
    def __init__(
        self, base_dir: str = "local_storage", url_prefix: str = "/storage"
    ):
        """
        Initialize local storage client
        Args:
            base_dir: Base directory for file storage
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        self.url_prefix = url_prefix

    async def upload_file(
        self,
        object_key: str,
        data: bytes | str,
        mime: str = "application/octet-stream",
        overwrite: bool = True,
    ) -> dict[str, Any]:
        """
        Upload file to local storage
        Args:
            object_key: Unique identifier for the file
            data: File content
            mime: MIME type of the file
            overwrite: Whether to overwrite existing file
        """
        file_path = self.base_dir / object_key
        file_path.parent.mkdir(parents=True, exist_ok=True)

        # Raise exception if file exists and overwrite is not allowed
        if file_path.exists() and not overwrite:
            raise FileExistsError(f"File {object_key} already exists")

        # Write data to file
        async with aiofiles.open(file_path, "wb") as f:
            if isinstance(data, str):
                await f.write(data.encode())
            else:
                await f.write(data)

        return {}

    async def get_read_url(self, object_key: str) -> str:
        """
        Get web-accessible URL for the file
        Args:
            object_key: Unique identifier for the file
        """
        file_path = self.base_dir / object_key
        if not file_path.exists():
            raise FileNotFoundError(f"File {object_key} not found")

        # Return web-accessible URL
        return f"{self.url_prefix}/{object_key}"

    async def delete_file(self, object_key: str) -> bool:
        """
        Delete file from local storage
        Args:
            object_key: Unique identifier for the file
        """
        file_path = self.base_dir / object_key
        if file_path.exists():
            file_path.unlink()
            return True
        return False
