"""Repository for user file database operations."""

from datetime import UTC, datetime

from bson import ObjectId

from common.log import error
from common.models.organization import TenantInfo
from common.models.user_files import UserFile
from common.repository.base import BaseRepository


class UserFileRepository(BaseRepository[UserFile]):
    def __init__(self):
        super().__init__(UserFile)

    async def create_user_file(
        self,
        creator_id: ObjectId,
        path: str,
        workflow_id: ObjectId,
        tenant_info: TenantInfo,
    ) -> ObjectId | None:
        """Create a new user file and return its ID"""
        user_file = UserFile(
            creator_id=creator_id,
            org_id=tenant_info.org.id,
            created_time=datetime.now(UTC),
            workflow_id=workflow_id,
            path=path,
        )

        try:
            collection = await self._get_collection(tenant_info)
            # Use model serialization with context-aware ObjectId handling
            user_file_dict = user_file.model_dump(by_alias=True, mode="python")
            result = await collection.insert_one(user_file_dict)
            return result.inserted_id
        except Exception as e:
            error(f"Failed to create user file: {e}")
            return None
