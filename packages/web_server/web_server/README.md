# Service/Repository Code Structure

These services follow a 3-layer architecture pattern:

- **Handler layer**: Manages API endpoints (e.g., gRPC). It handles request parsing, validation, auth, and **converts data between API-specific formats (like Protobuf) and internal application models**. Located in `/handlers/grpc/`
- **Service layer**: Contains the core business logic, orchestrated using the application's internal data models. It remains independent of the API layer. Files like `workflow_service.py`
- **Repository layer**: Database operations and data persistence. Files like `workflow_repository.py` extending `BaseRepository`
  - To **avoid duplicate code**, shared database utilities are in `/common/repository/base.py`

## Current Structure
```
web_server/
├── handlers/grpc/          # gRPC endpoints
├── services/               # Business logic
│   └── workflow_service.py
├── repositories/           # Data access
│   └── workflow_repository.py
└── common/repository/      # Shared DB utilities
    └── base.py
```

## Multi-Tenant Pattern
All layers enforce tenant isolation via `tenant_info` parameter for secure data segregation.

## Purposes
- Organized async/await Python architecture
- Easy unit testing with AsyncMock for each layer  
- Clean separation for Chainlit chat + gRPC services
- Ready for microservice extraction when scaling
