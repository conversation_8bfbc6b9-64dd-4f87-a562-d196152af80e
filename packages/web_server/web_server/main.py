from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager

from chainlit.server import app as chainlit_app
from chainlit.utils import mount_chainlit
from fastapi import APIRouter, FastAPI
import uvicorn

from common.log import info, warn
from common.monitoring.fastapi import (
    create_prometheus_middleware,
    metrics_endpoint,
)
from web_server.middleware.context_middleware import RequestContextMiddleware

from .api.auth_local import router as local_auth_router
from .api.code_management import router as code_management_router
from .api.storage import router as storage_router
from .core.config import server_config
from .utils.cleanup import perform_cleanup


@asynccontextmanager
async def app_init(app: FastAPI) -> AsyncGenerator[None]:
    """Application startup and shutdown lifecycle."""
    # Startup
    info("🚀 Starting Vibe Automation Server...")

    yield

    # Shutdown
    info("🛑 Shutting down Vibe Automation Server...")

    # Perform comprehensive cleanup
    try:
        await perform_cleanup()
    except Exception as e:
        warn(f"Error during cleanup: {e}")

    info("✅ Vibe Automation Server shutdown complete")


app = FastAPI(
    title="Vibe Automation Server",
    lifespan=app_init,
)

# Add Prometheus middleware
app.add_middleware(create_prometheus_middleware(route_prefixes=["/api/v1/va"]))
# Add metrics endpoint
app.add_route("/metrics", metrics_endpoint, methods=["GET"])

app.add_middleware(RequestContextMiddleware)

router = APIRouter(prefix="/api/v1/va")

# Add storage router
if server_config.mode == "local":
    router.include_router(storage_router)
    router.include_router(local_auth_router)

# Add code management router
router.include_router(code_management_router)


@router.get("/health")
async def health_check() -> dict[str, str]:
    return {"status": "ok"}


app.include_router(router)


@chainlit_app.middleware("http")
async def same_site_cookie_fix(request, call_next):
    """
    This is a workaround to fix the same site cookie issue in the chainlit app.
    When we login from the chainlit web app, chainlit server sets a access token cookie with SameSite=lax.
    Since we use a different domain for web app and chainlit app, we need to set the SameSite=None; Secure in
    order to use the cookie in the chainlit app.
    """
    response = await call_next(request)
    # If cookie is being set, update it
    if "set-cookie" in response.headers:
        raw_cookie = response.headers["set-cookie"]
        # Replace the SameSite=lax with SameSite=None; Secure in the cookie
        response.headers["set-cookie"] = raw_cookie.replace(
            "SameSite=lax", "SameSite=None; Secure"
        )
    return response


mount_chainlit(
    app=app,
    target="packages/web_server/web_server/chat_app.py",
    path="/api/v1/va/chainlit",
)


def main() -> None:
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=server_config.port,
        # Define the config to send INFO, WARNING, and DEBUG messages to stdout and ERROR and CRITICAL messages to stderr.
        # This is needed because in GKE logs by default are sent to stderr which is not what we want for INFO and WARNING messages.
        log_config="packages/web_server/logging.yaml",
    )


if __name__ == "__main__":
    main()
