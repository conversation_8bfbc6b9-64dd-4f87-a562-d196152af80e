from fastapi import APIRouter, HTTPException, Query
from github import Gith<PERSON>Exception
from pydantic import BaseModel

from ..clients.code_management.base import File, Repository
from ..clients.code_management.github import GithubCodeManager

router = APIRouter(prefix="/vcs", tags=["vcs"])


class CreateRepoRequest(BaseModel):
    name: str
    description: str = ""


class DeleteRepoRequest(BaseModel):
    repo_full_name: str


class UpdateRepoFilesRequest(BaseModel):
    repo_full_name: str
    message: str
    files: dict[str, str]
    base_branch_name: str = "main"
    target_branch_name: str = "main"


class GetRepoFilesRequest(BaseModel):
    repo_full_name: str
    branch_name: str = "main"


@router.post("/repo")
def create_repo(request: CreateRepoRequest):
    """
    Create a repository.

    Args:
        name (str): Repository name.
        description (str): Repository description. Defaults to "".
    """
    try:
        code_manager = GithubCodeManager.get_instance()
        repo = code_manager.create_repo(
            name=request.name,
            description=request.description,
        )
        return repo
    except GithubException as e:
        if e.status == 422:
            raise HTTPException(status_code=409, detail=e.message) from e
        else:
            raise HTTPException(status_code=500, detail=e.message) from e


@router.delete("/repo")
def delete_repo(request: DeleteRepoRequest):
    """
    Delete a repository.

    Args:
        repo_full_name (str): Repository full name. e.g. "owner/repo"
    """
    try:
        code_manager = GithubCodeManager.get_instance()
        code_manager.delete_repo(
            repo=Repository(full_name=request.repo_full_name),
        )
    except GithubException as e:
        if e.status == 404:
            raise HTTPException(status_code=404, detail=e.message) from e
        else:
            raise HTTPException(status_code=500, detail=e.message) from e


@router.post("/repo/files")
def update_repo_files(request: UpdateRepoFilesRequest):
    """
    Update files in a repository.

    Args:
        repo_full_name (str): Repository full name. e.g. "owner/repo"
        message (str): Commit message.
        files (dict[str, str]): Mapping of file paths to their contents, e.g. {"main.py": "print('Hello, world!')"}
        base_branch_name (str): Base branch name. Defaults to "main".
        target_branch_name (str): Target branch name. Defaults to "main".

    Returns:
    """
    try:
        code_manager = GithubCodeManager.get_instance()
        files = [
            File(path=path, content=content)
            for path, content in request.files.items()
        ]
        commit_sha = code_manager.update_files(
            files=files,
            message=request.message,
            repo=Repository(full_name=request.repo_full_name),
            base_branch_name=request.base_branch_name,
            target_branch_name=request.target_branch_name,
        )
        return {"commit_sha": commit_sha}
    except GithubException as e:
        if e.status == 404:
            raise HTTPException(status_code=404, detail=e.message) from e
        else:
            raise HTTPException(status_code=500, detail=e.message) from e


@router.get("/repo/files")
def get_repo_files(
    repo: str = Query(..., description="Repository name"),
    ref: str = Query("main", description="Branch or commit SHA"),
    path: str = Query("", description="Path within the repository"),
):
    """
    Get files from a repository.

    Args:
        repo (str): Repository full name. e.g. "owner/repo"
        ref (str): Branch or commit SHA. Defaults to "main".
        path (str): Path within the repository. Defaults to "".

    Returns:
        list[File]: List of File objects.
    """
    try:
        code_manager = GithubCodeManager.get_instance()
        files = code_manager.get_files(
            repo=Repository(full_name=repo),
            ref=ref,
            path=path,
        )
        result = []
        for file in files:
            try:
                decoded_content = file.decoded_content.decode("utf-8")
            except Exception:
                decoded_content = None
            result.append(
                {
                    "name": file.name,
                    "path": file.path,
                    "content": file.content,
                    "decoded_content": decoded_content,
                    "encoding": file.encoding,
                    "size": file.size,
                    "sha": file.sha,
                }
            )
        return result
    except GithubException as e:
        if e.status == 404:
            raise HTTPException(status_code=404, detail=e.message) from e
        else:
            raise HTTPException(status_code=500, detail=e.message) from e
