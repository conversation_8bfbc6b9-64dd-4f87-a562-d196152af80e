from fastapi import APIRouter, HTTPException, Request, Response
from fastapi.responses import JSONResponse

from common.log import warn
from common.services.user_service import UserService
from web_server.constants.env import LOCAL_MODE
from web_server.core.config import server_config

router = APIRouter(prefix="/local", tags=["local"])


@router.post("/login")
async def local_login(request: Request):
    """
    This is a workaround to to login to the system when we are running in local mode.
    """
    if server_config.mode != LOCAL_MODE:
        raise HTTPException(
            status_code=400,
            detail="This endpoint is only available in local mode",
        )

    # Get the email from the request
    data = await request.json()
    email = data.get("email").lower().strip()

    user_service = UserService()
    db_user = await user_service.get_user_by_email(email)
    if not db_user:
        warn(f"User not found: {email}")
        raise HTTPException(status_code=401, detail="User not found")

    # Json stringify the user
    user_json = db_user.model_dump_json()

    org_id = None
    if db_user.org_ids and len(db_user.org_ids) > 0:
        org_id = db_user.org_ids[0]

    # Set cookie in the response
    response = JSONResponse(content=user_json)
    response.headers.append(
        "Set-Cookie",
        f"local_token={user_json}; HttpOnly; Secure; SameSite=None; Max-Age=86400; Path=/",
    )
    if org_id:
        response.headers.append(
            "Set-Cookie",
            f"org_id={org_id}; Secure; SameSite=None; Max-Age=86400; Path=/",
        )

    return response


@router.post("/logout")
async def local_logout():
    """
    This is a workaround to to login to the system when we are running in local mode.
    """
    if server_config.mode != LOCAL_MODE:
        raise HTTPException(
            status_code=400,
            detail="This endpoint is only available in local mode",
        )

    # Set cookie in the response
    response = JSONResponse(content="")
    response.headers.append(
        "Set-Cookie",
        "local_token=; HttpOnly; Secure; SameSite=None; Max-Age=3600; Path=/",
    )
    response.headers.append(
        "Set-Cookie",
        "access_token=; HttpOnly; Secure; SameSite=None; Max-Age=3600; Path=/",
    )

    return response


@router.get("/user")
async def local_get_user(request: Request):
    """
    This is a workaround to to login to the system when we are running in local mode.
    """

    if server_config.mode != LOCAL_MODE:
        raise HTTPException(
            status_code=400,
            detail="This endpoint is only available in local mode",
        )

    # Get the local token from the request
    local_token = request.cookies.get("local_token")
    if not local_token:
        return Response(content="", media_type="text/plain", status_code=401)

    # Get the user from the database
    return JSONResponse(content=local_token)
