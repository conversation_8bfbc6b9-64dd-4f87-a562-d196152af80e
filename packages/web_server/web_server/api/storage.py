from pathlib import Path

from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse

router = APIRouter(prefix="/storage", tags=["storage"])

# Get the base storage directory from settings
STORAGE_DIR = Path("local_storage")


@router.get("/{path:path}")
async def get_file(path: str) -> FileResponse:
    """
    Get file from local storage
    Args:
        path: Path to the file relative to storage directory
    """
    file_path = STORAGE_DIR / path

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")

    if not file_path.is_file():
        raise HTTPException(status_code=400, detail="Not a file")

    return FileResponse(
        path=file_path,
        filename=file_path.name,
        media_type="application/octet-stream",
    )
