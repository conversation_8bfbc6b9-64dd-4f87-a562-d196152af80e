"""
Chainlit command definitions and handlers
"""


import chainlit as cl
from orbot.configuration import Configuration
from orbot.system_prompt import (
    get_compression_prompt,
    get_flow_prompt,
    get_orbot_system_prompt,
)
from orbot.tools.registry import ToolRegistry
from orbot.turn_by_turn_agent import TurnByTurnAgent

from common.log import error, info
from execution.v1.services.execution_service import ExecutionService
from web_server.constants.chat import WindowMessageType
from web_server.data.orby_chainlit_data_layer import TenantInfoMixin
from web_server.session.mcp_loader import load_system_mcp_servers
from web_server.session.runtime_manager import get_runtime_session_manager
from web_server.session.state_manager import (
    AppMode,
    StateKey,
    get_state_manager,
)


# Define commands based on current mode
def get_commands():
    """Get the list of commands to register with Chain<PERSON> based on current mode"""
    try:
        state_manager = get_state_manager()
        current_mode = state_manager.get_app_mode()
        use_orbot = state_manager.get_state_value(StateKey.USE_ORBOT, False)
        use_flow = state_manager.get_state_value(StateKey.USE_FLOW, False)
    except Exception:
        # Fallback during initialization
        current_mode = AppMode.BUILD
        use_orbot = False
        use_flow = False

    # Handle case where mode is not initialized yet (during chat resume)
    if current_mode is None:
        current_mode = AppMode.BUILD  # Default to build mode

    mode_text = current_mode.value.upper()
    commands = []

    if current_mode == AppMode.BUILD:
        # Build mode commands
        commands.extend(
            [
                {
                    "id": "execution",
                    "icon": "play",
                    "description": f"[{mode_text}] Switch to execution mode",
                },
            ]
        )

        # Orbot commands in build mode
        if use_orbot:
            commands.append(
                {
                    "id": "orbot-off",
                    "icon": "bot-off",
                    "description": f"[{mode_text}] Disable Orbot",
                }
            )
        else:
            commands.append(
                {
                    "id": "orbot-on",
                    "icon": "bot",
                    "description": f"[{mode_text}] Enable Orbot",
                }
            )

        # Orbot commands in build mode
        if use_flow:
            commands.append(
                {
                    "id": "flow-off",
                    "icon": "bot-off",
                    "description": f"[{mode_text}] Disable Flow",
                }
            )
        else:
            commands.append(
                {
                    "id": "flow-on",
                    "icon": "bot",
                    "description": f"[{mode_text}] Enable Flow",
                }
            )

        commands.append(
            {
                "id": "orbot-status",
                "icon": "info",
                "description": f"[{mode_text}] Check Orbot status",
            }
        )

    elif current_mode == AppMode.EXECUTION:
        # Execution mode commands
        commands.extend(
            [
                {
                    "id": "build",
                    "icon": "hammer",
                    "description": f"[{mode_text}] Switch to build mode",
                },
                {
                    "id": "run",
                    "icon": "play-circle",
                    "description": f"[{mode_text}] Execute workflow",
                },
            ]
        )

    # Common commands available in all modes
    commands.extend(
        [
            {
                "id": "help",
                "icon": "help-circle",
                "description": f"[{mode_text}] Show help and debug commands",
            },
            {
                "id": "state",
                "icon": "code",
                "description": f"[{mode_text}] Show session state",
            },
            {
                "id": "user-simulator",
                "icon": "bot",
                "description": f"[{mode_text}] Start user simulator (use: /user-simulator ID)",
            },
        ]
    )

    return commands


async def register_commands():
    """Register commands with Chainlit"""
    commands = get_commands()
    await cl.context.emitter.set_commands(commands)


async def refresh_commands():
    """Refresh commands when mode changes"""
    commands = get_commands()
    await cl.context.emitter.set_commands(commands)


async def handle_command(command_id: str, message: cl.Message) -> bool:
    """
    Handle a command by its ID.
    Returns True if the command was handled, False otherwise.
    """
    if command_id == "build":
        await handle_build_command()
        return True
    elif command_id == "execution":
        await handle_execution_command()
        return True
    elif command_id == "orbot-on":
        await handle_orbot_on_command()
        return True
    elif command_id == "orbot-off":
        await handle_orbot_off_command()
        return True
    elif command_id == "orbot-status":
        await handle_orbot_status_command()
        return True
    elif command_id == "flow-on":
        await handle_flow_on_command()
        return True
    elif command_id == "flow-off":
        await handle_flow_off_command()
        return True
    elif command_id == "run":
        return (
            await handle_run_command()
        )  # Returns True/False to control message processing
    elif command_id == "help":
        await handle_help_command()
        return True
    elif command_id == "state":
        await handle_state_command()
        return True
    elif command_id == "user-simulator":
        # Parse parameters from the message content
        if not hasattr(message, "content") or not message.content:
            # No content provided, show help
            await handle_user_simulator_help_command()
            return True
        try:
            use_case_id = int(message.content.strip().split()[0])
            # Valid ID provided, start simulation
            await handle_user_simulator_command(id=use_case_id)
            return True
        except (ValueError, IndexError):
            # Invalid ID or no ID provided, show help
            await handle_user_simulator_help_command()
            return True
        except Exception as e:
            # Other errors, show error message
            await cl.Message(
                content=f"❌ **Error starting user simulator**\n\n{e}"
            ).send()
            return True

    return False


# Command handler functions


async def handle_build_command():
    """Handle build mode command"""
    state_manager = get_state_manager()

    info("Switched to BUILD MODE")
    state_manager.set_app_mode(AppMode.BUILD)
    state_manager.update_state(
        StateKey.EXECUTION_MODE, False
    )  # Reset execution state
    await state_manager.construct_and_send_window_message(
        type=WindowMessageType.MODE_CHANGE,
        mode="BUILD",
    )

    # Start a new browser session for the build mode and send the live view url to the client
    from web_server.session.runtime_manager import get_runtime_session_manager

    runtime_manager = get_runtime_session_manager()
    selected_workflow = await state_manager.get_workflow(
        cl.context.session.thread_id
    )

    try:
        runtime = await runtime_manager.get_runtime(cl.context.session.id)
        if runtime and hasattr(runtime, "create_browser_session"):
            await runtime.create_browser_session(
                selected_workflow.bb_context_id
            )
            if runtime.config.use_browser_session and runtime.browser_session:
                new_live_view_url = runtime.browser_session.debugger_url
                await state_manager.construct_and_send_window_message(
                    type=WindowMessageType.BB_SESSION_LIVE_VIEW_URL,
                    url=new_live_view_url,
                )

                # Update state manager with the new browser session info
                state_manager.update_runtime_info(runtime)
    except Exception as e:
        error(f"Failed to create browser session: {e}")

    await cl.Message(
        content="🔨 **Switched to BUILD MODE**\n\nYou can now:\n• Describe automation needs to generate workflows\n• Create new workflows through conversation\n• Use `/run` to execute workflows when ready"
    ).send()

    # Refresh commands for new mode
    await refresh_commands()


async def handle_execution_command():
    """Handle execution mode command"""
    state_manager = get_state_manager()

    info("Switched to EXECUTION MODE")
    state_manager.set_app_mode(AppMode.EXECUTION)
    await state_manager.construct_and_send_window_message(
        type=WindowMessageType.MODE_CHANGE,
        mode="EXECUTION",
    )
    await cl.Message(
        content="⚙️ **Switched to EXECUTION MODE**\n\nNow you can:\n• Upload files for workflow execution\n• Use `/run` to execute workflows\n• Use `/build` to go back to workflow creation"
    ).send()

    # Refresh commands for new mode
    await refresh_commands()


async def handle_orbot_on_command():
    """Handle orbot on command"""
    state_manager = get_state_manager()

    info("Enabling Orbot for build mode")

    # Check if runtime is initialized first
    if not state_manager.get_state_value(
        StateKey.IS_RUNTIME_INITIALIZED, False
    ):
        await cl.Message(
            content="⏳ **Runtime not yet initialized**\n\nPlease wait a moment for the runtime to initialize before enabling Orbot."
        ).send()
        return

    # Check if Orbot is already initialized
    orbot_agent = state_manager.get_state_value(StateKey.ORBOT)
    if not orbot_agent:
        # Initialize Orbot with MCP servers and tools
        try:
            await cl.Message(
                content="🔄 **Initializing Orbot**\n\nSetting up MCP servers and tool registry..."
            ).send()

            runtime_info = state_manager.get_runtime_info()

            if not runtime_info:
                await cl.Message(
                    content="Runtime not initialized. Cannot setup Orbot. Try again later"
                ).send()
                return

            info("Initializing Orbot with MCP servers...")

            # Setup MCP servers.
            await load_system_mcp_servers()
            config = Configuration()
            config.validate_required_fields()
            tool_registry = ToolRegistry(config)
            await tool_registry.install_default_tools()
            await tool_registry.discover_mcp_tools()

            state_manager.update_state(
                StateKey.ORBOT,
                TurnByTurnAgent(
                    tool_registry,
                    config,
                    cl.context.session.thread_id,
                    get_orbot_system_prompt(),
                    get_compression_prompt(),
                ),
            )

            info(
                "Orbot initialized successfully with MCP servers and tool registry"
            )

        except Exception as e:
            error(f"Failed to initialize Orbot: {e}")
            await cl.Message(
                content=f"❌ **Failed to initialize Orbot**\n\nError: {str(e)}"
            ).send()
            return

    state_manager.update_state(StateKey.USE_ORBOT, True)
    state_manager.update_state(StateKey.USE_FLOW, False)
    await cl.Message(
        content="🤖 **Orbot Enabled**\n\nNow using the turn-by-turn agent (Orbot) for build mode conversations.\n\nFeatures:\n• Advanced reasoning and thinking steps\n• Tool calling for automation tasks\n• Multi-turn conversation handling\n• MCP server integration\n\nUse `/orbot-off` to disable."
    ).send()

    # Refresh commands to show orbot-off instead of orbot-on
    await refresh_commands()


async def handle_orbot_off_command():
    """Handle orbot off command"""
    state_manager = get_state_manager()

    info("Disabled Orbot for build mode")
    state_manager.update_state(StateKey.USE_ORBOT, False)
    state_manager.update_state(StateKey.USE_FLOW, False)
    await cl.Message(
        content="🔧 **Orbot Disabled**\n\nNow using the standard build mode handler.\n\nUse `/orbot-on` to re-enable the turn-by-turn agent."
    ).send()

    # Refresh commands to show orbot-on instead of orbot-off
    await refresh_commands()


async def handle_flow_on_command():
    """Handle flow on command"""
    state_manager = get_state_manager()

    info("Enabling flow for build mode")

    # Check if runtime is initialized first
    if not state_manager.get_state_value(
        StateKey.IS_RUNTIME_INITIALIZED, False
    ):
        await cl.Message(
            content="⏳ **Runtime not yet initialized**\n\nPlease wait a moment for the runtime to initialize before enabling Flow."
        ).send()
        return

    # Check if Flow is already initialized
    flow_agent = state_manager.get_state_value(StateKey.FLOW)
    if not flow_agent:
        # Initialize Orbot with MCP servers and tools
        try:
            await cl.Message(
                content="🔄 **Initializing Flow**\n\nSetting up MCP servers and tool registry..."
            ).send()

            runtime_info = state_manager.get_runtime_info()

            if not runtime_info:
                await cl.Message(
                    content="Runtime not initialized. Cannot setup Flow. Try again later"
                ).send()
                return

            info("Initializing Flow with MCP servers...")

            # Setup MCP servers.
            await load_system_mcp_servers()
            config = Configuration()
            config.validate_required_fields()
            tool_registry = ToolRegistry(config)
            await tool_registry.install_default_tools()
            await tool_registry.discover_mcp_tools()

            runtime = await get_runtime_session_manager().get_runtime(
                cl.context.session.id
            )

            state_manager.update_state(
                StateKey.FLOW,
                TurnByTurnAgent(
                    tool_registry,
                    config,
                    cl.context.session.thread_id,
                    get_flow_prompt(runtime),
                    get_compression_prompt(),
                ),
            )

            info(
                "Flow initialized successfully with MCP servers and tool registry"
            )

        except Exception as e:
            error(f"Failed to initialize Orbot: {e}")
            await cl.Message(
                content=f"❌ **Failed to initialize Flow**\n\nError: {str(e)}"
            ).send()
            return

    state_manager.update_state(StateKey.USE_FLOW, True)
    state_manager.update_state(StateKey.USE_ORBOT, False)
    await cl.Message(
        content="🤖 Flow Enabled. Use `/flow-off` to disable."
    ).send()

    # Refresh commands to show orbot-off instead of orbot-on
    await refresh_commands()


async def handle_flow_off_command():
    """Handle orbot off command"""
    state_manager = get_state_manager()

    info("Disabled Flow for build mode")
    state_manager.update_state(StateKey.USE_FLOW, False)
    state_manager.update_state(StateKey.USE_ORBOT, False)
    await cl.Message(
        content="🔧 **Flow Disabled**\n\nNow using the standard build mode handler.\n\nUse `/flow-on` to re-enable the flow agent."
    ).send()

    # Refresh commands to show orbot-on instead of orbot-off
    await refresh_commands()


async def handle_orbot_status_command():
    """Handle orbot status command"""
    state_manager = get_state_manager()

    use_orbot = state_manager.get_state_value(StateKey.USE_ORBOT, False)
    status = "**Enabled** 🤖" if use_orbot else "**Disabled** 🔧"
    await cl.Message(
        content=f"🤖 **Orbot Status:** {status}\n\n{'Using turn-by-turn agent for advanced conversations.' if use_orbot else 'Using standard build mode handler.'}\n\nCommands:\n• `/orbot-on` - Enable Orbot\n• `/orbot-off` - Disable Orbot"
    ).send()


async def handle_run_command() -> bool:
    """Handle run workflow command - only works in execution mode"""
    state_manager = get_state_manager()
    current_mode = state_manager.get_app_mode()

    # Only allow run in execution mode
    if current_mode != AppMode.EXECUTION:
        await cl.Message(
            content="❌ **Run command only available in EXECUTION mode**\n\nUse `/execution` to switch to execution mode first."
        ).send()
        return True  # Command handled, stop processing

    # Check if workflow exists
    workflow = await state_manager.get_workflow(cl.context.session.thread_id)
    if not workflow:
        await cl.Message(
            content="❌ No workflow saved. Switch to build mode with `/build` to create a workflow first."
        ).send()
        return True  # Command handled, stop processing

    # Execute the workflow
    await cl.Message(
        content=f"🚀 **Executing workflow '{workflow.name}'**\n\n⏳ Starting execution..."
    ).send()

    # Switch to execution mode and proceed with execution
    state_manager.update_state(StateKey.EXECUTION_MODE, True)

    # Return False to let WorkflowExecutionHandler continue processing
    return False


async def handle_help_command():
    """Handle help command with debug information"""
    state_manager = get_state_manager()
    current_mode = state_manager.get_app_mode()

    help_text = _get_help_text(current_mode)
    await cl.Message(content=help_text).send()


async def handle_state_command():
    """Handle state debug command"""
    state_manager = get_state_manager()

    state_display = await state_manager.format_state_for_display(
        include_sensitive=True
    )
    await cl.Message(
        content=f"🔍 **Current Session State**\n\n```json\n{state_display}\n```"
    ).send()


async def handle_user_simulator_command(id: int | None = None):
    """Handle user simulator test command"""
    state_manager = get_state_manager()

    info("Starting USER SIMULATOR")
    state_manager.set_app_mode(AppMode.BUILD)
    
    # Always reset the user simulator to start fresh
    from agent.user_simulator_agent import get_user_simulator
    user_simulator = get_user_simulator(use_case_id=id, reset=True)
    use_case_text = f"[{id}] {user_simulator.use_case.name}" + (" <by default>" if id is None else "")

    await cl.Message(
        content=(
            f"""
**USER SIMULATOR STARTED**

User simulator is now active in build mode.
Two agents will now talk to each other about the use case **{use_case_text}**:
• User Simulator Agent (sends messages)
• Orby Automation Agent (processes and responds)
Use `/build` to return to normal mode.
            """
        )
    ).send()
    
    await user_simulator.start_simulation(cl.context.session.id)


async def handle_user_simulator_help_command():
    """Handle user simulator help command"""
    try:
        from agent.use_case_dataset import load_use_cases
        
        use_cases = load_use_cases()
        
        # Build the use cases list
        use_cases_text = ""
        for use_case in use_cases:
            use_cases_text += f"• **Use Case {use_case.id}:** {use_case.name}\n"
        
        await cl.Message(
            content=f"""
🤖 **USER SIMULATOR HELP**

**What is the User Simulator?**
The User Simulator is a tool that allows you to test the workflow creation process of a use case.
It simulates a conversation between a User Simulator Agent and the Orby Automation Agent.

**How to Use It:**
1. **Start the Simulator:**
   - Use `/user-simulator <ID>` to start the simulator.
   - You need to specify a use case ID (e.g., `/user-simulator 1`).

2. **Simulator Flow:**
   - The User Simulator Agent (sends messages) and the Orby Automation Agent (processes and responds) will engage in a conversation.
   - The workflow will be created based on the conversation between the two agents.
   - The conversation will end when either agent stops it, or being interrupted by the user.

3. **Available Use Cases:**
{use_cases_text}

Use `/build` to return to the main build mode.
            """
        ).send()
    except Exception as e:
        await cl.Message(
            content=f"❌ **Error loading use cases**\n\n{e}"
        ).send()


def _get_help_text(current_mode: AppMode) -> str:
    """Get help text based on current mode"""
    if current_mode == AppMode.BUILD:
        return """
🔨 **BUILD MODE - Workflow Creation**

**Current Mode:** BUILD
**File Upload:** ✅ Supported - Upload files to include in workflow context

**Available Commands:**
• **Type your automation needs** to create workflows
• `/execution` - Switch to execution mode
• `/orbot-on` - Enable Orbot for advanced conversations  
• `/orbot-off` - Disable Orbot
• `/orbot-status` - Check Orbot status
• `/state` - Show current session state
• `/help` - Show this help

**What you can do:**
• Create workflows through conversation
• Upload files for workflow context
• Generate automation scripts  
• Build standard operating procedures
• Use Orbot for advanced reasoning

**Next Steps:**
1. Describe your automation needs or upload files
2. Review generated workflow
3. Use `/execution` to switch modes and run the workflow
        """
    elif current_mode == AppMode.EXECUTION:
        return """
⚙️ **EXECUTION MODE - Workflow Running**

**Current Mode:** EXECUTION  
**File Upload:** ✅ Supported - Upload input files for workflow execution

**Available Commands:**
• `/run` - Execute the current workflow
• `/build` - Switch to build mode to create/modify workflows
• `/state` - Show current session state
• `/help` - Show this help

**Execution Flow:**
1. **Upload files** if your workflow needs input data
2. Use `/run` to execute the workflow
3. Provide input parameters when prompted
4. View the workflow results

**What you can do:**
• Upload input files for workflow execution
• Execute existing workflows
• View execution results
• Switch back to build mode to modify workflows
        """
    else:  # single-agent mode
        return """
🤖 **SINGLE-AGENT MODE - Unified Agent Experience**

**Current Mode:** SINGLE-AGENT
**File Upload:** ✅ Supported

**Available Commands:**
• **Type your automation needs** for intelligent assistance
• `/build` - Switch to build mode for workflow creation
• `/execution` - Switch to execution mode for workflow running
• `/user-simulator` - Start user simulator for testing
• `/state` - Show current session state
• `/help` - Show this help

**What you can do:**
• Get intelligent assistance for automation tasks
• Upload files for context
• Switch between specialized modes
• Access unified agent capabilities
• Test with user simulator (two agents conversing)
        """


async def handle_cancel_operation():
    """
    Handle execution kill request.
    """
    try:
        state_manager = get_state_manager()
        runtime_session_manager = get_runtime_session_manager()
        runtime = await runtime_session_manager.get_runtime(
            cl.context.session.id
        )
        selected_workflow = await state_manager.get_workflow(
            cl.context.session.thread_id
        )
        operation_cancelled = await runtime.kill_active_process()
        exec_id = state_manager.get_execution_id()
        if operation_cancelled and exec_id:
            # Only release browser session if it was used by an execution
            runtime.release_browser_session()
            info(f"Execution killed for session {cl.context.session.id}")
            cancel_execution_message = {
                "type": WindowMessageType.EXECUTION_EVENT,
                "description": "Execution cancelled by user",
                "raw": "Execution cancelled by user",
                "status": "TASK_CANCELLED",
            }
            await state_manager.construct_and_send_window_message(
                **cancel_execution_message
            )
            stop_execution_message = {
                "type": WindowMessageType.EXECUTION_LIFECYCLE,
                "execution_id": exec_id,
                "event": "STOP",
            }
            await state_manager.construct_and_send_window_message(
                **stop_execution_message
            )
            tenant_info = await TenantInfoMixin()._get_tenant_info()
            desc_txt = f"{cancel_execution_message['status']}: {cancel_execution_message['description']}"

            await ExecutionService().cancel_execution(
                execution_id=exec_id,
                workflow_id=selected_workflow.mongo_id,
                desc=desc_txt,
                tenant_info=tenant_info,
            )
            state_manager.set_execution_id(None)
    except Exception as e:
        error(f"Failed to cancel operation: {e}")
