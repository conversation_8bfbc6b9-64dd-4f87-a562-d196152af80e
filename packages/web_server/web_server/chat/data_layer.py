"""
Data layer configuration for Chainlit chat interface
"""

import chainlit as cl

from common.log import error
from web_server.core.config import server_config
from web_server.data.orby_chainlit_data_layer import OrbyChainlitDataLayer


@cl.data_layer
def get_data_layer():
    """Initialize and return the data layer for Chainlit"""
    try:
        return OrbyChainlitDataLayer(
            postgres_url=server_config.postgres_url,
            default_bucket_name=server_config.chainlit_default_bucket,
        )
    except Exception as e:
        error(f"Failed to initialize data layer: {e}")
        raise
