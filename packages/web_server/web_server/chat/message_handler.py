"""
Message handling for Chainlit chat interface
"""

import chainlit as cl

from common.log import error


async def handle_message(message: cl.Message, message_processor):
    """Handle incoming messages from the chat interface"""
    try:
        await message_processor.process_message(message)
    except Exception as e:
        error(f"Unhandled error in message processing: {e}")
        await cl.Message(
            content="❌ An unexpected error occurred. Please try again."
        ).send()
