"""
Authentication handlers for Chainlit chat interface
"""

import chainlit as cl

from common.log import error
from common.models.auth import AuthPayload
from common.services.user_service import UserService
from web_server.models.chainlit_user import UserMetadata
from web_server.utils.auth import get_auth_payload_wrapper


@cl.header_auth_callback
async def header_auth_callback(headers) -> cl.User | None:
    """
    Callback function to authenticate chainlit user based on the headers.
    We will rely on the login mechanism from our grpc apis to authenticate the user.

    This method expects to have a CHAINLIT_AUTH_SECRET environment variable set as this is the secret used to sign the JWT token.
    This is generated by running `chainlit create-secret`.
    More Info - https://docs.chainlit.io/authentication/overview

    Args:
        headers: The HTTP headers from the request.

    Returns:
        cl.User | None: The authenticated user or None if authentication fails. We return None instead of raising an exception since
        its how chainlit handles authentication failures. Docs - https://docs.chainlit.io/authentication/header
    """

    try:
        auth_payload: AuthPayload = get_auth_payload_wrapper(headers)
        if (
            not auth_payload.user_id
            or not auth_payload.user_email
            or not auth_payload.org_id
        ):
            error("Auth payload is missing user_id or user_email or org_id")
            # If the user_id or user_email is not present, we will not be able to authenticate the user, so we return None.
            return None

        # Get the user from the database
        user_service = UserService()
        db_user = await user_service.get_user_by_email(auth_payload.user_email)
        if not db_user:
            # If the user is not found, we will not be able to authenticate the user, so we return None.
            error(f"User not found: {auth_payload.user_email}")
            return None

        # We will then set the org_id in the metadata
        metadata = UserMetadata(
            selected_org_id=str(auth_payload.org_id),
        )

        # Note: Chainlit also stores this user in the database, for same record it will update the metadata.
        return cl.User(
            identifier=auth_payload.user_email,
            display_name=db_user.full_name,
            metadata=dict(metadata),
        )

    except Exception as e:
        # If any exception occurs, we will not be able to authenticate the user, so we return None.
        error(f"Unexpected error authenticating user: {e}")
        return None 
