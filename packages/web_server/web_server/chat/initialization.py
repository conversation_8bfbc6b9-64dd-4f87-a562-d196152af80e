"""
Session initialization handlers for Chainlit chat interface
"""

import asyncio
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
import os
import shutil

import chainlit as cl
from chainlit.types import ThreadDict
from e2b import FilesystemEventType

from common.log import error, info
from common.sandbox.file_watcher import ProcessedEvent
from common.utils.gcs_utils import generate_signed_url
from web_server.constants.chat import WindowMessageType
from web_server.core.config import server_config
from web_server.session.runtime_manager import (
    get_runtime_session_manager,
)
from web_server.session.state_manager import (
    <PERSON><PERSON><PERSON><PERSON>,
    State<PERSON>ey,
    StateManager,
    get_state_manager,
)

# Initialize session manager once at module level
session_manager = get_runtime_session_manager(
    # runtime_type=RuntimeType.LOCAL,
    envs={"ORBY_BASE_URL": server_config.orby_base_url}
)

# Thread pool for runtime initialization - limit to reasonable number of workers
runtime_init_thread_pool = ThreadPoolExecutor(
    max_workers=100, thread_name_prefix="runtime-init"
)


async def initialize_runtime_async(session_id: str):
    """
    Initialize runtime asynchronously, running the heavy work in a thread pool.
    """
    state_manager = get_state_manager()
    previous_runtime_config = state_manager.get_runtime_info()
    thread_id = cl.context.session.thread_id
    info(f"Previous runtime config: {previous_runtime_config}")

    try:
        info(f"Creating runtime for session {session_id}")

        # Run the blocking runtime creation in a thread pool
        # Include workflow lookup in the thread to avoid blocking main thread
        def _create_runtime():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # Do workflow lookup directly via database in the thread
                # This avoids using state_manager which requires Chainlit context
                async def _get_runtime():
                    workflow = await StateManager.get_workflow_from_db(
                        thread_id
                    )

                    runtime = await session_manager.get_runtime(
                        session_id, previous_runtime_config, workflow
                    )
                    return runtime, workflow

                return loop.run_until_complete(_get_runtime())
            finally:
                loop.close()

        runtime, workflow = await asyncio.get_event_loop().run_in_executor(
            runtime_init_thread_pool, _create_runtime
        )

        info(f"Runtime created successfully for session {session_id}")
        state_manager.update_state(StateKey.IS_RUNTIME_INITIALIZED, True)
        # Update runtime info for potential reuse
        state_manager.update_runtime_info(runtime)

        # Update workflow (and notify client). Must be awaited.
        await state_manager.set_workflow(workflow)

        # Send window message to client indicating session is created
        await state_manager.construct_and_send_window_message(
            type=WindowMessageType.RUNTIME_INITIALIZED,
            session_id=runtime.sandbox_session_id,
        )

        if runtime.browser_session:
            await state_manager.construct_and_send_window_message(
                type=WindowMessageType.BB_SESSION_LIVE_VIEW_URL,
                url=runtime.browser_session.debugger_url,
            )

        info(
            f"Session initialized successfully with runtime {runtime.sandbox_session_id}"
        )

    except Exception as e:
        error(f"Failed to initialize runtime: {e}")
        # Mark as failed so other parts of the app know
        state_manager.update_state(StateKey.IS_RUNTIME_INITIALIZED, False)


def initialize_session_state(resumable_thread: ThreadDict | None = None):
    """
    Initialize session state.

    Args:
        resumable_thread: Optional thread to resume from
    """
    state_manager = get_state_manager()

    # Initialize session state (always needed since state is not persisted)
    state_manager.update_state(StateKey.IS_RUNTIME_INITIALIZED, False)

    # Reset attachments and saved workflows
    state_manager.update_state(StateKey.ATTACHMENTS, [])

    if not resumable_thread:
        state_manager.set_app_mode(AppMode.BUILD)
    else:
        pass
        # Extract attachments from resumable thread
        # attachments = []
        # elements = resumable_thread.get("elements") or []
        # for element in elements:
        #     if element.get("type") != "text":
        #         if element.get("url"):
        #             attachments.append(
        #                 Attachment(
        #                     name=element.get("name", "") or "",
        #                     path=element.get("url", "") or "",
        #                     mime=element.get("mime", "") or "",
        #                 )
        #             )
        # state_manager.update_state(StateKey.ATTACHMENTS, attachments)


async def start_chat_session():
    """Initialize the chat session."""
    state_manager = get_state_manager()
    session_id = state_manager.get_session_id()

    # Send window message indicating chat session is starting
    await state_manager.construct_and_send_window_message(
        type=WindowMessageType.CHAT_SESSION_START,
        session_id=session_id,
    )

    # Initialize session state
    initialize_session_state()

    # Inform client that no workflow is associated yet
    await state_manager.set_workflow(None)

    # Send simple welcome message without runtime info
    welcome_msg = """
🤖 **Workflow Automation Assistant**

🔨 **BUILD MODE** - You're currently in workflow creation mode.

**What you can do:**
• **Type your automation needs** to create workflows
• **Upload files** for workflow context
• Create standard operating procedures (SOPs)
• Generate automation scripts

**Available Commands:**
• `/execution` - Switch to execution mode to run workflows
• `/orbot-on` - Enable Orbot for advanced conversations  
• `/help` - Show all commands and help

💡 **Tip:** Type `/` and select commands from the dropdown

**What automation would you like to build today?**
"""

    await cl.Message(content=welcome_msg).send()

    # Start runtime initialization asynchronously and store task for potential cancellation
    runtime_init_task = asyncio.create_task(
        initialize_runtime_async(session_id)
    )
    state_manager.update_state(StateKey.RUNTIME_INIT_TASK, runtime_init_task)


async def end_chat_session():
    """Clean up on chat end."""
    state_manager = get_state_manager()
    session_id = state_manager.get_session_id()

    # Clean up temp files
    temp_dir = f"local_storage/{session_id}"
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)

    # Cancel runtime initialization task if running
    runtime_init_task = state_manager.get_state_value(
        StateKey.RUNTIME_INIT_TASK
    )
    if runtime_init_task is not None and not runtime_init_task.done():
        info(f"Cancelling runtime initialization task for session {session_id}")
        runtime_init_task.cancel()

    # Fire-and-forget runtime cleanup in worker thread - completely non-blocking
    def cleanup_runtime_worker():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:

            async def _cleanup():
                await session_manager.close_runtime(session_id)
                info(f"Runtime cleanup completed for session {session_id}")

            loop.run_until_complete(_cleanup())
        except Exception as e:
            error(f"Failed to close runtime for session {session_id}: {e}")
        finally:
            loop.close()

    asyncio.get_event_loop().run_in_executor(
        runtime_init_thread_pool, cleanup_runtime_worker
    )

    # Cancel exec-log dispatcher task
    dispatcher_task = state_manager.get_state_value(
        StateKey.EXEC_LOG_DISPATCHER_TASK
    )
    if dispatcher_task is not None:
        dispatcher_task.cancel()


async def handle_chat_resume(thread: ThreadDict):
    """Handle chat resume - simplified to just initialize runtime in thread."""
    state_manager = get_state_manager()
    session_id = state_manager.get_session_id()

    # Send window message indicating chat session is resuming
    await state_manager.construct_and_send_window_message(
        type=WindowMessageType.CHAT_SESSION_RESUME,
        session_id=session_id,
    )

    # Attempt to fetch an existing workflow for this thread so we can inform the
    # client immediately. If none exists we will still send an empty id.
    existing_workflow = await state_manager.get_workflow(
        cl.context.session.thread_id
    )
    if existing_workflow:
        # TODO: This is a temporary solution to get the files associated with the workflow
        for (
            file_path,
            signed_url,
        ) in state_manager.get_temp_file_paths().items():
            signed_url = await generate_signed_url(signed_url)
            event = ProcessedEvent(
                name=file_path,
                type=FilesystemEventType.CREATE,
                signed_url=await generate_signed_url(signed_url),
                is_sop=file_path.endswith("SOP.md"),
            )
            await state_manager.construct_and_send_window_message(
                type=WindowMessageType.FILE_CHANGE_EVENT,
                event_data=event.to_dict(),
            )

    # Initialize session state with resume data
    initialize_session_state(resumable_thread=thread)

    # Start runtime initialization asynchronously (fire-and-forget)
    runtime_init_task = asyncio.create_task(
        initialize_runtime_async(session_id)
    )
    state_manager.update_state(StateKey.RUNTIME_INIT_TASK, runtime_init_task)
