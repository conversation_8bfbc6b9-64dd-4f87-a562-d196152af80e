"""
Chat module for Chainlit interface components
"""

from .auth import header_auth_callback
from .data_layer import get_data_layer
from .initialization import (
    end_chat_session,
    handle_chat_resume,
    start_chat_session,
)
from .mcp_handlers import (
    check_and_process_pending_connections,
    on_mcp_connect,
    on_mcp_disconnect,
)
from .message_handler import handle_message

__all__ = [
    "header_auth_callback",
    "start_chat_session",
    "end_chat_session",
    "handle_chat_resume",
    "handle_message",
    "on_mcp_connect",
    "on_mcp_disconnect",
    "check_and_process_pending_connections",
    "get_data_layer",
]
