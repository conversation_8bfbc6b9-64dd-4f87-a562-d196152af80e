"""
MCP (Model Control Protocol) connection handlers for Chainlit.
Handles MCP server connections and tool discovery.
"""

import asyncio
from typing import cast

import chainlit as cl
from mcp import ClientSession
from orbot.turn_by_turn_agent import TurnByTurnAgent

from common.log import error, info, warn
from web_server.session.state_manager import <PERSON><PERSON><PERSON>, get_state_manager

# Store pending MCP connections that couldn't register tools due to agent not being ready
pending_mcp_connections: list[tuple[str, ClientSession]] = []


async def wait_for_agent(timeout: float = 30.0) -> TurnByTurnAgent | None:
    """
    Wait for the agent to be populated in the state manager.
    
    Args:
        timeout: Maximum time to wait in seconds
        
    Returns:
        The agent if found within timeout, None otherwise
    """
    state_manager = get_state_manager()
    start_time = asyncio.get_event_loop().time()
    
    while asyncio.get_event_loop().time() - start_time < timeout:
        agent = state_manager.get_state_value(StateKey.ORBOT)
        if agent is not None:
            info("Agent found in state manager")
            return cast(TurnByTurnAgent, agent)
        
        # Wait a bit before checking again
        await asyncio.sleep(0.5)
    
    warn(f"Agent not found in state manager after {timeout} seconds")
    return None


async def process_pending_mcp_connections():
    """
    Process any pending MCP connections once the agent becomes available.
    This runs as a background task.
    """
    global pending_mcp_connections
    
    if not pending_mcp_connections:
        return
    
    info(f"Processing {len(pending_mcp_connections)} pending MCP connections")
    
    # Wait for agent to become available
    agent = await wait_for_agent(timeout=60.0)  # Longer timeout for background processing
    
    if not agent:
        warn("Agent still not available after timeout, abandoning pending MCP connections")
        pending_mcp_connections.clear()
        return
    
    # Process all pending connections
    processed = []
    for server_name, session in pending_mcp_connections:
        try:
            info(f"Processing pending MCP connection: {server_name}")
            
            # Discover tools from this server
            registered_tools = await agent.tool_registry.discovery_tools_of_mcp_server(
                server_name, 
                session
            )
            if registered_tools > 0:
              processed.append(server_name)
            
        except Exception as e:
            error(f"Error processing pending MCP connection {server_name}: {e}")
    
    # Re-bind all tools to the client
    if processed:
        agent.bind_tools(agent.tool_registry.get_all_tools())
        
        # Notify user
        await cl.Message(
            content=f"🔄 **Registered tools from {len(processed)} pending MCP servers:**\n" +
            "\n".join([f"• {name}" for name in processed])
        ).send()
    
    # Clear pending connections
    pending_mcp_connections.clear()


@cl.on_mcp_connect # type: ignore
async def on_mcp_connect(connection, session: ClientSession):
    """
    Handle MCP server connection events.
    This is called when a new MCP server is connected.
    
    Args:
        connection: Connection information containing server details
        session: The MCP client session
    """
    try:
        info(f"MCP connection established: {connection.name}")
        # List available tools from the MCP server
        result = await session.list_tools()
        
        if result and result.tools:
            info(f"Discovered {len(result.tools)} tools from MCP server: {connection.name}")
            
            # Wait for agent to be available
            agent = await wait_for_agent()
            
            if agent:
                
                # Discover tools from this specific server
                registered_tool_count = await agent.tool_registry.discovery_tools_of_mcp_server(
                    connection.name, 
                    session
                )
                if registered_tool_count > 0:
                    # Re-bind tools to the client
                    agent.bind_tools(agent.tool_registry.get_all_tools())
                
                # Send notification to user
                await cl.Message(
                    content=f"✅ Connected to MCP server **{connection.name}**\n"
                    f"Discovered {len(result.tools)} tools:\n" +
                    "\n".join([f"• `{t.name}` - {t.description or 'No description'}" 
                              for t in result.tools[:5]]) +
                    (f"\n... and {len(result.tools) - 5} more" if len(result.tools) > 5 else "")
                ).send()
            else:
                # Agent not ready yet, store connection for later
                info(f"Agent not ready, storing MCP connection {connection.name} for later")
                pending_mcp_connections.append((connection.name, session))
                
                await cl.Message(
                    content=f"✅ Connected to MCP server **{connection.name}**\n"
                    f"Discovered {len(result.tools)} tools. Tools will be registered once initialization completes."
                ).send()

        else:
            await cl.Message(
                content=f"✅ Connected to MCP server **{connection.name}**\n"
                f"No tools found on this server."
            ).send()
            
    except Exception as e:
        error(f"Error in MCP connect handler: {e}")
        await cl.Message(
            content=f"⚠️ Connected to MCP server **{connection.name}** but encountered an error: {str(e)}"
        ).send()


@cl.on_mcp_disconnect # type: ignore
async def on_mcp_disconnect(name: str, session: ClientSession):
    """
    Handle MCP server disconnection events.
    This is called when an MCP server is disconnected.
    
    Args:
        connection: Connection information containing server details
    """
    try:
        info(f"MCP disconnection: {name}")
        
        # Wait for agent to be available
        agent = await wait_for_agent(timeout=5.0)  # Shorter timeout for disconnect
        
        if agent:
            removed_count = agent.tool_registry.unregister_mcp_server_tools(name)
                
            # Re-bind remaining tools to the client
            agent.bind_tools(agent.tool_registry.get_all_tools())
            
            await cl.Message(
                content=f"🔌 Disconnected from MCP server **{name}**\n"
                f"Removed {removed_count} tools from this server."
            ).send()
        else:
            # Agent not available, just notify about disconnection
            await cl.Message(
                content=f"🔌 Disconnected from MCP server **{name}**"
            ).send()
            
    except Exception as e:
        error(f"Error in MCP disconnect handler: {e}")
        await cl.Message(
            content=f"⚠️ Error disconnecting from MCP server **{name}**: {str(e)}"
        ).send()


async def check_and_process_pending_connections():
    """
    Check for and process any pending MCP connections.
    This can be called after agent initialization is complete.
    """
    if pending_mcp_connections:
        info(f"Found {len(pending_mcp_connections)} pending MCP connections to process")
        await process_pending_mcp_connections()
