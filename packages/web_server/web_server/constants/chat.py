from enum import Enum


class WindowMessageType(str, Enum):
    """Enumerates all window message types used between server and client."""

    RUNTIME_INITIALIZATION_STARTED = "RUNTIME-INITIALIZATION-STARTED"
    RUNTIME_INITIALIZED = "RUNTIME-INIT<PERSON>LIZED"
    CHAT_SESSION_START = "CHAT-SESSION-START"
    CHAT_SESSION_RESUME = "CHAT-SESSION-RESUME"
    BB_SESSION_LIVE_VIEW_URL = "BROWSERBASE_LIVE_VIEW_URL"
    EXECUTION_EVENT = "EXECUTION_EVENT"
    # Sent when the server notifies the client which workflow (if any) is
    # associated with the active thread. `workflow_mongo_id` can be an empty
    # string when no workflow exists yet.
    WORKFLOW_ID_INFO = "WORKFLOW_ID_INFO"
    RECORDING_READY = "RECORDING_READY"
    REVIEW_REQUEST = "REVIEW_REQUEST"
    EXECUTION_LIFECYCLE = "EXECUTION_LIFECYCLE"
    FILE_CHANGE_EVENT = "FILE_CHANGE_EVENT"
    WORKFLOW_READY = "WORKFLOW_READY"
    MODE_CHANGE = "MODE_CHANGE"
