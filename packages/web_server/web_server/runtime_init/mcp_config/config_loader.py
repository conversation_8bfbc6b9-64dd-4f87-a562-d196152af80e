import json
from pathlib import Path
from typing import Any

import yaml

from common.log.log import warn


class MCPConfigManager:
    """Manages MCP server configurations with dynamic addition and override capabilities."""

    def __init__(self, config_path: Path | None = None):
        """Initialize the configuration manager."""
        if config_path is None:
            config_path = Path(__file__).parent / "mcp_config.yaml"

        self.config_path = config_path
        self._base_config = self._load_config()
        # Runtime dictionary of MCP servers
        self.mcp_servers: dict[str, dict[str, Any]] = {}
        self._load_default_servers()

    def _load_config(self) -> dict[str, Any]:
        """Load base configuration from YAML file."""
        try:
            with open(self.config_path) as file:
                return yaml.safe_load(file) or {}
        except FileNotFoundError:
            warn(f"Configuration file not found: {self.config_path}")
            return {}
        except yaml.YAMLError as e:
            warn(f"Error parsing YAML configuration: {e}")
            return {}

    def _load_default_servers(self):
        """Load default servers from YAML into the runtime dictionary."""
        default_servers = self._base_config.get("default", {})
        for server_name, config in default_servers.items():
            self.mcp_servers[server_name] = {
                "command": config.get("command"),
                "args": config.get("args", []).copy(),
            }

    def add_server(
        self, server_name: str, command: str, args: list | None = None
    ):
        """Add a new MCP server or override existing one."""
        self.mcp_servers[server_name] = {"command": command, "args": args or []}

    def add_server_args(self, server_name: str, args: list):
        """Add additional args to an existing server."""
        if server_name in self.mcp_servers:
            self.mcp_servers[server_name]["args"].extend(args)

    def get_server_config(self, server_name: str, **kwargs) -> dict[str, Any]:
        """Get configuration for an MCP server with optional dynamic args."""
        if server_name not in self.mcp_servers:
            return {}

        # Start with base config
        server_config = self.mcp_servers[server_name].copy()

        # Add any additional args from kwargs
        if kwargs:
            additional_args = kwargs.get("args", [])
            if additional_args:
                server_config["args"].extend(additional_args)

        return server_config

    def list_servers(self) -> list:
        """List all available MCP servers."""
        return list(self.mcp_servers.keys())

    def export_to_json(self, file_path: Path | None = None) -> str:
        """Export current MCP servers configuration to JSON format."""
        config = {"mcpServers": self.mcp_servers}

        json_str = json.dumps(config, indent=2)

        if file_path:
            with open(file_path, "w") as f:
                f.write(json_str)

        return json_str

    def get_mcp_servers_dict(self) -> dict[str, dict[str, Any]]:
        """Get the current MCP servers dictionary."""
        return self.mcp_servers.copy()
