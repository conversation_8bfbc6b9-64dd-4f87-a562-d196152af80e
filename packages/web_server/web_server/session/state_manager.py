"""
Session State Manager for Chainlit Application
Provides centralized state management and debugging utilities
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json
from typing import Any, cast

from bson import ObjectId
import chainlit as cl

from common.log import error, info
from common.sandbox.base import BrowserSession, Runtime, RuntimeType
from web_server.constants.chat import WindowMessageType
from web_server.services.workflow_service import WorkflowService


class AppMode(Enum):
    BUILD = "build"
    EXECUTION = "execution"
    SINGLE_AGENT = "single-agent"


class StateKey(Enum):
    """Enum for all state keys to provide type safety and prevent typos"""

    # Core app state
    APP_MODE = "app_mode"
    IS_RUNTIME_INITIALIZED = "is_runtime_initialized"

    # File handling
    ATTACHMENTS = "attachments"

    # Runtime state
    RUNTIME_INFO = "runtime_info"

    # Internal state
    EXEC_LOG_DISPATCHER_TASK = "_exec_log_dispatcher_task"
    RUNTIME_INIT_TASK = "_runtime_init_task"

    USE_ORBOT = "use_orbot"
    USE_FLOW = "use_flow"
    EXECUTION_MODE = "execution_mode"
    EXECUTION_ID = "execution_id"

    ORBOT = "orbot"
    FLOW = "flow"

    # Workflow state
    WORKFLOW = "workflow"

    # File handling
    TEMP_FILE_PATHS = "temp_file_paths"


@dataclass
class RuntimeConfiguration:
    """Represents the runtime configuration"""

    chainlit_session_id: str
    runtime_type: str = RuntimeType.E2B.value
    sandbox_session_id: str = ""
    browser_session: BrowserSession | None = None
    is_previous_runtime_session_restored: bool = False

    def to_dict(self) -> dict:
        """Convert to dictionary for JSON serialization"""
        return {
            "chainlit_session_id": self.chainlit_session_id,
            "runtime_type": self.runtime_type,
            "sandbox_session_id": self.sandbox_session_id,
            "browser_session": {
                "id": self.browser_session.id,
                "connect_url": self.browser_session.connect_url,
                "debugger_url": self.browser_session.debugger_url,
                "context_id": self.browser_session.context_id,
            }
            if self.browser_session
            else None,
            "is_previous_runtime_session_restored": self.is_previous_runtime_session_restored,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "RuntimeConfiguration":
        """Create instance from dictionary"""
        browser_session = None
        if data.get("browser_session"):
            browser_session = BrowserSession(
                id=data["browser_session"]["id"],
                connect_url=data["browser_session"]["connect_url"],
                debugger_url=data["browser_session"]["debugger_url"],
                context_id=data["browser_session"]["context_id"],
            )

        return cls(
            chainlit_session_id=data["chainlit_session_id"],
            runtime_type=data.get("runtime_type", RuntimeType.E2B.value),
            sandbox_session_id=data.get("sandbox_session_id", ""),
            browser_session=browser_session,
            is_previous_runtime_session_restored=data.get(
                "is_previous_runtime_session_restored", False
            ),
        )


@dataclass
class Attachment:
    """Represents an attachment"""

    name: str = ""
    path: str = ""
    size: int = 0
    mime: str = ""


@dataclass
class Workflow:
    """Represents a workflow"""

    name: str
    mongo_id: ObjectId
    # The corresponding chat thread id of the workflow
    thread_id: str
    is_executable: bool = False
    commit_hash: str = ""
    bb_context_id: str = ""

    def to_dict(self) -> dict:
        """Convert to dictionary for JSON serialization"""
        return {
            "name": self.name,
            "mongo_id": str(self.mongo_id),
            "thread_id": self.thread_id,
            "is_executable": self.is_executable,
            "commit_hash": self.commit_hash,
            "bb_context_id": self.bb_context_id,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "Workflow":
        """Create instance from dictionary"""
        mongo_id_str = data.get("mongo_id")
        if not mongo_id_str:
            # Handle cases where the ID might be missing or empty
            raise ValueError(
                "mongo_id is required to restore a Workflow object from dict"
            )

        return cls(
            name=data.get("name", ""),
            mongo_id=ObjectId(mongo_id_str),
            thread_id=data.get("thread_id", ""),
            is_executable=data.get("is_executable", False),
            commit_hash=data.get("commit_hash", ""),
            bb_context_id=data.get("bb_context_id", ""),
        )


@dataclass
class UserSessionState:
    """Represents the complete session state"""

    # Core session info
    session_id: str
    created_at: str
    last_updated: str

    # App state
    app_mode: AppMode = AppMode.BUILD
    is_runtime_initialized: bool = False

    # Workflow state
    workflow: Workflow | None = None

    # File handling
    attachments: list[Attachment] = field(default_factory=list)

    # Runtime state
    runtime_info: RuntimeConfiguration | None = None

    # TODO: This is a temporary solution to add temp file paths to the workflow.
    # Key is the file path, value is the signed url
    temp_file_paths: dict[str, str] = field(default_factory=dict)


class StateManager:
    """Manages session state for Chainlit application"""

    def __init__(self):
        # By default, we don't want to enable debug mode
        self.debug_mode = False

    async def construct_and_send_window_message(
        self, type: WindowMessageType, **kwargs
    ) -> None:
        """Construct a window message (JSON string) and send it to the client.

        Args:
            type: A ``WindowMessageType`` enum value indicating the message category.
            **kwargs: Additional fields to include in the JSON payload.
        """
        message_data = {"type": type.value}
        message_data.update(kwargs)
        message = json.dumps(message_data)
        await self.send_window_message(message)

    async def send_window_message(self, message: str) -> None:
        """
        Send a window message (JSON string) to the client through chainlit.

        Args:
            message: stringified JSON message to send to the client
        """
        try:
            await cl.send_window_message(message)

            if self.debug_mode:
                info(f"Window message sent: {message}")

        except Exception as e:
            error(f"Failed to send window message: {message} - {e}")

    async def collect_session_state(self) -> UserSessionState:
        """Collect all current session state into a structured format"""
        try:
            session_id = cl.context.session.id
            current_time = datetime.now().isoformat()
            workflow = await self.get_workflow(cl.context.session.thread_id)
            # Collect all session data with proper type casting
            state = UserSessionState(
                session_id=session_id,
                created_at=current_time,
                last_updated=current_time,
                app_mode=cast(
                    AppMode,
                    cl.user_session.get(StateKey.APP_MODE.value, AppMode.BUILD),
                ),
                is_runtime_initialized=cast(
                    bool,
                    cl.user_session.get(
                        StateKey.IS_RUNTIME_INITIALIZED.value, False
                    ),
                ),
                workflow=workflow,
                attachments=cast(
                    list[Attachment],
                    cl.user_session.get(StateKey.ATTACHMENTS.value, []),
                ),
                runtime_info=self.get_runtime_info(),
                temp_file_paths=self.get_temp_file_paths(),
            )

            return state

        except Exception as e:
            error(f"Error collecting session state: {e}")
            return UserSessionState(
                session_id="unknown",
                created_at=datetime.now().isoformat(),
                last_updated=datetime.now().isoformat(),
            )

    async def format_state_for_display(
        self, include_sensitive: bool = False
    ) -> str:
        """Format state for display in the UI"""
        state = await self.collect_session_state()

        def _get_attachment_size(attachment: Attachment) -> str:
            if attachment.size < 1024:
                return f"{attachment.size} bytes"
            elif attachment.size < 1024 * 1024:
                return f"{attachment.size / 1024:.1f} KB"
            else:
                return f"{attachment.size / (1024 * 1024):.1f} MB"

        # Create a display-friendly version
        display_state = {
            "🔍 Session Info": {
                "ID": state.session_id,
                "Created": state.created_at,
                "Last Updated": state.last_updated,
            },
            "🔧 App State": {
                "Mode": (
                    "🔨 BUILD"
                    if state.app_mode == AppMode.BUILD
                    else "⚙️ EXECUTION"
                ),
                "Runtime Ready": "✅ Ready"
                if state.is_runtime_initialized
                else "❌ Not Ready",
            },
            "📋 Workflow": state.workflow.to_dict()
            if state.workflow
            else "None",
            "📎 Files": {
                "Attachments Count": len(state.attachments),
                "Files": [
                    {
                        "name": a.name,
                        "size": _get_attachment_size(a),
                        "mime": a.mime,
                        "path": a.path,
                    }
                    if a is not None
                    else None
                    for a in state.attachments
                ]
                if state.attachments
                else [],
            },
            "🔗 Runtime Info": (
                {
                    "chainlit_session_id": state.runtime_info.chainlit_session_id,
                    "runtime_type": state.runtime_info.runtime_type,
                    "sandbox_session_id": state.runtime_info.sandbox_session_id,
                    "browser_session": vars(state.runtime_info.browser_session)
                    if state.runtime_info.browser_session
                    else None,
                    "is_previous_runtime_session_restored": state.runtime_info.is_previous_runtime_session_restored,
                }
                if state.runtime_info
                else {"runtime_type": RuntimeType.LOCAL.value}
            ),
            "📎 Workflow Files": [
                {
                    "name": file_path,
                }
                for file_path in state.temp_file_paths.keys()
            ],
        }

        return json.dumps(display_state, indent=2, ensure_ascii=False)

    def update_state(self, key: StateKey, value: Any) -> None:
        """Update a specific state value and log the change"""
        # Convert enum to string if needed
        key_str = key.value if isinstance(key, StateKey) else key

        old_value = cl.user_session.get(key_str)
        cl.user_session.set(key_str, value)

        if self.debug_mode:
            info(f"State updated: {key_str} = {value} (was: {old_value})")

    def get_state_value(self, key: StateKey, default: Any = None) -> Any:
        """Get a state value with optional default"""
        # Convert enum to string if needed
        key_str = key.value if isinstance(key, StateKey) else key
        return cl.user_session.get(key_str, default)

    def enable_debug_mode(self) -> None:
        """Enable debug mode for verbose logging"""
        self.debug_mode = True
        info("State manager debug mode enabled")

    def disable_debug_mode(self) -> None:
        """Disable debug mode"""
        self.debug_mode = False
        info("State manager debug mode disabled")

    # Convenience methods for common state operations
    def get_session_id(self) -> str:
        """Get current session id"""
        return cl.context.session.id

    def get_app_mode(self) -> AppMode:
        """Get current app mode"""
        return self.get_state_value(StateKey.APP_MODE, AppMode.BUILD)

    def get_execution_id(self) -> str | None:
        """Get current execution id"""
        return self.get_state_value(StateKey.EXECUTION_ID)

    def set_app_mode(self, mode: AppMode) -> None:
        """Set app mode"""
        self.update_state(StateKey.APP_MODE, mode)

    def is_execution_mode(self) -> bool:
        """Check if in execution mode"""
        return self.get_app_mode() == AppMode.EXECUTION

    def set_execution_mode(self) -> None:
        """Set execution mode"""
        self.update_state(StateKey.APP_MODE, AppMode.EXECUTION)

    def set_execution_id(self, execution_id) -> None:
        """Set current execution id"""
        self.update_state(StateKey.EXECUTION_ID, execution_id)

    @staticmethod
    async def get_workflow_from_db(thread_id: str) -> Workflow | None:
        """Static method to get workflow from database by thread_id"""
        if not thread_id:
            return None

        workflow_repo = WorkflowService()
        mongo_workflow = await workflow_repo.get_workflow_by_thread_id(
            thread_id
        )

        if mongo_workflow:
            return Workflow(
                name=mongo_workflow.display_name or "Unknown Workflow Name",
                mongo_id=str(mongo_workflow.id),
                thread_id=thread_id,
                is_executable=True,
                commit_hash=mongo_workflow.git_repository.commit_hash
                if mongo_workflow.git_repository
                else "",
                bb_context_id=mongo_workflow.bb_context_id or "",
            )
        return None

    async def get_workflow(self, thread_id: str) -> Workflow | None:
        """Get selected workflow from state or database"""
        if not thread_id:
            return None

        # Check state first
        workflow_data = self.get_state_value(StateKey.WORKFLOW)
        if workflow_data:
            if isinstance(workflow_data, dict):
                return Workflow.from_dict(workflow_data)
            elif isinstance(workflow_data, Workflow):
                return workflow_data

        # Fetch from database if not in state
        workflow = await self.get_workflow_from_db(thread_id)
        if workflow:
            await self.set_workflow(workflow)

        return workflow

    async def set_workflow(self, workflow: Workflow | None) -> None:
        """Set selected workflow in session state"""
        if workflow is not None:
            # Store the workflow (as dict to keep session serialisable)
            self.update_state(StateKey.WORKFLOW, workflow.to_dict())

            # Notify the client – always send a message, whether the workflow was
            # newly created or loaded from the DB.
            await self.construct_and_send_window_message(
                type=WindowMessageType.WORKFLOW_ID_INFO,
                thread_id=workflow.thread_id,
                workflow_mongo_id=str(workflow.mongo_id),
            )

        else:
            self.update_state(StateKey.WORKFLOW, None)

            # Inform client that the current thread has no associated workflow yet
            await self.construct_and_send_window_message(
                type=WindowMessageType.WORKFLOW_ID_INFO,
                thread_id=cl.context.session.thread_id,
                workflow_mongo_id="",
            )

    def get_temp_file_paths(self) -> dict[str, str]:
        """Get temp file paths"""
        return self.get_state_value(StateKey.TEMP_FILE_PATHS, {})

    # TODO: This is a temporary solution to add temp file paths to the workflow.
    def add_temp_file_path(self, file_path: str, signed_url: str) -> None:
        """Add a temp file path to the workflow"""
        temp_file_paths = self.get_temp_file_paths()
        temp_file_paths[file_path] = signed_url
        self.update_state(StateKey.TEMP_FILE_PATHS, temp_file_paths)

    def get_attachments(self) -> list[Attachment]:
        """Get attachments"""
        return self.get_state_value(StateKey.ATTACHMENTS, [])

    def add_attachment(self, attachment: Attachment) -> None:
        """Add an attachment"""
        attachments = self.get_attachments()
        attachments.append(attachment)
        self.update_state(StateKey.ATTACHMENTS, attachments)

    def get_runtime_info(self) -> RuntimeConfiguration | None:
        """Get runtime information"""
        runtime_data = self.get_state_value(StateKey.RUNTIME_INFO)
        if runtime_data and isinstance(runtime_data, dict):
            return RuntimeConfiguration.from_dict(runtime_data)
        elif isinstance(runtime_data, RuntimeConfiguration):
            # Handle legacy case where it might already be a RuntimeConfiguration
            return runtime_data
        return None

    def update_runtime_info(self, runtime: Runtime) -> None:
        """Update runtime information"""
        # Only update runtime info if the runtime is an E2B runtime
        if runtime.runtime_type == RuntimeType.E2B:
            runtime_config = RuntimeConfiguration(
                chainlit_session_id=runtime.config.chainlit_session_id,
                sandbox_session_id=runtime.sandbox_session_id or "",
                browser_session=runtime.browser_session,
                is_previous_runtime_session_restored=runtime.is_previous_runtime_session_restored,
            )
            # Store as dictionary for JSON serialization
            self.update_state(StateKey.RUNTIME_INFO, runtime_config.to_dict())
        elif runtime.runtime_type == RuntimeType.LOCAL:
            runtime_config = RuntimeConfiguration(
                chainlit_session_id=runtime.config.chainlit_session_id,
                sandbox_session_id="",  # local
                browser_session=None,  # local
                is_previous_runtime_session_restored=runtime.is_previous_runtime_session_restored,
            )
            # Store as dictionary for JSON serialization
            self.update_state(StateKey.RUNTIME_INFO, runtime_config.to_dict())


# Global state manager instance
state_manager = StateManager()


def get_state_manager() -> StateManager:
    """Get the global state manager instance"""
    return state_manager
