"""
Runtime Session Manager - Manages runtime instances for chat sessions.
Provides singleton pattern for managing multiple runtime sessions with proper lifecycle management.
"""

import asyncio
import os
from typing import Optional

from common.log import error, info
from common.sandbox.base import (
    PreviousRuntimeSessionConfig,
    Runtime,
    RuntimeConfig,
    RuntimeType,
)
from common.sandbox.impl.e2b import E2BRuntime
from common.sandbox.impl.local import LocalRuntime
from web_server.session.state_manager import (
    RuntimeConfiguration,
    Workflow,
)


class RuntimeSessionManager:
    """
    Singleton manager for runtime sessions with proper lifecycle management.

    Each chat session gets its own isolated runtime instance.
    Supports both local and E2B runtime types with automatic detection.
    """

    _instance: Optional["RuntimeSessionManager"] = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(
        self,
        runtime_type: RuntimeType | None = None,
        envs: dict[str, str] | None = None,
    ):
        # Prevent re-initialization of the singleton
        if hasattr(self, "_initialized"):
            return

        self._runtime_registry = {
            RuntimeType.LOCAL: LocalRuntime,
            RuntimeType.E2B: E2BRuntime,
        }
        self.runtime_type = runtime_type or self._detect_runtime_type()
        self._runtime_sessions: dict[str, Runtime] = {}
        self._runtime_session_lock: dict[str, asyncio.Lock] = {}
        self._initialized = True
        self._envs = envs or {}

        # Validate required environment variables
        self._validate_required_env_vars()

    def _validate_required_env_vars(self):
        """Validate that required environment variables are set."""
        # Skip environment validation when running tests
        if os.getenv("PYTEST_RUNNING"):
            return

        required_vars = [
            "ANTHROPIC_API_KEY",
            "OPENAI_API_KEY",
            "GOOGLE_API_KEY",
            "GOOGLE_CLOUD_PROJECT_ID",
        ]

        for var in required_vars:
            if not os.getenv(var):
                raise RuntimeError(f"{var} environment variable is not set")

        # Add required API keys to environment variables
        anthropic_key = os.getenv("ANTHROPIC_API_KEY")
        openai_key = os.getenv("OPENAI_API_KEY")
        google_key = os.getenv("GOOGLE_API_KEY")
        google_project_id = os.getenv("GOOGLE_CLOUD_PROJECT_ID")

        if anthropic_key and openai_key and google_key and google_project_id:
            self._envs.update(
                {
                    "ANTHROPIC_API_KEY": anthropic_key,
                    "OPENAI_API_KEY": openai_key,
                    "GOOGLE_API_KEY": google_key,
                    "GOOGLE_CLOUD_PROJECT_ID": google_project_id,
                }
            )

    async def get_runtime(
        self,
        session_id: str,
        session_runtime_info: RuntimeConfiguration | None = None,
        workflow: Workflow | None = None,
    ) -> Runtime:
        """
        Get or create a runtime instance for the given session.

        Args:
            session_id: Unique identifier for the chat session

        Returns:
            Runtime instance for the session

        Raises:
            RuntimeError: If runtime initialization fails
        """
        # Create lock if it doesn't exist for this session
        if session_id not in self._runtime_session_lock:
            self._runtime_session_lock[session_id] = asyncio.Lock()

        async with self._runtime_session_lock[session_id]:
            # Return existing runtime if available
            if session_id in self._runtime_sessions:
                existing_runtime = self._runtime_sessions[session_id]
                return existing_runtime

            # Create a new runtime session
            try:
                previous_config = None
                if session_runtime_info:
                    previous_config = PreviousRuntimeSessionConfig(
                        chainlit_session_id=session_runtime_info.chainlit_session_id,
                        sandbox_session_id=session_runtime_info.sandbox_session_id,
                        browser_session_id=session_runtime_info.browser_session.id
                        if session_runtime_info.browser_session
                        else None,
                    )
                bb_context_id = None
                if workflow and workflow.bb_context_id:
                    bb_context_id = workflow.bb_context_id
                elif (
                    session_runtime_info
                    and session_runtime_info.browser_session.context_id
                ):
                    bb_context_id = (
                        session_runtime_info.browser_session.context_id
                    )

                runtime_config = RuntimeConfig(
                    run_type=self.runtime_type,
                    chainlit_session_id=session_id,
                    envs=self._envs,
                    use_browser_session=self.runtime_type == RuntimeType.E2B,
                    previous_runtime_session_config=previous_config,
                    workflow_commit_hash=workflow.commit_hash
                    if workflow
                    else None,
                    bb_context_id=bb_context_id,
                )

                runtime = self._runtime_registry[self.runtime_type](
                    runtime_config
                )

                # Initialize the runtime
                await runtime.initialize()
                info(
                    f"Initialized {self.runtime_type} runtime for session {session_id}"
                )

                self._runtime_sessions[session_id] = runtime
                return runtime

            except Exception as e:
                error(
                    f"Error initializing runtime for session {session_id}: {e}"
                )
                raise RuntimeError(
                    f"Failed to initialize runtime: {str(e)}"
                ) from e

    async def close_runtime(self, session_id: str) -> None:
        """
        Clean up and close a runtime session.

        Args:
            session_id: Session identifier to close
        """
        if session_id not in self._runtime_sessions:
            return

        runtime = self._runtime_sessions.pop(session_id)
        # Clean up the associated lock
        self._runtime_session_lock.pop(session_id, None)

        try:
            await runtime.stop()
            info(f"Closed runtime for session {session_id}")

        except Exception as e:
            error(f"Error stopping runtime for session {session_id}: {e}")

    async def cleanup_all_runtimes(self) -> None:
        """
        Clean up all active runtime sessions.
        This should be called during application shutdown.
        """
        if not self._runtime_sessions:
            info("No active runtime sessions to clean up")
            return

        info(
            f"Cleaning up {len(self._runtime_sessions)} active runtime sessions..."
        )

        # Create tasks for all runtime cleanup operations
        cleanup_tasks = []
        session_ids = list(self._runtime_sessions.keys())

        for session_id in session_ids:
            cleanup_tasks.append(self.close_runtime(session_id))

        # Wait for all cleanup operations to complete
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            info("All runtime sessions cleaned up")

        # Clear the registry
        self._runtime_sessions.clear()
        self._runtime_session_lock.clear()

    async def get_session_states(
        self,
    ) -> dict[str, RuntimeType | int | list[str]]:
        """
        Get current state information about all sessions.

        Returns:
            Dictionary with runtime statistics
        """
        return {
            "runtime_type": self.runtime_type,
            "total_runtime_sessions": len(self._runtime_sessions),
            "active_session_ids": list(self._runtime_sessions.keys()),
        }

    def _detect_runtime_type(self) -> RuntimeType:
        """
        Auto-detect the appropriate runtime type based on environment.

        Returns:
            RuntimeType.E2B if E2B_API_KEY is available, otherwise RuntimeType.LOCAL
        """
        e2b_api_key = os.environ.get("E2B_API_KEY")
        if e2b_api_key and e2b_api_key.strip():
            info("Detected E2B runtime (E2B_API_KEY found)")
            return RuntimeType.E2B
        else:
            info("Using LOCAL runtime (no E2B_API_KEY found)")
            return RuntimeType.LOCAL

    @classmethod
    def get_instance(cls) -> Optional["RuntimeSessionManager"]:
        """Get the singleton instance."""
        if cls._instance is None:
            return None
        return cls._instance

    @classmethod
    def reset_instance(cls) -> None:
        """Reset singleton instance (primarily for testing)."""
        cls._instance = None


def get_runtime_session_manager(
    runtime_type: RuntimeType | None = None, envs: dict[str, str] | None = None
) -> RuntimeSessionManager:
    """
    Get or create the runtime session manager singleton.

    Args:
        runtime_type: Optional runtime type override
        envs: Optional environment variables

    Returns:
        RuntimeSessionManager singleton instance
    """
    instance = RuntimeSessionManager.get_instance()
    if instance is None:
        RuntimeSessionManager(runtime_type, envs)
        instance = RuntimeSessionManager.get_instance()
        if instance is None:
            raise RuntimeError(
                "Failed to create RuntimeSessionManager instance"
            )
    return instance
