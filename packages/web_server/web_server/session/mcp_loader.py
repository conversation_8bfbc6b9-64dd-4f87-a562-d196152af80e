"""
MCP server loader for automatically connecting system MCP servers.
"""

from contextlib import AsyncExitStack
import typing

import chainlit as cl
from chainlit.session import WebsocketSession
from mcp import ClientSession
from mcp.client.stdio import (
    StdioServerParameters,
    get_default_environment,
    stdio_client,
)

from common.log import error, info
from web_server.core.mcp_config import MCPServerConfig, get_mcp_system_config
from web_server.session import state_manager


async def connect_mcp_server(server: MCPServerConfig):
    """
    Connect to a single MCP server.
    
    Args:
        server: MCP server configuration
        
    """
    try:
        info(f"Attempting to connect to MCP server: {server.name} ({server.type})")
        session = typing.cast(WebsocketSession, cl.context.session)
        
        if server.name in session.mcp_sessions:
            _, old_exit_stack = session.mcp_sessions[server.name]
            try:
                await old_exit_stack.aclose()
            except Exception:
                pass

        exit_stack = AsyncExitStack()

        if server.type == "sse":
            raise RuntimeError("SSE MCP server is not supported")
        elif not server.command:
            raise RuntimeError(f"MCP server command is missing in config: {server}")
        elif server.type == "stdio":

            env = get_default_environment()
            env.update(server.env)
            args = list(server.args)  # Create a copy to avoid modifying original
            if server.name == "playwright":
                sm = state_manager.get_state_manager()
                runtime_info = sm.get_runtime_info()
                if runtime_info and runtime_info.browser_session:
                  args.append(f"--cdp-endpoint={runtime_info.browser_session.connect_url}")
            # Create the server parameters
            server_params = StdioServerParameters(
                command=server.command,
                args=args,  # Use the modified args
                env=env,
            )

            info(f"setting up mcp server: {server_params}")

            transport = await exit_stack.enter_async_context(
                stdio_client(server_params)
            )

            read, write = transport

            mcp_session: ClientSession = await exit_stack.enter_async_context(
                ClientSession(
                    read_stream=read, write_stream=write, sampling_callback=None
                )
            )

            # Initialize the session
            await mcp_session.initialize()

            # Store the session
            session.mcp_sessions[server.name] = (mcp_session, exit_stack)
        
    except Exception as e:
        error(f"Failed to connect to MCP server {server.name}: {e}")
        return False


async def load_system_mcp_servers():
    """
    Load and connect to all system MCP servers configured for auto-connect.
    
    Returns:
        Number of servers successfully connected
    """
    config = get_mcp_system_config()
    auto_connect_servers = config.get_mcp_servers()
    
    if not auto_connect_servers:
        info("No system MCP servers configured for auto-connect")
        return 0
    
    info(f"Loading {len(auto_connect_servers)} system MCP servers")
    for server in auto_connect_servers:
        await connect_mcp_server(server)
    
