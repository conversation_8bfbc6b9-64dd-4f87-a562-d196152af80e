import asyncio
from datetime import datetime
import json
from typing import Any

import asyncpg
from bson import ObjectId
from chainlit import PersistedUser, User, make_async
from chainlit.context import ChainlitContextException, get_context
from chainlit.data.chainlit_data_layer import ChainlitDataLayer
from chainlit.data.storage_clients.base import BaseStorageClient
from chainlit.types import (
    PageInfo,
    PaginatedResponse,
    Pagination,
    ThreadDict,
    ThreadFilter,
)
from google.auth import default
from google.auth.impersonated_credentials import (
    Credentials as ImpersonatedCredentials,
)
from google.cloud import storage

from common.log import error, warn
from common.models.organization import TenantInfo
from common.services.tenant_service import TenantService
from common.services.user_service import UserService
from common.utils.auth import _ORG_ID_HEADER_KEY
from web_server.core.config import server_config
from web_server.middleware.context_middleware import get_orby_org_header_id


class TenantInfoMixin:
    """Shared tenant information retrieval logic"""

    def __init__(self):
        self.tenant_service = TenantService()

    async def _get_tenant_info(self) -> TenantInfo:
        """Get tenant info from header or chainlit context"""
        # If the HTTP API is used, the org_id is in the headers
        selected_org_id = get_orby_org_header_id()
        # If the websocket API is used, the org_id is in the user_env
        if not selected_org_id:
            selected_org_id = self._get_org_id_from_chainlit_context()

        if not selected_org_id:
            error("No org_id found in headers or user_env")
            raise RuntimeError("No org_id found in headers or user_env")

        return await self.tenant_service.get_tenant_info(
            ObjectId(selected_org_id)
        )

    def _get_org_id_from_chainlit_context(self) -> str | None:
        """Extract org_id from chainlit context"""
        try:
            resolved_context = get_context()
            if not resolved_context or not resolved_context.session:
                return None
            session = resolved_context.session
            if session.user_env:
                # Handle case where user_env might be a string or dictionary
                if isinstance(session.user_env, dict):
                    return session.user_env.get(_ORG_ID_HEADER_KEY)
                elif isinstance(session.user_env, str):
                    # For some reason, the user_env is a string in the websocket API, but based on the code, it should be a dictionary. so we can handle both cases here.
                    # Try to parse the string as JSON
                    user_env_dict = json.loads(session.user_env)
                    return user_env_dict.get(_ORG_ID_HEADER_KEY)
                else:
                    error(
                        f"Unexpected session.user_env type: {type(session.user_env)}"
                    )
                    raise
            else:
                error(
                    "User environment missing in session; unable to extract org_id from chainlit context."
                )
                raise

        except ChainlitContextException as e:
            warn(f"Chainlit context error: {e}")
            return None
        except Exception as e:
            error(f"Error reading context: {e}")
            return None


class OrbyChainlitDataLayer(ChainlitDataLayer, TenantInfoMixin):
    """Multi-tenant Chainlit data layer with org-based isolation"""

    def __init__(self, postgres_url: str, default_bucket_name: str):
        self.user_service = UserService()
        self.postgres_url = postgres_url

        # Manages different tenant database connections with metadata
        # Key: pool_key, Value: (pool, loop_id, created_at)
        self.tenant_pools: dict[str, tuple[asyncpg.Pool, int, datetime]] = {}

        # Lock to protect tenant_pools from race conditions - make it lazy to avoid event loop issues
        self._tenant_pools_lock: asyncio.Lock | None = None

        # Background cleanup task
        self._cleanup_task: asyncio.Task | None = None

        # Initialize mixins
        TenantInfoMixin.__init__(self)

        # Initialize parent with GCS storage client
        gcs_storage_client = OrbyGCSStorageClient(
            default_bucket_name=default_bucket_name
        )
        super().__init__(
            database_url=postgres_url.rstrip("/")
            + "/"
            + server_config.default_db_name,
            storage_client=gcs_storage_client,
            show_logger=True,
        )

        # Start background cleanup task
        self._start_cleanup_task()

    def _get_tenant_pools_lock(self) -> asyncio.Lock:
        """Get or create the tenant pools lock in the current event loop"""
        if self._tenant_pools_lock is None:
            self._tenant_pools_lock = asyncio.Lock()
        return self._tenant_pools_lock

    async def get_user(self, identifier: str) -> PersistedUser | None:
        """Get user combining Orby user service with Chainlit user data"""
        try:
            user = await self.user_service.get_user_by_email(identifier)
            if not user:
                error(f"User not found: {identifier}")
                return None

            return PersistedUser(
                id=str(user.id),
                identifier=user.email,
                display_name=user.full_name,
                createdAt=datetime.now().isoformat(),
            )
        except Exception as e:
            error(f"Error getting user {identifier}: {e}")
            return None

    async def create_user(self, user: User):
        """Create user combining Orby user service with Chainlit user data"""
        try:
            mongo_user = await self.user_service.get_user_by_email(
                user.identifier
            )
            if not mongo_user:
                error(f"User not found in MongoDB: {user.identifier}")
                raise RuntimeError(
                    f"User not found in MongoDB: {user.identifier}"
                )

            now = datetime.now()
            params = {
                "id": str(mongo_user.id),
                "identifier": user.identifier,
                "metadata": json.dumps(user.metadata),
                "created_at": now,
                "updated_at": now,
            }
            query = """
            INSERT INTO "User" (id, identifier, metadata, "createdAt", "updatedAt")
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (id) DO UPDATE
            SET metadata = $3
            RETURNING *
            """
            result = await self.execute_query(query, params)
            row = result[0]

            return PersistedUser(
                id=str(row.get("id")),
                identifier=str(row.get("identifier")),
                createdAt=row.get("createdAt").isoformat(),  # type: ignore
                metadata=json.loads(row.get("metadata", "{}")),
            )
        except Exception as e:
            error(f"Error creating user {user.identifier}: {e}")
            return None

    async def _get_tenant_pool(self, tenant_info: TenantInfo) -> asyncpg.Pool:
        """
        Get or create a tenant-specific asyncpg connection pool for the current event loop.

        Why this is needed:
        -------------------
        asyncpg pools (and connections) are bound to the event loop they were created in.
        If you try to use them in a different loop, you get:
            RuntimeError: Task got Future attached to a different loop

        This is common in Chainlit's background tasks, websocket contexts, or when running
        across multiple threads or uvicorn workers.

        So we cache pools using a composite key: (loop_id + tenant_db_url)
        """

        tenant_db_url = self._build_tenant_database_url(tenant_info)
        loop_id = id(asyncio.get_running_loop())
        pool_key = f"{loop_id}:{tenant_db_url}"

        async with self._get_tenant_pools_lock():
            if pool_key not in self.tenant_pools:
                # Create pool with 1-2 connections
                pool = await asyncpg.create_pool(
                    tenant_db_url,
                    min_size=1,
                    max_size=2,
                )

                self.tenant_pools[pool_key] = (
                    pool,
                    loop_id,
                    datetime.now(),
                )

                warn(
                    f"Created pool for tenant {tenant_info.org.id} in loop {loop_id} (Total: {len(self.tenant_pools)})"
                )

        return self.tenant_pools[pool_key][0]

    async def _cleanup_expired_pools(self):
        """Clean up pools older than 1 minute"""
        try:
            async with self._get_tenant_pools_lock():
                current_time = datetime.now()
                expired_keys = []

                for pool_key, (
                    _pool,
                    _,
                    created_at,
                ) in self.tenant_pools.items():
                    # Check if pool is older than 1 minute
                    if (current_time - created_at).total_seconds() > 60:
                        expired_keys.append(pool_key)

                # Clean up expired pools
                for key in expired_keys:
                    try:
                        _pool, _, created_at = self.tenant_pools.pop(key)
                        await _pool.close()
                        warn(
                            f"Cleaned up expired pool: {key} (age: {(current_time - created_at).total_seconds():.1f}s)"
                        )
                    except Exception as e:
                        error(f"Error cleaning up expired pool {key}: {e}")
        except Exception as e:
            error(f"Error in background cleanup: {e}")

    def _build_tenant_database_url(self, tenant_info: TenantInfo) -> str:
        """Build tenant-specific database URL by appending the tenant database name to the base URL"""
        return (
            self.postgres_url.rstrip("/")
            + "/"
            + tenant_info.get_database_name()
        )

    async def execute_query(
        self, query: str, params: dict | None = None
    ) -> list[dict[str, Any]]:
        """Execute query using tenant-specific database connection"""
        try:
            tenant_info = await self._get_tenant_info()
            pool = await self._get_tenant_pool(tenant_info)

            async with pool.acquire() as connection:
                if params:
                    records = await connection.fetch(query, *params.values())
                else:
                    records = await connection.fetch(query)
                return [dict(record) for record in records]

        except Exception as e:
            error(f"Query execution failed: {e}")
            raise

    async def update_thread(
        self,
        thread_id: str,
        name: str | None = None,
        user_id: str | None = None,
        metadata: dict | None = None,
        tags: list[str] | None = None,
    ):
        """Update thread with automatic org_id injection"""
        try:
            tenant_info = await self._get_tenant_info()
            current_org_id = str(tenant_info.org.id)

            # Inject org_id into metadata
            if metadata is None:
                metadata = {}
            metadata[_ORG_ID_HEADER_KEY] = current_org_id
            # Call the parent class to update the thread
            await super().update_thread(
                thread_id, name, user_id, metadata, tags
            )

        except Exception as e:
            error(f"Failed to update thread {thread_id}: {e}")
            raise

    async def list_threads(
        self, pagination: Pagination, filters: ThreadFilter
    ) -> PaginatedResponse[ThreadDict]:
        """List threads filtered by current organization"""
        try:
            # Get all threads from parent
            result = await super().list_threads(pagination, filters)

            tenant_info = await self._get_tenant_info()
            current_org_id = str(tenant_info.org.id)

            # Filter threads by org_id
            # TODO: This is a hack to filter threads by org_id. We should rather query the database for threads that belong to the current org only
            filtered_threads = [
                thread
                for thread in result.data
                if (metadata := thread.get("metadata"))
                and metadata.get(_ORG_ID_HEADER_KEY) == current_org_id
            ]

            return PaginatedResponse(
                pageInfo=result.pageInfo,
                data=filtered_threads,
            )

        except Exception as e:
            error(f"Failed to list threads: {e}")
            return PaginatedResponse(
                pageInfo=PageInfo(
                    hasNextPage=False, startCursor=None, endCursor=None
                ),
                data=[],
            )

    async def cleanup(self):
        """Close all tenant connection pools and cleanup parent"""
        # Stop background cleanup task
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None

        for pool, _, _ in self.tenant_pools.values():
            if pool:
                await pool.close()
        self.tenant_pools.clear()

        await super().cleanup()

    def _start_cleanup_task(self):
        """Start a background cleanup task to clean up expired pools"""
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._background_cleanup())

    async def _background_cleanup(self):
        """Background cleanup task to clean up expired pools"""
        while True:
            await self._cleanup_expired_pools()
            await asyncio.sleep(60)


# TODO: Create a common storage client that can be used by all the services
class OrbyGCSStorageClient(BaseStorageClient, TenantInfoMixin):
    """Multi-tenant GCS storage client using different buckets per tenant"""

    def __init__(self, default_bucket_name: str, project_id: str | None = None):
        # Python SDK doesn't handle impersonation out of the box, so we need to do it manually
        self.client = storage.Client(
            project=project_id if project_id else None,
            credentials=self._impersonate_service_account(
                server_config.default_service_account
            ),
        )

        self.default_bucket_name = default_bucket_name

        # Initialize mixin
        TenantInfoMixin.__init__(self)

    def _impersonate_service_account(self, service_account_email: str):
        """Impersonate a service account"""
        # Please note in order to impersonate a service account, you need to have the "Service Account Token Creator" role on the service account you are impersonating.
        default_credentials, _ = default()
        impersonated_credentials = ImpersonatedCredentials(
            source_credentials=default_credentials,
            target_principal=service_account_email,
            target_scopes=[
                "https://www.googleapis.com/auth/devstorage.full_control"
            ],
        )
        return impersonated_credentials

    def _get_bucket_name(self, tenant_info: TenantInfo | None) -> str:
        """Get tenant-specific bucket name"""
        if not tenant_info:
            return self.default_bucket_name
        return tenant_info.get_bucket_name(self.default_bucket_name)

    async def upload_file(
        self,
        object_key: str,
        data: bytes | str,
        mime: str = "application/octet-stream",
        overwrite: bool = True,
    ) -> dict[str, Any]:
        """Upload file to tenant-specific bucket"""
        try:
            tenant_info = await self._get_tenant_info()
            bucket_name = self._get_bucket_name(tenant_info)
            bucket = self.client.bucket(bucket_name)
            blob = bucket.blob(object_key)

            if not overwrite and await make_async(blob.exists)():
                raise Exception(f"File {object_key} already exists")

            if isinstance(data, str):
                data = data.encode("utf-8")

            await make_async(blob.upload_from_string)(data, content_type=mime)

            return {
                "object_key": object_key,
                "url": f"https://storage.googleapis.com/{bucket.name}/{object_key}",
                "tenant_id": str(tenant_info.org.id) if tenant_info else None,
                "bucket_name": bucket.name,
            }

        except Exception as e:
            error(f"Failed to upload file {object_key}: {e}")
            raise

    async def get_read_url(self, object_key: str) -> str:
        """Get signed read URL from tenant-specific bucket"""
        try:
            tenant_info = await self._get_tenant_info()
            bucket_name = self._get_bucket_name(tenant_info)
            bucket = self.client.bucket(bucket_name)

            # Build signed URL options
            signed_url_kwargs = {
                "version": "v4",
                "expiration": 3600,
                "method": "GET",
            }
            return await make_async(
                bucket.blob(object_key).generate_signed_url
            )(**signed_url_kwargs)

        except Exception as e:
            error(f"Failed to get read URL for {object_key}: {e}")
            raise

    async def delete_file(self, object_key: str) -> bool:
        """Delete file from tenant-specific bucket"""
        try:
            tenant_info = await self._get_tenant_info()
            bucket_name = self._get_bucket_name(tenant_info)
            bucket = self.client.bucket(bucket_name)

            await make_async(bucket.blob(object_key).delete)()
            return True

        except Exception as e:
            warn(f"Failed to delete file {object_key}: {e}")
            return False
