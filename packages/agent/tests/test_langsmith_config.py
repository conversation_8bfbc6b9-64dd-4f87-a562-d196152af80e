"""
Unit tests for <PERSON><PERSON><PERSON> configuration and integration.
Tests focus on our integration code, not LangSmith's internal functionality.
"""

import os
from unittest.mock import MagicMock, patch

import pytest


class TestEnvironmentSetup:
    """Test LangSmith environment setup functionality."""

    def test_setup_without_api_key(self):
        """Test setup when API key is not provided."""
        from agent.langsmith_config import setup_langsmith_environment

        with patch.dict(os.environ, {}, clear=True):
            result = setup_langsmith_environment()
            assert result is False
            assert "LANGSMITH_TRACING" not in os.environ

    def test_setup_with_api_key_sets_defaults(self):
        """Test setup with API key sets default values."""
        from agent.langsmith_config import setup_langsmith_environment

        with patch.dict(
            os.environ, {"LANGSMITH_API_KEY": "test-key"}, clear=True
        ):
            result = setup_langsmith_environment()
            assert result is True
            assert os.environ["LANGSMITH_TRACING"] == "true"
            assert os.environ["LANGSMITH_PROJECT"] == "vibe-automation-server"
            assert (
                os.environ["LANGSMITH_ENDPOINT"]
                == "https://api.smith.langchain.com"
            )

    def test_setup_preserves_existing_values(self):
        """Test setup preserves user-provided environment values."""
        from agent.langsmith_config import setup_langsmith_environment

        existing_env = {
            "LANGSMITH_API_KEY": "custom-key",
            "LANGSMITH_TRACING": "false",
            "LANGSMITH_PROJECT": "my-project",
            "LANGSMITH_ENDPOINT": "https://custom.endpoint.com",
        }
        with patch.dict(os.environ, existing_env, clear=True):
            result = setup_langsmith_environment()
            assert result is True
            # Should NOT override existing values
            assert os.environ["LANGSMITH_TRACING"] == "false"
            assert os.environ["LANGSMITH_PROJECT"] == "my-project"
            assert (
                os.environ["LANGSMITH_ENDPOINT"]
                == "https://custom.endpoint.com"
            )

    def test_setup_with_server_config(self):
        """Test setup with ServerConfig object."""
        from agent.langsmith_config import setup_langsmith_environment

        # Mock ServerConfig
        class MockServerConfig:
            langsmith_api_key = "config-api-key"
            langsmith_project = "config-project"
            langsmith_endpoint = "https://config.endpoint.com"
            langsmith_tracing = "true"

        config = MockServerConfig()

        with patch.dict(os.environ, {}, clear=True):
            result = setup_langsmith_environment(config)
            assert result is True
            assert os.environ["LANGSMITH_API_KEY"] == "config-api-key"
            assert os.environ["LANGSMITH_PROJECT"] == "config-project"
            assert (
                os.environ["LANGSMITH_ENDPOINT"]
                == "https://config.endpoint.com"
            )
            assert os.environ["LANGSMITH_TRACING"] == "true"

    def test_setup_with_server_config_no_api_key(self):
        """Test setup with ServerConfig object but no API key."""
        from agent.langsmith_config import setup_langsmith_environment

        # Mock ServerConfig without API key
        class MockServerConfig:
            langsmith_api_key = ""
            langsmith_project = "config-project"
            langsmith_endpoint = "https://config.endpoint.com"
            langsmith_tracing = "true"

        config = MockServerConfig()

        with patch.dict(os.environ, {}, clear=True):
            result = setup_langsmith_environment(config)
            assert result is False
            assert "LANGSMITH_API_KEY" not in os.environ


class TestTracingUtilities:
    """Test utility functions for tracing state."""

    def test_is_tracing_enabled_without_api_key(self):
        """Test tracing check without API key."""
        from agent.langsmith_config import is_tracing_enabled

        with patch.dict(os.environ, {"LANGSMITH_TRACING": "true"}, clear=True):
            assert is_tracing_enabled() is False

    def test_is_tracing_enabled_with_api_key_but_disabled(self):
        """Test tracing check with API key but tracing disabled."""
        from agent.langsmith_config import is_tracing_enabled

        with patch.dict(
            os.environ,
            {"LANGSMITH_API_KEY": "test-key", "LANGSMITH_TRACING": "false"},
            clear=True,
        ):
            assert is_tracing_enabled() is False

    def test_is_tracing_enabled_fully_configured(self):
        """Test tracing check when fully configured."""
        from agent.langsmith_config import is_tracing_enabled

        with patch.dict(
            os.environ,
            {"LANGSMITH_API_KEY": "test-key", "LANGSMITH_TRACING": "true"},
            clear=True,
        ):
            assert is_tracing_enabled() is True

    def test_get_tracing_metadata_default_environment(self):
        """Test metadata generation with default environment."""
        from agent.langsmith_config import get_tracing_metadata

        with patch.dict(os.environ, {}, clear=True):
            metadata = get_tracing_metadata("test-agent")
            assert metadata["agent_name"] == "test-agent"
            assert metadata["service"] == "vibe-automation-server"
            assert metadata["version"] == "0.1.0"
            assert metadata["environment"] == "development"

    def test_get_tracing_metadata_custom_environment(self):
        """Test metadata generation with custom environment."""
        from agent.langsmith_config import get_tracing_metadata

        with patch.dict(os.environ, {"ENVIRONMENT": "production"}, clear=True):
            metadata = get_tracing_metadata("test-agent")
            assert metadata["environment"] == "production"


class TestLangSmithUtilityFunctions:
    """Test LangSmith utility functions with proper mocking."""

    @patch("langsmith.get_current_run_tree")
    def test_get_current_run_id_with_active_run(self, mock_get_run_tree):
        """Test getting run ID when a run is active."""
        from agent.langsmith_config import get_current_run_id

        # Mock the run object
        mock_run = MagicMock()
        mock_run.id = "test-run-123"
        mock_get_run_tree.return_value = mock_run

        run_id = get_current_run_id()
        assert run_id == "test-run-123"
        mock_get_run_tree.assert_called_once()

    @patch("langsmith.get_current_run_tree")
    def test_get_current_run_id_no_active_run(self, mock_get_run_tree):
        """Test getting run ID when no run is active."""
        from agent.langsmith_config import get_current_run_id

        mock_get_run_tree.return_value = None

        run_id = get_current_run_id()
        assert run_id is None

    @patch("langsmith.get_current_run_tree")
    def test_get_current_run_id_handles_import_error(self, mock_get_run_tree):
        """Test getting run ID when langsmith import fails."""
        from agent.langsmith_config import get_current_run_id

        mock_get_run_tree.side_effect = ImportError("No module")

        run_id = get_current_run_id()
        assert run_id is None

    def test_add_run_metadata_handles_errors_gracefully(self):
        """Test add_run_metadata handles errors gracefully."""
        from agent.langsmith_config import add_run_metadata

        # Should not raise exception even with invalid langsmith module
        add_run_metadata({"test": "data"})

    def test_add_run_tags_handles_errors_gracefully(self):
        """Test add_run_tags handles errors gracefully."""
        from agent.langsmith_config import add_run_tags

        # Should not raise exception even with invalid langsmith module
        add_run_tags(["test"])


class TestAgentDecorators:
    """Test agent-specific decorators using direct function inspection."""

    def test_trace_agent_function_parameters(self):
        """Test that trace_agent_function creates correct decorator parameters."""
        from agent.langsmith_config import get_tracing_metadata

        # Test metadata generation (which is used by the decorator)
        metadata = get_tracing_metadata("test-agent")
        assert metadata["agent_name"] == "test-agent"
        assert metadata["service"] == "vibe-automation-server"

        # Test expected tags format
        expected_tags = ["test-agent", "vibe-automation", "langgraph"]
        assert all(isinstance(tag, str) for tag in expected_tags)

    def test_decorator_function_naming(self):
        """Test that decorator creates correct function names."""
        # When trace_agent_function is applied to a function named 'sample_function'
        # with agent_name 'test-agent', it should create name 'test-agent_sample_function'
        agent_name = "test-agent"
        function_name = "sample_function"
        expected_name = f"{agent_name}_{function_name}"
        assert expected_name == "test-agent_sample_function"

    @patch("langsmith.traceable")
    def test_all_decorators_use_traceable(self, mock_traceable):
        """Test that all agent decorators properly use the traceable decorator."""
        # Mock traceable to track calls
        mock_traceable.return_value = lambda f: f

        # Force reload of the module to apply our mock
        import importlib

        import agent.langsmith_config

        importlib.reload(agent.langsmith_config)

        # Import decorators after reload
        from agent.langsmith_config import (
            trace_coding_agent,
            trace_knowledge_agent,
            trace_supervisor_agent,
        )

        # Apply decorators
        @trace_supervisor_agent
        def supervisor_func():
            pass

        @trace_knowledge_agent
        def knowledge_func():
            pass

        @trace_coding_agent
        def coding_func():
            pass

        # Verify traceable was called for each decorator
        assert mock_traceable.call_count >= 3


class TestIntegrationScenarios:
    """Test complete integration scenarios."""

    def test_environment_variables_interaction(self):
        """Test how environment variables affect tracing behavior."""
        from agent.langsmith_config import (
            is_tracing_enabled,
            setup_langsmith_environment,
        )

        # Scenario 1: No API key
        with patch.dict(os.environ, {}, clear=True):
            assert not is_tracing_enabled()
            assert not setup_langsmith_environment()

        # Scenario 2: API key present but tracing disabled
        with patch.dict(
            os.environ,
            {"LANGSMITH_API_KEY": "test-key", "LANGSMITH_TRACING": "false"},
            clear=True,
        ):
            assert not is_tracing_enabled()
            # Setup should succeed but not override the false value
            assert setup_langsmith_environment()
            assert os.environ["LANGSMITH_TRACING"] == "false"

        # Scenario 3: Full configuration
        with patch.dict(
            os.environ, {"LANGSMITH_API_KEY": "test-key"}, clear=True
        ):
            assert setup_langsmith_environment()
            assert is_tracing_enabled()

    def test_metadata_consistency(self):
        """Test that metadata is consistent across different agent types."""
        from agent.langsmith_config import get_tracing_metadata

        agents = ["supervisor", "knowledge", "coding"]

        for agent in agents:
            metadata = get_tracing_metadata(agent)
            # All agents should have the same service and version
            assert metadata["service"] == "vibe-automation-server"
            assert metadata["version"] == "0.1.0"
            # But different agent names
            assert metadata["agent_name"] == agent

    def test_no_side_effects_when_disabled(self):
        """Test that tracing functions don't cause issues when disabled."""
        from agent.langsmith_config import (
            add_run_metadata,
            add_run_tags,
            get_current_run_id,
            is_tracing_enabled,
        )

        with patch.dict(os.environ, {}, clear=True):
            # These should all work without errors
            assert not is_tracing_enabled()
            assert get_current_run_id() is None
            add_run_metadata({"test": "data"})
            add_run_tags(["test"])


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
