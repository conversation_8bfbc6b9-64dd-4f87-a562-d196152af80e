"""
Integration tests for <PERSON><PERSON><PERSON> with actual agent implementations.
These tests verify that our agents work correctly with <PERSON><PERSON><PERSON> tracing.
"""

import os
from unittest.mock import patch

from langchain_core.messages import AIMessage
import pytest

from agent.configuration import Configuration


class TestAgentLangSmithIntegration:
    """Test LangSmith integration with actual agent creation."""

    @pytest.fixture
    def langsmith_enabled_env(self):
        """Enable LangSmith in environment."""
        env_vars = {
            "LANGSMITH_API_KEY": "test-api-key",
            "LANGSMITH_TRACING": "true",
            "LANGSMITH_PROJECT": "test-project",
        }
        with patch.dict(os.environ, env_vars):
            yield

    @pytest.fixture
    def mock_anthropic(self):
        """Mock Anthropic model for testing."""
        with patch("agent.supervisor_agent.ChatGoogleGenerativeAI") as mock:
            mock_instance = mock.return_value
            mock_instance.invoke.return_value = AIMessage(
                content="Test response"
            )
            yield mock

    def test_supervisor_agent_creation_succeeds(
        self, mock_anthropic, langsmith_enabled_env
    ):
        """Test supervisor agent creation succeeds with <PERSON><PERSON><PERSON> enabled."""
        from agent.supervisor_agent import create_supervisor_agent

        config = Configuration()
        agent = create_supervisor_agent(config)

        # Verify agent was created successfully
        assert agent is not None

    def test_knowledge_agent_creation_succeeds(self, langsmith_enabled_env):
        """Test knowledge agent creation succeeds with LangSmith enabled."""
        from agent.knowledge_agent_light import create_knowledge_agent

        agent = create_knowledge_agent()

        # Verify agent was created successfully
        assert agent is not None

    def test_coding_agent_creation_succeeds(self, langsmith_enabled_env):
        """Test coding agent creation succeeds with LangSmith enabled."""
        from agent.runtime_coding_agent import create_runtime_coding_agent

        agent = create_runtime_coding_agent()

        # Verify agent was created successfully
        assert agent is not None

    def test_graph_creation_with_tracing(
        self, mock_anthropic, langsmith_enabled_env
    ):
        """Test complete graph creation with LangSmith enabled."""
        from agent.graph import create_graph

        config = Configuration()
        graph = create_graph(config)

        # Verify graph was created
        assert graph is not None

    def test_agents_work_without_langsmith(self, mock_anthropic):
        """Test that agents work correctly when LangSmith is not available."""
        with patch.dict(os.environ, {}, clear=True):
            # Should not raise any import errors
            from agent.knowledge_agent_light import create_knowledge_agent
            from agent.runtime_coding_agent import create_runtime_coding_agent
            from agent.supervisor_agent import create_supervisor_agent

            # Create agents without LangSmith
            config = Configuration()
            supervisor = create_supervisor_agent(config)
            knowledge = create_knowledge_agent()
            coding = create_runtime_coding_agent()

            # All agents should be created successfully
            assert supervisor is not None
            assert knowledge is not None
            assert coding is not None

    def test_langsmith_initialization_in_app(self, langsmith_enabled_env):
        """Test LangSmith initialization in application context."""
        from agent.langsmith_config import setup_langsmith_environment

        result = setup_langsmith_environment()

        # Should return True when API key is present
        assert result is True

        # Should set up environment correctly
        assert os.environ["LANGSMITH_TRACING"] == "true"
        assert os.environ["LANGSMITH_PROJECT"] == "test-project"


class TestErrorHandling:
    """Test error handling in LangSmith integration."""

    @patch("langsmith.traceable")
    def test_graceful_handling_of_langsmith_errors(self, mock_traceable):
        """Test that LangSmith errors don't break agent functionality."""
        # Make traceable raise an error
        mock_traceable.side_effect = Exception("LangSmith error")

        with patch.dict(os.environ, {"LANGSMITH_API_KEY": "test-key"}):
            # Should still be able to import
            from agent.langsmith_config import trace_supervisor_agent

            # Decorator should handle the error gracefully
            @trace_supervisor_agent
            def test_function():
                return "success"

            # Function should still work
            assert test_function() == "success"

    def test_import_error_handling(self):
        """Test handling when langsmith package is not installed."""
        with patch.dict("sys.modules", {"langsmith": None}):
            # Should handle import error gracefully
            from agent.langsmith_config import get_current_run_id

            # Should return None instead of raising
            assert get_current_run_id() is None


class TestLangSmithConfiguration:
    """Test LangSmith configuration behavior."""

    def test_environment_setup_behavior(self):
        """Test that environment setup works correctly in different scenarios."""
        from agent.langsmith_config import (
            is_tracing_enabled,
            setup_langsmith_environment,
        )

        # Test without any configuration
        with patch.dict(os.environ, {}, clear=True):
            assert not setup_langsmith_environment()
            assert not is_tracing_enabled()

        # Test with minimal configuration
        with patch.dict(
            os.environ, {"LANGSMITH_API_KEY": "test-key"}, clear=True
        ):
            assert setup_langsmith_environment()
            assert is_tracing_enabled()
            assert os.environ["LANGSMITH_TRACING"] == "true"
            assert os.environ["LANGSMITH_PROJECT"] == "vibe-automation-server"

        # Test that existing values are preserved
        with patch.dict(
            os.environ,
            {
                "LANGSMITH_API_KEY": "test-key",
                "LANGSMITH_TRACING": "false",
                "LANGSMITH_PROJECT": "custom-project",
            },
            clear=True,
        ):
            assert setup_langsmith_environment()
            assert not is_tracing_enabled()  # Should remain false
            assert (
                os.environ["LANGSMITH_PROJECT"] == "custom-project"
            )  # Should be preserved

    def test_metadata_generation(self):
        """Test that metadata is generated correctly for different agents."""
        from agent.langsmith_config import get_tracing_metadata

        # Test different agent types
        for agent_name in ["supervisor", "knowledge", "coding"]:
            metadata = get_tracing_metadata(agent_name)

            # Verify required fields
            assert metadata["agent_name"] == agent_name
            assert metadata["service"] == "vibe-automation-server"
            assert metadata["version"] == "0.1.0"
            assert "environment" in metadata


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
