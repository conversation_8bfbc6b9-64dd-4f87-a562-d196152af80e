"""
LangGraph agent that wraps Claude CLI for direct execution with streaming support.
This provides a simple pass-through interface while maintaining LangGraph structure.
"""

import logging
from typing import Annotated, Optional

from langchain_core.messages import AIMessage, HumanMessage
from langgraph.graph import END, START, StateGraph
from langgraph.graph.message import add_messages
from langgraph.graph.state import CompiledStateGraph
from typing_extensions import TypedDict

from common.sandbox.base import Runtime

from .utils.claude_util import run_claude_command_with_runtime

logger = logging.getLogger(__name__)


class ClaudeCliState(TypedDict):
    """State for the Claude CLI agent."""

    messages: Annotated[list, add_messages]
    attachments: list[str]
    runtime: Runtime
    is_first_message: bool
    session_id: str
    execution_service_url: str
    execution_id: str


async def claude_cli_tool(
    instruction: str,
    attachments: list[str],
    runtime: Runtime,
    is_first_message: bool = False,
    execution_id: str = "",
) -> str:
    """
    Execute Claude CLI with the given instruction and attachments.

    Args:
        instruction: The instruction to send to <PERSON>
        attachments: List of file paths that have been uploaded
        runtime: The runtime environment to execute in
        is_first_message: Whether this is the first message in conversation

    Returns:
        The result from Claude CLI execution
    """
    try:
        # Execute Claude CLI command
        success, output = await run_claude_command_with_runtime(
            runtime=runtime,
            instruction=instruction,
            folder_path=".",
            additional_files=attachments,
            is_first_message=is_first_message,
            envs={"VA_EXECUTION_ID": execution_id},
        )

        if success:
            return ""  # Content already streamed via runtime
        else:
            return f"❌ Claude CLI execution failed: {output}"

    except Exception as e:
        logger.error(f"Error in claude_cli_tool: {e}")
        return f"❌ Error executing Claude CLI: {str(e)}"


async def claude_cli_node(state: ClaudeCliState) -> dict:
    """
    Main node that executes Claude CLI with the user's message.
    """
    try:
        # Get the latest user message
        messages = state["messages"]
        if not messages:
            return {"messages": [AIMessage(content="No message to process.")]}

        last_message = messages[-1]
        if not isinstance(last_message, HumanMessage):
            return {
                "messages": [AIMessage(content="Expected a human message.")]
            }

        # Execute Claude CLI
        result = await claude_cli_tool(
            instruction=str(last_message.content),
            attachments=state.get("attachments", []),
            runtime=state["runtime"],
            is_first_message=state.get("is_first_message", False),
            execution_id=state.get("execution_id", ""),
        )

        # Return result only if there's an error (content is streamed via runtime)
        if result:  # Only return non-empty results (errors)
            return {
                "messages": [AIMessage(content=result)],
                "is_first_message": False,
            }
        else:  # Success case - content already streamed
            return {
                "messages": [],  # No additional message needed
                "is_first_message": False,
            }

    except Exception as e:
        logger.error(f"Error in claude_cli_node: {e}")
        return {
            "messages": [
                AIMessage(content=f"❌ Error processing message: {str(e)}")
            ]
        }


def create_claude_cli_graph() -> CompiledStateGraph:
    """
    Create a simple LangGraph that wraps Claude CLI execution.
    """
    # Create the graph
    graph = StateGraph(ClaudeCliState)

    # Add the single node
    graph.add_node("claude_cli", claude_cli_node)

    # Set up the flow: START -> claude_cli -> END
    graph.add_edge(START, "claude_cli")
    graph.add_edge("claude_cli", END)

    return graph.compile()


class ClaudeCliAgent:
    """
    Simple agent wrapper around Claude CLI using LangGraph.
    Implemented as a singleton.
    """

    _instance: Optional["ClaudeCliAgent"] = None

    def __new__(cls) -> "ClaudeCliAgent":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        # Only initialize once
        if not hasattr(self, "graph"):
            self.graph = create_claude_cli_graph()
            logger.info("Claude CLI Agent initialized")

    @classmethod
    def get_instance(cls) -> "ClaudeCliAgent":
        """
        Helper method to get the singleton instance.
        Returns:
            ClaudeCliAgent: The singleton instance
        """
        if cls._instance is None:
            return cls()
        return cls._instance


def get_claude_cli_agent() -> ClaudeCliAgent:
    """
    Get the singleton instance of the Claude CLI graph.
    Returns:
        CompiledStateGraph: The singleton instance of the Claude CLI graph
    """
    return ClaudeCliAgent()
