from common.sandbox.base import Runtime, RuntimeType


def load_documentation(runtime: Runtime) -> str:
    """Load VA SDK documentation based on runtime type."""
    is_local = runtime.runtime_type == RuntimeType.LOCAL
    if is_local:
        get_page_code = "page = await browser_context.new_page()"
        requirements_txt = """
vibe-automation
pydantic
playwright
# Add other dependencies as needed
"""
    else:
        get_page_code = "page = browser_context.pages[0]"
        requirements_txt = """
The runtime environment comes with vibe-automation, playwright, and pydantic pre-installed, along with their associated browsers. Therefore, we can skip adding these packages to the requirements.txt file.
# Add other dependencies as needed
"""

    return va_sdk_doc_template.format(
        get_page_code=get_page_code, requirements_txt=requirements_txt
    )


# TODO: remove hardcoded WorkflowInput and csv_file_path
# NOTE: revise page.step part
va_sdk_doc_template = '''\
# VA SDK (Vibe Automation SDK) Documentation

## Overview
The VA SDK is designed to create structured automation workflows with built-in logging, type validation, and step tracking.

## Core Concepts

### 1. Workflow Decorator
Use the `@workflow` decorator to define your main automation function:
NOTE: there should only be one @workflow decorator in the main.py file. All others should be steps.

```python
from va import workflow, step, review, ReviewStatus

@workflow("Workflow description")
async def main(input: WorkflowInput, logger: logging.Logger):
    # Your workflow logic here
    pass
```

### 2. Input Handling with Pydantic
Define input models using Pydantic with class name "WorkflowInput" for automatic type validation:

```python
import logging
import asyncio
from pydantic import BaseModel
from va import workflow, step, review, ReviewStatus

class WorkflowInput(BaseModel):
    name: str
    phone: str

@workflow("Contact processing workflow")
async def main(input: WorkflowInput, logger: logging.Logger):
    logger.info("Processing contact: %s", input.name)
    logger.info("Phone number: %s", input.phone)

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    asyncio.run(main(WorkflowInput(name="John", phone="************"), logger=logging.getLogger(__name__)))
```

**Important Notes:**
- The first parameter MUST be named "input" to activate automatic input replacement in managed environments
- During local development, use the input provided in the main guard
- In managed environments, the `workflow` wrapper replaces the first argument with execution input after type validation
- If a file attachment is uploaded by the user, include file path in the input BaseModel and read it inside main(). If it is a csv file, the input name should be csv_file_path

### 3. Managed Logging
The SDK provides a managed logger instance that automatically collects logs for the execution service:

```python
async def main(input: WorkflowInput, logger: logging.Logger):
    logger.info("This log will be collected automatically")
    logger.error("Error logs are also tracked")
```

### 4. Step Tracking
Use the `step` context manager to organize your workflow into logical steps:

```python
from va import workflow, step, review, ReviewStatus

@workflow("Multi-step workflow")
async def main():
    async with step("Step 1: Initialize"):
        # Initialization logic
        pass
    
    async with step("Step 2: Process data"):
        # Processing logic
        pass
    
    async with step("Step 3: Finalize"):
        # Finalization logic
        pass
```

### 5. Using page.step to wrap atomic actions
In addition to using the step context manager for general workflow steps, please also use page.step to wrap atomic actions,
such as individual clicks, or interactions with dropdowns and other elements.
The code underneath this `with page.step()` statement is the code that should run.
However, the description in the argument of page.step() is a fallback when the code fails. This way, our code is self-documenting

Here are some examples:
    async with page.step("Fill the name field", context={{"name": "John Smith"}}) as context:
        # LLM would be triggered since no Page action is defined
        pass

    async with page.step(
        "Fill in telephone field", context={{"telephone": "************"}}
    ) as context:
        # the following statement would be triggered first, and since it is successful
        # LLM won't be triggered
        await page.get_by_label("Telephone: ").fill(context["telephone"])

### 6. Human Review
The provided SOP will mention which steps or parts of the workflow need human review.
In this case, you need to use the `review` function to pause the workflow and wait for human review.
There are two types of human review:
1) After human review, the current execution will be terminated and the workflow will be restarted from the beginning.
2) After human review, the current execution will be resumed. To use this, you need to add `await r.wait(REVIEW_TIMEOUT)` after creating the review object.

**IMPORTANT: You always need to use `if r.status != ReviewStatus.READY:` to check review status after creating reviews.

Example:
```python
from va import workflow, step, review, ReviewStatus
REVIEW_TIMEOUT = 1000  # Review timeout in seconds

async with step("Step 4: Finished task"):
    # print logs, validate data, etc.
    r = review("review_step_name", "Review step description", (optional) review_data: List[Any])
    # Include this line below by default. If SOP mentions we need to terminate and rerun the workflow after review, skip this line.
    await r.wait(REVIEW_TIMEOUT)
    if r.status != ReviewStatus.READY:
        print("exiting execution since review is not ready")
        return

    # final logic after review: submit form, etc.
```

NOTE: Only use this review step if the SOP explicitly mentions it.

### 7. Error Detection & Resilience
After major browser actions (e.g., navigation, form submission), you must verify that the action completed successfully.
You can do this by checking if the page URL has changed, if a specific element is present after the action, or any other reliable method based on page structure and behavior.
You can also use LLM as fallback in the following way:
```python
result = await page.extract(
    "Does this page show any error because the form was not updated/submitted properly? Answer with exactly 'yes' or 'no' (lowercase, no additional text)."
)
answer = result.extraction.lower()
if answer == "yes":
    r = review("review_step_name", "Review step description")
    # The review step description is shown to the user. Make it clear and actionable — explain exactly what the user needs to do
    # (e.g., "Please fill all required fields and click submit" or "Check for errors and try submitting again").
    await r.wait(REVIEW_TIMEOUT)
    if r.status != ReviewStatus.READY:
        log.error("exiting execution since review is not ready")
        return
```
If the action did not complete successfully, you should always create a review step to pause the workflow and wait for human intervention.

### 8. Browser Automation
The SDK includes built-in Playwright integration for web automation:

```python
import logging
import asyncio
from va import workflow, step, review, ReviewStatus
from va.playwright import get_browser_context

@workflow("Example workflow")
async def main():
    async with get_browser_context(headless=False, slow_mo=1000) as browser_context:
        {get_page_code}

        async with step("navigate to the page"):
            await page.goto("https://httpbin.org/forms/post")

        async with page.step("get_by_prompt step", context={{"customer_name": "John Done"}}) as context:
            element = page.get_by_prompt("Customer name")
            await element.fill(context["customer_name"])

        async with page.step("get_by_prompt as a fallback (not triggered)", context={{"telephone": "************"}}) as context:
            element = page.get_by_label("Telephone: ") | page.get_by_prompt("Telephone")
            await element.fill(context["telephone"])

        async with page.step("get_by_prompt as a fallback (triggered)", context={{"email": "<EMAIL>"}}) as context:
            element = page.get_by_label("Email") | page.get_by_prompt("Email address")
            await element.fill(context["email"])

        async with page.step("Submit") as context:
            await page.get_by_text("Submit order").click()

        # Custom logic to verify if the form was submitted successfully, else create a review step
```

**IMPORTANT**: Always use `get_browser_context()` with an `async with` statement as it returns an async context manager, not the browser context directly.
It returns an async PlayWright context, keep that in mind.

### 9. Element Selection with Fallbacks
The VA SDK supports both PlayWright native selectors and locators + a AI-based fallback.
You should always use PlayWright selectors first.
These are the recommended built-in locators.

page.get_by_role() to locate by explicit and implicit accessibility attributes.
page.get_by_text() to locate by text content.
page.get_by_label() to locate a form control by associated label's text.
page.get_by_placeholder() to locate an input by placeholder.
page.get_by_alt_text() to locate an element, usually image, by its text alternative.
page.get_by_title() to locate an element by its title attribute.
page.get_by_test_id() to locate an element based on its data-testid attribute (other attributes can be configured).

**DO NOT use page.get_by_id(), it does not exist.

After that, use the pipe operator `| page.get_by_prompt(<description of the element in language)` for fallback element selection.
**IMPORTANT: Only page.get_by_prompt can be appended through the pipe operator '|' Other locators are not supported: 
e.g. Do not include something like `element = page.get_by_label("xxx") | page.get_by_name("yyy")` or `| page.locators('xxx')`
Only use two locators for each element: playwright locator | page.get_by_prompt('XXX')

```python
# Try label first, fallback to prompt-based selection
element = page.get_by_label("Email") | page.get_by_prompt("Email address")
await element.fill("<EMAIL>")

# Try multiple selectors with get_by_prompt as fallback
first_name_field = page.get_by_text("First Name") | page.get_by_prompt("First name field")
await first_name_field.fill("John Doe")

# Use locators with get_by_prompt as fallback
title_input = page.locator('input.whsOnd.zHQkBf').nth(3) | page.get_by_prompt("Optional Title Field")
await title_input.fill("CEO")
```

### 10. Code Structure Requirements
When generating VA SDK code, follow this structure:

```python
import logging
import asyncio
from pydantic import BaseModel
from va import workflow, step, review, ReviewStatus

# Define input model if needed
class WorkflowInput(BaseModel):
    # Define your input fields based on SOP requirements
    pass

@workflow("SOP implementation")
async def main(input: WorkflowInput, logger: logging.Logger):
    """Implementation of the SOP workflow"""
    
    async with step("Step 1"):
        logger.info("Starting step 1")
        # For web automation, use:
        # async with get_browser_context() as browser_context:
        #     {get_page_code}
    
    async with step("Step 2"):
        logger.info("Starting step 2")
        # Implementation

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    asyncio.run(main(WorkflowInput(), logger=logging.getLogger(__name__)))
```


### 11. Requirements.txt Format
Always include these dependencies in your requirements.txt:

```
{requirements_txt}
```

### 12. Best Practices
- Always use descriptive workflow and step names
- Include proper logging for debugging and monitoring
- When there is an exception, print out the entire error trace
- Define clear input models with appropriate types
- Use step context managers to organize workflow logic
- Test locally with sample input before deployment
- Handle errors gracefully within steps
- ALWAYS fallback element selection for robust web automation with a fallback to get_by_prompt
- Never call `browser_context = get_browser_context()` directly - always use `async with get_browser_context() as browser_context:`
- If calling OpenAI models, stick with newer version of the client without the 'proxies' argument and newer models

### 13. Available Imports
```python
from va import workflow, step, review, ReviewStatus
from va.playwright import get_browser_context
from pydantic import BaseModel
import logging
import asyncio
```
'''
