"""
State management for multi-agent SOP generation and code implementation system.
"""

from __future__ import annotations

from typing import Annotated

from langgraph.graph import add_messages
from langgraph.prebuilt.chat_agent_executor import AgentState


class OverallState(AgentState):
    """Main state shared across all agents."""

    messages: Annotated[list, add_messages]
    automation_plan_sop: str = ""
    claude_sessions: dict[
        str, str
    ] = {}  # generated_code_dir -> claude_session_id
    chainlit_session_id: str = ""
    is_sop_complete: bool = False

    execution_logs: str = ""
    form_url: str = ""

    execution_service_url: str = ""
    browser_connection_url: str = ""
