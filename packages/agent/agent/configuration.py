"""
Configuration for multi-agent SOP generation and code implementation system.
"""

import os
from typing import Any

from langchain_core.runnables import RunnableConfig
from pydantic import BaseModel, Field


class Configuration(BaseModel):
    """The configuration for the multi-agent system."""

    supervisor_agent_model: str = Field(
        default="gemini-2.5-flash",
        metadata={
            "description": "The name of the language model to use for the supervisor agent."
        },
    )

    knowledge_agent_model: str = Field(
        default="claude-3-7-sonnet-latest",
        metadata={
            "description": "The name of the language model to use for the knowledge agent."
        },
    )

    coding_agent_model: str = Field(
        default="claude-3-7-sonnet-latest",
        metadata={
            "description": "The name of the language model to use for the coding agent."
        },
    )

    temperature: float = Field(
        default=1.0,
        metadata={
            "description": "The temperature setting for all language models."
        },
    )

    enable_thinking: bool = Field(
        default=True,
        metadata={"description": "Enable thinking mode for Claude models."},
    )

    thinking_budget_tokens: int = Field(
        default=2000,
        metadata={"description": "Token budget for Claude thinking mode."},
    )

    max_tokens: int = Field(
        default=5000,
        metadata={"description": "Maximum tokens for model responses."},
    )

    @classmethod
    def from_runnable_config(
        cls, config: RunnableConfig | None = None
    ) -> "Configuration":
        """Create a Configuration instance from a RunnableConfig."""
        configurable = (
            config["configurable"]
            if config and "configurable" in config
            else {}
        )

        # Get raw values from environment or config
        raw_values: dict[str, Any] = {
            name: os.environ.get(name.upper(), configurable.get(name))
            for name in cls.model_fields.keys()
        }

        # Filter out None values
        values = {k: v for k, v in raw_values.items() if v is not None}

        return cls(**values)
