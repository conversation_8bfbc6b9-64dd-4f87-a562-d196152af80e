# NOTE: After the exploration tool is ready, we need to switch out of this file
import json

from playwright.sync_api import sync_playwright

from .utils.browser_utils import get_browser_page


def get_website_dom(url, browser_connection_url=""):
    with sync_playwright() as p:
        # Use helper function to get browser and page
        with get_browser_page(p, browser_connection_url) as (browser, page):
            # Get the CDP session
            cdp_session = page.context.new_cdp_session(page)

            # Navigate to the URL
            page.goto(url)

            # Enable the DOM domain
            cdp_session.send("DOM.enable")

            # Get the document using DOM.getDocument
            document = cdp_session.send(
                "DOM.getDocument",
                {
                    "depth": -1,  # Get full depth of the DOM tree
                    "pierce": True,  # Include shadow DOM
                },
            )

            # Get the root node ID
            root_node_id = document["root"]["nodeId"]

            # Get the outer HTML of the document
            outer_html = cdp_session.send(
                "DOM.getOuterHTML", {"nodeId": root_node_id}
            )

            # Get all form elements
            form_elements = cdp_session.send(
                "DOM.querySelectorAll",
                {"nodeId": root_node_id, "selector": "form"},
            )

            # Get details of form elements
            form_details = []
            for node_id in form_elements["nodeIds"]:
                form_html = cdp_session.send(
                    "DOM.getOuterHTML", {"nodeId": node_id}
                )
                form_details.append(form_html["outerHTML"])

            # Also look for form-like elements that might not be wrapped in form tags
            # Look for input elements
            input_elements = cdp_session.send(
                "DOM.querySelectorAll",
                {
                    "nodeId": root_node_id,
                    "selector": "input, textarea, select, button[type='submit'], button[role='combobox'], [role='combobox'], [role='listbox']",
                },
            )

            # Look for form-like containers (divs with form-like content)
            form_like_containers = cdp_session.send(
                "DOM.querySelectorAll",
                {
                    "nodeId": root_node_id,
                    "selector": "div[role='form'], div[class*='form'], div[class*='Form']",
                },
            )

            # Get details of form-like elements
            form_like_details = []

            # Add input elements details
            for node_id in input_elements["nodeIds"]:
                try:
                    element_html = cdp_session.send(
                        "DOM.getOuterHTML", {"nodeId": node_id}
                    )

                    # Determine the specific type of element
                    html_content = element_html["outerHTML"]
                    element_type = "input_element"

                    if (
                        'role="combobox"' in html_content
                        or 'role="listbox"' in html_content
                    ):
                        element_type = "dropdown"
                    elif html_content.startswith("<textarea"):
                        element_type = "textarea"
                    elif html_content.startswith("<select"):
                        element_type = "select"
                    elif 'type="submit"' in html_content:
                        element_type = "submit_button"

                    form_like_details.append(
                        {"type": element_type, "html": html_content}
                    )
                except Exception:
                    pass

            # Add form-like container details
            for node_id in form_like_containers["nodeIds"]:
                try:
                    container_html = cdp_session.send(
                        "DOM.getOuterHTML", {"nodeId": node_id}
                    )
                    form_like_details.append(
                        {
                            "type": "form_container",
                            "html": container_html["outerHTML"],
                        }
                    )
                except Exception:
                    pass

            # Browser cleanup is handled automatically by the context manager

            return {
                "document": document,
                "outer_html": outer_html["outerHTML"],
                "form_details": form_details,
                "form_like_details": form_like_details,
            }


def main():
    # Example usage
    url = "https://docs.google.com/forms/d/e/1FAIpQLSd_NmkLEJM59-KHZKG7LF_3-0EV7b3bWgeSUqvHsOWAqPSYbA/viewform"
    # url = "https://ashby-clone-form-static.lovable.app/"  # This websites uses form-like elements but not in the <form> tag
    result = get_website_dom(url)

    # Save the full DOM structure to a JSON file
    with open("dom_structure.json", "w", encoding="utf-8") as f:
        json.dump(result["document"], f, indent=2)

    # Save the outer HTML to a file
    with open("dom_example.html", "w", encoding="utf-8") as f:
        f.write(result["outer_html"])

    # Save form details to a separate file
    with open("form_details.html", "w", encoding="utf-8") as f:
        for form in result["form_details"]:
            f.write(form + "\n\n")

    # Save form-like details to a separate file
    with open("form_like_details.json", "w", encoding="utf-8") as f:
        json.dump(result["form_like_details"], f, indent=2)

    print("Files have been saved:")
    print("- dom_structure.json: Contains the full DOM tree structure")
    print("- dom_example.html: Contains the outer HTML of the page")
    print("- form_details.html: Contains the HTML of all form elements")
    print(
        "- form_like_details.json: Contains form-like elements (inputs, containers, etc.)"
    )
    print(f"Number of forms found: {len(result['form_details'])}")
    print(
        f"Number of form-like elements found: {len(result['form_like_details'])}"
    )

    # Print summary of form-like elements
    if result["form_like_details"]:
        print("\nForm-like elements found:")
        input_count = sum(
            1
            for item in result["form_like_details"]
            if item["type"] == "input_element"
        )
        dropdown_count = sum(
            1
            for item in result["form_like_details"]
            if item["type"] == "dropdown"
        )
        textarea_count = sum(
            1
            for item in result["form_like_details"]
            if item["type"] == "textarea"
        )
        select_count = sum(
            1
            for item in result["form_like_details"]
            if item["type"] == "select"
        )
        submit_count = sum(
            1
            for item in result["form_like_details"]
            if item["type"] == "submit_button"
        )
        container_count = sum(
            1
            for item in result["form_like_details"]
            if item["type"] == "form_container"
        )

        print(f"- Input elements: {input_count}")
        print(f"- Dropdowns: {dropdown_count}")
        print(f"- Textareas: {textarea_count}")
        print(f"- Select elements: {select_count}")
        print(f"- Submit buttons: {submit_count}")
        print(f"- Form containers: {container_count}")


# Run the example
if __name__ == "__main__":
    main()
