"""
Claude CLI utility functions for runtime integration.
Handles command building and execution for both local and remote (E2B) runtimes.
"""

import logging
import os
import subprocess

from common.sandbox.base import Runtime, RuntimeType
from web_server.constants.env import MCP_CONFIG_FILE_NAME

from ..prompts import get_claude_cli_prompt

logger = logging.getLogger(__name__)


# Claude Code CLI Utility Functions
# To be deprecated: Use Claude Code Python SDK after it stabilizes
def build_claude_command(
    runtime: Runtime,
    instruction: str,
    folder_path: str,
    allowed_tools: list[str] | None = None,
    additional_files: list[str] | None = None,
    is_local_runtime: bool = False,
    is_first_message: bool = False,
    session_id: str | None = None,
) -> tuple[str, list[str]]:
    """
    Build a claude CLI command with proper argument handling for both local and remote runtimes.

    Args:
        runtime: The runtime instance to execute the command
        instruction: The instruction for claude
        folder_path: Folder within workspace for this session
        allowed_tools: List of tools claude can use (default: ["Read", "Write", "Bash"])
        additional_files: Optional list of additional files available that are not part of the generated code directory
        is_local_runtime: Whether this is running on local runtime (affects claude path detection)
        is_first_message: Whether this is the first message in the conversation
        session_id: Optional session_id to resume the conversation

    Returns:
        Tuple of (command, args) that can be passed to runtime.run_command()

    Note:
        - For local runtime, validates claude CLI availability
    """
    if allowed_tools is None:
        allowed_tools = ["Read", "Write", "Bash"]

    # Get the system prompt with VA SDK documentation for this runtime
    system_prompt = get_claude_cli_prompt(runtime, is_local_runtime)

    # Build context about the workspace
    workspace_context = f"""IMPORTANT: You are working in an isolated directory: {folder_path}, which is the runtime workspace root (runtime_workspace/<session_id>/). You may access files in the `local_storage/` directory within your workspace. Do not reference or import any files outside the runtime_workspace/<session_id>/ directory."""

    # Add information about additional files if any
    additional_files_text = "All data input files will be available in the runtime workspace root directory under the `local_storage/` directory."
    if additional_files:
        additional_files_text += f"\n\nAvailable user attachment files: {', '.join(additional_files)}"

    instruction = f"""{instruction}{additional_files_text}
The data input files listed above are available at the exact paths shown (they are relative paths from your current working directory).
Create all necessary files within this directory only. Do not create any subfolders like "tmp", and you must write all files under this directory flat. If you need any external dependencies, add them to requirements.txt.
BE SURE TO CREATE the main.py file in the current directory under runtime_workspace/<session_id>/.
"""
    # Build claude command arguments
    args = []

    args.extend(["--system-prompt", system_prompt])

    # Add streaming JSON output format
    args.extend(["--output-format", "stream-json", "--verbose"])

    # Add MCP config (will be created dynamically in runtime)
    args.extend(["--mcp-config", MCP_CONFIG_FILE_NAME])

    # Add working directory (runtime will resolve the full path)
    args.extend(["--add-dir", folder_path])

    # Add allowed tools
    if allowed_tools:
        args.extend(["--allowedTools", ",".join(allowed_tools)])

    # Add continue flag to maintain conversation history
    if not is_first_message:
        args.extend(["-p", instruction])
        args.append("--continue")
        # For continuation, add the user message as the final argument (prompt)
    else:
        # For first message, use -p with full context
        args.extend(["-p", f"{workspace_context}\n\nHuman: {instruction}"])

    # Skip all permission checks for seamless automation
    args.append("--dangerously-skip-permissions")

    # For local runtime, validate claude CLI availability
    if is_local_runtime:
        claude_cmd = _find_local_claude_cli()
        return claude_cmd, args

    # For remote runtime (E2B), use the global claude command
    logger.debug(
        f"Building claude command for remote runtime with {len(args)} arguments"
    )
    return "claude", args


def _find_local_claude_cli() -> str:
    """
    Find and validate the local Claude CLI installation.

    Returns:
        Path to the claude CLI executable

    Raises:
        RuntimeError: If claude CLI is not found or not working
    """
    # Try to find claude in common locations
    claude_paths = [
        "claude",  # If it's in PATH
        "/usr/local/bin/claude",  # System install
        os.path.expanduser("~/.local/bin/claude"),  # User install
    ]

    for path in claude_paths:
        if path == "claude" or os.path.exists(path):
            try:
                # Test if the command works
                result = subprocess.run(
                    [path, "--version"],
                    capture_output=True,
                    timeout=10,
                    check=True,
                )
                if result.returncode == 0:
                    logger.info(f"Found claude CLI at: {path}")
                    return path
            except (
                subprocess.TimeoutExpired,
                subprocess.CalledProcessError,
                FileNotFoundError,
            ) as e:
                logger.debug(f"Claude CLI not found at {path}: {e}")
                continue

    raise RuntimeError(
        "Claude CLI not found. Please ensure Claude CLI is installed and accessible. "
        "Visit https://claude.ai/cli for installation instructions."
    )


async def run_claude_command_with_runtime(
    runtime: Runtime,
    instruction: str,
    folder_path: str,
    additional_files: list[str] | None = None,
    session_id: str | None = None,
    is_first_message: bool = False,
    envs: dict[str, str] | None = None,
) -> tuple[bool, str]:
    """
    Execute a claude command using the provided runtime.

    Args:
        runtime: The runtime instance to execute the command
        instruction: The instruction for claude
        folder_path: Working directory for the command
        additional_files: Optional list of additional files to include
        session_id: Optional session_id to resume the conversation

    Returns:
        Tuple of (success: bool, output: json str)
    """
    try:
        # Determine if this is a local runtime
        is_local = runtime.runtime_type == RuntimeType.LOCAL

        command, args = build_claude_command(
            runtime,
            instruction,
            folder_path,
            additional_files=additional_files,
            is_local_runtime=is_local,
            session_id=session_id,
            is_first_message=is_first_message,
        )

        logger.info(f"Executing claude command with {len(args)} arguments")

        result = await runtime.run_command(
            command, args, folder_path, envs=envs
        )
        return True, result.stdout

    except Exception as e:
        logger.error(f"Error building claude command: {e}")
        return False, f"Error building Claude command: {str(e)}"


# Claude Code Python SDK
# NOTE: Switch to this after claude python sdk stabilizes
# async def _run_claude_code_sdk_async(
#     runtime: Runtime,
#     instruction: str,
#     folder_path: str,
#     continue_conversation: bool,
#     session_id: str,
#     additional_files: list[str] | None = None,
# ) -> tuple[str, str, list[Any]]:
#     """
#     Run Claude Code SDK asynchronously.
#     Returns (session_id, all_messages)
#     """
#     is_local_runtime = runtime.runtime_type == RuntimeType.LOCAL
#     # Build isolated instruction with proper context
#     additional_files_text = ""
#     if additional_files:
#         additional_files_text = (
#             f"\n\nData Input Files available: {', '.join(additional_files)}"
#         )

#     isolated_instruction = f"""IMPORTANT: You are working in an isolated directory: {folder_path}, which follows the format runtime_workspace/session_id/generated_code_dir. You may access files in the local_storage directory, located one level above the generated code directory. Do not reference or import any files outside the runtime_workspace/session_id/ directory.

# {instruction}{additional_files_text}
# Make sure to reference the additional_files_text files correctly in the code, they are absolute paths and are available one level above the generated code directory under `local_storage`.
# Create all necessary files within this directory only. If you need any external dependencies, add them to requirements.txt.
# """

#     # Add runtime-specific package notes
#     if not is_local_runtime:
#         isolated_instruction += "Note: No need to add vibe automation, pydantic, playwright packages to the requirements.txt as they are already installed."

#     logger.info(
#         f"🤖 Running Claude Code SDK in isolated directory: {folder_path}"
#     )
#     if continue_conversation and session_id:
#         logger.info(f"🔄 Continuing conversation (session_id: {session_id})")
#     else:
#         logger.info("🔄 Starting new conversation")
#         continue_conversation = False
#         session_id = None

#     options = ClaudeCodeOptions(
#         cwd=runtime.get_full_path(folder_path),
#         permission_mode="acceptEdits",
#         allowed_tools=["Read", "Write", "Bash"],
#         resume=session_id,
#     )

#     all_messages = []
#     result_session_id = None

#     try:
#         async for message in query(
#             prompt=isolated_instruction, options=options
#         ):
#             all_messages.append(message)
#             if hasattr(message, "session_id"):
#                 result_session_id = message.session_id

#             # Simple progress logging
#             print("📤 Claude: Processing...")
#             print(message)

#     # TODO: If claude python SDK stabilizes, we can remove this exception handling
#     except (KeyError, RuntimeError, asyncio.CancelledError) as e:
#         print(f"⚠️ SDK cleanup error (ignoring as it might be a known bug): {e}")

#     return result_session_id or session_id or "", all_messages


# def run_claude_sdk_with_runtime(
#     state: OverallState,
#     runtime: Runtime,
#     instruction: str,
#     generated_code_dir: str,
#     continue_conversation: bool = False,
#     additional_files: list[str] | None = None,
# ) -> tuple[bool, str]:
#     """
#     Run Claude Code SDK, handling session management, async execution, and error workarounds.
#     Returns (success, output_message, new_session_id)
#     """
#     session_id = state.get("claude_sessions", {}).get(generated_code_dir, "")

#     start_time = time.time()
#     success = False
#     output = ""
#     new_session_id = None

#     try:
#         new_session_id, all_messages = asyncio.run(
#             _run_claude_code_sdk_async(
#                 runtime,
#                 instruction,
#                 generated_code_dir,
#                 continue_conversation,
#                 session_id,
#                 additional_files,
#             )
#         )

#         if all_messages:
#             success = True
#             duration = time.time() - start_time
#             output = f"Claude Code completed in {duration:.1f}s. Messages: {len(all_messages)}"

#     except Exception as e:
#         # Print out full traceback
#         output = f"Error during Claude Code SDK execution: {e}"
#         logger.error(f"❌ {output}", exc_info=True)
#         success = False

#     return (
#         success,
#         output,
#         new_session_id
#         if new_session_id and new_session_id != session_id
#         else None,
#     )
