def pretty_print_result(result, indent=False, index=None):
    messages = result["messages"]
    if index is not None:
        messages = [messages[index]]
    for message in messages:
        # print(f"Message: {message}")
        if message.type == "tool_result":
            print(f"Tool result: {message.tool_result}")
            continue
        pretty_message = message.pretty_repr(html=True)
        # if not indent:
        #     print(pretty_message)
        #     return
        indented = "\n".join("\t" + c for c in pretty_message.split("\n"))
        print(indented)
