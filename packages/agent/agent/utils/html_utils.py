import re

from bs4 import BeautifulSoup, Comment


def simplify_html_for_form_understanding(html_content: str) -> str:
    """
    Simplifies HTML content for web form understanding, preserving 'id' and 'name' attributes.

    Args:
        html_content: The original HTML string.

    Returns:
        A much smaller, simplified HTML string.
    """
    if not html_content:
        return ""

    soup = BeautifulSoup(html_content, "lxml")

    # 1. Remove script and style elements
    for script_or_style in soup(["script", "style"]):
        script_or_style.decompose()

    # 2. Remove comments
    for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
        comment.extract()

    # 3. Define tags to keep (form-specific and essential structural)
    # This list can be adjusted based on how much structural context you need.
    tags_to_keep = {
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "ul",
        "ol",
        "li",
        "table",
        "thead",
        "tbody",
        "tfoot",
        "tr",
        "th",
        "td",
        "form",
        "input",
        "select",
        "textarea",
        "button",
        "label",
        "fieldset",
        "legend",
        "option",
        "optgroup",
        "a",  # Links can be important for form submission or navigation related to forms
        # Add other tags if you find them consistently useful for your "understanding"
    }

    # 4. Process each tag: remove non-essential attributes and filter tags
    all_tags = soup.find_all(True)  # Get all tags in the soup
    for tag in all_tags:
        # If tag is not in tags_to_keep, replace it with its contents (unwrap)
        if tag.name not in tags_to_keep:
            tag.unwrap()
            continue

        # Preserve 'id' and 'name' attributes, remove others
        attrs_to_preserve = [
            "id",
            "name",
            "type",
            "value",
            "placeholder",
            "action",
            "method",
            "for",
        ]
        # 'type', 'value', 'placeholder', 'action', 'method', 'for', 'href', 'src'
        # are also crucial for understanding form fields and links, so keeping them.

        current_attrs = dict(tag.attrs)
        tag.attrs = {}  # Clear all attributes

        for attr, value in current_attrs.items():
            if attr in attrs_to_preserve:
                tag[attr] = value

    # 5. Condense whitespace and clean up empty tags that might remain
    simplified_html = str(soup)

    # Remove excessive whitespace, newlines, and tabs
    simplified_html = re.sub(r"\s+", " ", simplified_html).strip()

    # Remove empty tags that might have resulted from stripping content,
    # but be careful not to remove self-closing tags like <input> or <br>
    # This regex is a bit more aggressive and might need fine-tuning for edge cases.
    # It targets tags that have only whitespace or no content between opening and closing.
    simplified_html = re.sub(
        r"<(?!input|br|img|hr|meta|link)\b([a-z]+)\b[^>]*>\s*<\/\1>",
        "",
        simplified_html,
        flags=re.IGNORECASE,
    )

    return simplified_html
