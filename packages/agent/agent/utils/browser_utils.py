"""
Browser connection utilities for web exploration tools.

This module provides helper functions for connecting to browsers,
either through Browserbase CDP connections or local Playwright instances.
"""

from collections.abc import Generator
from contextlib import contextmanager

from playwright.sync_api import <PERSON><PERSON><PERSON>, <PERSON>, Playwright


@contextmanager
def get_browser_page(
    playwright_instance: <PERSON><PERSON>, browser_connection_url: str = ""
) -> Generator[tuple[<PERSON><PERSON><PERSON>, <PERSON>]]:
    """
    Get a browser and page instance, either from Browserbase CDP or local Playwright.

    This helper function abstracts the logic of connecting to either a remote
    Browserbase session via CDP or launching a local browser instance.

    Args:
        playwright_instance: The Playwright instance to use
        browser_connection_url: Optional Browserbase CDP connection URL

    Yields:
        Tuple of (browser, page) instances ready for use

    Example:
        with sync_playwright() as p:
            with get_browser_page(p, connection_url) as (browser, page):
                page.goto("https://example.com")
                content = page.content()
                # browser cleanup handled automatically
    """
    browser = None
    page = None

    try:
        if browser_connection_url:
            print(
                f"🔗 Using Browserbase session via CDP: {browser_connection_url}"
            )
            # Connect to existing Browserbase session via CDP
            browser = playwright_instance.chromium.connect_over_cdp(
                browser_connection_url
            )

            # Get existing page or create new one
            if browser.contexts and browser.contexts[0].pages:
                page = browser.contexts[0].pages[0]
            else:
                page = browser.contexts[0].new_page()
        else:
            print("🖥️ Using local Playwright browser")
            # Launch browser locally (fallback for local runtime)
            browser = playwright_instance.chromium.launch(headless=True)
            page = browser.new_page()

        yield browser, page

    finally:
        # Clean up browser connection
        if browser:
            browser.close()
