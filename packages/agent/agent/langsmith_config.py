"""
LangSmith configuration and tracing setup for the Agent package.
Based on official LangSmith tutorial patterns.
"""

import functools
import os
from typing import Any

from langsmith import traceable


def setup_langsmith_environment(config=None):
    """
    Set up LangSmith environment variables for automatic tracing.
    This should be called ONCE at the application entry point.

    Args:
        config: Optional ServerConfig object. If not provided, reads from environment.
    """
    if config:
        # Use provided config (from ServerConfig)
        if not config.langsmith_api_key:
            return False

        os.environ["LANGSMITH_API_KEY"] = config.langsmith_api_key
        os.environ["LANGSMITH_PROJECT"] = config.langsmith_project
        os.environ["LANGSMITH_ENDPOINT"] = config.langsmith_endpoint
        os.environ["LANGSMITH_TRACING"] = config.langsmith_tracing

        print(
            f"✅ LangSmith tracing enabled for project: {config.langsmith_project}"
        )
        print(
            f"🔍 LangSmith API Key (first 10 chars): {config.langsmith_api_key[:10]}..."
        )
        print(f"🔍 LangSmith Endpoint: {config.langsmith_endpoint}")
        print(f"🔍 LangSmith Tracing: {config.langsmith_tracing}")
        return True
    else:
        # Fallback to environment variables (for backwards compatibility)
        api_key = os.getenv("LANGSMITH_API_KEY")
        if not api_key:
            return False

        # Set default values if not already configured
        if not os.getenv("LANGSMITH_TRACING"):
            os.environ["LANGSMITH_TRACING"] = "true"

        if not os.getenv("LANGSMITH_PROJECT"):
            os.environ["LANGSMITH_PROJECT"] = "vibe-automation-server"

        if not os.getenv("LANGSMITH_ENDPOINT"):
            os.environ["LANGSMITH_ENDPOINT"] = "https://api.smith.langchain.com"

        print(
            f"✅ LangSmith tracing enabled for project: {os.getenv('LANGSMITH_PROJECT')}"
        )
        print(
            f"🔍 LangSmith API Key (first 10 chars): {os.environ['LANGSMITH_API_KEY'][:10]}..."
        )
        print(f"🔍 LangSmith Endpoint: {os.environ['LANGSMITH_ENDPOINT']}")
        print(f"🔍 LangSmith Tracing: {os.environ['LANGSMITH_TRACING']}")
        return True


def get_tracing_metadata(agent_name: str) -> dict[str, Any]:
    """
    Get metadata for LangSmith tracing.

    Args:
        agent_name: Name of the agent (supervisor, knowledge, coding)

    Returns:
        Dictionary with metadata for tracing
    """
    return {
        "agent_name": agent_name,
        "environment": os.getenv("ENVIRONMENT", "development"),
        "version": "0.1.0",
        "service": "vibe-automation-server",
    }


def trace_agent_function(agent_name: str, run_type: str = "chain"):
    """
    Decorator to trace agent functions using LangSmith @traceable decorator.

    Args:
        agent_name: Name of the agent
        run_type: Type of run (chain, llm, tool, etc.)
    """

    def decorator(func):
        @traceable(
            name=f"{agent_name}_{func.__name__}",
            run_type=run_type,
            metadata=get_tracing_metadata(agent_name),
            tags=[agent_name, "vibe-automation", "langgraph"],
        )
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        return wrapper

    return decorator


def trace_supervisor_agent(func):
    """Decorator specifically for supervisor agent functions."""
    return trace_agent_function("supervisor", "chain")(func)


def trace_knowledge_agent(func):
    """Decorator specifically for knowledge agent functions."""
    return trace_agent_function("knowledge", "chain")(func)


def trace_coding_agent(func):
    """Decorator specifically for coding agent functions."""
    return trace_agent_function("coding", "chain")(func)


# Utility functions for manual tracing
def get_current_run_id() -> str | None:
    """Get the current run ID if available."""
    try:
        from langsmith import get_current_run_tree

        run = get_current_run_tree()
        return str(run.id) if run else None
    except Exception:
        return None


def add_run_metadata(metadata: dict[str, Any]):
    """Add metadata to the current run."""
    try:
        from langsmith import update_current_run

        update_current_run(metadata=metadata)
    except Exception:
        pass


def add_run_tags(tags: list[str]):
    """Add tags to the current run."""
    try:
        from langsmith import update_current_run

        update_current_run(tags=tags)
    except Exception:
        pass


# Configuration validation
def is_tracing_enabled() -> bool:
    """Check if LangSmith tracing is enabled."""
    return (
        os.getenv("LANGSMITH_TRACING", "false").lower() == "true"
        and os.getenv("LANGSMITH_API_KEY") is not None
    )
