"""
Knowledge Agent - Specializes in information gathering and SOP generation/refinement.
"""

import pprint
from typing import Annotated

from langchain_anthropic import <PERSON>t<PERSON><PERSON><PERSON>
from langchain_core.messages import ToolMessage
from langchain_core.tools import InjectedToolCallId, tool
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.config import get_stream_writer
from langgraph.prebuilt import InjectedState, create_react_agent
from langgraph.types import Command

from .configuration import Configuration
from .knowledge_agent_helper.video_sop_extractor import (
    extract_sop_from_video_gemini,
)
from .langsmith_config import trace_knowledge_agent
from .prompts import (
    create_update_validate_sop_prompt,
    knowledge_agent_light_prompt,
)
from .state import OverallState
from .tools import (
    ExplorationTools,
    list_available_attachments,
    read_file,
)
from .utils.pretty_print_util import pretty_print_result

GPT_4o_MODEL = "gpt-4o-2024-08-06"
GPT_4_1_MINI_MODEL = "gpt-4.1-mini-2025-04-14"

AUTOMATION_PLAN_SOP_KEY = "automation_plan_sop"
IS_SOP_COMPLETE_KEY = "is_sop_complete"
SOP_KEY = "sop"
CLARIFICATION_MESSAGE_KEY = "clarification_message"


class KnowledgeTools:
    """Tools available to the knowledge agent for information gathering and SOP work."""

    @staticmethod
    @tool
    def create_update_validate_sop(
        conversation_history: str,
        source_data_metadata: str,
        target_form_metadata: str,
        state: Annotated[OverallState, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
    ) -> Command:
        """
        Create, update and validate a sop for a web automation task.

        Args:
            conversation_history: Conversation history between AI and the human as it is.
            source_data_metadata: Information about source data that can be used to get source schema, etc. Empty if not available.
            target_form_metadata: Information about target form that can be used to get form field locators, types, etc. Empty if not available.
            state: State of the knowledge agent
            tool_call_id: Tool call id

        Returns:
            New clarifying question to be asked from the user
        """
        status_message = "📋 **Generating your automation plan...** Creating detailed step-by-step instructions."
        writer = get_stream_writer()
        if writer:
            try:
                writer({"status_update": status_message})
            except Exception as e:
                print(f"Error emitting status update: {e}")

        print(
            "=============== create_update_validate_blueprint input ==============="
        )
        if AUTOMATION_PLAN_SOP_KEY not in state:
            state[AUTOMATION_PLAN_SOP_KEY] = ""
        print(f"current_sop: {state[AUTOMATION_PLAN_SOP_KEY]}")
        print(f"conversation_history: {conversation_history}")
        print(f"source_data_metadata: {source_data_metadata}")
        print(f"target_form_metadata: {target_form_metadata}")
        print("state:")
        pretty_print_result(state)

        # Create prompt for use case identification
        prompt = create_update_validate_sop_prompt.format(
            current_sop=state[AUTOMATION_PLAN_SOP_KEY],
            conversation_history=conversation_history,
            source_data_metadata=source_data_metadata,
            target_form_metadata=target_form_metadata,
        )

        # Make LLM call to identify use case
        config = Configuration()
        model = ChatOpenAI(
            model=GPT_4o_MODEL,
            temperature=config.temperature,
            max_tokens=config.max_tokens,
        ).with_structured_output(method="json_mode")

        response = model.invoke(prompt)
        print(
            "=============== create_update_validate_sop response ==============="
        )
        pprint.pprint(response)

        # This time we explicitly update the state with a ToolMessage inside
        # the tool.
        tool_message_content = ""
        # Fallback to the current SOP if the SOP is not in the response
        response_sop = response.get(SOP_KEY, state[AUTOMATION_PLAN_SOP_KEY])
        if IS_SOP_COMPLETE_KEY in response and response[IS_SOP_COMPLETE_KEY]:
            tool_message_content = (
                "SOP is complete and ready to be shown to the user.\n\n"
                + response_sop
            )
        else:
            status_message = "📋 **Generating some clarification questions...**"
            writer = get_stream_writer()
            if writer:
                try:
                    writer({"status_update": status_message})
                except Exception as e:
                    print(f"Error emitting status update: {e}")
            tool_message_content = (
                "NEEDS CLARIFICATION TO PROCEED FURTHER: "
                + response.get(CLARIFICATION_MESSAGE_KEY, "")
            )

        print(f"tool_message_content: {tool_message_content}")

        # Update the automation_plan_sop state with the new blueprint
        state_update = {
            "automation_plan_sop": response_sop,
            "is_sop_complete": response.get(IS_SOP_COMPLETE_KEY, False),
            "messages": [
                ToolMessage(tool_message_content, tool_call_id=tool_call_id)
            ],
        }
        # We return a Command object in the tool to update our state.
        return Command(update=state_update)


class KnowledgeAgentLight:
    """Knowledge agent specialized in information gathering and SOP generation."""

    @trace_knowledge_agent
    def __init__(self):
        """Initialize the knowledge agent with tools and model."""
        config = Configuration()

        thinking_config = None
        if config.enable_thinking:
            thinking_config = {
                "type": "enabled",
                "budget_tokens": config.thinking_budget_tokens,
            }

        self.model = ChatAnthropic(
            model=config.knowledge_agent_model,
            temperature=config.temperature,
            max_tokens=config.max_tokens,
            thinking=thinking_config,
        )

        self.tools = [
            KnowledgeTools.create_update_validate_sop,
            extract_sop_from_video_gemini,
        ]
        self.tools = self.tools + [
            read_file,
            list_available_attachments,
            ExplorationTools.webpage_agent_exploration,
        ]

        self.agent = create_react_agent(
            model=self.model,
            tools=self.tools,
            prompt=self._get_system_prompt(),
            name="knowledge_agent",
            state_schema=OverallState,
            checkpointer=MemorySaver(),
        )

    def _get_system_prompt(self) -> str:
        """Get the system prompt for the knowledge agent."""
        return knowledge_agent_light_prompt


# Export the agent for use by supervisor
@trace_knowledge_agent
def create_knowledge_agent():
    """Factory function to create a knowledge agent instance."""
    knowledge_agent = KnowledgeAgentLight()
    return knowledge_agent.agent
