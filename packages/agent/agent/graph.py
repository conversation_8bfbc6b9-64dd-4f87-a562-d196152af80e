"""
LangGraph setup for multi-agent SOP+code generation system.
"""

from collections.abc import Async<PERSON>enerator
from typing import Any

from .configuration import Configuration
from .supervisor_agent import create_supervisor_agent


def create_graph(config: Configuration = None):
    """
    Create and return the multi-agent supervisor graph.

    Args:
        config: Configuration object with model settings

    Returns:
        Compiled LangGraph for the multi-agent system.
    """
    config = config or Configuration()

    return create_supervisor_agent(config)


def process_request(graph, user_request: str) -> dict[str, Any]:
    """
    Process a user request through the multi-agent system.

    Args:
        graph: The compiled LangGraph
        user_request: The user's request for SOP generation and code implementation

    Returns:
        Final results from the multi-agent workflow
    """
    try:
        initial_state = {
            "messages": [{"role": "user", "content": user_request}]
        }

        # Process through the multi-agent system
        final_result = None
        for chunk in graph.stream(initial_state, subgraphs=True):
            final_result = chunk

        return {
            "status": "success",
            "workflow": "sop_generation_and_coding",
            "result": final_result,
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "workflow": "sop_generation_and_coding",
        }


async def stream_process(graph, user_request: str) -> AsyncGenerator[Any]:
    """
    Stream the processing of a user request, yielding intermediate results.

    Args:
        graph: The compiled LangGraph
        user_request: The user's request for SOP generation and code implementation

    Yields:
        Intermediate results from each agent
    """
    initial_state = {"messages": [{"role": "user", "content": user_request}]}

    async for chunk in graph.astream(initial_state, subgraphs=True):
        yield chunk
