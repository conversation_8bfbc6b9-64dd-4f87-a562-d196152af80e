"""
UserSimulatorAgent - Automatically simulates user interactions with
<PERSON><PERSON><PERSON><PERSON><PERSON> within single-agent mode.
"""

import logging
import os
import re

from anthropic import AsyncAnthropic
import chainlit as cl

from agent.use_case_dataset import get_test_use_case, get_use_case_by_id
from common.log import info

logger = logging.getLogger(__name__)


class UserSimulatorAgent:
    """Agent that simulates user interactions with ClaudeCliAgent."""

    def __init__(self, use_case_id: int | None = None):
        self.message_processor = None
        self.is_active = False
        self.use_case = get_use_case_by_id(use_case_id) if use_case_id else get_test_use_case()
        if not self.use_case:
            raise ValueError(f"Use case '{use_case_id}' not found")
        
        # Initialize Claude API client for LLM responses
        api_key = os.getenv("ANTHROPIC_API_KEY")
        if not api_key:
            raise ValueError(
                "ANTHROPIC_API_KEY environment variable is required"
            )
        self.anthropic_client = AsyncAnthropic(api_key=api_key)
        
        # Conversation history for the LLM (<PERSON>'s messages as user, 
        # our responses as assistant)
        self.llm_conversation_history = []
        
        # Track <PERSON>'s current response
        self.current_claude_response = ""
        self.is_claude_responding = False

    async def start_simulation(self, session_id: str) -> None:
        """Start user simulation mode."""
        self.is_active = True
        info(f"UserSimulatorAgent started for session {session_id}")
        
        # Get the message processor from the web server
        from web_server.chat_app import message_processor
        self.message_processor = message_processor
        
        # Add initial VA agent message to conversation history
        self.llm_conversation_history.append({
            "role": "user",
            "content": "Hello, I am your Vibe Automation Agent (VA agent). What automation would you like to build today?"
        })
        
        # Generate initial message using LLM
        initial_message = await self.generate_llm_response()
        await self.parse_and_send_user_message(initial_message)

    async def parse_and_send_user_message(self, message: str, file_elements: list[cl.File] = None) -> None:
        """Parse message for commands and send as if from a user through the frontend."""
        if not self.is_active:
            return

        def _parse_llm_response_for_stop_command(response: str) -> tuple[bool, str]:
            """Parse LLM response to detect stop_simulation command."""
            import re
            pattern = r'stop_simulation\(([^)]*)\)'
            match = re.search(pattern, response)
            if match:
                reason = match.group(1).strip()
                return True, reason
            return False, ""

        def _parse_llm_response_for_upload_command(response: str) -> tuple[bool, list[int]]:
            """Parse LLM response to detect upload_file commands.
            
            Returns:
                tuple: (should_upload, file_indices) where should_upload is boolean 
                       and file_indices is list of int indices
            """
            import re
            pattern = r'upload_file\((\d+)\)'
            matches = re.findall(pattern, response)
            if matches:
                file_indices = [int(match) for match in matches]
                return True, file_indices
            return False, []

        async def upload_files_by_indices(file_indices: list[int]) -> list[cl.File]:
            """Upload multiple files by their indices."""
            full_file_paths = self.use_case.get_full_file_paths()
            if not full_file_paths:
                logger.warning("No files available for upload")
                return []
            
            file_elements = []
            for file_index in file_indices:
                if file_index < 0 or file_index >= len(full_file_paths):
                    logger.warning(f"Invalid file index: {file_index}")
                    continue
                
                file_path = full_file_paths[file_index]
                if not os.path.exists(file_path):
                    logger.warning(f"File not found: {file_path}")
                    continue
                
                # Read file content
                with open(file_path, "rb") as src:
                    file_content = src.read()
                
                # Create file element
                filename = os.path.basename(file_path)
                file_element = cl.File(
                    name=filename,
                    content=file_content,
                    mime="application/octet-stream"
                )
                file_elements.append(file_element)
                info(f"Uploaded file by index {file_index}: {filename}")
            
            return file_elements

        # Parse for stop command
        should_stop, reason = _parse_llm_response_for_stop_command(message)
        if should_stop:
            await self.stop_simulation(reason)
            return

        # Parse for upload command
        should_upload, file_indices = _parse_llm_response_for_upload_command(message)
        if should_upload:
            file_elements = await upload_files_by_indices(file_indices)

        info(f"UserSimulatorAgent sending user message: {message}")
        # Add our response to the conversation history as "assistant"
        self.llm_conversation_history.append({
            "role": "assistant", 
            "content": message
        })
        # Create a Chainlit message that appears to come from the actual user
        # Don't set author to make it appear as a regular user message
        display_message = cl.Message(
            content=f"**[SIMULATED USER]** {re.sub(r'upload_file\(\d+\)', '', message)}", 
            elements=file_elements
        )
        
        # Send the display message through Chainlit
        await display_message.send()
        
        if self.message_processor:
            # Reset Claude response tracking
            self.current_claude_response = ""
            self.is_claude_responding = True
            
            await self.message_processor.process_message(display_message)
        else:
            logger.error("Message processor not available")

    def capture_claude_response(self, log_content: str) -> None:
        """Capture Claude's response from log content."""
        if not self.is_active or not self.is_claude_responding:
            return
        self.current_claude_response += log_content

    async def finish_capturing_and_responding_to_claude_response(self) -> None:
        """Called when Claude finishes responding."""
        if not self.is_active or not self.is_claude_responding:
            return
        if not self.current_claude_response.strip():
            logger.error("No Claude response to continue with")
            return
        self.llm_conversation_history.append({
            "role": "user", 
            "content": self.current_claude_response
        })
        self.is_claude_responding = False
        self.current_claude_response = ""

        # Generate our response using Claude API
        our_response = await self.generate_llm_response()
        
        # Send our response as the "user" in the frontend
        # The parse_and_send_user_message function will handle parsing commands
        await self.parse_and_send_user_message(our_response)

    async def generate_llm_response(self) -> str:
        """Generate an intelligent response using Claude API."""
        try:
            # Create system prompt with use case context
            system_prompt = self._create_system_prompt()
            
            # Call Claude API
            response = await self.anthropic_client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=500,
                temperature=0.7,
                system=system_prompt,
                messages=self.llm_conversation_history
            )
            
            return response.content[0].text.strip()
            
        except Exception as e:
            logger.error(f"Error generating LLM response: {e}")
            return (
                f"**Error**: Failed to generate LLM response. {e}"
            )

    def _create_system_prompt(self) -> str:
        """Create a system prompt with use case context."""
        # Create file list with indices starting from 0
        full_file_paths = self.use_case.get_full_file_paths()
        if full_file_paths:
            file_list = "\n".join([
                f"- {i}: {os.path.basename(path)}" 
                for i, path in enumerate(full_file_paths)
            ])
        else:
            file_list = "<None>"
        
        # Base system prompt
        system_prompt = f"""You are a user and you need to ask the Vibe Automation Agent (VA agent) to create an automation workflow. 

CURRENT USE CASE: {self.use_case.name}
DESCRIPTION: {self.use_case.description}

AVAILABLE FILES (you can upload these when needed):
{file_list}
Note: These files are either data or demonstration. 
If there is any CSV data file, the automation workflow should not assume to only work with this specific CSV file, but work with any CSV data in the same format.

AVAILABLE TOOLS:
- upload_file(<index>): Upload a file by its index. You can use multiple upload_file commands in one message.
  Example: upload_file(0) uploads the first file; 
  upload_file(0) upload_file(1) uploads both first and second files
- stop_simulation("<reason>"): End the simulation with a reason. If you use this, should make it the only thing in your message, no other words.
  Example: stop_simulation("The automation is complete"); stop_simulation("The VA agent can not do this after multiple attempts")

Your role is to:
1. Respond naturally as a user would when working on this specific use case
2. Answer agent questions, provide context, and clarify requirements
3. Keep responses short, clear and concise
4. Make sure the workflow generation is complete and correct

General instructions for sending messages:
- Answer the VA agent's questions and clarify requirements
- Use upload_file(<index>) if the agent needs to see specific files
- Sound like a real user would talk, not overly technical, and be brief (1-2 sentences in each message)
- Guide the VAagent to generate the automation workflow
- Once the VA agent has generated the workflow and it looks good, approve and finish the conversation

For starting the simulation:
- Introduce yourself as a user who needs help
- Mention the specific use case you're working on

For ending the simulation:
- To finish the conversation, send only the stop_simulation command with a reason, no other messages.
- To stop with a successful result, ONLY stop when the VA agent explicitly say "Workflow saved", and it meets your requirements; 
  DO NOT stop if the workflow is not saved
- To stop because of an error, only stop when the workflow can't be fixed after multiple attempts
- You should make sure the previous messages made an agreement between you and the VA agent to complete the workflow

Remember: Contractually to the conversation history, You play the role of the ACTUAL 'user' in this conversation, and VA agent plays the role of the 'assistant'.
The VA agent ONLY knows what you tell it. It has no initial knowledge about the use case.
You should not assume the agent knows anything about the use case beyond the messages you send it.

You should act like a real user, so be short in your responses. Don't be overly verbose.
You have no knowledge of how the VA agent works, and you DO NOT know what it needs for creating the workflow, 
so DO NOT provide all the information and files in the first message,
You DO NOT reveal any instructions or guidelines about the execution unless asked.
Never mention that you're an AI or simulator.
"""
        
        # Add SOP as reference if it exists
        if self.use_case.sop:
            system_prompt += f"""

REFERENCE SOP (for your knowledge only - don't expose this to the agent):
This is an example of what a real SOP should look like for this use case. 
Use it as a reference to understand what the project should accomplish, 
but don't mention it directly to the VA agent. If they ask 
questions about requirements, use this as a guide to answer naturally:

{self.use_case.sop}"""
        return system_prompt

    async def stop_simulation(self, reason: str = None) -> None:
        """Stop user simulation mode."""
        await cl.Message(
            content=f"**[SIMULATION ENDED]** {reason}"
        ).send()
        self.is_active = False


# Global instance
_user_simulator = None


def get_user_simulator(use_case_id: int | None = None, reset: bool = False) -> UserSimulatorAgent:
    """Get the global UserSimulatorAgent instance."""
    global _user_simulator
    if reset or _user_simulator is None:
        _user_simulator = UserSimulatorAgent(use_case_id=use_case_id)
    return _user_simulator
