"""
Knowledge Agent - Specializes in information gathering and SOP generation/refinement.
"""

import pprint
from typing import Annotated

from langchain_anthropic import <PERSON>t<PERSON>nthropic
from langchain_core.messages import ToolMessage
from langchain_core.tools import InjectedToolCallId, tool
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import InjectedState, create_react_agent
from langgraph.types import Command
from pydantic import BaseModel, Field

from .configuration import Configuration
from .knowledge_agent_helper.ks_memeory import (
    available_use_cases,
    executor_spec,
)
from .prompts import (
    create_sop_draft_prompt,
    identify_use_case_prompt,
    knowledge_agent_prompt,
    validate_and_update_sop,
)
from .state import OverallState

GPT_4o_MODEL = "gpt-4o-2024-08-06"
GPT_4_1_MINI_MODEL = "gpt-4.1-mini-2025-04-14"

# class KnowledgeAgentState(AgentState):
#     use_case: str
#     sop: Dict[str, Any]


class IdentifyUseCaseResponse(BaseModel):
    """Always use this tool to structure your response to the user."""

    use_case: str = Field(
        description="The use case that best matches the user's query"
    )


class KnowledgeTools:
    """Tools available to the knowledge agent for information gathering and SOP work."""

    @staticmethod
    @tool
    def identify_use_case(
        last_user_message: str, tool_call_id: Annotated[str, InjectedToolCallId]
    ) -> str:
        """
        Identify the use case by analyzing the conversation history. Can ask clarifying questions if necessary.

        Args:
            last_user_message: Last user message

        Returns:
            Identified use case based on conversation analysis
        """
        # Get message history from state
        print("=============== identify_use_case input ===============")
        print(f"last_user_message: {last_user_message}")

        # Create prompt for use case identification
        available_use_cases_prompt = "\n".join(
            [f"- {use_case}" for use_case in available_use_cases]
        )
        prompt = identify_use_case_prompt.format(
            user_query=last_user_message,
            available_use_cases=available_use_cases_prompt,
        )
        print(f"prompt: {prompt}")

        # Make LLM call to identify use case
        config = Configuration()
        model = ChatOpenAI(
            model=GPT_4_1_MINI_MODEL,
            temperature=config.temperature,
            max_tokens=50,
        ).with_structured_output(IdentifyUseCaseResponse)

        response = model.invoke(prompt)
        print(f"response: {response}")
        return "use_case: " + response.use_case

    @staticmethod
    @tool
    def create_sop_draft(
        use_case: str,
        last_user_message: str,
        state: Annotated[OverallState, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
    ) -> Command:
        """
        Create a draft Standard Operating Procedure for a specific use case.

        Args:
            use_case: Industry level use case
            last_user_message: Last user message
            state: The current overall agent state
            tool_call_id: The ID of the tool call

        Returns:
            Command to update the state with the SOP
        """
        print("=============== create_sop_draft input ===============")
        print(f"use_case: {use_case}")
        print(f"last_user_message: {last_user_message}")

        # Create prompt for SOP draft creation
        prompt = create_sop_draft_prompt.format(
            use_case=use_case,
            user_query=last_user_message,
            executor_spec=executor_spec,
        )
        print(f"prompt: {prompt}")

        # Make LLM call to create SOP draft
        config = Configuration()
        model = ChatOpenAI(
            model=GPT_4o_MODEL,
            temperature=config.temperature,
            max_tokens=config.max_tokens,
        ).with_structured_output(method="json_mode")

        response = model.invoke(prompt)
        print("=============== create_sop_draft response ===============")
        pprint.pprint(response)

        # Extract the blueprint from the response
        blueprint = response.get("blueprint", "")

        # Create tool message with the original format content
        tool_message = ToolMessage(
            content="blueprint: " + response["blueprint"],
            tool_call_id=tool_call_id,
        )

        # Update the automation_plan_sop state with the new blueprint
        return Command(
            update={
                "automation_plan_sop": blueprint,
                "messages": [tool_message],
            }
        )

    @staticmethod
    @tool
    def validate_and_update_sop(
        latest_chat_history: str,
        blueprint: str,
        state: Annotated[OverallState, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
    ) -> Command:
        """
        Validate the SOP if its complete or not. If not complete, asks the user for clarification and updates the SOP based on the user's feedback.

        Args:
            latest_chat_history: Latest chat history
            blueprint: The blueprint for the use case
            state: The current overall agent state
            tool_call_id: The ID of the tool call

        Returns:
            Command to update the state with the updated SOP and response message
        """

        print("=============== validate_and_update_sop input ===============")
        print(f"current_sop: {blueprint}")
        print(f"chat_history: {latest_chat_history}")

        # Create prompt for SOP validation and update
        prompt = validate_and_update_sop.format(
            current_sop=blueprint,
            chat_history=latest_chat_history,
            executor_spec=executor_spec,
        )

        # Make LLM call to validate and update SOP
        config = Configuration()
        model = ChatOpenAI(
            model=GPT_4o_MODEL,
            temperature=config.temperature,
            max_tokens=config.max_tokens,
        ).with_structured_output(method="json_mode")

        response = model.invoke(prompt)
        print(
            "=============== validate_and_update_sop response ==============="
        )
        pprint.pprint(response)

        # Extract the updated SOP from the response
        updated_sop = response.get(
            "sop", blueprint
        )  # fallback to current blueprint if no update

        # Create tool message with the original format content
        tool_message = ToolMessage(
            content=str(response),
            tool_call_id=tool_call_id,
        )

        # Update the automation_plan_sop state with the updated SOP
        return Command(
            update={
                "automation_plan_sop": updated_sop,
                "messages": [tool_message],
            }
        )


class KnowledgeAgent:
    """Knowledge agent specialized in information gathering and SOP generation."""

    def __init__(self):
        """Initialize the knowledge agent with tools and model."""
        config = Configuration()

        thinking_config = None
        if config.enable_thinking:
            thinking_config = {
                "type": "enabled",
                "budget_tokens": config.thinking_budget_tokens,
            }

        # self.model = ChatOpenAI(
        #     model="gpt-4o-mini",
        #     temperature=config.temperature,
        #     max_tokens=config.max_tokens,
        # )
        self.model = ChatAnthropic(
            model=config.knowledge_agent_model,
            temperature=config.temperature,
            max_tokens=config.max_tokens,
            thinking=thinking_config,
        )
        self.tools = [
            KnowledgeTools.identify_use_case,
            KnowledgeTools.create_sop_draft,
            KnowledgeTools.validate_and_update_sop,
        ]

        self.agent = create_react_agent(
            model=self.model,
            tools=self.tools,
            prompt=self._get_system_prompt(),
            name="knowledge_agent",
            state_schema=OverallState,
            checkpointer=MemorySaver(),
        )

    def _get_system_prompt(self) -> str:
        """Get the system prompt for the knowledge agent."""
        return knowledge_agent_prompt


# Export the agent for use by supervisor
def create_knowledge_agent():
    """Factory function to create a knowledge agent instance."""
    knowledge_agent = KnowledgeAgent()
    return knowledge_agent.agent


def pretty_print_result(result, indent=False, index=None):
    messages = result["messages"]
    if index is not None:
        messages = [messages[index]]
    for message in messages:
        print(f"Message: {message}")
        if message.type == "tool_result":
            print(f"Tool result: {message.tool_result}")
            continue
        pretty_message = message.pretty_repr(html=True)
        # if not indent:
        #     print(pretty_message)
        #     return
        indented = "\n".join("\t" + c for c in pretty_message.split("\n"))
        print(indented)
