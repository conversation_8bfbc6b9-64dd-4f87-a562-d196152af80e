"""
Use case dataset for user simulation scenarios.
Contains predefined use cases that the UserSimulatorAgent can simulate.
"""

import json
import os

from pydantic import BaseModel

user_simulator_dataset_root_dir = "packages/evals/alpha_dataset"


class UseCase(BaseModel):
    """Represents a specific use case for user simulation."""
    id: int
    name: str
    description: str
    file_paths: list[str] = []
    sop: str | None = None

    def get_full_file_paths(self) -> list[str]:
        """Get full file paths by prepending the dataset directory."""
        return [
            os.path.join(user_simulator_dataset_root_dir, file_path) 
            for file_path in self.file_paths
        ]


class UseCaseDataset(BaseModel):
    """Dataset containing all use cases."""
    use_cases: list[UseCase]


def load_use_cases() -> list[UseCase]:
    """Load use cases from the JSON file."""
    json_path = os.path.join(user_simulator_dataset_root_dir, "user_simulator_files/use_cases.json")
    
    try:
        with open(json_path, encoding='utf-8') as f:
            data = json.load(f)
        
        dataset = UseCaseDataset(**data)
        return dataset.use_cases
    except FileNotFoundError as err:
        raise FileNotFoundError(
            f"Use cases file not found: {json_path}"
        ) from err
    except json.JSONDecodeError as err:
        raise ValueError(f"Invalid JSON in use cases file: {err}") from err
    except Exception as err:
        raise ValueError(f"Error loading use cases: {err}") from err


def get_use_case_by_name(name: str) -> UseCase | None:
    """Get a use case by its name."""
    use_cases = load_use_cases()
    for use_case in use_cases:
        if use_case.name.lower() == name.lower():
            return use_case
    return None


def get_test_use_case() -> UseCase:
    """Get the test use case from the dataset."""
    use_cases = load_use_cases()
    return use_cases[0]


def get_use_case_by_id(id: int) -> UseCase:
    """Get a use case by its id."""
    use_cases = load_use_cases()
    for use_case in use_cases:
        if use_case.id == id:
            return use_case
    raise ValueError(f"Use case with ID {id} not found")
