"""
Coding Agent - Specializes in generating code based on SOPs and requirements.
Updated to use Claude Code SDK instead of <PERSON><PERSON>.
"""

import asyncio
import json
import logging
import os
import time
from typing import Annotated, Any
import uuid

import chainlit as cl
from langchain_anthropic import ChatAnthropic
from langchain_core.messages import ToolMessage
from langchain_core.tools import InjectedToolCallId, tool
from langgraph.checkpoint.memory import MemorySaver
from langgraph.config import get_stream_writer
from langgraph.prebuilt import InjectedState, create_react_agent
from langgraph.types import Command

from agent.configuration import Configuration
from agent.langsmith_config import trace_coding_agent
from agent.prompts import coding_agent_prompt
from agent.state import OverallState
from agent.tools import ExplorationTools
from agent.utils.claude_util import run_claude_command_with_runtime
from agent.va_sdk_documentation import load_documentation
from common.sandbox.base import ExecutionResult
from web_server.session.runtime_manager import get_runtime_session_manager

# Constants for execution
EXECUTION_TIMEOUT = 60  # 1 minute timeout for code execution

logger = logging.getLogger(__name__)


class RuntimeCodingTools:
    """Tools available to the coding agent for code generation."""

    @staticmethod
    async def _get_runtime():
        """Get the runtime session for the current user session."""
        try:
            runtime = await get_runtime_session_manager().get_runtime(
                session_id=cl.context.session.id
            )
            return runtime
        except Exception as e:
            logger.error(f"Failed to get runtime: {e}")
            raise

    @staticmethod
    @tool
    def generate_code_from_sop(
        state: Annotated[OverallState, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
    ) -> str:
        """
        Generate code directly from an SOP.

        Args:
            state: The current overall agent state
            tool_call_id: The ID of the tool call

        Returns:
            Generated code implementing the SOP
        """
        status_message = "💻 **Generating automation code...** Creating Python scripts for your workflow."

        writer = get_stream_writer()
        if writer:
            try:
                writer({"status_update": status_message})
            except Exception as e:
                logger.error(f"Error emitting status update: {e}")

        try:
            automation_plan_sop = state.get("automation_plan_sop", "")
            form_url = state.get("form_url", "")
            if not automation_plan_sop:
                logger.error("No automation plan SOP found in state")
                raise ValueError("No automation plan SOP found in state")
            if not form_url:
                logger.warning("No form URL found in state")

            html_exploration_result = None

            # Extract form details if URL provided
            if form_url:
                html_exploration_result_full = (
                    ExplorationTools.get_website_dom_details(state, form_url)
                )
                if html_exploration_result_full["form_details"]:
                    html_exploration_result = html_exploration_result_full[
                        "form_details"
                    ][0]
                else:
                    html_exploration_result = json.dumps(
                        html_exploration_result_full["form_like_details"],
                        indent=2,
                    )

            # Log the inputs for debugging
            logger.info(
                f"Inputs to generate_code_from_sop: sop_content='{automation_plan_sop}', form_url='{form_url}', html_exploration_result='{html_exploration_result[:50] if html_exploration_result else None}...'"
            )

            # Get the runtime session
            runtime = asyncio.run(RuntimeCodingTools._get_runtime())

            # Create unique directory for generated code
            generated_code_dir = (
                f"generated_code_{time.strftime('%m%d_%H%M%S')}"
            )

            async def create_generated_code_dir():
                try:
                    await runtime.run_command(
                        "mkdir", ["-p", generated_code_dir]
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to create directory {generated_code_dir}: {e}"
                    )
                    raise

            asyncio.run(create_generated_code_dir())

            logger.info(f"Generated code directory: {generated_code_dir}")

            # Handle file attachments (for demo use)
            attachments = cl.user_session.get("attachments", [])
            csv_file_path = attachments[0].path if attachments else ""

            # Build context section from HTML exploration
            context_section = ""
            if html_exploration_result:
                context_length = len(html_exploration_result)
                logger.info(
                    f"Context details - Length: {context_length} characters"
                )

                context_section = f"""

**ADDITIONAL CONTEXT:**
*HTML Content of the form:*
{html_exploration_result}

**IMPORTANT**: Use the context above to inform your code generation. If HTML content is provided, analyze the structure to:
- Identify form fields, buttons, and interactive elements
- Choose appropriate selectors (prefer get_by_prompt with descriptive fallbacks)
- Understand the page flow and navigation requirements
- Generate more accurate and robust automation code
"""

            # Build instruction for Claude Code generation
            instruction = f"""Create a complete Python workflow using the VA SDK that implements the SOP:

{automation_plan_sop}

**Project Requirements:**
1. Create a main.py file as the primary executable script. It should contain default parameters for testing by python main.py.
2. **MANDATORY: Create a requirements.txt file listing ALL external packages used in the code (pydantic, playwright, pandas, etc.).**
3. Include clear status reporting throughout execution with "STARTING:", "PROGRESS:", "SUCCESS:", "COMPLETED:", "FAILED:" messages.
4. Use the CSV file as an example input to the workflow: {csv_file_path} if provided, otherwise use the sample data in the SOP.
5. Always end with "TASK_COMPLETED_SUCCESSFULLY" or "TASK_FAILED: [reason]".
6. Proper error handling with try-catch blocks and informative error messages.
7. Debugging output for tracking progress.
8. Assume API keys (OpenAI, Anthropic, etc.) are available in environment variables.

IMPORTANT: The main executable code should be in main.py - this is the file that will be run.

{context_section}

**VA SDK Documentation & Requirements:**
{load_documentation(runtime)}

**Additional Requirements:**
- Follow the exact patterns shown in the documentation above
- The first parameter MUST be named "input"
- Use step() context managers to organize workflow
- To allow the user to see browser action, use headless=False, slow_mo=300
- When reading a file in main.py's __main__ block (e.g. sample data), never use the file's absolute path.
Instead, infer it's relative path to main.py's directory and use: path = os.path.join(os.path.dirname(__file__), "relative_path_to_file")"""

            # Execute Claude Code generation
            success, claude_output_json = run_claude_command_with_runtime(
                runtime,
                instruction,
                generated_code_dir,
                additional_files=[csv_file_path] if csv_file_path else None,
            )
            claude_output_dict = json.loads(claude_output_json)
            claude_output = claude_output_dict["result"]
            # Parse the claude session_id from the output
            claude_session_id = claude_output_dict.get("session_id", "")
            if not claude_session_id:
                logger.error(
                    f"No session_id found in Claude Code CLI output for {generated_code_dir}"
                )
                raise ValueError(
                    f"No session_id found in Claude Code CLI output for {generated_code_dir}"
                )

            # Handle generation results
            if success:
                logger.info(
                    f"Claude Code completed successfully: {claude_output}"
                )
                response_content = f"I've successfully used Claude Code to create the code in the isolated {generated_code_dir} directory.\n\n"
                response_content += f"\n\nClaude Code output:\n{claude_output}"
            else:
                logger.error(f"Claude Code failed: {claude_output}")
                response_content = f"Claude Code failed to generate the code.\n\nError output:\n{claude_output}"

            return Command(
                update={
                    "execution_service_url": f"{generated_code_dir}",
                    "messages": [
                        ToolMessage(
                            content=response_content, tool_call_id=tool_call_id
                        )
                    ],
                    "claude_sessions": {generated_code_dir: claude_session_id},
                }
            )

        except Exception as e:
            error_msg = f"Error in generate_code_from_sop: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return Command(
                update={
                    "messages": [
                        ToolMessage(
                            content=f"❌ {error_msg}", tool_call_id=tool_call_id
                        )
                    ],
                }
            )

    @staticmethod
    @tool
    def execute_generated_code(
        state: Annotated[OverallState, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
    ):
        """Execute the generated code in the runtime environment."""
        try:
            generated_code_dir = state.get("execution_service_url")
            if not generated_code_dir:
                raise ValueError("No execution service URL found in state")

            logger.info(f"Executing code in {generated_code_dir}")

            status_message = (
                "💻 **Executing generated code...** Testing your workflow."
            )

            writer = get_stream_writer()
            if writer:
                try:
                    writer({"status_update": status_message})
                except Exception as e:
                    logger.error(f"Error emitting status update: {e}")

            runtime = asyncio.run(RuntimeCodingTools._get_runtime())

            # Execute the generated code
            async def run_code():
                try:
                    execution_id = f"agent_test_${cl.context.session.id}_{uuid.uuid4().hex[:4]}"
                    result: ExecutionResult = await runtime.run_code(
                        generated_code_dir,
                        envs={
                            "VA_EXECUTION_ID": execution_id,
                        },
                    )
                    return result
                except Exception as e:
                    logger.error(f"Code execution failed: {e}")
                    raise

            result: ExecutionResult = asyncio.run(run_code())

            # Format execution output
            execution_output = ""
            if result.stdout:
                execution_output += f"**STDOUT:**\n{result.stdout}\n\n"
            if result.stderr:
                execution_output += f"**STDERR:**\n{result.stderr}\n\n"
            execution_output += f"**Exit Code:** {result.returncode}\n"
            logger.info(f"execution_output: {execution_output}")
            has_errors = result.returncode != 0

            if has_errors:
                logger.warning("Execution completed with errors")
                return_message = f"❌ **Execution completed with errors**\n\n{execution_output}"
            else:
                logger.info("Execution completed successfully")
                return_message = f"✅ **Execution completed successfully**\n\n{execution_output}"

            return Command(
                update={
                    "execution_logs": execution_output,
                    "messages": [
                        ToolMessage(
                            content=return_message, tool_call_id=tool_call_id
                        )
                    ],
                }
            )

        except Exception as e:
            error_msg = f"Error executing code: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return Command(
                update={
                    "messages": [
                        ToolMessage(
                            content=f"❌ {error_msg}", tool_call_id=tool_call_id
                        )
                    ],
                }
            )

    @staticmethod
    @tool
    def analyze_logs_and_refine(
        state: Annotated[OverallState, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
        auto_refinement_count: int = 0,
    ):
        """Analyze the execution logs and refine the code based on errors."""

        status_message = "💻 **Analyzing execution logs and refining code...** Fixing errors in your workflow."

        writer = get_stream_writer()
        if writer:
            try:
                writer({"status_update": status_message})
            except Exception as e:
                logger.error(f"Error emitting status update: {e}")

        try:
            runtime = asyncio.run(RuntimeCodingTools._get_runtime())

            generated_code_dir = state.get("execution_service_url")
            if not generated_code_dir:
                raise ValueError("No execution service URL found in state")

            execution_logs = state.get("execution_logs", "")
            if not execution_logs:
                raise ValueError("No execution logs found in state")

            filtered_logs = filter_execution_logs(execution_logs)
            logger.info(
                f"🔍 Debug: Filtered execution_logs for analysis: '{filtered_logs[:100]}...'"
            )
            if not filtered_logs or len(filtered_logs.strip()) < 10:
                return "❌ Cannot analyze logs - no execution logs available. The code must be executed first using execute_generated_code before calling analyze_logs_and_refine."

            instruction = f"""Based on these execution logs, analyze and fix the issues:

**Execution Logs:**
```
{filtered_logs}
```

**Fix the following:**
1. Identify root cause of errors from the logs above
2. Make specific code changes to resolve these problems
3. CRITICAL: If import errors exist, you MUST add the missing packages to the `requirements.txt` file.
4. Ensure the task completes successfully with proper status reporting

Auto-refinement attempt #{auto_refinement_count + 1}"""

            # Handle file attachments (for demo use)
            attachments = cl.user_session.get("attachments", [])
            csv_file_path = attachments[0].path if attachments else ""

            claude_session_id = state.get("claude_sessions", {}).get(
                generated_code_dir, ""
            )
            if not claude_session_id:
                logger.error(
                    f"No Claude session ID found in state during refinement for {generated_code_dir}"
                )
                raise ValueError(
                    f"No Claude session ID found in state during refinement for {generated_code_dir}"
                )

            success, claude_output_json = run_claude_command_with_runtime(
                runtime,
                instruction,
                generated_code_dir,
                additional_files=[csv_file_path] if csv_file_path else None,
                session_id=claude_session_id,
            )
            claude_output_dict = json.loads(claude_output_json)
            claude_output = claude_output_dict["result"]

            # Handle generation results
            if success:
                logger.info(
                    f"Claude Code completed successfully: {claude_output}"
                )
                response_content = f"I've successfully used Claude Code to create the code in the isolated {generated_code_dir} directory.\n\n"
                response_content += f"\n\nClaude Code output:\n{claude_output}"
            else:
                logger.error(f"Claude Code failed: {claude_output}")
                response_content = f"Claude Code failed to generate the code.\n\nError output:\n{claude_output}"

            return Command(
                update={
                    "messages": [
                        ToolMessage(
                            content=response_content, tool_call_id=tool_call_id
                        )
                    ],
                }
            )

        except Exception as e:
            error_msg = f"Error in analyze_logs_and_refine: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return Command(
                update={
                    "messages": [
                        ToolMessage(
                            content=f"❌ {error_msg}", tool_call_id=tool_call_id
                        )
                    ],
                }
            )

    @staticmethod
    @tool
    def refine_code_from_feedback(
        state: Annotated[OverallState, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
        human_feedback: str,
    ):
        """Refine the generated code based on human feedback and suggestions."""

        status_message = "💻 **Refining code based on feedback...** Implementing your suggestions."

        writer = get_stream_writer()
        if writer:
            try:
                writer({"status_update": status_message})
            except Exception as e:
                logger.error(f"Error emitting status update: {e}")

        try:
            runtime = asyncio.run(RuntimeCodingTools._get_runtime())

            generated_code_dir = state.get("execution_service_url")
            if not generated_code_dir:
                raise ValueError("No execution service URL found in state")

            if not human_feedback:
                raise ValueError("Human feedback must be provided")

            logger.info(
                f"🔍 Debug: Refining code based on feedback: '{human_feedback}'"
            )

            instruction = f"""Based on the following human feedback, refine and improve the existing code:

**Human Feedback:**
```
{human_feedback}
```

**Refinement Instructions:**
1. Carefully analyze the feedback and understand what changes are requested
2. Make specific code modifications to address the feedback
3. Maintain the existing functionality while implementing the requested improvements
4. If the feedback mentions missing features, add them appropriately
5. If the feedback mentions bugs or issues, fix them
6. If the feedback suggests performance improvements, implement them
7. Ensure the code remains robust and follows best practices
8. Update requirements.txt if new dependencies are needed
9. Maintain proper error handling and status reporting

**Important Notes:**
- Preserve the existing code structure and workflow
- Only make changes that directly address the feedback
- Test your changes to ensure they work correctly
- If the feedback is unclear, make reasonable assumptions based on context"""

            # Handle file attachments (for demo use)
            attachments = cl.user_session.get("attachments", [])
            csv_file_path = attachments[0].path if attachments else ""

            claude_session_id = state.get("claude_sessions", {}).get(
                generated_code_dir, ""
            )
            if not claude_session_id:
                logger.error(
                    f"No Claude session ID found in state during feedback refinement for {generated_code_dir}"
                )
                raise ValueError(
                    f"No Claude session ID found in state during feedback refinement for {generated_code_dir}"
                )

            success, claude_output_json = run_claude_command_with_runtime(
                runtime,
                instruction,
                generated_code_dir,
                additional_files=[csv_file_path] if csv_file_path else None,
                session_id=claude_session_id,
            )
            claude_output_dict = json.loads(claude_output_json)
            claude_output = claude_output_dict["result"]

            # Handle generation results
            if success:
                logger.info(
                    f"Claude Code completed successfully: {claude_output}"
                )
                response_content = f"I've successfully refined the code based on your feedback in the isolated {generated_code_dir} directory.\n\n"
                response_content += f"\n\nClaude Code output:\n{claude_output}"
            else:
                logger.error(f"Claude Code failed: {claude_output}")
                response_content = f"Claude Code failed to refine the code.\n\nError output:\n{claude_output}"

            return Command(
                update={
                    "messages": [
                        ToolMessage(
                            content=response_content, tool_call_id=tool_call_id
                        )
                    ],
                }
            )

        except Exception as e:
            error_msg = f"Error in refine_code_from_feedback: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return Command(
                update={
                    "messages": [
                        ToolMessage(
                            content=f"❌ {error_msg}", tool_call_id=tool_call_id
                        )
                    ],
                }
            )

    @staticmethod
    @tool
    def generate_manifest(
        state: Annotated[OverallState, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
        workflow_name: str,
        workflow_description: str,
    ) -> str:
        """Generate a manifest for the generated code.
        Args:
            state: The current agent state
            tool_call_id: The ID of the tool call
            workflow_name: The name of the workflow
            workflow_description: The description of the workflow
        """
        try:
            # Get the generated_code_dir from the state
            generated_code_dir = state["execution_service_url"]
            if not generated_code_dir:
                return "❌ No execution service URL found in the state"

            logger.info(
                f"Generating manifest for {workflow_name} in {generated_code_dir}"
            )

            runtime = asyncio.run(RuntimeCodingTools._get_runtime())

            manifest_script_path = os.path.join(
                os.path.dirname(__file__), "utils/runtime_generate_manifest.py"
            )

            # Move the manifest.py file to the generated_code_dir
            with open(manifest_script_path) as f:
                manifest_script_content = f.read()

                async def write_file():
                    await runtime.write_file(
                        os.path.join(generated_code_dir, "manifest.py"),
                        manifest_script_content,
                    )

                logger.info(
                    f"Writing manifest.py to {generated_code_dir}/manifest.py"
                )
                asyncio.run(write_file())

            async def run_code():
                result: ExecutionResult = await runtime.run_code(
                    work_dir=generated_code_dir,
                    entry_point="manifest.py",
                    args=[workflow_name, workflow_description],
                )
                return result

            result: ExecutionResult = asyncio.run(run_code())

            if result.returncode != 0:
                logger.error(
                    f"Manifest generation failed with return code: {result.returncode}"
                )
                return f"❌ Manifest generation failed with return code: {result.returncode}"
            else:
                logger.info("Manifest generation completed successfully")

        except Exception as e:
            error_msg = f"Error generating manifest: {e}"
            logger.error(error_msg, exc_info=True)
            return f"❌ {error_msg}"

        return f"Manifest for {workflow_name} has been generated and saved to {generated_code_dir}/manifest.json."


class RuntimeCodingAgent:
    """Coding agent specialized in code generation based on SOPs using runtime."""

    @trace_coding_agent
    def __init__(self):
        """Initialize the runtime coding agent with tools and model."""
        config = Configuration()

        thinking_config = None
        if config.enable_thinking:
            thinking_config = {
                "type": "enabled",
                "budget_tokens": config.thinking_budget_tokens,
            }
        self.model = ChatAnthropic(
            model=config.coding_agent_model,
            temperature=config.temperature,
            max_tokens=config.max_tokens,
            thinking=thinking_config,
        )

        self.tools = [
            RuntimeCodingTools.generate_code_from_sop,
            RuntimeCodingTools.execute_generated_code,
            RuntimeCodingTools.analyze_logs_and_refine,
            RuntimeCodingTools.refine_code_from_feedback,
            RuntimeCodingTools.generate_manifest,
        ]

        self.agent = create_react_agent(
            model=self.model,
            tools=self.tools,
            prompt=self._get_system_prompt(),
            name="runtime_coding_agent",
            state_schema=OverallState,
            checkpointer=MemorySaver(),
        )

    def _get_system_prompt(self) -> str:
        """Get the system prompt for the coding agent."""
        return coding_agent_prompt

    def process_request(self, request: dict[str, Any]) -> dict[str, Any]:
        """Process a coding request and return structured results."""
        try:
            result = self.agent.invoke(request)
            return {
                "status": "success",
                "agent": "coding_agent",
                "result": result,
                "type": "code_generation",
            }
        except Exception as e:
            return {
                "status": "error",
                "agent": "coding_agent",
                "error": str(e),
                "type": "code_generation",
            }


# Export the agent for use by supervisor
@trace_coding_agent
def create_runtime_coding_agent():
    """Factory function to create a coding agent instance."""
    coding_agent = RuntimeCodingAgent()
    return coding_agent.agent


def filter_execution_logs(output: str) -> str:
    """Filter out execution logs that are not helpful to the user and coding agent."""
    # Remove lines containing va.store.in_memory without using regex
    lines = output.split("\n")
    filtered_lines = []
    for line in lines:
        if "va.store.in_memory" not in line:
            filtered_lines.append(line)
    output = "\n".join(filtered_lines)

    return output
