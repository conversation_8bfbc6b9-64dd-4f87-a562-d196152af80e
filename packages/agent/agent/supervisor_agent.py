from typing import Annotated

from langchain_core.messages import ToolMessage
from langchain_core.tools import InjectedToolCallId, tool
from langchain_google_genai import ChatGoogleGenerativeAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.config import get_stream_writer
from langgraph.graph import START, StateGraph
from langgraph.prebuilt import InjectedState, create_react_agent
from langgraph.types import Command

from agent.runtime_coding_agent import create_runtime_coding_agent

from .configuration import Configuration
from .knowledge_agent_light import create_knowledge_agent
from .langsmith_config import trace_supervisor_agent
from .prompts import supervisor_prompt
from .state import OverallState
from .tools import list_available_attachments, read_file


def create_handoff_tool_with_checkpointing(
    *, agent_subgraph, agent_name: str, description: str
):
    """Create a handoff tool that directly handles subgraph calls and checkpointing."""

    @tool(f"transfer_to_{agent_name}", description=description)
    def handoff_tool(
        task_description: Annotated[
            str,
            "Description of what the agent should do, or the user's request verbatim if passing to the knowledge agent.",
        ],
        state: Annotated[dict, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
    ):
        status_message = "🔄 **Processing your response...** Coordinating between specialized agents."
        writer = get_stream_writer()
        if writer:
            try:
                writer({"status_update": status_message})
            except Exception as e:
                print(f"Error emitting status update: {e}")

        # Extract state fields and call agent
        agent_input = {
            "messages": [{"role": "user", "content": task_description}],
            "automation_plan_sop": state.get("automation_plan_sop", ""),
            "claude_sessions": state.get("claude_sessions", {}),
            "chainlit_session_id": state.get("chainlit_session_id", ""),
            "is_sop_complete": state.get("is_sop_complete", False),
            "execution_logs": state.get("execution_logs", ""),
            "form_url": state.get("form_url", ""),
            "execution_service_url": state.get("execution_service_url", ""),
            "browser_connection_url": state.get("browser_connection_url", ""),
        }

        config = {
            "configurable": {
                "thread_id": f"{agent_name}_{state.get('chainlit_session_id', '')}"
            }
        }

        final_state = None

        try:
            for chunk in agent_subgraph.stream(
                agent_input,
                config=config,
                stream_mode=["custom", "values"],
                subgraphs=True,
            ):
                namespace, mode, data = chunk
                if mode == "custom" and "status_update" in data:
                    if writer:
                        writer(data)
                elif mode == "values":
                    final_state = data

            if final_state and final_state.get("messages"):
                agent_response = final_state["messages"][-1]

                tool_message = ToolMessage(
                    content=f"Successfully transferred back from {agent_name} agent. Agent response: {agent_response}",
                    tool_call_id=tool_call_id,
                )

                updates = {"messages": [tool_message]}

                for field in [
                    "automation_plan_sop",
                    "claude_sessions",
                    "is_sop_complete",
                    "execution_logs",
                    "form_url",
                    "execution_service_url",
                ]:
                    if field in final_state:
                        updates[field] = final_state[field]

                return Command(update=updates)
            else:
                return Command(
                    update={
                        "messages": [
                            ToolMessage(
                                content=f"Error: The {agent_name} agent did not produce a final result.",
                                tool_call_id=tool_call_id,
                            )
                        ]
                    }
                )

        except Exception as e:
            return Command(
                update={
                    "messages": [
                        ToolMessage(
                            content=f"An exception occurred in the {agent_name} agent: {e}",
                            tool_call_id=tool_call_id,
                        )
                    ]
                }
            )

    return handoff_tool


@trace_supervisor_agent
def create_supervisor_agent(configuration: Configuration):
    # Configure thinking mode if enabled

    model = ChatGoogleGenerativeAI(
        model=configuration.supervisor_agent_model,
        temperature=configuration.temperature,
        max_tokens=configuration.max_tokens,
    )

    knowledge_agent = create_knowledge_agent()
    coding_agent = create_runtime_coding_agent()

    # Create handoff tools that handle everything
    transfer_to_knowledge = create_handoff_tool_with_checkpointing(
        agent_subgraph=knowledge_agent,
        agent_name="knowledge",
        description="Assign requirements gathering, SOP creation, or process analysis tasks to the knowledge agent.",
    )

    transfer_to_coding = create_handoff_tool_with_checkpointing(
        agent_subgraph=coding_agent,
        agent_name="coding",
        description="Assign code generation, testing, or technical implementation tasks to the coding agent.",
    )

    supervisor_agent = create_react_agent(
        model=model,
        tools=[
            transfer_to_knowledge,
            transfer_to_coding,
            read_file,
            list_available_attachments,
        ],
        prompt=supervisor_prompt,
        state_schema=OverallState,
        name="supervisor",
    )

    graph = (
        StateGraph(OverallState)
        .add_node("supervisor", supervisor_agent)
        .add_edge(START, "supervisor")
    )

    checkpointer = MemorySaver()
    return graph.compile(checkpointer=checkpointer)
