"""
Video SOP Extractor - Extracts data entry SOPs from video recordings.
Supports two algorithms: frame extraction with OpenAI and direct video analysis with Gemini.
"""

import base64
from functools import wraps
import os
import time
from typing import Annotated

import cv2
from langchain_core.messages import HumanMessage, ToolMessage
from langchain_core.tools import InjectedToolCallId, tool
from langchain_google_vertexai import ChatVertexAI
from langchain_openai import ChatOpenAI
from langgraph.config import get_stream_writer
from langgraph.prebuilt import InjectedState
from langgraph.types import Command

from ..configuration import Configuration
from ..state import OverallState


def timer_decorator(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"⏱️ {func.__name__} took {execution_time:.2f} seconds to execute")
        return result

    return wrapper


class VideoSOPExtractor:
    """Extracts data entry SOPs from video recordings using multiple algorithms."""

    def __init__(self):
        """Initialize the video SOP extractor with configuration."""
        self.config = Configuration()

        # Initialize OpenAI model for frame analysis
        self.openai_model = ChatOpenAI(
            model="gpt-4o-2024-08-06",
            temperature=0.1,
            max_tokens=8192,
        )

        # Initialize Vertex AI model for video analysis
        self.vertex_model = ChatVertexAI(
            model="gemini-2.5-pro",
            temperature=0.1,
            max_tokens=16384,
        )

    @staticmethod
    @tool
    @timer_decorator
    def extract_sop_from_video_frames(
        video_path: str,
        state: Annotated[OverallState, InjectedState],
        mode: str = "significant",
        required_fps: float = 1.0,
    ) -> str:
        """
        Extract SOP from video using frame extraction and OpenAI analysis.

        Args:
            video_path: Path to the video file
            state: Current state of the agent
            mode: Frame extraction mode - "1fps" for fixed interval or "significant" for change detection
            required_fps: Frames per second to extract (for 1fps mode)

        Returns:
            Detailed SOP extracted from video frames
        """
        extractor = VideoSOPExtractor()
        return extractor._extract_sop_from_frames(
            video_path, mode, required_fps
        )

    @staticmethod
    @tool
    @timer_decorator
    def extract_sop_from_video_gemini(
        video_path: str,
        state: Annotated[OverallState, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
    ) -> str:
        """
        Extract SOP from video using Vertex AI's direct video understanding.

        Args:
            video_path: Full path to the video file
            state: Current state of the agent

        Returns:
            Detailed SOP extracted from video using Vertex AI
        """

        status_message = (
            "📋 **Analyzing video to create SOP. This might take a while ...**"
        )
        writer = get_stream_writer()
        if writer:
            try:
                writer({"status_update": status_message})
            except Exception as e:
                print(f"Error emitting status update: {e}")

        extractor = VideoSOPExtractor()
        response_sop = extractor._extract_sop_with_gemini(video_path)
        # Update the automation_plan_sop state with the new blueprint
        if "Error analyzing video with Vertex AI" in response_sop:
            status_message = response_sop
        else:
            status_message = "✅ Successfully analyzed video with Vertex AI. Processed the video to create initial SOP."
        state_update = {
            "automation_plan_sop": response_sop,
            "is_sop_complete": False,
            "messages": [
                ToolMessage(status_message, tool_call_id=tool_call_id)
            ],
        }
        # We return a Command object in the tool to update our state.
        return Command(update=state_update)

    @timer_decorator
    def _extract_sop_from_frames(
        self, video_path: str, mode: str = "1fps", required_fps: float = 1.0
    ) -> str:
        """Extract SOP using frame extraction and OpenAI analysis.

        Args:
            video_path: Path to the video file
            mode: Extraction mode - "1fps" for fixed interval or "significant" for change detection
            required_fps: Frames per second to extract (for 1fps mode)

        Returns:
            Detailed SOP extracted from video frames
        """
        try:
            print(f"🎬 Extracting frames from video: {video_path}")
            print(f"🔧 Frame extraction mode: {mode}")
            if mode == "1fps":
                print(f"📊 Required FPS: {required_fps}")

            # Extract frames using specified mode
            frames = self._extract_frames(video_path, mode, required_fps)

            if not frames:
                return "❌ No frames could be extracted from the video."

            print(f"📸 Extracted {len(frames)} frames for analysis")

            # Analyze frames with OpenAI
            sop = self._analyze_frames_with_openai(frames, video_path)

            return sop

        except Exception as e:
            error_msg = f"❌ Error extracting SOP from video frames: {str(e)}"
            print(error_msg)
            return error_msg

    @timer_decorator
    def _extract_sop_with_gemini(self, video_path: str) -> str:
        """Extract SOP using Gemini's direct video understanding."""
        try:
            print(f"🎬 Analyzing video with Vertex AI: {video_path}")
            # Open video file
            cap = cv2.VideoCapture(video_path)

            if not cap.isOpened():
                raise ValueError(f"Could not open video file: {video_path}")

            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps if fps > 0 else 0

            # Get video file size
            video_size = os.path.getsize(video_path)
            video_size_mb = video_size / (1024 * 1024)  # Convert to MB

            cap.release()

            print(
                f"📹 Video info: {fps:.2f} fps, {total_frames} frames, {duration:.2f}s duration, {video_size_mb:.2f} MB"
            )

            # Read video file
            with open(video_path, "rb") as video_file:
                video_data = video_file.read()

            # Create message with video
            message = HumanMessage(
                content=[
                    {"type": "text", "text": self._get_gemini_prompt()},
                    {
                        "type": "media",
                        "mime_type": self._get_mime_type(video_path),
                        "data": base64.b64encode(video_data).decode("utf-8"),
                    },
                ]
            )

            # Get response from Vertex AI
            response = self.vertex_model.invoke([message])
            print(
                f"Response from Vertex AI: {len(response.content)} characters"
            )
            print(f"Response metadata: {response.response_metadata}")
            print("✅ Successfully analyzed video with Gemini")
            return response.content

        except Exception as e:
            error_msg = f"❌ Error analyzing video with Vertex AI: {str(e)}"
            print(error_msg)
            return error_msg

    def _extract_frames(
        self, video_path: str, mode: str = "1fps", required_fps: float = 1.0
    ) -> list[str]:
        """Extract frames from video using specified mode.

        Args:
            video_path: Path to the video file
            mode: Extraction mode - "1fps" for fixed interval or "significant" for change detection
            required_fps: Frames per second to extract (for 1fps mode)
        """
        frames = []

        # Open video file
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            raise ValueError(f"Could not open video file: {video_path}")

        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps if fps > 0 else 0

        print(
            f"📹 Video info: {fps:.2f} fps, {total_frames} frames, {duration:.2f}s duration"
        )

        if mode == "1fps":
            frames = self._extract_frames_fps(
                cap, fps, total_frames, required_fps
            )
        elif mode == "significant":
            frames = self._extract_frames_significant(cap, fps, total_frames)
        else:
            raise ValueError(
                f"Invalid mode: {mode}. Use '1fps' or 'significant'"
            )

        cap.release()
        return frames

    def _extract_frames_fps(
        self, cap, fps: float, total_frames: int, required_fps: float = 1.0
    ) -> list[str]:
        """Extract frames at specified fps intervals."""
        frames = []
        frame_interval = (
            int(fps / required_fps) if fps > 0 else 30
        )  # Calculate interval based on required_fps

        frame_count = 0
        first_frame_read = False

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # Print frame size info after reading the first frame
            if not first_frame_read:
                print(
                    f"📐 Original Frame size: {frame.shape[1]}x{frame.shape[0]} pixels"
                )
                first_frame_read = True

            # Extract frame at specified fps intervals
            if frame_count % frame_interval == 0:
                # Resize frame to max width while maintaining aspect ratio
                frame = self._resize_frame(frame, max_width=1024)

                # Convert frame to base64
                _, buffer = cv2.imencode(
                    ".jpg", frame, [cv2.IMWRITE_JPEG_QUALITY, 85]
                )
                frame_base64 = base64.b64encode(buffer).decode("utf-8")
                frames.append(frame_base64)

                print(
                    f"📸 Extracted frame {frame_count} (time: {frame_count / fps:.2f}s, size: {frame.shape[1]}x{frame.shape[0]})"
                )

            frame_count += 1

        print(
            f"🎯 Total frames extracted ({required_fps}fps): {len(frames)} out of {frame_count} total frames"
        )
        return frames

    def _extract_frames_significant(
        self, cap, fps: float, total_frames: int
    ) -> list[str]:
        """Extract significant frames from video by detecting changes."""
        frames = []

        frame_count = 0
        first_frame_read = False
        last_significant_frame = None
        similarity_threshold = (
            0.85  # SSIM threshold for considering frames similar
        )

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # Print frame size info after reading the first frame
            if not first_frame_read:
                print(
                    f"📐 Original Frame size: {frame.shape[1]}x{frame.shape[0]} pixels"
                )
                first_frame_read = True

            # Resize frame for processing
            processed_frame = self._resize_frame(frame, max_width=1024)

            # Check if this frame is significantly different from the last significant frame
            is_significant = self._is_frame_significant(
                processed_frame, last_significant_frame, similarity_threshold
            )

            if is_significant:
                # Convert frame to base64
                _, buffer = cv2.imencode(
                    ".jpg", processed_frame, [cv2.IMWRITE_JPEG_QUALITY, 85]
                )
                frame_base64 = base64.b64encode(buffer).decode("utf-8")
                frames.append(frame_base64)

                # Update last significant frame
                last_significant_frame = processed_frame.copy()

                print(
                    f"📸 Extracted significant frame {frame_count} (time: {frame_count / fps:.2f}s, size: {processed_frame.shape[1]}x{processed_frame.shape[0]})"
                )

            frame_count += 1

        print(
            f"🎯 Total significant frames extracted: {len(frames)} out of {frame_count} total frames"
        )
        return frames

    def _is_frame_significant(
        self, current_frame, last_frame, threshold: float = 0.85
    ) -> bool:
        """Check if current frame is significantly different from the last frame."""
        # If this is the first frame, it's always significant
        if last_frame is None:
            return True

        # Convert frames to grayscale for comparison
        current_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)
        last_gray = cv2.cvtColor(last_frame, cv2.COLOR_BGR2GRAY)

        # Ensure both frames have the same size
        if current_gray.shape != last_gray.shape:
            # Resize current frame to match last frame
            current_gray = cv2.resize(
                current_gray, (last_gray.shape[1], last_gray.shape[0])
            )

        # Calculate structural similarity index (SSIM)
        similarity = self._calculate_ssim(current_gray, last_gray)

        # Frame is significant if similarity is below threshold
        return similarity < threshold

    def _calculate_ssim(self, img1, img2) -> float:
        """Calculate Structural Similarity Index between two images."""
        try:
            from skimage.metrics import structural_similarity as ssim

            return ssim(img1, img2, data_range=255)
        except ImportError:
            # Fallback to mean squared error if skimage is not available
            return self._calculate_mse(img1, img2)

    def _calculate_mse(self, img1, img2) -> float:
        """Calculate Mean Squared Error between two images as fallback."""
        import numpy as np

        mse = np.mean((img1.astype(float) - img2.astype(float)) ** 2)
        # Convert MSE to similarity score (0 = identical, 1 = completely different)
        # Normalize to 0-1 range where 0 is most similar
        max_mse = 255**2  # Maximum possible MSE for 8-bit images
        similarity = 1 - (mse / max_mse)
        return similarity

    def _resize_frame(self, frame, max_width: int = 512):
        """Resize frame to max width while maintaining aspect ratio."""
        height, width = frame.shape[:2]

        # If width is already smaller than max_width, return original frame
        if width <= max_width:
            return frame

        # Calculate new dimensions maintaining aspect ratio
        aspect_ratio = width / height
        new_width = max_width
        new_height = int(max_width / aspect_ratio)

        # Resize the frame
        resized_frame = cv2.resize(
            frame, (new_width, new_height), interpolation=cv2.INTER_AREA
        )

        return resized_frame

    def _analyze_frames_with_openai(
        self, frames: list[str], video_path: str
    ) -> str:
        """Analyze extracted frames with OpenAI to generate SOP."""
        try:
            # Create prompt for frame analysis
            prompt = self._get_openai_prompt(len(frames))

            # Prepare messages with frames
            messages = []

            # Add text prompt
            messages.append({"type": "text", "text": prompt})

            # Add frames as images
            for _, frame_base64 in enumerate(frames):
                messages.append(
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{frame_base64}",
                            "detail": "high",
                        },
                    }
                )

            # Create human message
            human_message = HumanMessage(content=messages)

            # Get response from OpenAI
            response = self.openai_model.invoke([human_message])
            # print(response)

            print("✅ Successfully analyzed frames with OpenAI")
            return response.content

        except Exception as e:
            raise Exception(
                f"Error analyzing frames with OpenAI: {str(e)}"
            ) from e

    def _get_openai_prompt(self, num_frames: int) -> str:
        """Get prompt for OpenAI frame analysis."""
        return OPENAI_FRAME_ANALYSIS_PROMPT.format(num_frames=num_frames)

    def _get_gemini_prompt(self) -> str:
        """Get prompt for Gemini video analysis."""
        return GEMINI_VIDEO_ANALYSIS_PROMPT

    def _get_mime_type(self, video_path: str) -> str:
        """Get MIME type based on video file extension."""
        ext = os.path.splitext(video_path)[1].lower()
        mime_types = {
            ".mp4": "video/mp4",
            ".avi": "video/x-msvideo",
            ".mov": "video/quicktime",
            ".mkv": "video/x-matroska",
            ".webm": "video/webm",
            ".flv": "video/x-flv",
            ".wmv": "video/x-ms-wmv",
        }
        return mime_types.get(ext, "video/mp4")


# Export the tools for use by agents
extract_sop_from_video_frames = VideoSOPExtractor.extract_sop_from_video_frames
extract_sop_from_video_gemini = VideoSOPExtractor.extract_sop_from_video_gemini

# =====================
# PROMPT STRINGS BELOW
# =====================

OPENAI_FRAME_ANALYSIS_PROMPT = """You are an expert automation specialist analyzing a video recording of a data entry process. 

I will provide you with {num_frames} frames extracted from the video at 1 frame per second. Your task is to analyze these frames and create a comprehensive Standard Operating Procedure (SOP) for automating this data entry process.

Please analyze the frames and provide a detailed SOP that includes:

## 1. Source Schema
- Identify the source data format and structure
- List all data fields visible in the source
- Note any data validation rules or patterns

## 2. Target Schema  
- Identify the target form/system structure
- List all required fields in the target and their type like textbox, dropdown, checkbox, datepicker, etc.
- Note any specific formatting requirements

## 3. Data Mapping
- Map source fields to target fields
    - Output mapping as "source_field_name: target_field_name : transformation_needed"
- Identify any data transformations needed
- Note any business rules for data conversion

## 4. Navigation Instructions
- Document how to navigate to the source data
- Document how to navigate to the target form
- Include any login/logout procedures (only if done in the video)

## 5. Steps Involved
- Identify the steps involved in the data entry process in order
- Note any specific actions or interactions required. 
    - Mainly when submitting a form, or going to a different page, or opening a different section, popups, etc.
- Document any conditional logic or decision points

Please provide the SOP in a structured, detailed format. Focus on being comprehensive and precise in your analysis.
"""

GEMINI_VIDEO_ANALYSIS_PROMPT = """You are an expert automation specialist analyzing a video recording of a data entry process.

Your task is to watch this video (or audio) and create a comprehensive Standard Operating Procedure (SOP) for automating this data entry process.

Please analyze the video (or audio) and provide a detailed and complete SOP that includes:

## 1. Source Schema
- Identify the source data format and structure
- List all data fields in the source
   - Double check that none of the fields are missed
- Note any data validation rules or patterns

## 2. Target Schema  
- Identify the target form/system structure
- List all required fields in the target and their type like textbox, dropdown, checkbox, datepicker, etc.
- Note any specific formatting requirements

## 3. Data Mapping
- Map source fields to target fields
    - Output mapping as "source_field_name: target_field_name : transformation_needed"
- Identify any data transformations needed
- Identify if some target fields are set to static values

## 4. Navigation Instructions
- Document how to navigate to the source data
- Document how to navigate to the target form
- Include any login/logout procedures (only if applicable from the video)

## 5. Steps Involved
- Identify the steps involved in the data entry process in order
- Note any specific actions or interactions required. 
    - Mainly when submitting a form, or going to a different page, or opening a different section, popups, etc.
- Document any conditional logic or decision points
"""
