"""
Example script demonstrating how to use the Video SOP Extractor tools.
This script shows how to extract SOPs from video recordings using both algorithms.
"""

import argparse
import os

# Now we can import from the agent package
from . import video_sop_extractor


def example_video_sop_extraction(
    video_path: str,
    method: str = "both",
    frame_mode: str = "1fps",
    required_fps: float = 1.0,
):
    """Example of using the video SOP extraction tools."""

    # Check if video file exists
    if not os.path.exists(video_path):
        print(f"❌ Video file not found: {video_path}")
        print("Please provide a valid video file path.")
        return

    print("🎬 Video SOP Extraction Example")
    print("=" * 50)
    print(f"📹 Video file: {video_path}")
    print(f"🔧 Method: {method}")
    print(f"📸 Frame mode: {frame_mode}")

    # Initialize the extractor
    extractor = video_sop_extractor.VideoSOPExtractor()

    # Method 1: Extract SOP using frame extraction with OpenAI
    if method in ["both", "frames"]:
        print("\n📸 Method 1: Frame Extraction with OpenAI")
        print("-" * 40)
        try:
            sop_frames = extractor._extract_sop_from_frames(
                video_path, frame_mode, required_fps
            )
            print("✅ Frame extraction completed successfully!")
            print("\nExtracted SOP:")
            print(sop_frames)
        except Exception as e:
            print(f"❌ Error in frame extraction: {str(e)}")

    # Method 2: Extract SOP using Vertex AI direct video analysis
    if method in ["both", "video"]:
        print("\n🤖 Method 2: Direct Video Analysis with Vertex AI")
        print("-" * 40)
        try:
            sop_gemini = extractor._extract_sop_with_gemini(video_path)
            print("✅ Vertex AI analysis completed successfully!")
            print("\nExtracted SOP:")
            print(sop_gemini)
        except Exception as e:
            print(f"❌ Error in Vertex AI analysis: {str(e)}")


def example_video_sop_extraction_using_tools(
    video_path: str, method: str = "both", frame_mode: str = "1fps"
):
    """Example of using the tools directly as LangChain tools."""

    from knowledge_agent_helper.video_sop_extractor import (
        extract_sop_from_video_frames,
        extract_sop_from_video_gemini,
    )

    print("\n🔧 Using Tools Directly")
    print("=" * 30)
    print(f"📹 Video file: {video_path}")
    print(f"🔧 Method: {method}")
    print(f"📸 Frame mode: {frame_mode}")

    # Create a mock state for the tools
    mock_state = {}

    # Use frame extraction tool
    if method in ["both", "frames"]:
        print("\n📸 Using extract_sop_from_video_frames tool:")
        try:
            result_frames = extract_sop_from_video_frames.invoke(
                {
                    "video_path": video_path,
                    "state": mock_state,
                    "mode": frame_mode,
                }
            )
            print("✅ Frame extraction tool result:")
            print(result_frames)
        except Exception as e:
            print(f"❌ Error: {str(e)}")

    # Use Vertex AI tool
    if method in ["both", "video"]:
        print("\n🤖 Using extract_sop_from_video_gemini tool:")
        try:
            result_gemini = extract_sop_from_video_gemini.invoke(
                {"video_path": video_path, "state": mock_state}
            )
            print("✅ Vertex AI tool result:")
            print(result_gemini)
        except Exception as e:
            print(f"❌ Error: {str(e)}")


def main():
    """Main function to parse arguments and run examples."""
    parser = argparse.ArgumentParser(
        description="Video SOP Extraction Tools Example",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python example_video_sop_extraction.py --video path/to/video.mp4
  python example_video_sop_extraction.py -v path/to/video.mp4 --method frames --frame-mode 1fps
  python example_video_sop_extraction.py -v path/to/video.mp4 --method frames --frame-mode significant
  python example_video_sop_extraction.py -v path/to/video.mp4 --method video
        """,
    )

    parser.add_argument(
        "-v", "--video", required=True, help="Path to the video file to analyze"
    )

    parser.add_argument(
        "-m",
        "--method",
        choices=["both", "frames", "video"],
        default="both",
        help="Method to use for SOP extraction (default: both). "
        "frames: Use frame extraction with OpenAI. "
        "video: Use Vertex AI direct video analysis.",
    )

    parser.add_argument(
        "--frame-mode",
        choices=["1fps", "significant"],
        default="1fps",
        help="Frame extraction mode for OpenAI analysis (default: significant)",
    )

    parser.add_argument(
        "--required-fps",
        type=float,
        default=1,
        help="Required FPS for 1fps mode (default: 1.0)",
    )

    parser.add_argument(
        "--tools-only",
        action="store_true",
        help="Only run the tools example, skip direct class usage",
    )

    args = parser.parse_args()

    print("Video SOP Extraction Tools Example")
    print("=" * 50)
    print("\nPrerequisites:")
    print("- OpenAI API key set in environment")
    print("- Google Cloud credentials set in environment")
    print("- A video file to analyze")

    video_path = args.video
    method = args.method
    frame_mode = args.frame_mode

    if not args.tools_only:
        example_video_sop_extraction(
            video_path, method, frame_mode, args.required_fps
        )
    else:
        # Run tools example
        example_video_sop_extraction_using_tools(video_path, method, frame_mode)

    print("\n" + "=" * 50)
    print("Example completed!")


if __name__ == "__main__":
    main()
