"""
Dataset of known enterprise use cases with their blueprints.
"""

available_use_cases = [
    # Create opportunity from sales leads
    # "Sales Lead to Opportunity Automation",
]

executor_spec = """
Or<PERSON> executes a workflow by first translating the workflow into Python code and then executing it in a managed environment. Python version: 3.13.

The workflow can use common Python libraries accessible via pypi. It can interact with the browser using Playwright. 

The workflow execution environment will also be configured with environment variables to enable the workflow to use OpenAI API and Anthropic API.

For the below use cases, LLM-based approaches are preferred:
1. Entity extraction. For example, extracting entities from CSV, JSON, images.
2. Data mapping. For example, converting a dictionary into another. 
3. Classification. For example, identifying the closest option to select.
"""
