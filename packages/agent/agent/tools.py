"""
Tools available to the knowledge and coding agents.
"""

from functools import wraps
import os
import time
from typing import Annotated

from langchain_anthropic import Cha<PERSON><PERSON><PERSON>hropic
from langchain_core.messages import ToolMessage
from langchain_core.tools import InjectedToolCallId, tool
from langgraph.config import get_stream_writer
from langgraph.prebuilt import InjectedState
from langgraph.types import Command

# Protobuf imports will be done dynamically in the tool to avoid import issues
from playwright.sync_api import sync_playwright
import requests

from agent.utils.browser_utils import get_browser_page
from web_server.session.state_manager import get_state_manager

from .get_dom_example import get_website_dom
from .state import OverallState
from .utils.html_utils import simplify_html_for_form_understanding


def timer_decorator(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"⏱️ {func.__name__} took {execution_time:.2f} seconds to execute")
        return result

    return wrapper


@tool
@timer_decorator
def read_file(path: str) -> str:
    """Read a file from the attachments"""
    # Check if file is a media file
    media_extensions = {".mp4", ".mov", ".webm"}
    # Check if path is a GCS signed URL
    if path.startswith("https://storage.googleapis.com/"):
        # Download the file
        response = requests.get(path)
        if response.status_code == 200:
            return response.content.decode("utf-8")

    # Check if path exists
    if not os.path.exists(path):
        return f"File not found: {path}"
    file_extension = os.path.splitext(path)[1].lower()
    if file_extension in media_extensions:
        return f"Cannot read media file: {path}. Media files are not supported for text reading."

    with open(path) as f:
        return f.read()


@tool
@timer_decorator
def list_available_attachments() -> str:
    """
    List all available attachments in the current session.

    This tool provides a formatted list of all files uploaded in the current session,
    including file names, sizes, and paths.

    Returns:
        Formatted string listing all available attachments
    """
    try:
        state_manager = get_state_manager()
        attachments = state_manager.get_attachments()
        if not attachments:
            return "No attachments found in the current session."

        attachment_list = []
        for i, attachment in enumerate(attachments, 1):
            filename = os.path.basename(attachment.path)
            file_size = "unknown size"
            if attachment.size:
                if attachment.size < 1024:
                    file_size = f"{attachment.size} bytes"
                elif attachment.size < 1024 * 1024:
                    file_size = f"{attachment.size / 1024:.1f} KB"
                else:
                    file_size = f"{attachment.size / (1024 * 1024):.1f} MB"

            attachment_list.append(
                f"{i}. {filename} ({file_size}) - path: {attachment.path}"
            )

        return "Available attachments:\n" + "\n".join(attachment_list)
    except Exception as e:
        return f"Error retrieving attachments: {str(e)}"


# ============================================================================
# FOR AGENT EXPLORATION
# ============================================================================


class ExplorationTools:
    """Additional tools for agent exploration and website analysis."""

    @staticmethod
    @timer_decorator
    def get_website_dom_details(
        state: Annotated[OverallState, InjectedState],
        url: str,
    ) -> str:
        """
        Get the DOM of the website.
        """
        browser_connection_url = state.get("browser_connection_url", "")
        return get_website_dom(url, browser_connection_url)

    @staticmethod
    @tool
    @timer_decorator
    def webpage_agent_exploration(
        url: str,
        state: Annotated[OverallState, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
        max_length: int | None = 10000,
        simplify: bool = True,
    ):
        """
        Explore and analyze webpage content for automation planning using Playwright and LLM analysis.

        Args:
            url: The URL to explore and analyze
            max_length: Maximum length of raw HTML content (default: 10000 chars)
            simplify: Whether to use LLM to extract form-related elements (default: True)

        Returns:
            Simplified automation guidance or raw HTML content for webpage understanding
        """

        print(
            f"Calling webpage_agent_exploration with url: {url} and state: {state}"
        )
        shortened_url = url[:40] + "..." if len(url) > 40 else url

        status_message = f"🌐 **Exploring {shortened_url} form now.** Analyzing webpage structure and form elements."

        writer = get_stream_writer()
        if writer:
            try:
                writer({"status_update": status_message})
            except Exception as e:
                print(f"Error emitting status update: {e}")

        try:
            print(f"🌐 Reading HTML from: {url}")

            with sync_playwright() as p:
                browser_connection_url = state.get("browser_connection_url", "")
                with get_browser_page(p, browser_connection_url) as (
                    browser,
                    page,
                ):
                    # Navigate to the URL
                    page.goto(url, wait_until="domcontentloaded", timeout=30000)

                    # Wait a bit for dynamic content to load
                    page.wait_for_timeout(2000)

                    # Get the HTML content
                    html_content = page.content()

                if simplify:
                    # Use LLM to simplify and extract automation-relevant information
                    print("🤖 Simplifying HTML with LLM...")
                    simplified_content = simplify_html_for_form_understanding(
                        html_content
                    )
                    print(f"✅ Simplified content: {simplified_content}")
                    content_result = simplified_content
                else:
                    # Return raw HTML (legacy behavior)
                    if max_length and len(html_content) > max_length:
                        truncated_html = html_content[:max_length]
                        content_result = f"**HTML Content from {url}** (truncated to {max_length} characters):\n\n```html\n{truncated_html}\n...\n```\n\n**Note**: Content was truncated. Original length: {len(html_content)} characters."
                    else:
                        content_result = f"**HTML Content from {url}**:\n\n```html\n{html_content}\n```"

                tool_message = ToolMessage(
                    content=f"Successfully explored webpage: {url}\n\n{content_result}",
                    tool_call_id=tool_call_id,
                )
                return Command(
                    update={"messages": [tool_message], "form_url": url}
                )

        except Exception as e:
            error_msg = f"❌ **Error reading HTML from {url}**: {str(e)}"
            print(error_msg)

            tool_message = ToolMessage(
                content=error_msg,
                tool_call_id=tool_call_id,
            )
            return Command(
                update={
                    "messages": [tool_message],
                }
            )

    @staticmethod
    def _simplify_html_for_automation(html_content: str, url: str) -> str:
        """
        Use LLM to analyze HTML and extract automation-relevant information.

        Args:
            html_content: Raw HTML content
            url: The URL for context

        Returns:
            Simplified automation guidance
        """
        try:
            # Initialize LLM (using the same configuration as the coding agent)
            # config = Configuration()
            llm = ChatAnthropic(
                model="claude-3-7-sonnet-latest",  # Use faster model for HTML analysis
                temperature=0.1,
                max_tokens=2000,
            )

            # Create prompt for HTML simplification
            simplification_prompt = f"""Analyze this HTML content from {url} and extract automation-relevant information.

Focus on identifying:
1. Forms and their structure
2. Input fields (name, type, labels, IDs)
3. Buttons and clickable elements
4. Navigation elements
5. Key interactive components

Provide concise automation guidance including:
- Playwright locators for each element based on XPath, CSS Selector, or ID, name, class, label, etc.
- Detailed element descriptions
- Form structure and flow
- Recommended automation approach

HTML Content:
```html
{html_content[:100000]}  # Limit to avoid token limits
```

Provide a structured analysis that will help generate accurate automation code."""

            # Get LLM response
            response = llm.invoke(simplification_prompt)
            simplified_content = response.content

            print(
                f"✅ HTML simplified successfully ({len(simplified_content)} characters)"
            )

            return f"""**Simplified Automation Analysis for {url}**

{simplified_content}

"""

        except Exception as e:
            print(f"⚠️ HTML simplification failed, returning raw HTML: {e}")
            # Fallback to truncated raw HTML if LLM fails
            truncated_html = html_content[:5000]
            return f"**HTML Content from {url}** (LLM simplification failed, showing raw HTML):\n\n```html\n{truncated_html}\n...\n```\n\n**Note**: Original length: {len(html_content)} characters."
