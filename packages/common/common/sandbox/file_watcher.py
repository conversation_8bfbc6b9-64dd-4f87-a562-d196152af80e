"""
File watcher for runtime environments.
"""

import asyncio
from collections.abc import Callable
import re
from typing import Any
import uuid

from dotenv import load_dotenv
from e2b import FilesystemEvent, FilesystemEventType

from common.log.log import error
from common.storage.gcs import get_gcs_client
from web_server.constants.env import DEV_MODE, LOCAL_MODE
from web_server.core.config import server_config
from web_server.data.orby_chainlit_data_layer import TenantInfoMixin

load_dotenv()


class ProcessedEvent(FilesystemEvent):
    """
    Processed event object that includes a signed URL for the file along with the e2b event.
    """

    def __init__(
        self,
        name: str,
        type: FilesystemEventType,
        signed_url: str,
        is_sop: bool = False,
    ):
        super().__init__(name, type)
        self.signed_url = signed_url
        self.is_sop = is_sop

    def to_dict(self):
        """Convert ProcessedEvent to JSON-serializable dictionary."""
        return {
            "name": self.name,
            "signed_url": self.signed_url,
            "timestamp": getattr(self, "timestamp", None),
            "is_sop": self.is_sop,
        }


class FileWatcher(TenantInfoMixin):
    """
    Simple file watcher for E2B runtime environments.

    Monitors file system changes and sends processed E2B events to callback handlers.
    Filters for CREATE, DELETE, and WRITE events only, excluding system files.
    """

    def __init__(self, poll_interval: float = 1.0):
        super().__init__()
        self._is_active = False
        self._on_event_callback: Callable[[Any], None] | None = None
        self._watch_handle = None
        self._watch_task: asyncio.Task | None = None
        self._poll_interval = poll_interval
        self._runtime = None

        # Filter configuration
        self._allowed_event_types = {
            FilesystemEventType.CREATE,
            FilesystemEventType.REMOVE,
            FilesystemEventType.WRITE,
        }
        # Use regex patterns for more efficient filtering
        self._excluded_patterns = {
            # Hidden files and directories
            r"^\.",  # Matches any file/dir starting with .
            # Cache and build files
            r"__pycache__",
            r".*\.(pyc|pyo|pyd)$",
            r"build",
            r"dist",
            r".*\.egg-info",
            # IDE and editor files
            r"\.idea",
            r"\.vscode",
            r".*\.(swp|swo)$",
            # Temp and log files
            r".*\.(log|tmp|temp)$",
            r"tmp",
            r"temp",
            # Project specific files
            "runtime_generate_manifest.py",
            "mcp_config.json",
            "video_sop_tool.py",
            "claude_wrapper.sh",
            "claude_expect.exp",
        }

        # Dev-specific files (only excluded in non-dev environments)
        self._dev_excluded_patterns = {
            "claude_prompt.txt",
            "claude_system_prompt.txt",
        }

    def start(
        self,
        watch_path: str,
        on_event_callback: Callable[[Any], None],
        runtime=None,
    ):
        """Start watching directory for file changes.

        Args:
            watch_path: Directory path to watch
            on_event_callback: Function that receives ProcessedEvent objects
            runtime: Runtime instance
        """
        if self._is_active:
            return

        if not runtime or not runtime._sandbox:
            raise ValueError("Runtime must be provided")

        self._is_active = True
        self._on_event_callback = on_event_callback
        self._runtime = runtime
        self._watch_handle = runtime._sandbox.files.watch_dir(
            watch_path, recursive=True
        )
        self._watch_task = asyncio.create_task(self._watch_loop())

    def stop(self):
        """Stop watching directory and clean up resources."""
        self._is_active = False

        # Cancel watch task
        if self._watch_task and not self._watch_task.done():
            self._watch_task.cancel()

        # Clean up all references
        self._on_event_callback = None
        self._watch_handle = None
        self._watch_task = None
        self._runtime = None

    def _should_process_event(self, event) -> bool:
        """Check if event should be processed based on filters."""
        try:
            # Filter by event type
            if event.type not in self._allowed_event_types:
                return False

            # Filter by filename using regex patterns
            for pattern in self._excluded_patterns:
                if re.match(pattern, event.name):
                    return False

            # Check dev-specific exclusions (only exclude in non-dev environments)
            is_dev = (
                server_config.mode == LOCAL_MODE
                or server_config.mode == DEV_MODE
            )
            if not is_dev:
                # In non-dev environments, exclude these files
                if event.name in self._dev_excluded_patterns:
                    return False

            return True
        except Exception as e:
            error(f"Error processing event: {e}")
            # Missing required attributes, skip event
            return False

    async def _process_event(
        self, event: FilesystemEvent
    ) -> ProcessedEvent | None:
        """
        Process the event by reading the file, saving the file to gcs in a temp directory and returning a ProcessedEvent.
        """
        try:
            if not self._runtime:
                error("Runtime not available for file processing")
                return

            gcs_client = get_gcs_client()
            tenant_info = await self._get_tenant_info()
            bucket_name = tenant_info.get_bucket_name(
                server_config.chainlit_default_bucket
            )
            # Set the TTL property on the bucket
            path = f"temp_sandbox_files/ttl=1d/{uuid.uuid4()}/{event.name}"
            # read the file content from the runtime
            content = await self._runtime.read_bytes(event.name)

            # Convert bytearray to bytes if needed
            if isinstance(content, bytearray):
                content = bytes(content)

            await gcs_client.upload(bucket_name, path, content)
            signed_url = await gcs_client.get_signed_url(bucket_name, path)

            return ProcessedEvent(
                event.name,
                event.type,
                signed_url,
                is_sop=event.name.endswith("SOP.md"),
            )
        except Exception as e:
            error(f"Error processing event: {e}")
            return

    async def _watch_loop(self):
        """Main watch loop that polls for file system events."""
        try:
            while self._is_active and self._watch_handle:
                # Get new events from E2B
                events: list[FilesystemEvent] = (
                    self._watch_handle.get_new_events()
                )

                for event in events:
                    if not self._is_active:
                        break

                    if not self._should_process_event(event):
                        continue
                    processed_event = await self._process_event(event)
                    # Send processed event to callback
                    if self._on_event_callback and processed_event:
                        try:
                            self._on_event_callback(processed_event)
                        except Exception:
                            # Don't let callback errors break file watching
                            pass

                # Wait before checking again
                await asyncio.sleep(self._poll_interval)

        except asyncio.CancelledError:
            # Task was cancelled, clean exit
            pass
        except Exception as e:
            error(f"Error in watch loop: {e}")

    def is_active(self) -> bool:
        """Check if currently watching files."""
        return self._is_active
