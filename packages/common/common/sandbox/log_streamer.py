"""
Log streamer classes for runtime environments.
"""

import asyncio
from collections import deque
import json
import time
from typing import TypedDict

from common.cache.redis_client import get_redis_client


class ExecutionRedisContentType(TypedDict):
    """TypedDict for Redis log stream content."""

    stream_type: str  # "stdout" or "stderr"
    content: str  # actual log message


class FilterType:
    """Available filter types for log streaming."""

    RAW = "raw"  # No filtering
    CLAUDE = "claude"  # Claude JSON filtering
    CUSTOM = "custom"  # Custom filter function


class LogEntry:
    """Simple log entry."""

    def __init__(
        self,
        stream_type: str,
        content: str,
        message_type: str = "raw",
        source: str = "local",
    ):
        self.stream_type = stream_type  # "stdout" or "stderr"
        self.content = content
        self.message_type = message_type  # "raw", "claude", "filtered"
        self.source = source  # "local" or "redis"
        self.timestamp = time.time()


class LogStreamer:
    """
    Simple log streamer that handles two types of log streams:

    1. LOCAL LOGS: Added via add_log() - from processes
    2. REDIS LOGS: From Redis streams "execution:{id}" - from remote

    Both streams are unified and sent to the same callback handler.
    Optional filtering can be applied (RAW, CLAUDE, CUSTOM).

    REDIS LOG FORMAT:
    Redis stream messages must have this format:
    {
        "stream_type": "stdout" | "stderr",
        "content": "actual log message text"
    }

    Usage:
        log_streamer = LogStreamer()
        log_streamer.start(on_log_callback=handle_log, execution_id="my-exec")
        log_streamer.add_log("stdout", "test")  # Local log
        # Redis logs from stream "execution:my-exec" automatically appear in callback
        log_streamer.stop()
    """

    def __init__(self, max_size: int = 1000):
        # Internal buffering for both local and Redis logs
        self._stdout_logs = deque(maxlen=max_size)
        self._stderr_logs = deque(maxlen=max_size)

        self._is_active = False
        self._on_log_callback = None
        self._filter_type = FilterType.RAW
        self._custom_filter_func = None

        # Redis stream integration for remote execution logs
        self._execution_id: str | None = None
        self._redis_client = None
        self._redis_task: asyncio.Task | None = None

    def start(
        self,
        on_log_callback,  # Required - all logs go through callback
        filter_type=FilterType.RAW,
        custom_filter_func=None,
        execution_id: str | None = None,
    ):
        """Start collecting logs from both local and Redis sources.

        Args:
            on_log_callback: Function that receives all log entries
            filter_type: How to filter logs (RAW/CLAUDE/CUSTOM)
            custom_filter_func: Custom filter function for FilterType.CUSTOM
            execution_id: If provided, reads from Redis stream "execution:{id}"
        """
        self._is_active = True
        self._on_log_callback = on_log_callback
        self._filter_type = filter_type
        self._custom_filter_func = custom_filter_func
        self._execution_id = execution_id
        self._stdout_logs.clear()
        self._stderr_logs.clear()

        # Start Redis stream reader if execution_id provided
        if execution_id:
            self._redis_client = get_redis_client()
            self._redis_task = asyncio.create_task(
                self._read_redis_logs_for_execution()
            )

    def stop(self):
        """Stop collecting logs and close Redis stream."""
        self._is_active = False

        # Cancel Redis stream reading task
        if self._redis_task and not self._redis_task.done():
            self._redis_task.cancel()

        # Clean up all references
        self._on_log_callback = None
        self._filter_type = FilterType.RAW
        self._custom_filter_func = None
        self._execution_id = None
        self._redis_client = None
        self._redis_task = None

    async def _read_redis_logs_for_execution(self):
        """Read execution logs from Redis stream."""
        if not self._execution_id or not self._redis_client:
            return

        stream_name = f"execution:{self._execution_id}"

        try:
            # Read all messages from Redis stream with LogContent typing
            async for message in self._redis_client.stream_reader(
                stream_name, ExecutionRedisContentType, start_id="0", block=0
            ):
                if not self._is_active:
                    break

                log_data = message.data
                stream_type = log_data["stream_type"]
                content = log_data["content"]
                if content:
                    self._add_log_entry(stream_type, content, source="redis")

        except asyncio.CancelledError:
            # If the task is cancelled, we don't need to do anything
            pass
        except Exception as e:
            # Send Redis errors as stderr logs
            self._add_log_entry(
                "stderr", f"Redis stream error: {e}", source="redis"
            )

    def add_log(self, stream_type: str, content: str):
        """Add a local log entry."""
        if not self._is_active:
            return

        self._add_log_entry(stream_type, content, source="local")

    def _add_log_entry(
        self, stream_type: str, content: str, source: str = "local"
    ):
        """Process log entry with filtering and send to callback."""
        if source == "redis" and self._filter_type == FilterType.CLAUDE:
            # For Redis logs with CLAUDE filter type, skip filtering to show raw logs
            # This allows reviewing unfiltered workflow build output
            entry = LogEntry(stream_type, content, "raw", source)
        # Apply filtering (RAW = no filter, CLAUDE = parse JSON, CUSTOM = user function)
        elif self._filter_type == FilterType.RAW:
            entry = LogEntry(stream_type, content, "raw", source)
        elif self._filter_type == FilterType.CLAUDE and stream_type == "stdout":
            filtered_content = self._filter_claude_output(content)
            if filtered_content is None:
                return  # Skip this log entry
            entry = LogEntry(stream_type, filtered_content, "claude", source)
        elif (
            self._filter_type == FilterType.CUSTOM and self._custom_filter_func
        ):
            try:
                filtered_content = self._custom_filter_func(
                    stream_type, content
                )
                if filtered_content is None:
                    return  # Skip this log entry
                entry = LogEntry(
                    stream_type, filtered_content, "custom", source
                )
            except Exception:
                # If custom filter fails, fall back to raw
                entry = LogEntry(stream_type, content, "raw", source)
        else:
            entry = LogEntry(stream_type, content, "raw", source)

        # Store internally and send to callback
        if stream_type == "stdout":
            self._stdout_logs.append(entry)
        elif stream_type == "stderr":
            self._stderr_logs.append(entry)

        # Main interface: send all logs to callback immediately
        if self._on_log_callback:
            try:
                self._on_log_callback(entry)
            except Exception:
                # Don't let callback errors break log collection
                pass

    def is_active(self) -> bool:
        """Check if currently collecting logs."""
        return self._is_active

    def _filter_claude_output(self, output: str) -> str | None:
        """Extract meaningful content from Claude's JSON output."""
        try:
            json_data = json.loads(output)
            message_type = json_data.get("type")

            if message_type == "system":
                return None

            elif message_type == "assistant" and "message" in json_data:
                message = json_data["message"]
                if "content" in message:
                    for content_item in message["content"]:
                        if content_item.get("type") == "text":
                            text = content_item.get("text", "")
                            if text.strip():
                                return text
                        elif content_item.get("type") == "tool_use":
                            # Skip tool usage messages to reduce clutter
                            return
                return None

            elif message_type == "user" and "message" in json_data:
                return None

            elif message_type == "result":
                return None

            return None

        except json.JSONDecodeError:
            return None
        except Exception:
            # If anything goes wrong with parsing, it's not a claude message
            return None
