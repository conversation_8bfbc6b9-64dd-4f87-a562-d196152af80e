from datetime import datetime
from enum import Enum
from typing import ClassVar

from bson import ObjectId
from pydantic import BaseModel, ConfigDict, Field, field_validator

from ..utils.constants import TASK_COLLECTION
from .base import OrbyDataModel


class TaskStatus(str, Enum):
    PENDING = "pending"
    FAILED = "failed"
    COMPLETED = "completed"


class TaskData(BaseModel):
    # placeholder for output/result of the task
    before: dict[str, str] | None = None
    after: dict[str, str] | None = None


class TaskUsers(BaseModel):
    model_config = ConfigDict(
        arbitrary_types_allowed=True,
    )
    creator_id: ObjectId | None = None


class TaskTimestamps(BaseModel):
    created_at: datetime | None = None
    updated_at: datetime | None = None
    completed_at: datetime | None = None
    cancelled_at: datetime | None = None


class Task(OrbyDataModel):
    IS_SINGLE_TENANT_COLLECTION: ClassVar[bool] = True

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
    )

    id: ObjectId = Field(default_factory=ObjectId, alias="_id")
    org_id: ObjectId
    workflow_id: ObjectId
    # unique index for identify_key and execution_id
    identify_key: str
    execution_id: ObjectId
    display_name: str
    description: str
    status: TaskStatus
    users: TaskUsers | None = None
    data: TaskData | None = None
    metadata: dict[str, str] | None = None
    timestamps: TaskTimestamps | None = None

    @field_validator("id", mode="before")
    @classmethod
    def validate_id(cls, v):
        if isinstance(v, str):
            return ObjectId(v)
        return v

    @classmethod
    def get_collection_name(cls) -> str:
        return TASK_COLLECTION
