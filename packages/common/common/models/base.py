from typing import Any, ClassVar

from bson import ObjectId
from pydantic import BaseModel, field_serializer

"""
This is a base class for all models that are used in the application.
It provides a way to determine if a collection is a single tenant collection.
"""


class OrbyDataModel(BaseModel):
    IS_SINGLE_TENANT_COLLECTION: ClassVar[bool] = False

    model_config = {
        "arbitrary_types_allowed": True,
        "use_enum_values": True,
    }

    @field_serializer("*", mode="wrap")
    def serialize_objectid(self, value: Any, serializer, info) -> Any:
        """Custom serializer to handle ObjectId types"""
        # Only convert ObjectId to string for JSON serialization
        # For Python dict serialization (MongoDB), keep native ObjectId
        if isinstance(value, ObjectId) and info.mode_is_json():
            return str(value)
        elif (
            isinstance(value, list)
            and value
            and isinstance(value[0], ObjectId)
            and info.mode_is_json()
        ):
            return [str(item) for item in value]
        return serializer(value)

    @classmethod
    def is_single_tenant_collection(cls) -> bool:
        return getattr(cls, "IS_SINGLE_TENANT_COLLECTION", False)
