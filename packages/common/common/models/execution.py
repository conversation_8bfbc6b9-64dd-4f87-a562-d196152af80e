from datetime import datetime
from enum import Enum
from typing import Any, ClassVar

from bson import ObjectId
from pydantic import BaseModel, Field, field_validator

from ..utils.constants import EXECUTION_COLLECTION, EXECUTION_LOG_COLLECTION
from .base import OrbyDataModel


class ExecutionStatus(str, Enum):
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"
    TIMEOUT = "TIMEOUT"


class ExecutionIdentifierType(str, Enum):
    USER = "USER"
    SCHEDULE = "SCHEDULE"


class ExecutionWorkflowContext(BaseModel):
    commit_hash: str | None = None


class ExecutionTriggeredBy(BaseModel):
    model_config = {
        "arbitrary_types_allowed": True,
        "use_enum_values": True,
    }

    type: ExecutionIdentifierType
    identifier: str | None = None


class ExecutionBrowserService(BaseModel):
    session_id: str | None = None
    context_id: str | None = None


class ExecutionUsers(BaseModel):
    cancelled_by: str | None = None


class ExecutionTimestamps(BaseModel):
    created_at: datetime | None = None
    updated_at: datetime | None = None
    started_at: datetime | None = None
    finished_at: datetime | None = None
    cancelled_at: datetime | None = None


class Execution(OrbyDataModel):
    IS_SINGLE_TENANT_COLLECTION: ClassVar[bool] = True

    id: ObjectId = Field(default_factory=ObjectId, alias="_id")
    org_id: ObjectId
    workflow_id: ObjectId
    workflow_context: ExecutionWorkflowContext | None = None
    inputs: dict[str, Any] | None = None
    triggered_by: ExecutionTriggeredBy | None = None
    status: ExecutionStatus | None = None
    browser_service: ExecutionBrowserService | None = None
    users: ExecutionUsers | None = None
    timestamps: ExecutionTimestamps | None = None
    rrweb_recording_gcs_uri: str | None = None

    @field_validator("id", mode="before")
    @classmethod
    def validate_id(cls, v):
        if isinstance(v, str):
            return ObjectId(v)
        return v

    @classmethod
    def get_collection_name(cls) -> str:
        return EXECUTION_COLLECTION


class ExecutionLog(OrbyDataModel):
    IS_SINGLE_TENANT_COLLECTION: ClassVar[bool] = True

    id: ObjectId = Field(default_factory=ObjectId, alias="_id")
    execution_id: ObjectId
    workflow_id: ObjectId
    org_id: ObjectId
    status: ExecutionStatus | None = None
    step_id: int | None = None
    description: str | None = None
    screenshot: str | None = None
    metadata: dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime | None = None

    @classmethod
    def get_collection_name(cls) -> str:
        return EXECUTION_LOG_COLLECTION
