from bson import ObjectId
from pydantic import Field, field_validator

from .base import OrbyDataModel


class User(OrbyDataModel):
    id: ObjectId = Field(default_factory=ObjectId, alias="_id")
    email: str
    first_name: str | None = None
    last_name: str | None = None
    image_url: str | None = None
    full_name: str | None = None
    org_ids: list[ObjectId] | None = None

    @field_validator("id", mode="before")
    @classmethod
    def validate_id(cls, v):
        if isinstance(v, str):
            return ObjectId(v)
        return v

    @field_validator("org_ids", mode="before")
    @classmethod
    def validate_org_ids(cls, v):
        if v is None:
            return v
        if isinstance(v, list):
            return [
                ObjectId(item) if isinstance(item, str) else item for item in v
            ]
        return v

    @classmethod
    def get_collection_name(cls) -> str:
        return "users"
