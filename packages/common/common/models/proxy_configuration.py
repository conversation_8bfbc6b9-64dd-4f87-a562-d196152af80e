from bson import ObjectId
from pydantic import BaseModel, Field

from .base import OrbyDataModel


class ProxyConfigurationEntry(OrbyDataModel):
    """
    Model for browser proxy configuration entries
    """

    id: ObjectId | None = Field(default_factory=ObjectId, alias="_id")
    """Domain pattern for which this proxy should be used. If omitted, defaults to all domains. Optional."""
    domain_pattern: str

    @classmethod
    def get_collection_name(cls) -> str:
        """Get the collection name for this model"""
        return "proxy_configuration"


class ProxyConfiguration(BaseModel):
    """
    Model for proxy configuration used for creating browser sessions.
    """

    entries: list[ProxyConfigurationEntry]
