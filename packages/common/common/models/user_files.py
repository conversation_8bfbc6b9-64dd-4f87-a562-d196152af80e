from datetime import datetime
from typing import ClassVar

from bson import ObjectId
from pydantic import ConfigDict, Field

from common.models.base import OrbyDataModel
from common.utils.constants import USER_FILES_COLLECTION


class UserFile(OrbyDataModel):
    """User file model for storing uploaded and generated files"""

    IS_SINGLE_TENANT_COLLECTION: ClassVar[bool] = True

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
    )

    id: ObjectId = Field(default_factory=ObjectId, alias="_id")
    creator_id: ObjectId
    org_id: ObjectId
    created_time: datetime
    workflow_id: ObjectId
    path: str

    @classmethod
    def get_collection_name(cls) -> str:
        return USER_FILES_COLLECTION
