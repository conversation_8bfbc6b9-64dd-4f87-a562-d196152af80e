from enum import Enum
import os

from bson import ObjectId
from pydantic import BaseModel, Field

from .base import OrbyDataModel


class TenantType(str, Enum):
    SHARED_TENANT = "shared tenant"
    SINGLE_TENANT = "single tenant"


class Organization(OrbyDataModel):
    id: ObjectId = Field(default_factory=ObjectId, alias="_id")
    display_name: str
    tenant_type: TenantType | None = TenantType.SHARED_TENANT

    @classmethod
    def get_collection_name(cls) -> str:
        return "organizations"


class TenantInfo(BaseModel):
    org: Organization

    def _is_single_tenant(self, org: Organization) -> bool:
        return org.tenant_type == TenantType.SINGLE_TENANT

    def get_database_name(self, collection: OrbyDataModel | None = None) -> str:
        """
        Get database name for both PostgreSQL and MongoDB.

        Args:
            collection: Optional collection model
                - If None: PostgreSQL mode - only depends on organization tenancy
                - If provided: MongoDB mode - depends on both organization and collection tenancy

        Returns:
            Database name based on tenant configuration
        """
        default_database_name = os.getenv("DEFAULT_DB_NAME", "dev")

        # TODO: Uncomment this when we have a way to handle single tenant collections
        # if self._is_single_tenant(self.org):
        #     # For PostgreSQL (collection=None): always use tenant-specific database
        #     # For MongoDB: only use tenant-specific database if collection is also single tenant
        #     if collection is None or collection.is_single_tenant_collection():
        #         return f"{default_database_name}_{str(self.org.id)}"

        return default_database_name

    def get_bucket_name(self, default_bucket_name: str) -> str:
        # TODO: Uncomment this when we have a way to handle single tenant collections
        # if self._is_single_tenant(self.org):
        #     return f"{default_bucket_name}_{str(self.org.id)}"
        return default_bucket_name
