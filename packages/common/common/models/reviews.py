from datetime import UTC, datetime
from enum import Enum

from bson import ObjectId
from pydantic import ConfigDict, Field

from common.models.base import OrbyDataModel
from common.utils.constants import REVIEW_COLLECTION


class ReviewStatus(str, Enum):
    PENDING = "pending"
    READY = "ready"


class Review(OrbyDataModel):
    model_config = ConfigDict(
        arbitrary_types_allowed=True,
    )

    id: ObjectId = Field(default_factory=ObjectId, alias="_id")
    execution_id: str
    user_message: str = ""
    status: ReviewStatus = ReviewStatus.PENDING
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC))
    completed_at: datetime | None = None
    user_response: str | None = None

    @classmethod
    def get_collection_name(cls) -> str:
        return REVIEW_COLLECTION
