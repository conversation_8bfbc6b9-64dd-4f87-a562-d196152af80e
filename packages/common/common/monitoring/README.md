# Prometheus Monitoring Module

This module provides comprehensive Prometheus monitoring capabilities for both gRPC and FastAPI servers in the Vibe Automation Server project.

## Module Structure

The monitoring module is split into separate files to avoid dependencies between different server types:

- `base.py` - Core metrics functionality (PrometheusMetrics, get_metrics, monitor_function)
- `fastapi.py` - FastAPI-specific monitoring (middleware, metrics endpoint)
- `grpc.py` - gRPC-specific monitoring (interceptor)
- `__init__.py` - Unified interface for all functionality

## Features

- **HTTP/FastAPI Metrics**: Request counts, durations, and status codes
- **gRPC Metrics**: Request counts, durations, and error codes
- **Error Tracking**: Centralized error collection across services
- **Business Metrics**: Custom operation tracking and connection monitoring
- **Easy Integration**: Simple middleware and interceptor setup
- **Decorator Support**: Easy function-level monitoring

## Quick Start

### For FastAPI Applications

```python
from fastapi import FastAPI
from common.monitoring import PrometheusFastAPIMiddleware, metrics_endpoint

app = FastAPI()

# Setup monitoring
app.add_middleware(PrometheusFastAPIMiddleware)
app.add_route("/metrics", metrics_endpoint, methods=["GET"])

# Your endpoints will now be automatically monitored
@app.get("/api/data")
async def get_data():
    return {"data": "example"}
```

Alternatively, you can use the factory function:

```python
from fastapi import FastAPI
from common.monitoring import create_prometheus_middleware, metrics_endpoint

app = FastAPI()

# Setup monitoring
app.add_middleware(create_prometheus_middleware())
app.add_route("/metrics", metrics_endpoint, methods=["GET"])

# Your endpoints will now be automatically monitored
@app.get("/api/data")
async def get_data():
    return {"data": "example"}
```

### For gRPC Servers

```python
import grpc
from common.monitoring import create_prometheus_interceptor

# Create server with monitoring
server = grpc.aio.server()
interceptor = create_prometheus_interceptor()
server = grpc.aio.server(interceptors=[interceptor])

# Your gRPC services will now be automatically monitored
```

## Available Metrics

### HTTP/FastAPI Metrics

- `http_requests_total`: Total number of HTTP requests by method, endpoint, and status code
- `http_request_duration_seconds`: Request duration histogram by method and endpoint

### gRPC Metrics

- `grpc_requests_total`: Total number of gRPC requests by method, service, and code
- `grpc_request_duration_seconds`: Request duration histogram by method and service

### Error Metrics

- `errors_total`: Total number of errors by type and service

### Business Metrics

- `business_operations_total`: Total number of business operations by operation and status
- `business_operation_duration_seconds`: Business operation duration histogram by operation and service

## Usage Examples

### Manual Metrics Recording

```python
from common.monitoring import get_metrics

metrics = get_metrics()

# Record HTTP request
metrics.record_http_request("GET", "/api/users", 200, 0.15)

# Record gRPC request
metrics.record_grpc_request("/pb.v1.ExecutionService/Execute", "execution", "OK", 0.25)

# Record errors
metrics.record_error("database_connection", "web_server")

# Record business operations
metrics.record_business_operation("user_registration", "success")

# Record business operation duration
metrics.record_business_operation_duration("user_registration", "web_server", 0.25)
```

### Using Decorators

```python
from common.monitoring import monitor_function

@monitor_function("data_processing", "web_server")
async def process_data(data):
    # Your processing logic
    return processed_data
```

### Context Manager

```python
from common.monitoring import get_metrics

metrics = get_metrics()

with metrics.track_request("POST", "/api/upload"):
    # Your request processing logic
    pass
```

## Configuration

### Custom Metrics Registry

```python
from prometheus_client import CollectorRegistry
from common.monitoring import PrometheusMetrics

# Create custom registry
registry = CollectorRegistry()
metrics = PrometheusMetrics(registry=registry)
```

### FastAPI Middleware with Custom Metrics

```python
from common.monitoring import PrometheusFastAPIMiddleware

app.add_middleware(PrometheusFastAPIMiddleware, metrics=custom_metrics)
```

Or using the factory function:

```python
from common.monitoring import create_prometheus_middleware

app.add_middleware(create_prometheus_middleware(metrics=custom_metrics))
```

### gRPC Interceptor with Custom Metrics

```python
from common.monitoring import PrometheusGRPCInterceptor

interceptor = PrometheusGRPCInterceptor(metrics=custom_metrics)
server = grpc.aio.server(interceptors=[interceptor])
```

## Metrics Endpoint

The module provides a built-in metrics endpoint for FastAPI applications:

```python
from common.monitoring import metrics_endpoint

app.add_route("/metrics", metrics_endpoint, methods=["GET"])
```

This endpoint returns metrics in Prometheus format at `/metrics`.

The local endpoint for web server : http://localhost:8180/metrics

The local endpoint for grpc server: http://localhost:9091/metrics
