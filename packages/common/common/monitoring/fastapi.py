"""
FastAPI monitoring module for Prometheus metrics collection.

This module provides FastAPI-specific monitoring functionality including
middleware and metrics endpoint.
"""

from collections.abc import Callable

from fastapi import Request, Response
from fastapi.responses import PlainTextResponse
from prometheus_client import CONTENT_TYPE_LATEST
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from .base import PrometheusMetrics, get_metrics


class PrometheusFastAPIMiddleware(BaseHTTPMiddleware):
    """FastAPI middleware for collecting Prometheus metrics."""

    def __init__(
        self,
        app: ASGIApp,
        metrics: PrometheusMetrics | None = None,
        route_prefixes: list[str] | None = None,
    ):
        super().__init__(app)
        self.metrics = metrics or get_metrics()
        self.route_prefixes = route_prefixes or []

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        import time

        # Skip monitoring for non-route paths
        if not any(
            request.url.path.startswith(prefix)
            for prefix in self.route_prefixes
        ):
            return await call_next(request)

        start_time = time.time()

        # Extract method and endpoint
        method = request.method
        endpoint = request.url.path

        try:
            response = await call_next(request)
            duration = time.time() - start_time

            # Record metrics
            self.metrics.record_http_request(
                method=method,
                endpoint=endpoint,
                status_code=response.status_code,
                duration=duration,
            )

            if response.status_code >= 400:
                self.metrics.record_error("http_request", "web_server")

            return response

        except Exception:
            duration = time.time() - start_time

            # Record error metrics
            self.metrics.record_http_request(
                method=method,
                endpoint=endpoint,
                status_code=500,
                duration=duration,
            )
            self.metrics.record_error("http_request", "web_server")

            raise


def create_prometheus_middleware(
    metrics: PrometheusMetrics | None = None,
    route_prefixes: list[str] | None = None,
):
    """Create FastAPI middleware for Prometheus metrics."""

    def middleware_factory(app: ASGIApp):
        return PrometheusFastAPIMiddleware(
            app, metrics=metrics, route_prefixes=route_prefixes
        )

    return middleware_factory


def metrics_endpoint(request: Request) -> PlainTextResponse:
    """FastAPI endpoint for exposing Prometheus metrics."""
    metrics = get_metrics()
    return PlainTextResponse(
        content=metrics.get_metrics(), media_type=CONTENT_TYPE_LATEST
    )
