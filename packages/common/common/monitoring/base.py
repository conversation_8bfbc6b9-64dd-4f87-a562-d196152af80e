"""
Base Prometheus metrics collection module.

This module provides the core Prometheus metrics functionality that can be used
across different server types (FastAPI, gRPC, etc.).
"""

from collections.abc import Callable
from contextlib import contextmanager
import functools
import time

from prometheus_client import (
    CollectorRegistry,
    Counter,
    Histogram,
    generate_latest,
)


class PrometheusMetrics:
    """
    Centralized Prometheus metrics collection for the application.

    This class provides a unified interface for collecting various types of metrics
    including request counts, durations, errors, and custom business metrics.
    """

    def __init__(self, registry: CollectorRegistry | None = None):
        """
        Initialize Prometheus metrics.

        Args:
            registry: Optional custom registry. If None, uses default registry.
        """
        self.registry = registry or CollectorRegistry()

        # HTTP/FastAPI metrics
        self.http_requests_total = Counter(
            "http_requests_total",
            "Total number of HTTP requests",
            ["method", "endpoint", "status_code"],
            registry=self.registry,
        )

        self.http_request_duration_seconds = Histogram(
            "http_request_duration_seconds",
            "HTTP request duration in seconds",
            ["method", "endpoint"],
            registry=self.registry,
        )

        # gRPC metrics
        self.grpc_requests_total = Counter(
            "grpc_requests_total",
            "Total number of gRPC requests",
            ["grpc_method", "grpc_service", "grpc_code"],
            registry=self.registry,
        )

        self.grpc_request_duration_seconds = Histogram(
            "grpc_request_duration_seconds",
            "gRPC request duration in seconds",
            ["grpc_method", "grpc_service"],
            registry=self.registry,
        )

        # Error metrics
        self.errors_total = Counter(
            "errors_total",
            "Total number of errors",
            ["type", "service"],
            registry=self.registry,
        )

        # Custom business metrics
        self.business_operations_total = Counter(
            "business_operations_total",
            "Total number of business operations",
            ["operation", "status"],
            registry=self.registry,
        )

        self.business_operation_duration_seconds = Histogram(
            "business_operation_duration_seconds",
            "Business operation duration in seconds",
            ["operation", "service"],
            registry=self.registry,
        )

    def record_http_request(
        self, method: str, endpoint: str, status_code: int, duration: float
    ):
        """Record HTTP request metrics."""
        self.http_requests_total.labels(
            method=method, endpoint=endpoint, status_code=status_code
        ).inc()
        self.http_request_duration_seconds.labels(
            method=method, endpoint=endpoint
        ).observe(duration)

    def record_grpc_request(
        self, method: str, service: str, code: str, duration: float
    ):
        """Record gRPC request metrics."""
        self.grpc_requests_total.labels(
            grpc_method=method, grpc_service=service, grpc_code=code
        ).inc()
        self.grpc_request_duration_seconds.labels(
            grpc_method=method, grpc_service=service
        ).observe(duration)

    def record_error(self, error_type: str, service: str):
        """Record error metrics."""
        self.errors_total.labels(type=error_type, service=service).inc()

    def record_business_operation(self, operation: str, status: str):
        """Record business operation metrics."""
        self.business_operations_total.labels(
            operation=operation, status=status
        ).inc()

    def record_business_operation_duration(
        self, operation: str, service: str, duration: float
    ):
        """Record business operation duration metrics."""
        self.business_operation_duration_seconds.labels(
            operation=operation, service=service
        ).observe(duration)

    @contextmanager
    def track_request(self, method: str, endpoint: str):
        """Context manager for tracking HTTP requests."""
        start_time = time.time()
        try:
            yield
            self.record_http_request(
                method, endpoint, 200, time.time() - start_time
            )
        except Exception:
            self.record_http_request(
                method, endpoint, 500, time.time() - start_time
            )
            self.record_error("http_request", "web_server")
            raise

    def get_metrics(self) -> bytes:
        """Get metrics in Prometheus format."""
        return generate_latest(self.registry)


# Global metrics instance
_metrics: PrometheusMetrics | None = None


def get_metrics_registry() -> CollectorRegistry:
    """Get the global metrics registry."""
    global _metrics
    if _metrics is None:
        _metrics = PrometheusMetrics()
    return _metrics.registry


def get_metrics() -> PrometheusMetrics:
    """Get the global metrics instance."""
    global _metrics
    if _metrics is None:
        _metrics = PrometheusMetrics()
    return _metrics


def monitor_function(operation: str, service: str = "unknown"):
    """Decorator for monitoring function calls."""

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time

                metrics = get_metrics()
                metrics.record_business_operation(operation, "success")
                metrics.record_business_operation_duration(
                    operation, service, duration
                )

                return result
            except Exception:
                duration = time.time() - start_time

                metrics = get_metrics()
                metrics.record_business_operation(operation, "error")
                metrics.record_error("function_call", service)
                metrics.record_business_operation_duration(
                    operation, service, duration
                )

                raise

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time

                metrics = get_metrics()
                metrics.record_business_operation(operation, "success")
                metrics.record_business_operation_duration(
                    operation, service, duration
                )

                return result
            except Exception:
                duration = time.time() - start_time

                metrics = get_metrics()
                metrics.record_business_operation(operation, "error")
                metrics.record_error("function_call", service)
                metrics.record_business_operation_duration(
                    operation, service, duration
                )

                raise

        import asyncio

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator
