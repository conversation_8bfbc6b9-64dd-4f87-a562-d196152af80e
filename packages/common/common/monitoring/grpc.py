"""
gRPC monitoring module for Prometheus metrics collection.

This module provides gRPC-specific monitoring functionality including
interceptors for request monitoring.
"""

from collections.abc import Callable
import time

import grpc

from ..log import error
from .base import PrometheusMetrics, get_metrics


class PrometheusGRPCInterceptor(grpc.aio.ServerInterceptor):
    """gRPC interceptor for collecting Prometheus metrics."""

    def __init__(self, metrics: PrometheusMetrics | None = None):
        self.metrics = metrics or get_metrics()

    async def intercept_service(
        self,
        continuation: Callable,
        handler_call_details: grpc.HandlerCallDetails,
    ) -> grpc.RpcMethodHandler:
        method_name = handler_call_details.method
        service_name = (
            method_name.split("/")[1] if "/" in method_name else "unknown"
        )

        handler = await continuation(handler_call_details)

        # Handle different gRPC method types
        if handler.unary_unary:
            return self._wrap_unary_unary(handler, method_name, service_name)
        elif handler.unary_stream:
            return self._wrap_unary_stream(handler, method_name, service_name)
        elif handler.stream_unary:
            return self._wrap_stream_unary(handler, method_name, service_name)
        elif handler.stream_stream:
            return self._wrap_stream_stream(handler, method_name, service_name)
        else:
            # Fallback to original handler if no method type is supported
            return handler

    def _wrap_unary_unary(self, handler, method_name: str, service_name: str):
        """Wrap unary-unary gRPC method."""

        async def wrapped_handler(request, context):
            start_time = time.time()
            status_code = grpc.StatusCode.OK

            try:
                return await handler.unary_unary(request, context)
            finally:
                duration = time.time() - start_time
                status_code = context.code() or grpc.StatusCode.OK
                self._record_metrics(
                    method_name, service_name, status_code, duration
                )

        return grpc.unary_unary_rpc_method_handler(
            wrapped_handler,
            request_deserializer=handler.request_deserializer,
            response_serializer=handler.response_serializer,
        )

    def _wrap_unary_stream(self, handler, method_name: str, service_name: str):
        """Wrap unary-stream gRPC method."""

        async def wrapped_handler(request, context):
            start_time = time.time()
            status_code = grpc.StatusCode.OK

            try:
                async for response in handler.unary_stream(request, context):
                    yield response
            finally:
                duration = time.time() - start_time
                status_code = context.code() or grpc.StatusCode.OK
                self._record_metrics(
                    method_name, service_name, status_code, duration
                )

        return grpc.unary_stream_rpc_method_handler(
            wrapped_handler,
            request_deserializer=handler.request_deserializer,
            response_serializer=handler.response_serializer,
        )

    def _wrap_stream_unary(self, handler, method_name: str, service_name: str):
        """Wrap stream-unary gRPC method."""

        async def wrapped_handler(request_iterator, context):
            start_time = time.time()
            status_code = grpc.StatusCode.OK

            try:
                return await handler.stream_unary(request_iterator, context)
            finally:
                duration = time.time() - start_time
                status_code = context.code() or grpc.StatusCode.OK
                self._record_metrics(
                    method_name, service_name, status_code, duration
                )

        return grpc.stream_unary_rpc_method_handler(
            wrapped_handler,
            request_deserializer=handler.request_deserializer,
            response_serializer=handler.response_serializer,
        )

    def _wrap_stream_stream(self, handler, method_name: str, service_name: str):
        """Wrap stream-stream gRPC method."""

        async def wrapped_handler(request_iterator, context):
            start_time = time.time()
            status_code = grpc.StatusCode.OK

            try:
                async for response in handler.stream_stream(
                    request_iterator, context
                ):
                    yield response
            finally:
                duration = time.time() - start_time
                status_code = context.code() or grpc.StatusCode.OK
                self._record_metrics(
                    method_name, service_name, status_code, duration
                )

        return grpc.stream_stream_rpc_method_handler(
            wrapped_handler,
            request_deserializer=handler.request_deserializer,
            response_serializer=handler.response_serializer,
        )

    def _record_metrics(
        self,
        method_name: str,
        service_name: str,
        status_code: grpc.StatusCode,
        duration: float,
    ):
        """Record metrics for the gRPC request."""
        try:
            self.metrics.record_grpc_request(
                method=method_name,
                service=service_name,
                code=status_code.name,
                duration=duration,
            )
            if status_code != grpc.StatusCode.OK:
                self.metrics.record_error("grpc_request", "grpc_server")
        except Exception as e:
            # Don't let metrics recording errors affect the main request
            error(f"Failed to record metrics for {method_name}: {str(e)}")


def create_prometheus_interceptor(
    metrics: PrometheusMetrics | None = None,
) -> PrometheusGRPCInterceptor:
    """Create gRPC interceptor for Prometheus metrics."""
    return PrometheusGRPCInterceptor(metrics=metrics)
