"""
Monitoring module for Prometheus metrics collection.

This module provides utilities for collecting and exposing Prometheus metrics
for both gRPC and FastAPI servers.
"""

# Base functionality
from .base import (
    PrometheusMetrics,
    get_metrics,
    get_metrics_registry,
    monitor_function,
)

# FastAPI functionality
from .fastapi import (
    PrometheusFastAPIMiddleware,
    create_prometheus_middleware,
    metrics_endpoint,
)

# gRPC functionality
from .grpc import (
    PrometheusGRPCInterceptor,
    create_prometheus_interceptor,
)

__all__ = [
    # Base
    "PrometheusMetrics",
    "get_metrics_registry",
    "get_metrics",
    "monitor_function",
    # FastAPI
    "PrometheusFastAPIMiddleware",
    "create_prometheus_middleware",
    "metrics_endpoint",
    # gRPC
    "PrometheusGRPCInterceptor",
    "create_prometheus_interceptor",
]
