"""
Logging interface with GCP Cloud Logging integration.

Usage Examples:

    Basic logging:
        from common.log import info, debug, warn, error

        info("User authenticated successfully")
        debug("Processing user data", user_id=123)
        warn("Rate limit approaching", current=95, limit=100)
        error("Database connection failed", error="timeout")

    Logger with predefined fields:
        from common.log import with_fields

        user_logger = with_fields(service="auth", user_id=456)
        user_logger.info("Login attempt")
        user_logger.error("Invalid password", attempt=3)

    Trace ID tracking (any string format):
        from common.log import set_trace_id, info

        set_trace_id("1234567890abcdef")  # Any format you want
        info("Processing request", operation="get_user")
        # Log will include trace_id=1234567890abcdef

    Auto-generate trace ID:
        set_trace_id()  # Auto-generates: 32-character hex string
        info("Processing request")

Output format (GCP JSON):
    {"time": "2024-01-01T12:00:00.000Z", "severity": "INFO", "message": "User authenticated successfully"}
    {"time": "2024-01-01T12:00:01.000Z", "severity": "DEBUG", "message": "Processing user data", "user_id": 123}
    {"time": "2024-01-01T12:00:02.000Z", "severity": "INFO", "message": "Processing request", "operation": "get_user", "trace_id": "req-12345"}
"""

from abc import ABC, abstractmethod
from contextvars import ContextVar
from datetime import UTC, datetime
import inspect
import json
import logging
import os
import secrets
import sys
from typing import Any

# Context variable for trace ID tracking
trace_id: ContextVar[str | None] = ContextVar("trace_id", default=None)


def generate_trace_id() -> str:
    """Generate an OpenTelemetry-compliant trace ID (32-character hex string)."""
    return secrets.token_hex(16)


class Logger(ABC):
    """Logger interface for structured logging with context support."""

    @abstractmethod
    def with_fields(self, **kwargs: Any) -> "Logger":
        """Create a new logger with additional key-value pairs."""
        pass

    @abstractmethod
    def debug(self, msg: str, **kwargs: Any) -> None:
        """Log debug message with optional key-value pairs."""
        pass

    @abstractmethod
    def info(self, msg: str, **kwargs: Any) -> None:
        """Log info message with optional key-value pairs."""
        pass

    @abstractmethod
    def warn(self, msg: str, **kwargs: Any) -> None:
        """Log warning message with optional key-value pairs."""
        pass

    @abstractmethod
    def error(self, msg: str, **kwargs: Any) -> None:
        """Log error message with optional key-value pairs."""
        pass


class LoggerImpl(Logger):
    """Implementation of Logger interface using Python's logging module."""

    def __init__(
        self,
        base_logger: logging.Logger,
        extra_fields: dict[str, Any] | None = None,
    ):
        self.base_logger = base_logger
        self.extra_fields = extra_fields or {}

    def _get_extra_fields(self, **kwargs: Any) -> dict[str, Any]:
        """Combine base extra fields with new kwargs and trace ID."""
        extra = self.extra_fields.copy()
        extra.update(kwargs)

        # Add trace ID if available
        tid = trace_id.get()
        if tid:
            extra["trace_id"] = tid

        return extra

    def with_fields(self, **kwargs: Any) -> "Logger":
        """Create a new logger with additional key-value pairs."""
        new_extra = self.extra_fields.copy()
        new_extra.update(kwargs)
        return LoggerImpl(self.base_logger, new_extra)

    def debug(self, msg: str, **kwargs: Any) -> None:
        """Log debug message with optional key-value pairs."""
        extra = self._get_extra_fields(**kwargs)
        self.base_logger.debug(msg, extra={"fields": extra} if extra else None)

    def info(self, msg: str, **kwargs: Any) -> None:
        """Log info message with optional key-value pairs."""
        extra = self._get_extra_fields(**kwargs)
        self.base_logger.info(msg, extra={"fields": extra} if extra else None)

    def warn(self, msg: str, **kwargs: Any) -> None:
        """Log warning message with optional key-value pairs."""
        extra = self._get_extra_fields(**kwargs)
        self.base_logger.warning(
            msg, extra={"fields": extra} if extra else None
        )

    def error(self, msg: str, **kwargs: Any) -> None:
        """Log error message with optional key-value pairs."""
        extra = self._get_extra_fields(**kwargs)
        self.base_logger.error(msg, extra={"fields": extra} if extra else None)


class GCPJSONFormatter(logging.Formatter):
    """GCP Cloud Logging compatible JSON formatter."""

    def format(self, record: logging.LogRecord) -> str:
        # Create RFC3339 timestamp
        timestamp = datetime.fromtimestamp(record.created, tz=UTC)

        log_data = {
            "time": timestamp.isoformat(),
            "severity": record.levelname,
            "message": record.getMessage(),
        }

        # Add source location - find the actual caller
        source_location = self._get_actual_source_location()
        if source_location:
            log_data["sourceLocation"] = source_location

        # Add extra fields if present
        if hasattr(record, "fields") and record.fields:
            log_data.update(record.fields)

        return json.dumps(log_data, default=self._json_serializer)

    def _get_actual_source_location(self) -> dict[str, str] | None:
        """Get the actual source location by walking up the call stack."""
        try:
            # Get the current call stack
            stack = inspect.stack()

            # Walk up the stack to find the first frame that's not in logging infrastructure
            for frame_info in stack:
                filename = frame_info.filename
                function_name = frame_info.function or "unknown"

                # Skip frames from logging infrastructure
                if self._is_logging_frame(filename, function_name):
                    continue

                return {
                    "file": os.path.basename(filename),
                    "line": str(frame_info.lineno),
                    "function": function_name,
                }

            # Fallback if we can't find a good frame
            return None

        except Exception:
            # If anything goes wrong with stack inspection, return None
            return None

    def _is_logging_frame(self, filename: str, function_name: str) -> bool:
        """Check if a frame is part of the logging infrastructure and should be skipped."""
        # Names of functions that are part of the logger's internal implementation
        IMPLEMENTATION_NAMES = {
            "debug",
            "info",
            "warn",
            "error",
            "warning",
            "_get_extra_fields",
            "format",
            "_get_actual_source_location",
            "_is_logging_frame",
        }

        # Names of functions that are re-exported from __init__.py files
        REEXPORT_NAMES = {"debug", "info", "warn", "error", "with_fields"}

        # Skip frames from the logging module itself
        if filename.endswith("log.py") or filename.endswith("log/__init__.py"):
            return True

        # Skip frames from logging package imports and re-exports
        if filename.endswith("__init__.py") and function_name in REEXPORT_NAMES:
            return True

        # Skip frames from logging method implementations
        if function_name in IMPLEMENTATION_NAMES:
            return True

        # Skip standard library logging frames
        if "/logging/" in filename or filename.endswith("logging.py"):
            return True

        return False

    def _json_serializer(self, obj):
        """Safe JSON serializer that handles complex objects."""
        # Handle dataclasses with to_dict method
        if hasattr(obj, "to_dict"):
            return obj.to_dict()
        # Handle enums
        if hasattr(obj, "value"):
            return obj.value
        # Default to string representation
        return str(obj)


class GCPStreamHandler(logging.StreamHandler):
    """
    GCP-compatible stream handler that routes logs appropriately:
    - INFO, WARNING, DEBUG → stdout (for GCP Cloud Logging)
    - ERROR, CRITICAL → stderr
    """

    def __init__(self):
        super().__init__()
        self.setFormatter(GCPJSONFormatter())

    def emit(self, record):
        """Route logs to appropriate stream based on level."""
        if record.levelno >= logging.ERROR:
            self.stream = sys.stderr
        else:
            self.stream = sys.stdout
        super().emit(record)


# Default logger setup
_default_logger = logging.getLogger("vibe-automation")
_default_logger.setLevel(logging.INFO)

# Console handler with GCP JSON formatter and stream routing
if not _default_logger.handlers:
    handler = GCPStreamHandler()
    _default_logger.addHandler(handler)


def configure_uvicorn_logging():
    """Configure uvicorn/FastAPI logging to use our structured logger."""
    # Configure uvicorn loggers to use our handler
    uvicorn_loggers = ["uvicorn", "uvicorn.access", "uvicorn.error"]

    for logger_name in uvicorn_loggers:
        logger = logging.getLogger(logger_name)
        logger.handlers.clear()
        logger.addHandler(GCPStreamHandler())
        logger.propagate = False


default_logger = LoggerImpl(_default_logger)


# Package-level logging functions
def with_fields(**kwargs: Any) -> Logger:
    """Create a new logger with additional key-value pairs."""
    return default_logger.with_fields(**kwargs)


def debug(msg: str, **kwargs: Any) -> None:
    """Log debug message with optional key-value pairs."""
    default_logger.debug(msg, **kwargs)


def info(msg: str, **kwargs: Any) -> None:
    """Log info message with optional key-value pairs."""
    default_logger.info(msg, **kwargs)


def warn(msg: str, **kwargs: Any) -> None:
    """Log warning message with optional key-value pairs."""
    default_logger.warn(msg, **kwargs)


def error(msg: str, **kwargs: Any) -> None:
    """Log error message with optional key-value pairs."""
    default_logger.error(msg, **kwargs)


def set_trace_id(tid: str | None = None) -> None:
    """Set trace ID for current context. Generates simple ID if not provided."""
    if tid is None:
        tid = generate_trace_id()
    trace_id.set(tid)


def get_trace_id() -> str | None:
    """Get trace ID from current context."""
    return trace_id.get()
