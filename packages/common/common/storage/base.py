from abc import ABC, abstractmethod


class StorageClient(ABC):
    """Abstract base class for storage clients."""

    @abstractmethod
    async def upload(
        self,
        bucket: str,
        path: str,
        content: bytes,
        metadata: dict[str, str] | None = None,
    ) -> None:
        """
        Upload a file to storage.

        Args:
            bucket: The name of the bucket to upload the file to.
            path: The path to the file to upload.
            content: The content of the file in bytes to upload.
            metadata: Optional metadata of the file to upload.

        Raises:
            RuntimeError: If the upload fails.
        """
        pass

    @abstractmethod
    async def download(
        self,
        bucket: str,
        path: str,
    ) -> bytes:
        """
        Download a file from storage.

        Args:
            bucket: The name of the bucket to download the file from.
            path: The path to the file to download.

        Returns:
            bytes: The content of the file in bytes.

        Raises:
            ValueError: If the file does not exist.
            RuntimeError: If the download fails.
        """
        pass

    @abstractmethod
    async def exists(
        self,
        bucket: str,
        path: str,
    ) -> bool:
        """
        Check if a file exists in storage.

        Args:
            bucket: The name of the bucket to check if the file exists in.
            path: The path to the file to check if it exists.

        Returns:
            bool: True if the file exists, False otherwise.

        Raises:
            RuntimeError: If the check fails.
        """
        pass

    @abstractmethod
    async def get_signed_url(
        self,
        bucket: str,
        path: str,
        expiration: int = 3600,
    ) -> str:
        """
        Get a signed URL for a file in storage.

        Args:
            bucket: The name of the bucket to get the signed URL for.
            path: The path to the file to get the signed URL for.
            expiration: The expiration time of the signed URL in seconds.

        Returns:
            str: The signed URL for the file.

        Raises:
            RuntimeError: If the signed URL generation fails.
        """
        pass
