from common.storage.base import StorageClient


class MockStorageClient(StorageClient):
    _mock_data: dict[str, any] = {}

    def __init__(self, extra_mock_data: dict[str, any] | None = None):
        if extra_mock_data:
            self._mock_data.update(extra_mock_data)

    async def upload(
        self,
        bucket: str,
        path: str,
        content: bytes,
        metadata: dict[str, str] | None = None,
    ) -> None:
        return None

    async def download(self, bucket: str, path: str) -> bytes:
        if "download" in self._mock_data and isinstance(
            self._mock_data["download"], bytes
        ):
            return self._mock_data["download"]

        return b""

    async def exists(self, bucket: str, path: str) -> bool:
        if "exists" in self._mock_data and isinstance(
            self._mock_data["exists"], bool
        ):
            return self._mock_data["exists"]

        return False

    async def get_signed_url(
        self, bucket: str, path: str, expiration: int = 3600
    ) -> str:
        if "get_signed_url" in self._mock_data and isinstance(
            self._mock_data["get_signed_url"], str
        ):
            return self._mock_data["get_signed_url"]

        return "fake_signed_url"
