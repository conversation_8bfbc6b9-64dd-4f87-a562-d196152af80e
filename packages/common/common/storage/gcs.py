import os

from google.auth import default
from google.auth.impersonated_credentials import (
    Credentials as ImpersonatedCredentials,
)
from google.cloud import storage

from common.log import warn
from common.storage.base import StorageClient
from common.utils.singleton import Singleton


@Singleton
class GCSClient(StorageClient):
    """
    Singleton Google Cloud Storage client.

    Example to instantiate a new client:
    ```python
    gcs_client = GCSClient.instance()
    ```
    We will not allow to instantiate a new client using the constructor.
    """

    def __init__(self):
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT_ID", None)
        service_account_email = os.getenv("DEFAULT_SERVICE_ACCOUNT", "")
        self.client = storage.Client(
            project=project_id,
            credentials=self._impersonate_service_account(
                service_account_email
            ),
        )

    def _impersonate_service_account(self, service_account_email: str):
        """Impersonate a service account"""
        # Please note in order to impersonate a service account, you need to have the "Service Account Token Creator" role on the service account you are impersonating.
        default_credentials, _ = default()
        impersonated_credentials = ImpersonatedCredentials(
            source_credentials=default_credentials,
            target_principal=service_account_email,
            target_scopes=[
                "https://www.googleapis.com/auth/devstorage.full_control"
            ],
        )
        return impersonated_credentials

    async def upload(
        self,
        bucket: str,
        path: str,
        content: bytes,
        metadata: dict[str, str] | None = None,
    ) -> None:
        try:
            bucket_obj = self.client.bucket(bucket)
            blob = bucket_obj.blob(path)

            # Set metadata if provided
            if metadata:
                blob.metadata = metadata

            # Upload the bytes content
            blob.upload_from_string(content)

        except Exception as e:
            raise RuntimeError(
                f"Failed to upload file to gs://{bucket}/{path}: {str(e)}"
            ) from e

    async def download(
        self,
        bucket: str,
        path: str,
    ) -> bytes:
        try:
            # Check if the file exists before downloading
            is_exist = await self.exists(bucket, path)
            if not is_exist:
                raise ValueError(f"File {bucket}/{path} does not exist")

            bucket_obj = self.client.bucket(bucket)
            blob = bucket_obj.blob(path)
            return blob.download_as_bytes()
        except ValueError:
            raise
        except Exception as e:
            raise RuntimeError(
                f"Failed to download file from gs://{bucket}/{path}: {str(e)}"
            ) from e

    async def exists(
        self,
        bucket: str,
        path: str,
    ) -> bool:
        try:
            bucket_obj = self.client.bucket(bucket)
            blob = bucket_obj.blob(path)
            return blob.exists()

        except Exception as e:
            raise RuntimeError(
                f"Failed to check existence of gs://{bucket}/{path}: {str(e)}"
            ) from e

    async def get_signed_url(
        self, bucket: str, path: str, expiration: int = 3600
    ) -> str:
        try:
            bucket_obj = self.client.bucket(bucket)
            blob = bucket_obj.blob(path)
            signed_url_kwargs = {
                "version": "v4",
                "expiration": expiration,
                "method": "GET",
            }
            return blob.generate_signed_url(**signed_url_kwargs)
        except Exception as e:
            raise RuntimeError(
                f"Failed to get signed URL for gs://{bucket}/{path}: {str(e)}"
            ) from e
    

    async def generate_signed_url(self, gcs_url: str) -> str:
        """Convert a gs:// URL to a signed URL using the GCS client.

        Args:
            gcs_url: The original GCS URL in the format gs://bucket/path/to/object

        Returns:
            A time-limited signed URL that can be used to download the object.
        """

        if not gcs_url.startswith("gs://"):
            # Not a GCS URL – return as-is.
            return gcs_url

        # Strip scheme and split the remainder into bucket and path
        path_without_scheme = gcs_url[5:]
        bucket, _, blob_path = path_without_scheme.partition("/")

        if not bucket or not blob_path:
            # Malformed URL – return original to avoid breaking existing behaviour.
            return gcs_url

        try:
            signed_url = await self.get_signed_url(bucket, blob_path)
            return signed_url
        except Exception as e:
            # Log and fall back to the original URL if signed URL generation fails
            warn(f"Failed to generate signed URL for {gcs_url}: {e}")
            return gcs_url


def get_gcs_client():
    """Helper function to get the gcs client"""
    return GCSClient.instance()
