from typing import Any, TypeVar

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorCollection

from common.db.mongo_client import get_mongo_client
from common.log import error
from common.models.organization import TenantInfo

RepositoryType = TypeVar("RepositoryType")


class BaseRepository[RepositoryType]:
    """
    Base repository class providing common CRUD operations with multi-tenant support using motor
    """

    def __init__(self, model_class: type[RepositoryType]):
        self.model_class = model_class

    async def _get_mongo_client(self):
        """Get MongoDB client instance"""
        return await get_mongo_client()

    async def _get_collection(
        self, tenant_info: TenantInfo | None = None
    ) -> AsyncIOMotorCollection:
        """Get the collection for this model"""
        mongo_client = await self._get_mongo_client()
        # Get the database name for the tenant, TODO: Add redis cache here
        database_name = None
        if tenant_info:
            database_name = tenant_info.get_database_name(self.model_class)
        # Get the database for the tenant
        database = mongo_client.get_database(database_name)

        return database[self.model_class.get_collection_name()]

    async def get_by_id(
        self, doc_id: ObjectId, tenant_info: TenantInfo | None = None
    ) -> RepositoryType | None:
        """Get document by ID"""
        try:
            collection = await self._get_collection(tenant_info)
            document = await collection.find_one({"_id": doc_id})

            if document:
                return self.model_class(**document)
            return None
        except Exception as e:
            error(
                f"Failed to get {self.model_class.__name__} by ID",
                document_id=str(doc_id),
                error=str(e),
            )
            raise e

    async def find_one(
        self,
        filter: dict[str, Any],
        tenant_info: TenantInfo | None = None,
    ) -> RepositoryType | None:
        """Get document by filter"""
        try:
            collection = await self._get_collection(tenant_info)
            document = await collection.find_one(filter)

            if document:
                return self.model_class(**document)
            return None
        except Exception as e:
            error(
                f"Failed to get {self.model_class.__name__} by filter",
                filter=filter,
                error=str(e),
            )
            raise e

    async def insert_one(
        self,
        document: dict[str, Any],
        tenant_info: TenantInfo,
    ) -> ObjectId:
        """Insert one document"""
        try:
            collection = await self._get_collection(tenant_info)
            result = await collection.insert_one(document)
            return result.inserted_id
        except Exception as e:
            error(
                f"Failed to insert {self.model_class.__name__}",
                error=str(e),
            )
            raise e

    async def update_one(
        self,
        filter: dict[str, Any],
        update: dict[str, Any],
        tenant_info: TenantInfo,
    ) -> bool:
        """Update one document"""
        try:
            collection = await self._get_collection(tenant_info)
            result = await collection.update_one(filter, update)
            return result.modified_count > 0
        except Exception as e:
            error(
                f"Failed to update {self.model_class.__name__}",
                filter=filter,
                update=update,
                error=str(e),
            )
            raise e

    async def delete_one(
        self,
        filter: dict[str, Any],
        tenant_info: TenantInfo,
    ) -> bool:
        """Delete one document"""
        try:
            collection = await self._get_collection(tenant_info)
            result = await collection.delete_one(filter)
            return result.deleted_count > 0
        except Exception as e:
            error(
                f"Failed to delete {self.model_class.__name__}",
                filter=filter,
                error=str(e),
            )
            raise e
