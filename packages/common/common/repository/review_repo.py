from datetime import UTC, datetime

from bson import ObjectId

from common.log import error
from common.models.reviews import Review, ReviewStatus
from common.repository.base import BaseRepository


class ReviewRepository(BaseRepository[Review]):
    def __init__(self):
        super().__init__(Review)

    async def create_review(
        self,
        execution_id: str,
        user_message: str = "",
        user_response: str | None = None,
    ) -> ObjectId | None:
        """Create a new review and return its ID"""
        review = Review(
            execution_id=execution_id,
            user_message=user_message,
            status=ReviewStatus.PENDING.value,
            user_response=user_response,
            created_at=datetime.now(UTC),
        )

        try:
            collection = await self._get_collection()
            review_dict = review.model_dump(by_alias=True, mode="python")
            result = await collection.insert_one(review_dict)
            return result.inserted_id
        except Exception as e:
            error(f"Failed to create review: {e}")
            return None

    async def update_review_status(
        self,
        review_id: ObjectId,
        status: str = ReviewStatus.READY.value,
    ) -> bool:
        """Update the status of a review"""
        update_fields = {
            "status": status,
            "completed_at": datetime.now(UTC),
        }

        try:
            collection = await self._get_collection()
            result = await collection.update_one(
                {"_id": review_id}, {"$set": update_fields}
            )
            return result.modified_count > 0
        except Exception as e:
            error(f"Failed to update review status: {e}")
            return False

    async def get_review_status(
        self,
        review_id: ObjectId,
    ) -> str | None:
        """Get the status of a review by its ID"""
        try:
            review = await self.get_by_id(review_id)
            return review.status if review else None
        except Exception as e:
            error(f"Failed to get review status: {e}")
            return None
