from datetime import UTC, datetime

from bson import ObjectId

from ..models.execution import Execution
from ..models.organization import TenantInfo
from .base import BaseRepository


class ExecutionRepository(BaseRepository[Execution]):
    """Repository for Execution model with tenant-aware operations"""

    def __init__(self):
        super().__init__(Execution)

    async def create_execution(
        self, execution: Execution, tenant_info: TenantInfo | None = None
    ) -> Execution:
        """Create an execution"""
        collection = await self._get_collection(tenant_info)

        # set created_at
        execution.timestamps.created_at = datetime.now(UTC)

        result = await collection.insert_one(execution.model_dump())

        # The insert returns only the generated _id. Re-use the original
        # execution object, populate its id with the inserted ObjectId and
        # return it so the caller gets a fully populated model instance.
        execution.id = result.inserted_id  # type: ignore[attr-defined]
        return execution

    async def get_execution_by_id(
        self, execution_id: ObjectId, tenant_info: TenantInfo | None = None
    ) -> Execution | None:
        """Get an execution by its ID"""
        collection = await self._get_collection(tenant_info)
        document = await collection.find_one(
            {"_id": execution_id, "org_id": tenant_info.org.id}
        )
        if not document:
            return None
        return Execution(**document)

    async def delete_execution(
        self, execution_id: ObjectId, tenant_info: TenantInfo | None = None
    ) -> bool:
        """Delete an execution"""
        collection = await self._get_collection(tenant_info)
        result = await collection.delete_one(
            {"_id": execution_id, "org_id": tenant_info.org.id}
        )
        return result.deleted_count > 0

    async def list_executions(
        self,
        workflow_id: ObjectId,
        limit,
        offset,
        tenant_info: TenantInfo | None = None,
    ) -> list[Execution]:
        """List executions by workflow ID and status with pagination"""
        collection = await self._get_collection(tenant_info)
        query = {
            "workflow_id": workflow_id,
            "org_id": tenant_info.org.id,
        }

        list_executions = (
            await collection.find(query)
            .skip(offset)
            .limit(limit)
            .sort("_id", -1)
            .to_list()
        )
        return [Execution(**doc) for doc in list_executions]

    async def count_executions(
        self,
        workflow_id: ObjectId,
        tenant_info: TenantInfo | None = None,
    ) -> int:
        """Count executions by workflow ID"""
        collection = await self._get_collection(tenant_info)
        return await collection.count_documents(
            {
                "workflow_id": workflow_id,
                "org_id": tenant_info.org.id,
            }
        )

    async def update_execution(
        self,
        execution_id: ObjectId,
        tenant_info: TenantInfo | None = None,
        status: str | None = None,
        browser_service: dict | None = None,
        rrweb_recording_gcs_uri: str | None = None,
    ) -> Execution:
        """Update an execution by its ID"""
        collection = await self._get_collection(tenant_info)

        update_data = {}
        if status:
            update_data["status"] = status

        if browser_service is not None:
            update_data["browser_service"] = browser_service

        # Add rrweb recording fields if provided
        if rrweb_recording_gcs_uri is not None:
            update_data["rrweb_recording_gcs_uri"] = rrweb_recording_gcs_uri

        updated_doc = await collection.find_one_and_update(
            {"_id": execution_id, "org_id": tenant_info.org.id},
            {"$set": update_data},
            return_document=True,
        )

        if not updated_doc:
            raise ValueError(f"Execution with id {execution_id} not found")

        return Execution(**updated_doc)
