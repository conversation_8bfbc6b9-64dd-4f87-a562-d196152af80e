from bson import ObjectId

from common.models.execution import ExecutionLog
from common.repository.base import BaseRepository


class ExecutionLogRepository(BaseRepository[ExecutionLog]):
    """Repository for ExecutionLog model with tenant-aware operations"""

    def __init__(self):
        super().__init__(ExecutionLog)

    async def get_execution_logs(
        self, execution_id: str, page_number: int, page_size: int
    ) -> tuple[list[ExecutionLog], int]:
        """
        Get paginated execution logs.

        Args:
            execution_id: The id of the execution
            page_number: The page number
            page_size: The page size

        Returns:
            A tuple containing:
                - A list of execution logs
                - The total number of execution logs
        """
        collection = await self._get_collection()
        query = {"execution_id": execution_id}
        sort = [("timestamp", -1)]
        skip = (page_number - 1) * page_size
        limit = page_size

        logs_response = (
            await collection.find(query).sort(sort).skip(skip).to_list(limit)
        )
        logs = [ExecutionLog(**log) for log in logs_response]
        total_size = await collection.count_documents(query)

        return logs, total_size

    async def append_execution_log(
        self, execution_log: ExecutionLog
    ) -> ObjectId:
        """
        Append an execution log to the database.

        Args:
            execution_log: The execution log to append

        Returns:
            The id of the appended execution log
        """

        collection = await self._get_collection()
        execution_log_dict = execution_log.model_dump(
            by_alias=True, mode="python"
        )
        result = await collection.insert_one(execution_log_dict)
        return result.inserted_id
