from common.log import error, info
from common.models.proxy_configuration import ProxyConfigurationEntry

from .base import BaseRepository


class ProxyConfigurationRepository(BaseRepository[ProxyConfigurationEntry]):
    """Repository for managing proxy configuration entries in MongoDB"""

    def __init__(self):
        super().__init__(ProxyConfigurationEntry)

    async def get_all_entries(
        self,
    ) -> list[ProxyConfigurationEntry]:
        """
        Get all proxy configuration entries.

        Returns:
            List of proxy configuration entries
        """
        try:
            collection = await self._get_collection()
            cursor = collection.find({}).sort("_id", -1)

            entries = []
            async for document in cursor:
                entries.append(ProxyConfigurationEntry(**document))

            info(f"Retrieved {len(entries)} proxy configuration entries")
            return entries

        except Exception as e:
            error(f"Failed to get proxy configuration entries: {e}")
            raise
