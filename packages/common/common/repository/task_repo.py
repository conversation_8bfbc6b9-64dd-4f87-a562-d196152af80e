from datetime import UTC, datetime

from bson import ObjectId

from common.log import error
from common.models.organization import TenantInfo
from common.models.task import (
    Task,
    TaskStatus,
    TaskTimestamps,
    TaskUsers,
)
from common.repository.base import BaseRepository


class TaskRepository(BaseRepository[Task]):
    def __init__(self):
        super().__init__(Task)

    async def create_task(
        self,
        tenant_info: TenantInfo,
        workflow_id: ObjectId,
        identify_key: str,
        execution_id: ObjectId,
        display_name: str,
        description: str,
        creator_id: ObjectId,
    ) -> ObjectId | None:
        task = Task(
            org_id=tenant_info.org.id,
            workflow_id=workflow_id,
            execution_id=execution_id,
            identify_key=identify_key,
            display_name=display_name,
            description=description,
            status=TaskStatus.PENDING,
            users=TaskUsers(
                creator_id=creator_id,
            ),
            timestamps=TaskTimestamps(
                created_at=datetime.now(UTC),
                updated_at=datetime.now(UTC),
            ),
        )

        try:
            # Use model serialization with context-aware ObjectId handling
            task_dict = task.model_dump(by_alias=True, mode="python")
            return await self.insert_one(task_dict, tenant_info)
        except Exception as e:
            error(f"Failed to create task: {e}")
            return None

    async def update_task(
        self,
        tenant_info: TenantInfo,
        task_id: ObjectId,
        status: TaskStatus | None = None,
    ) -> bool:
        update_data = {
            "timestamps.updated_at": datetime.now(UTC),
        }

        if status:
            update_data["status"] = status

        try:
            return await self.update_one(
                {"_id": task_id, "org_id": tenant_info.org.id},
                {"$set": update_data},
                tenant_info,
            )
        except Exception as e:
            error(f"Failed to update task: {e}")
            return False

    async def delete_task(
        self,
        tenant_info: TenantInfo,
        task_id: ObjectId,
    ) -> bool:
        try:
            result = await self.delete_one(
                {"_id": task_id, "org_id": tenant_info.org.id},
                tenant_info,
            )
            return result
        except Exception as e:
            error(f"Failed to delete task: {e}")
            return False

    async def get_task_by_id(
        self,
        tenant_info: TenantInfo,
        task_id: ObjectId,
    ) -> Task | None:
        try:
            return await self.find_one(
                {"_id": task_id, "org_id": tenant_info.org.id},
                tenant_info,
            )
        except Exception as e:
            error("Failed to get task", id=str(task_id), error=str(e))
            return None

    async def list_tasks(
        self,
        tenant_info: TenantInfo,
        limit: int,
        offset: int,
        filter: dict | None = None,
    ) -> list[Task]:
        if filter is None:
            filter = {}
        filter = {"org_id": tenant_info.org.id, **filter}
        collection = await self._get_collection(tenant_info)

        cursor = (
            collection.find(filter).skip(offset).limit(limit).sort("_id", -1)
        )

        docs = await cursor.to_list(length=limit)
        return [Task(**doc) for doc in docs]

    async def count_tasks(
        self,
        tenant_info: TenantInfo,
        filter: dict | None = None,
    ) -> int:
        if filter is None:
            filter = {}
        filter = {"org_id": tenant_info.org.id, **filter}
        collection = await self._get_collection(tenant_info)
        return await collection.count_documents(filter)
