from bson import ObjectId

from common.log import error
from common.models.user import User

from .base import BaseRepository


class UserRepository(BaseRepository[User]):
    def __init__(self):
        super().__init__(User)

    async def get_user_by_email(self, email: str) -> User | None:
        # User collection is shared across all tenants
        collection = await self._get_collection()
        # Get the user by email
        try:
            user = await collection.find_one({"email": email})
            if user:
                user_obj = User(**user)
                # If the full name is not set, set it to the first name and last name
                if user_obj.full_name is None and user_obj.first_name:
                    user_obj.full_name = (
                        f"{user_obj.first_name} {user_obj.last_name}"
                    )
                return user_obj
            return None
        except Exception as e:
            error(f"Failed to get user by email: {e}")
            raise e

    async def get_users_by_ids(self, user_ids: list[ObjectId]) -> list[User]:
        """
        Get multiple users by their IDs.
        
        Args:
            user_ids: List of user ObjectIds
            
        Returns:
            List of User objects (may be fewer than input if some IDs not found)
        """
        if not user_ids:
            return []
        
        # User collection is shared across all tenants
        collection = await self._get_collection()
        
        try:
            cursor = collection.find({"_id": {"$in": user_ids}})
            users = []
            
            async for user_doc in cursor:
                user_obj = User(**user_doc)
                # If the full name is not set, set it to the first name and last name
                if user_obj.full_name is None and user_obj.first_name:
                    user_obj.full_name = (
                        f"{user_obj.first_name} {user_obj.last_name}"
                    )
                users.append(user_obj)
            
            return users
        except Exception as e:
            error(f"Failed to get users by IDs: {e}")
            raise e
