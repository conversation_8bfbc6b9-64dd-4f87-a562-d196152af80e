import asyncio

from bson import ObjectId

from common.log import info
from common.sandbox.base import RuntimeConfig, RuntimeType
from common.sandbox.impl.local import LocalRuntime
from common.services.tenant_service import TenantService


def run_anything() -> None:
    runtime = LocalRuntime(
        config=RuntimeConfig(
            run_type=RuntimeType.LOCAL,
            session_id="test_anything",
        )
    )

    async def run_command() -> None:
        await runtime.initialize()
        await runtime.run_command(
            "bash",
            ["-c", 'for i in {1..5}; do echo "Message #$i"; sleep 1; done'],
        )
        await runtime.stop()

    asyncio.run(run_command())


async def test_mongo_client():
    tenant_svc = TenantService()
    tenant_info = await tenant_svc.get_tenant_info(
        ObjectId("641aae5b328566b887c424ff")
    )
    info("Tenant info retrieved", tenant_info=str(tenant_info))


if __name__ == "__main__":
    asyncio.run(test_mongo_client())
