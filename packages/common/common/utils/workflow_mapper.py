"""
Utility functions for converting Workflow domain models to protobuf messages.
"""

from google.protobuf.json_format import ParseDict
from orby.va.workflow_pb2 import Workflow

from common.models.user import User
from common.models.workflow import Workflow as WorkflowModel
from common.models.workflow import WorkflowStatus
from common.utils.user_profile_mapper import user_to_profile_info


def convert_workflow_model_to_proto(
    workflow: WorkflowModel,
    user_info: User | None = None,
    current_user_id: str | None = None,
) -> Workflow:
    """
    Convert domain workflow model to protobuf message.

    Args:
        workflow: Domain workflow model
        user_info: Optional user info for creator field
        current_user_id: Optional current user ID to populate current_user_thread_id

    Returns:
        Workflow protobuf message
    """
    # Convert workflow model to dict using model_dump
    workflow_dict = workflow.model_dump(mode="json", by_alias=False)

    # Remove fields that don't exist in the protobuf or need special handling
    workflow_dict.pop("thread_id", None)
    workflow_dict.pop("bb_context_id", None)
    workflow_dict.pop("creator_id", None)  # We'll handle creator separately
    workflow_dict.pop(
        "status", None
    )  # We'll handle status enum mapping manually
    workflow_dict.pop("user_thread_mapping", None)

    # Convert to protobuf using ParseDict
    proto_workflow = ParseDict(workflow_dict, Workflow())

    # Handle status enum mapping manually

    if workflow.status == WorkflowStatus.DRAFT:
        proto_workflow.status = Workflow.Status.STATUS_DRAFT
    elif workflow.status == WorkflowStatus.COMPLETED:
        proto_workflow.status = Workflow.Status.STATUS_COMPLETED
    else:
        proto_workflow.status = Workflow.Status.STATUS_UNSPECIFIED

    # Add creator info if available
    if user_info:
        user_profile = user_to_profile_info(user_info)
        if user_profile:
            proto_workflow.creator.CopyFrom(user_profile)

    # Set current user's thread ID if available
    print(f" ------ Checking workflow {workflow.id} for user {current_user_id}")
    print(f" ------ workflow.user_thread_mapping: {getattr(workflow, 'user_thread_mapping', 'NONE')}")
    if current_user_id and workflow.user_thread_mapping:
        current_user_thread_id = workflow.user_thread_mapping.get(current_user_id)
        print(f" ------ Found current_user_thread_id: {current_user_thread_id}")
        if current_user_thread_id:
            proto_workflow.current_user_thread_id = current_user_thread_id
            print(f" ------ Set proto_workflow.current_user_thread_id to: {proto_workflow.current_user_thread_id}")
    else:
        print(f" ------ No mapping found: current_user_id={current_user_id}, has_mapping={bool(workflow.user_thread_mapping)}")

    return proto_workflow
