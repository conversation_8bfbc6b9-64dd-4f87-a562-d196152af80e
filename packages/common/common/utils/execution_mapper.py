from google.protobuf.json_format import ParseDict
from orby.va.public.execution_messages_pb2 import Execution

from common.models.execution import Execution as ExecutionModel


def execution_model_to_proto(execution: ExecutionModel) -> Execution:
    """Convert ExecutionModel to Execution proto message."""

    execution_dict = execution.model_dump(mode="json", by_alias=False)
    return ParseDict(execution_dict, Execution())
