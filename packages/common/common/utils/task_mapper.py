from bson import ObjectId
from orby.va.task_pb2 import Task, TaskStatus, TaskTimestamps, TaskUsers
from pytz import UTC

from common.models.task import Task as TaskModel
from common.models.task import TaskStatus as TaskStatusModel
from common.models.task import TaskUsers as TaskUsersModel


def convert_task_status_model_to_proto(status: TaskStatusModel) -> TaskStatus:
    """Convert TaskStatus model enum to TaskStatus proto enum."""
    match status:
        case TaskStatusModel.PENDING:
            return TaskStatus.TASK_STATUS_PENDING
        case TaskStatusModel.FAILED:
            return TaskStatus.TASK_STATUS_FAILED
        case TaskStatusModel.COMPLETED:
            return TaskStatus.TASK_STATUS_COMPLETED
        case _:
            return TaskStatus.TASK_STATUS_UNSPECIFIED


def convert_task_status_proto_to_model(
    status: TaskStatus,
) -> TaskStatusModel | None:
    """Convert TaskStatus proto enum to TaskStatus model enum."""
    match status:
        case TaskStatus.TASK_STATUS_PENDING:
            return TaskStatusModel.PENDING
        case TaskStatus.TASK_STATUS_FAILED:
            return TaskStatusModel.FAILED
        case TaskStatus.TASK_STATUS_COMPLETED:
            return TaskStatusModel.COMPLETED
        case _:
            return None


def task_model_to_proto(task: TaskModel) -> Task:
    """Convert Task model to Task proto message."""

    # Convert timestamps
    timestamps = None
    if task.timestamps:
        timestamps = TaskTimestamps()
        if task.timestamps.created_at:
            timestamps.created_at.FromDatetime(task.timestamps.created_at)
        if task.timestamps.updated_at:
            timestamps.updated_at.FromDatetime(task.timestamps.updated_at)

    # Convert users
    users = None
    if task.users and task.users.creator_id:
        users = TaskUsers(creator_id=str(task.users.creator_id))

    # Convert status enum
    status_proto = convert_task_status_model_to_proto(task.status)

    return Task(
        id=str(task.id),
        org_id=str(task.org_id),
        workflow_id=str(task.workflow_id),
        identify_key=task.identify_key,
        execution_id=str(task.execution_id),
        display_name=task.display_name,
        description=task.description,
        status=status_proto,
        users=users,
        timestamps=timestamps,
    )


def task_proto_to_model(task_proto: Task) -> TaskModel:
    """Convert Task proto message to Task model."""

    # Convert timestamps
    timestamps = None
    if task_proto.HasField("timestamps"):
        from common.models.task import TaskTimestamps

        timestamps = TaskTimestamps()

        if task_proto.timestamps.HasField("created_at"):
            timestamps.created_at = (
                task_proto.timestamps.created_at.ToDatetime().replace(
                    tzinfo=UTC
                )
            )

        if task_proto.timestamps.HasField("updated_at"):
            timestamps.updated_at = (
                task_proto.timestamps.updated_at.ToDatetime().replace(
                    tzinfo=UTC
                )
            )

    # Convert users
    users = None
    if task_proto.HasField("users") and task_proto.users.creator_id:
        users = TaskUsersModel(creator_id=ObjectId(task_proto.users.creator_id))

    # Convert status enum
    status_model = convert_task_status_proto_to_model(task_proto.status)
    if status_model is None:
        status_model = TaskStatusModel.PENDING  # Default fallback

    return TaskModel(
        _id=ObjectId(task_proto.id),
        org_id=ObjectId(task_proto.org_id),
        workflow_id=ObjectId(task_proto.workflow_id),
        identify_key=task_proto.identify_key,
        execution_id=ObjectId(task_proto.execution_id),
        display_name=task_proto.display_name,
        description=task_proto.description,
        status=status_model,
        users=users,
        timestamps=timestamps,
    )
