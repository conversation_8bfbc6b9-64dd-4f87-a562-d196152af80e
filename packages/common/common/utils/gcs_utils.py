import urllib.parse

from common.log import error, warn
from common.storage.gcs import get_gcs_client


def convert_signed_to_gcs(url: str) -> str:
    """Convert signed GCS URL to gs:// URI format.

    Args:
        url: The URL in one of these formats:
            - gs://bucket/path/to/object (already correct format)
            - https://storage.googleapis.com/bucket/path/to/object?X-Goog-Algorithm=... (signed URL)

    Returns:
        GCS URI in gs://bucket/path format, or original URL if conversion fails
    """
    if not url:
        error(f"Invalid URL provided: {url}")
        return url

    # Already in gs:// format
    if url.startswith("gs://"):
        return url

    # Handle signed Google Cloud Storage URLs only
    if "storage.googleapis.com" in url and "X-Goog-Algorithm" in url:
        try:
            parsed_url = urllib.parse.urlparse(url)

            if parsed_url.netloc == "storage.googleapis.com":
                # Extract bucket and path from URL path
                # Format: /bucket/path/to/object
                path_parts = parsed_url.path.lstrip("/").split("/", 1)
                if len(path_parts) >= 2:
                    bucket = path_parts[0]
                    blob_path = path_parts[1]
                    # URL decode the blob path in case it was encoded
                    blob_path = urllib.parse.unquote(blob_path)
                    return f"gs://{bucket}/{blob_path}"
                elif len(path_parts) == 1:
                    # Only bucket, no path
                    bucket = path_parts[0]
                    return f"gs://{bucket}/"

            error(f"Unsupported storage URL format: {url}")
            return url
        except Exception as e:
            error(f"Failed to parse storage URL {url}: {e}")
            return url

    # Unsupported format
    error(
        f"Unsupported URL format. Only gs:// and signed storage.googleapis.com URLs are supported: {url}"
    )
    return url


async def generate_signed_url(gcs_url: str) -> str:
    """Convert a gs:// URL or existing signed URL to a fresh signed URL using the shared GCS client.

    Args:
        gcs_url: The GCS URL in one of these formats:
            - gs://bucket/path/to/object
            - https://storage.googleapis.com/bucket/path/to/object?X-Goog-Algorithm=...

    Returns:
        A time-limited signed URL that can be used to download the object, or
        the original URL if it cannot be processed.
    """
    if not gcs_url:
        warn(f"Invalid GCS URL provided: {gcs_url}")
        return gcs_url

    try:
        # Convert to standard gs:// format
        gcs_uri = convert_signed_to_gcs(gcs_url)

        # If conversion failed, return original
        if not gcs_uri.startswith("gs://"):
            return gcs_url

        # Extract bucket and blob path from gs:// URI
        path_without_scheme = gcs_uri[5:]
        bucket, _, blob_path = path_without_scheme.partition("/")

        # Validate extracted components
        if not bucket:
            error(f"Could not extract bucket from URL: {gcs_url}")
            return gcs_url

        if not blob_path:
            error(f"Could not extract blob path from URL: {gcs_url}")
            return gcs_url

        gcs_client = get_gcs_client()
        signed_url = await gcs_client.get_signed_url(bucket, blob_path)

        return signed_url

    except Exception as e:
        error(f"Failed to generate signed URL for {gcs_url}: {e}")
        return gcs_url
