from bson import ObjectId

from common.models.auth import AuthPayload


def get_auth_payload_from_http_headers(headers: dict[str, str]) -> AuthPayload:
    """
    Extract auth payload from HTTP request headers.

    Args:
        headers: The HTTP headers from the request.

    Returns:
        AuthPayload: The auth payload model extracted from the headers.

    Raises:
        ValueError: If the user_id, org_id, or session_id is not a valid ObjectId.
    """
    user_id: ObjectId | None = None
    org_id: ObjectId | None = None
    session_id: ObjectId | None = None
    user_email: str | None = headers.get(_USERNAME_HEADER_KEY)
    auth_method: str | None = headers.get(_AUTH_METHOD_HEADER_KEY)
    token_type: str | None = headers.get(_TOKEN_TYPE_HEADER_KEY)

    if user_id_hex := headers.get(_USER_ID_HEADER_KEY):
        # Check if the user_id is a valid ObjectId
        if ObjectId.is_valid(user_id_hex):
            user_id = ObjectId(user_id_hex)
        else:
            raise ValueError(f"Invalid user_id: {user_id_hex}")
    if org_id_hex := headers.get(_ORG_ID_HEADER_KEY):
        # Check if the org_id is a valid ObjectId
        if ObjectId.is_valid(org_id_hex):
            org_id = ObjectId(org_id_hex)
        else:
            raise ValueError(f"Invalid org_id: {org_id_hex}")
    if session_id_hex := headers.get(_SESSION_ID_HEADER_KEY):
        # Check if the session_id is a valid ObjectId
        if ObjectId.is_valid(session_id_hex):
            session_id = ObjectId(session_id_hex)
        else:
            raise ValueError(f"Invalid session_id: {session_id_hex}")

    return AuthPayload(
        user_id=user_id,
        user_email=user_email,
        org_id=org_id,
        auth_method=auth_method,
        session_id=session_id,
        token_type=token_type,
    )


def get_auth_payload_from_grpc_metadata(
    metadata: list[tuple[str, str]],
) -> AuthPayload:
    """
    Extract auth payload from gRPC context metadata.

    Args:
        metadata: The context metadata from either ServicerContext or HandlerCallDetails.

    Returns:
        AuthPayload: The auth payload model extracted from the context metadata.

    Raises:
        ValueError: If the user_id, org_id, or session_id is not a valid ObjectId.
    """
    user_id: ObjectId | None = None
    user_email: str | None = None
    org_id: ObjectId | None = None
    auth_method: str | None = None
    session_id: ObjectId | None = None
    token_type: str | None = None

    for key, value in metadata:
        if key == _AUTH_METHOD_HEADER_KEY:
            if isinstance(value, str):
                auth_method = value
        elif key == _USER_ID_HEADER_KEY:
            if isinstance(value, str) and ObjectId.is_valid(value):
                user_id = ObjectId(value)
            else:
                raise ValueError(f"Invalid user_id: {value}")
        elif key == _USERNAME_HEADER_KEY:
            if isinstance(value, str):
                user_email = value
        elif key == _ORG_ID_HEADER_KEY:
            if isinstance(value, str) and ObjectId.is_valid(value):
                org_id = ObjectId(value)
            else:
                raise ValueError(f"Invalid org_id: {value}")
        elif key == _SESSION_ID_HEADER_KEY:
            if isinstance(value, str):
                if ObjectId.is_valid(value):
                    session_id = ObjectId(value)
                else:
                    raise ValueError(f"Invalid session_id: {value}")
        elif key == _TOKEN_TYPE_HEADER_KEY:
            if isinstance(value, str):
                token_type = value

    return AuthPayload(
        user_id=user_id,
        user_email=user_email,
        org_id=org_id,
        auth_method=auth_method,
        session_id=session_id,
        token_type=token_type,
    )


# Expected header keys for the auth payload
# We assume that these keys are set by the auth middleware in the web server's envoy configuration.
#
# Code pointer: https://github.com/orby-ai-engineering/web-api-server/blob/9f0c9243fc7e11d0ef15acd3df14638f67fe8d20/cmd/commands/auth/server.go#L117
_AUTH_METHOD_HEADER_KEY = "orby-auth-method"
_USER_ID_HEADER_KEY = "orby-user-id"
_USERNAME_HEADER_KEY = "orby-username"
_ORG_ID_HEADER_KEY = "orby-org-id"
_SESSION_ID_HEADER_KEY = "orby-session-id"
_TOKEN_TYPE_HEADER_KEY = "orby-token-type"
