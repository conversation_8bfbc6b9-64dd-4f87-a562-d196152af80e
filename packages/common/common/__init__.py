"""
Common package for the Vibe Automation Server project.

This package contains shared code and utilities used across all services.
"""

# Export monitoring module
from .monitoring import (
    PrometheusFastAPIMiddleware,
    PrometheusGRPCInterceptor,
    PrometheusMetrics,
    create_prometheus_interceptor,
    create_prometheus_middleware,
    get_metrics,
    get_metrics_registry,
    metrics_endpoint,
    monitor_function,
)

__all__ = [
    "PrometheusMetrics",
    "PrometheusFastAPIMiddleware",
    "PrometheusGRPCInterceptor",
    "create_prometheus_interceptor",
    "create_prometheus_middleware",
    "get_metrics_registry",
    "get_metrics",
    "monitor_function",
    "metrics_endpoint",
]
