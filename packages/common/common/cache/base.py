from abc import ABC, abstractmethod


class CacheClient(ABC):
    """Abstract base class for cache clients."""

    @abstractmethod
    async def get(self, key: str) -> str | None:
        """
        Get a value from the cache for a given key.

        Args:
            key: The key to get the value from.

        Returns:
            value: The value from the cache if it exists, otherwise None.
        """
        pass

    @abstractmethod
    async def set(self, key: str, value: str, ttl_seconds: int = 3600) -> None:
        """
        Set a value in the cache for a given key.

        Args:
            key: The key to set the value for.
            value: The value to set in the cache.
            ttl_seconds: The time to live for the value in seconds. Defaults to 3600 seconds.

        Returns:
            None
        """
        pass

    @abstractmethod
    async def delete(self, key: str) -> int:
        """
        Delete a value from the cache for a given key.

        Args:
            key: The key to delete the value for.

        Returns:
            The number of keys deleted from the cache if it exists, otherwise 0.
        """
        pass
