from common.cache.base import CacheClient


class MockClient(CacheClient):
    """
    Mock client for cache.
    """

    def __init__(self):
        self.cache: dict[str, str] = {}

    async def get(self, key: str) -> str | None:
        return self.cache.get(key, None)

    async def set(self, key: str, value: str, ttl_seconds: int = 3600) -> None:
        self.cache[key] = value

    async def delete(self, key: str) -> int:
        pop_result = self.cache.pop(key, None)
        return 1 if not pop_result else 0
