from collections.abc import AsyncGenerator
from dataclasses import dataclass
import os
from typing import TypeV<PERSON>, cast

from dotenv import load_dotenv
import redis.asyncio as redis

from common.cache.base import CacheClient
from common.log import warn
from common.utils.singleton import Singleton

ContentType = TypeVar("ContentType")

load_dotenv()

@dataclass
class StreamMessage[ContentType]:
    """Class representing a Redis stream message."""

    id: str
    stream: str
    data: ContentType


@Singleton
class RedisClient(CacheClient):
    """
    Hybrid Redis client with dual connections for standard and stream operations.
    
    Uses two separate Redis connections:
    - Standard operations (get, set, delete)
    - Stream operations (XADD, XREAD)
    """

    __DEFAULT_REDIS_DB = 0

    def __init__(self):
        # Standard Redis operations client (via Redis proxy)
        self.standard_client = redis.Redis.from_url(
            os.getenv("REDIS_URL", ""),
            db=self.__DEFAULT_REDIS_DB
        )

        # Stream operations client (via TCP proxy with cluster support)
        self.stream_client = redis.Redis.from_url(
            os.getenv("REDIS_STREAM_URL", ""),
            db=self.__DEFAULT_REDIS_DB
        )

    async def get(self, key: str) -> str | None:
        """Get a value from Redis using the standard client (Redis proxy)."""
        result = await self.standard_client.get(name=key)
        if not result:
            return None
        return result.decode("utf-8")

    async def set(self, key: str, value: str, ttl_seconds: int = 3600) -> None:
        """Set a value in Redis using the standard client (Redis proxy)."""
        await self.standard_client.set(name=key, value=value, ex=ttl_seconds)

    async def delete(self, key: str) -> int:
        """Delete a key from Redis using the standard client (Redis proxy)."""
        result = await self.standard_client.delete(key)
        return int(result)

    def _add_hash_tag(self, stream_name: str) -> str:
        """Add hash tag to stream name to ensure cluster consistency."""
        if not stream_name.startswith("{"):
            return f"{{stream}}:{stream_name}"
        return stream_name

    async def publish_stream(
        self,
        stream_name: str,
        message: object,
        stream_ttl: int = 3600,
    ) -> str:
        """
        Publish a message to a Redis Stream using the stream client (TCP proxy).
        Automatically adds hash tags for cluster consistency.
        """
        if not isinstance(message, dict):
            raise TypeError("Message must be a dictionary type")

        # Add hash tag for cluster consistency
        tagged_stream_name = self._add_hash_tag(stream_name)

        # Use stream client for XADD operations
        result = await self.stream_client.xadd(
            tagged_stream_name, {str(k): str(v) for k, v in message.items()}
        )

        # Always reset TTL on every message
        await self.stream_client.expire(tagged_stream_name, stream_ttl)
        
        # Return the message ID as string
        return result if isinstance(result, str) else result.decode("utf-8")

    async def stream_reader(
        self,
        stream_name: str,
        content_type: type[ContentType],
        start_id: str = "0",
        block: int = 1000,
    ) -> AsyncGenerator[StreamMessage[ContentType]]:
        """
        Dynamic stream reader using the stream client (TCP proxy).
        Automatically adds hash tags for cluster consistency.
        """
        # Add hash tag for cluster consistency
        tagged_stream_name = self._add_hash_tag(stream_name)
        
        last_id = start_id
        try:
            while True:
                # Use stream client for XREAD operations
                messages = await self.stream_client.xread(
                    {tagged_stream_name: last_id},
                    count=10,
                    block=block,
                )

                for stream, msgs in messages:
                    for msg_id, fields in msgs:
                        # Handle both bytes and string responses
                        if isinstance(fields, dict):
                            content_dict = {
                                (k.decode("utf-8") if isinstance(k, bytes) else k): 
                                (v.decode("utf-8") if isinstance(v, bytes) else v)
                                for k, v in fields.items()
                            }
                        else:
                            content_dict = fields

                        message = StreamMessage(
                            id=msg_id if isinstance(msg_id, str) else msg_id.decode("utf-8"),
                            stream=stream if isinstance(stream, str) else stream.decode("utf-8"),
                            data=cast(ContentType, content_dict),
                        )

                        last_id = message.id
                        yield message

        except Exception as e:
            warn(f"Error in stream reader for '{stream_name}': {e}")
            return

    async def close(self) -> None:
        """Close both Redis connections."""
        await self.standard_client.close()
        await self.stream_client.close()


def get_redis_client():
    """Helper function to get the redis client"""
    return RedisClient.instance()
