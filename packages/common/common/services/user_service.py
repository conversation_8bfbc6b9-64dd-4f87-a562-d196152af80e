from bson import ObjectId

from common.models.user import User
from common.repository.user_repo import UserRepository


class UserService:
    def __init__(self):
        self.user_repo = UserRepository()

    async def get_user_by_email(self, email: str):
        return await self.user_repo.get_user_by_email(email)

    async def get_users_by_ids(self, user_ids: list[ObjectId]) -> dict[ObjectId, User]:
        """
        Get multiple users by their IDs, returns dict for efficient lookup.
        
        Args:
            user_ids: List of user ObjectIds
            
        Returns:
            Dictionary mapping user ObjectId to User object
        """
        users = await self.user_repo.get_users_by_ids(user_ids)
        return {user.id: user for user in users}
