from bson import ObjectId

from common.models.organization import TenantInfo
from common.repository.org_repo import OrganizationRepository


class TenantService:
    def __init__(self):
        self.org_repo = OrganizationRepository()

    async def get_tenant_info(self, org_id: ObjectId) -> TenantInfo:
        try:
            # TODO: Add redis cache here
            org = await self.org_repo.get_by_id(org_id)
            if not org:
                raise ValueError(f"Organization not found for org_id: {org_id}")
            return TenantInfo(org=org)
        except ValueError as e:
            raise e
        except Exception as e:
            raise Exception(
                f"Failed to get tenant info for org_id: {org_id}: {e}"
            ) from e
