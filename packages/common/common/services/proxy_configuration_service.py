from common.log import error
from common.models.proxy_configuration import ProxyConfiguration
from common.repository.proxy_configuration_repository import (
    ProxyConfigurationRepository,
)


class ProxyConfigurationService:
    """Simple service for reading proxy configuration with automatic caching"""

    CACHE_KEY = "proxy_configuration:global"
    CACHE_TTL_SECONDS = 300  # 5 minutes

    def __init__(self):
        self.repository = ProxyConfigurationRepository()

    async def get_configuration(self) -> ProxyConfiguration:
        """
        Get proxy configuration with automatic caching.

        This method handles all caching logic internally:
        - First tries to load from Redis cache
        - If cache miss, loads from MongoDB and caches the result
        - Returns configuration ready for browserbase usage

        Returns:
            ProxyConfiguration object with entries
        """
        try:
            # TODO: Implement redis cache
            entries = await self.repository.get_all_entries()

            # Build configuration object
            config = ProxyConfiguration(entries=entries)

            return config

        except Exception as e:
            error(f"Failed to get proxy configuration: {e}")
            # Return empty configuration on error
            return ProxyConfiguration(entries=[])
