from bson import ObjectId

from common.log import error
from common.models.execution import ExecutionLog
from common.repository.execution_log_repo import ExecutionLogRepository


class ExecutionLogService:
    def __init__(self):
        self.execution_log_repo = ExecutionLogRepository()

    async def get_execution_logs(
        self, execution_id: str, page_number: int, page_size: int
    ) -> tuple[list[ExecutionLog], int]:
        """
        Get paginated execution logs.

        Args:
            execution_id: The id of the execution
            page_number: The page number
            page_size: The page size

        Returns:
            A tuple containing:
                - A list of execution logs
                - The total number of execution logs
        """
        try:
            (
                paginated_logs,
                total_size,
            ) = await self.execution_log_repo.get_execution_logs(
                execution_id, page_number, page_size
            )
            return paginated_logs, total_size
        except Exception as e:
            error(
                f"Failed to get execution logs for execution_id: {execution_id}: {e}"
            )
            raise e

    async def append_execution_log(
        self, execution_log: ExecutionLog
    ) -> ExecutionLog:
        try:
            execution_log.id = ObjectId()
            await self.execution_log_repo.append_execution_log(execution_log)
            return execution_log
        except Exception as e:
            error(f"Failed to append execution log: {execution_log}: {e}")
            raise e
