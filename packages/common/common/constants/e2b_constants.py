"""
E2B Runtime Constants and Configuration.
Defines the Docker template and configuration for E2B sandbox environments.
"""

# E2B Docker Template Configuration
from web_server.constants.env import DEV_MODE, LOCAL_MODE, STAGING_MODE

DOCKER_TEMPLATE_SPEC = """
FROM e2bdev/code-interpreter:latest

# Install git and openssh-client
RUN apt-get update && \
    apt-get install -y --no-install-recommends git openssh-client && \
    apt-get clean && rm -rf /var/cache/apt/archives/* /var/lib/apt/lists/*

# Set up SSH directory with proper permissions for root
RUN mkdir -p /root/.ssh && \
    chmod 700 /root/.ssh && \
    touch /root/.ssh/known_hosts && \
    chmod 600 /root/.ssh/known_hosts

# Copy e2b SSH key into the container
COPY id_ed25519_e2b /root/.ssh/id_ed25519
COPY id_ed25519_e2b.pub /root/.ssh/id_ed25519.pub
RUN chmod 600 /root/.ssh/id_ed25519 && \
    chmod 644 /root/.ssh/id_ed25519.pub

# Set up SSH configuration and known hosts for root
RUN ssh-keyscan -t rsa,ed25519 github.com >> /root/.ssh/known_hosts 2>/dev/null || true && \
    echo "Host *" > /root/.ssh/config && \
    echo "    StrictHostKeyChecking no" >> /root/.ssh/config && \
    echo "    UserKnownHostsFile /root/.ssh/known_hosts" >> /root/.ssh/config && \
    echo "    IdentitiesOnly yes" >> /root/.ssh/config && \
    echo "    IdentityFile /root/.ssh/id_ed25519" >> /root/.ssh/config && \
    chmod 600 /root/.ssh/config

# Copy and install git scripts globally
COPY git-push.sh /usr/local/bin/git-push
COPY git-pull.sh /usr/local/bin/git-pull
RUN chmod +x /usr/local/bin/git-push && \
    chmod +x /usr/local/bin/git-pull

# Install Python dependencies only (no system packages)
RUN pip install --no-cache-dir browserbase pydantic google-genai

# Create global startup script that installs everything at runtime
RUN echo '#!/bin/bash\nif ! command -v expect &> /dev/null || ! command -v node &> /dev/null; then\n  echo "Installing system dependencies globally..."\n  apt-get update --allow-releaseinfo-change --allow-unauthenticated\n  apt-get install -y --no-install-recommends --allow-unauthenticated expect\n  echo "Installing Node.js globally..."\n  curl -fsSL https://deb.nodesource.com/setup_20.x | bash -\n  apt-get install -y nodejs\n  npm install -g @anthropic-ai/claude-code\n  apt-get clean && rm -rf /var/cache/apt/archives/* /var/lib/apt/lists/*\n  echo "All dependencies installed globally"\nfi' > /etc/profile.d/auto-setup.sh \
    && chmod +x /etc/profile.d/auto-setup.sh

# Run the setup on any shell start
RUN echo 'source /etc/profile.d/auto-setup.sh' >> /etc/bash.bashrc

# Default command
CMD ["bash"]
"""

# e2b.toml (dev)
"""
team_id = "e5917549-1205-45e5-be40-87cb85238c1f"
memory_mb = 2_048
cpu_count = 2
start_cmd = "/root/.jupyter/start-up.sh"
dockerfile = "e2b.Dockerfile"
template_name = "isolated-vm-code-gen"
template_id = "1x94jyo1kkti564a3elk"
"""

# e2b.toml (staging)
"""
team_id = "7b41e93b-eddf-40f9-8bfb-efeb236ae62a"
memory_mb = 2_048
cpu_count = 2
start_cmd = "/root/.jupyter/start-up.sh"
dockerfile = "e2b.Dockerfile"
template_name = "isolated-vm-code-gen-staging"
template_id = "7g8hev702kk11fqpfpgo"
"""

# Steps to push to e2b:
# 1. Ensure both the TOML file and the Dockerfile are located in the same directory.
# 2. By default, the Dockerfile is named "e2b.Dockerfile" and the TOML file is named "e2b.toml".
# 3. Ensure that you are authenticated with e2b.
# 4. You can authenticate by running "e2b auth login".
# 5. Verify that the correct team or organization is configured in the terminal. You can check this by running "e2b auth info" and, if necessary, update it with "e2b auth configure".
# 6. Once everything is set up and the Dockerfile is modified, run Docker and execute "e2b template build" from the same directory.


# E2B template name identifier
def get_template_name(mode: str) -> str:
    if mode == LOCAL_MODE or mode == DEV_MODE:
        return "isolated-vm-code-gen"
    elif mode == STAGING_MODE:
        return "isolated-vm-code-gen-staging"
    else:
        raise ValueError(f"Invalid mode: {mode}")


# For now, we only support one repo, later we cna fetch this info from the user organization based on the tenant info
REPO_NAME = "**************:orby-ai-engineering/code-repository.git"
