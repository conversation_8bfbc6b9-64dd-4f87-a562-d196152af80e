import asyncio
from datetime import datetime
import os

from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase

from common.log import error, warn

load_dotenv()


class MongoClient:
    """Async MongoDB client using Motor — loop-safe singleton with expiry.

    Why per-event-loop?
    -------------------
    Motor's AsyncIOMotorClient is tied to the event loop it was created in.
    If you try to use a Motor client from a different event loop, you get:
        RuntimeError: Task ... got Future ... attached to a different loop

    This commonly happens in:
    - Websocket-based apps (e.g., Chainlit)
    - Background tasks
    - Multiple concurrent event loops (e.g., via uvicorn, asyncio.run(), etc.)

    Optimization:
    - Clients expire after 1 minute of inactivity
    - Background cleanup runs every 60 seconds
    - Prevents memory leaks from abandoned event loops
    """

    # Key: loop_id, Value: (client_instance, created_at)
    _instances: dict[int, tuple["MongoClient", datetime]] = {}
    _lock = asyncio.Lock()  # Protects _instances from race conditions
    _cleanup_task: asyncio.Task | None = None

    def __init__(self):
        self._uri = os.getenv("MONGODB_URI")
        if self._uri is None:
            raise RuntimeError("MONGODB_URI is not set")

        self._default_database = os.getenv("DEFAULT_DB_NAME", "dev")
        self.client: AsyncIOMotorClient | None = None

    async def _connect_if_needed(self):
        """Connect to MongoDB if not already connected (safe inside a loop)"""
        if self.client is not None:
            return

        try:
            # Create client bound to this event loop
            # tz_aware=True is required to ensure that datetime objects are
            # serialized/deserialized with the correct timezone.
            self.client = AsyncIOMotorClient(self._uri, tz_aware=True)

            # Ping to ensure the connection works
            await self.client.admin.command("ping")
        except Exception as e:
            error(f"MongoDB connection error: {e}. Exiting.")
            os._exit(1)

    def get_database(
        self, database_name: str | None = None
    ) -> AsyncIOMotorDatabase:
        """Return the requested database from the current loop's client"""
        if not self.client:
            raise RuntimeError(
                "MongoDB client not connected. Call get_mongo_client() first."
            )
        return self.client[database_name or self._default_database]

    def close(self):
        """Close the client connection for this instance"""
        if self.client:
            self.client.close()
            self.client = None

    @classmethod
    async def get_instance(cls) -> "MongoClient":
        """Return a per-event-loop MongoClient singleton instance"""

        # Identify the current event loop
        loop_id = id(asyncio.get_running_loop())

        # Lock protects _instances from race conditions during creation
        async with cls._lock:
            # Start cleanup task if not already running
            if cls._cleanup_task is None:
                cls._cleanup_task = asyncio.create_task(
                    cls._background_cleanup()
                )

            if loop_id not in cls._instances:
                client_instance = MongoClient()
                cls._instances[loop_id] = (client_instance, datetime.now())
                warn(
                    f"Created MongoDB client for loop {loop_id} (Total: {len(cls._instances)})"
                )

        instance, _ = cls._instances[loop_id]
        await instance._connect_if_needed()
        return instance

    @classmethod
    async def _background_cleanup(cls):
        """Background cleanup task to clean up expired clients"""
        while True:
            try:
                await cls._cleanup_expired_clients()
                await asyncio.sleep(60)
            except Exception as e:
                error(f"Error in MongoDB client background cleanup: {e}")

    @classmethod
    async def _cleanup_expired_clients(cls):
        """Clean up clients older than 1 minute"""
        try:
            async with cls._lock:
                current_time = datetime.now()
                expired_loop_ids = []

                for loop_id, (
                    _client_instance,
                    created_at,
                ) in cls._instances.items():
                    # Check if client is older than 1 minute
                    if (current_time - created_at).total_seconds() > 60:
                        expired_loop_ids.append(loop_id)

                # Clean up expired clients
                for loop_id in expired_loop_ids:
                    try:
                        client_instance, created_at = cls._instances.pop(
                            loop_id
                        )
                        client_instance.close()
                        age = (current_time - created_at).total_seconds()
                        warn(
                            f"Cleaned up expired MongoDB client: loop {loop_id} (age: {age:.1f}s)"
                        )
                    except Exception as e:
                        error(
                            f"Error cleaning up expired MongoDB client {loop_id}: {e}"
                        )
        except Exception as e:
            error(f"Error in MongoDB client cleanup: {e}")


# Helper function to get a per-loop singleton MongoDB client
async def get_mongo_client() -> MongoClient:
    """Get a per-loop singleton MongoDB client"""
    return await MongoClient.get_instance()
