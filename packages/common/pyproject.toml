[project]
name = "common"
version = "0.1.0"
description = "Common package for the project, which contains shared code across all packages, it cannot depend on any other package"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "prometheus-client>=0.22.1",
    "grpcio>=1.73.1",
    "fastapi>=0.115.0",
    "starlette>=0.35.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["common/"]
