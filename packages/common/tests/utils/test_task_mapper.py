from datetime import datetime
from unittest.mock import Mock

from bson import ObjectId
from orby.va.task_pb2 import Task, TaskStatus
from orby.va.task_pb2 import TaskTimestamps as TaskTimestampsProto
from orby.va.task_pb2 import TaskUsers as TaskUsersProto
import pytest
from pytz import UTC

from common.models.task import (
    Task as TaskModel,
)
from common.models.task import (
    TaskStatus as TaskStatusModel,
)
from common.models.task import (
    TaskTimestamps,
    TaskUsers,
)
from common.utils.task_mapper import (
    convert_task_status_model_to_proto,
    convert_task_status_proto_to_model,
    task_model_to_proto,
    task_proto_to_model,
)


@pytest.fixture
def sample_task_model():
    """Sample task model for testing."""
    return TaskModel(
        id=ObjectId("507f1f77bcf86cd799439012"),
        org_id=ObjectId("507f1f77bcf86cd799439011"),
        workflow_id=ObjectId("507f1f77bcf86cd799439013"),
        identify_key="test_key",
        execution_id=ObjectId("507f1f77bcf86cd799439014"),
        display_name="Test Task",
        description="Test task description",
        status=TaskStatusModel.PENDING,
        users=TaskUsers(creator_id=ObjectId("507f1f77bcf86cd799439015")),
        timestamps=TaskTimestamps(
            created_at=datetime(2025, 7, 1, 12, 0, 0, tzinfo=UTC),
            updated_at=datetime(2025, 7, 1, 12, 30, 0, tzinfo=UTC),
        ),
    )


@pytest.fixture
def sample_task_proto():
    """Sample task proto for testing."""
    timestamps = TaskTimestampsProto()
    timestamps.created_at.FromDatetime(datetime(2025, 7, 1, 12, 0, 0, tzinfo=UTC))
    timestamps.updated_at.FromDatetime(datetime(2025, 7, 1, 12, 30, 0, tzinfo=UTC))
    
    users = TaskUsersProto(creator_id="507f1f77bcf86cd799439015")
    
    return Task(
        id="507f1f77bcf86cd799439012",
        org_id="507f1f77bcf86cd799439011",
        workflow_id="507f1f77bcf86cd799439013",
        identify_key="test_key",
        execution_id="507f1f77bcf86cd799439014",
        display_name="Test Task",
        description="Test task description",
        status=TaskStatus.TASK_STATUS_PENDING,
        users=users,
        timestamps=timestamps,
    )


class TestConvertTaskStatusModelToProto:
    """Test cases for convert_task_status_model_to_proto function."""

    def test_convert_pending_status(self):
        """Test conversion of PENDING status."""
        result = convert_task_status_model_to_proto(TaskStatusModel.PENDING)
        assert result == TaskStatus.TASK_STATUS_PENDING

    def test_convert_failed_status(self):
        """Test conversion of FAILED status."""
        result = convert_task_status_model_to_proto(TaskStatusModel.FAILED)
        assert result == TaskStatus.TASK_STATUS_FAILED

    def test_convert_completed_status(self):
        """Test conversion of COMPLETED status."""
        result = convert_task_status_model_to_proto(TaskStatusModel.COMPLETED)
        assert result == TaskStatus.TASK_STATUS_COMPLETED

    def test_convert_unknown_status(self):
        """Test conversion of unknown status returns UNSPECIFIED."""
        # Create a mock status that doesn't match any case
        mock_status = Mock()
        mock_status.value = "unknown"
        
        result = convert_task_status_model_to_proto(mock_status)
        assert result == TaskStatus.TASK_STATUS_UNSPECIFIED


class TestConvertTaskStatusProtoToModel:
    """Test cases for convert_task_status_proto_to_model function."""

    def test_convert_pending_status(self):
        """Test conversion of PENDING status."""
        result = convert_task_status_proto_to_model(TaskStatus.TASK_STATUS_PENDING)
        assert result == TaskStatusModel.PENDING

    def test_convert_failed_status(self):
        """Test conversion of FAILED status."""
        result = convert_task_status_proto_to_model(TaskStatus.TASK_STATUS_FAILED)
        assert result == TaskStatusModel.FAILED

    def test_convert_completed_status(self):
        """Test conversion of COMPLETED status."""
        result = convert_task_status_proto_to_model(TaskStatus.TASK_STATUS_COMPLETED)
        assert result == TaskStatusModel.COMPLETED

    def test_convert_unknown_status(self):
        """Test conversion of unknown status returns None."""
        # Create a mock status that doesn't match any case
        mock_status = Mock()
        mock_status.value = 999  # Unknown status value
        
        result = convert_task_status_proto_to_model(mock_status)
        assert result is None


class TestTaskModelToProto:
    """Test cases for task_model_to_proto function."""

    def test_task_model_to_proto_basic_fields(self, sample_task_model):
        """Test basic field conversion."""
        result = task_model_to_proto(sample_task_model)
        
        assert result.id == str(sample_task_model.id)
        assert result.org_id == str(sample_task_model.org_id)
        assert result.workflow_id == str(sample_task_model.workflow_id)
        assert result.identify_key == sample_task_model.identify_key
        assert result.execution_id == str(sample_task_model.execution_id)
        assert result.display_name == sample_task_model.display_name
        assert result.description == sample_task_model.description
        assert result.status == TaskStatus.TASK_STATUS_PENDING

    def test_task_model_to_proto_with_timestamps(self, sample_task_model):
        """Test conversion with timestamps."""
        result = task_model_to_proto(sample_task_model)
        
        assert result.HasField("timestamps")
        assert result.timestamps.created_at.seconds == int(
            sample_task_model.timestamps.created_at.timestamp()
        )
        assert result.timestamps.updated_at.seconds == int(
            sample_task_model.timestamps.updated_at.timestamp()
        )

    def test_task_model_to_proto_with_users(self, sample_task_model):
        """Test conversion with users."""
        result = task_model_to_proto(sample_task_model)
        
        assert result.HasField("users")
        assert result.users.creator_id == str(sample_task_model.users.creator_id)

    def test_task_model_to_proto_without_timestamps(self):
        """Test conversion without timestamps."""
        task = TaskModel(
            id=ObjectId(),
            org_id=ObjectId(),
            workflow_id=ObjectId(),
            identify_key="test",
            execution_id=ObjectId(),
            display_name="Test",
            description="Test",
            status=TaskStatusModel.PENDING,
            timestamps=None,
        )
        
        result = task_model_to_proto(task)
        assert not result.HasField("timestamps")

    def test_task_model_to_proto_without_users(self):
        """Test conversion without users."""
        task = TaskModel(
            id=ObjectId(),
            org_id=ObjectId(),
            workflow_id=ObjectId(),
            identify_key="test",
            execution_id=ObjectId(),
            display_name="Test",
            description="Test",
            status=TaskStatusModel.PENDING,
            users=None,
        )
        
        result = task_model_to_proto(task)
        assert not result.HasField("users")

    def test_task_model_to_proto_with_empty_users(self):
        """Test conversion with empty users (no creator_id)."""
        task = TaskModel(
            id=ObjectId(),
            org_id=ObjectId(),
            workflow_id=ObjectId(),
            identify_key="test",
            execution_id=ObjectId(),
            display_name="Test",
            description="Test",
            status=TaskStatusModel.PENDING,
            users=TaskUsers(creator_id=None),
        )
        
        result = task_model_to_proto(task)
        assert not result.HasField("users")

    def test_task_model_to_proto_all_statuses(self):
        """Test conversion with all status types."""
        status_mappings = [
            (TaskStatusModel.PENDING, TaskStatus.TASK_STATUS_PENDING),
            (TaskStatusModel.FAILED, TaskStatus.TASK_STATUS_FAILED),
            (TaskStatusModel.COMPLETED, TaskStatus.TASK_STATUS_COMPLETED),
        ]
        
        for model_status, expected_proto_status in status_mappings:
            task = TaskModel(
                id=ObjectId(),
                org_id=ObjectId(),
                workflow_id=ObjectId(),
                identify_key="test",
                execution_id=ObjectId(),
                display_name="Test",
                description="Test",
                status=model_status,
            )
            
            result = task_model_to_proto(task)
            assert result.status == expected_proto_status


class TestTaskProtoToModel:
    """Test cases for task_proto_to_model function."""

    def test_task_proto_to_model_basic_fields(self, sample_task_proto):
        """Test basic field conversion."""
        result = task_proto_to_model(sample_task_proto)
        
        assert result.id == ObjectId(sample_task_proto.id)
        assert result.org_id == ObjectId(sample_task_proto.org_id)
        assert result.workflow_id == ObjectId(sample_task_proto.workflow_id)
        assert result.identify_key == sample_task_proto.identify_key
        assert result.execution_id == ObjectId(sample_task_proto.execution_id)
        assert result.display_name == sample_task_proto.display_name
        assert result.description == sample_task_proto.description
        assert result.status == TaskStatusModel.PENDING

    def test_task_proto_to_model_with_timestamps(self, sample_task_proto):
        """Test conversion with timestamps."""
        result = task_proto_to_model(sample_task_proto)
        
        assert result.timestamps is not None
        assert result.timestamps.created_at == datetime(2025, 7, 1, 12, 0, 0, tzinfo=UTC)
        assert result.timestamps.updated_at == datetime(2025, 7, 1, 12, 30, 0, tzinfo=UTC)

    def test_task_proto_to_model_with_users(self, sample_task_proto):
        """Test conversion with users."""
        result = task_proto_to_model(sample_task_proto)
        
        assert result.users is not None
        assert result.users.creator_id == ObjectId("507f1f77bcf86cd799439015")

    def test_task_proto_to_model_without_timestamps(self):
        """Test conversion without timestamps."""
        task_proto = Task(
            id="507f1f77bcf86cd799439012",
            org_id="507f1f77bcf86cd799439011",
            workflow_id="507f1f77bcf86cd799439013",
            identify_key="test",
            execution_id="507f1f77bcf86cd799439014",
            display_name="Test",
            description="Test",
            status=TaskStatus.TASK_STATUS_PENDING,
        )
        
        result = task_proto_to_model(task_proto)
        assert result.timestamps is None

    def test_task_proto_to_model_without_users(self):
        """Test conversion without users."""
        task_proto = Task(
            id="507f1f77bcf86cd799439012",
            org_id="507f1f77bcf86cd799439011",
            workflow_id="507f1f77bcf86cd799439013",
            identify_key="test",
            execution_id="507f1f77bcf86cd799439014",
            display_name="Test",
            description="Test",
            status=TaskStatus.TASK_STATUS_PENDING,
        )
        
        result = task_proto_to_model(task_proto)
        assert result.users is None

    def test_task_proto_to_model_with_empty_users(self):
        """Test conversion with empty users."""
        users = TaskUsersProto(creator_id="")
        task_proto = Task(
            id="507f1f77bcf86cd799439012",
            org_id="507f1f77bcf86cd799439011",
            workflow_id="507f1f77bcf86cd799439013",
            identify_key="test",
            execution_id="507f1f77bcf86cd799439014",
            display_name="Test",
            description="Test",
            status=TaskStatus.TASK_STATUS_PENDING,
            users=users,
        )
        
        result = task_proto_to_model(task_proto)
        assert result.users is None

    def test_task_proto_to_model_all_statuses(self):
        """Test conversion with all status types."""
        status_mappings = [
            (TaskStatus.TASK_STATUS_PENDING, TaskStatusModel.PENDING),
            (TaskStatus.TASK_STATUS_FAILED, TaskStatusModel.FAILED),
            (TaskStatus.TASK_STATUS_COMPLETED, TaskStatusModel.COMPLETED),
        ]
        
        for proto_status, expected_model_status in status_mappings:
            task_proto = Task(
                id="507f1f77bcf86cd799439012",
                org_id="507f1f77bcf86cd799439011",
                workflow_id="507f1f77bcf86cd799439013",
                identify_key="test",
                execution_id="507f1f77bcf86cd799439014",
                display_name="Test",
                description="Test",
                status=proto_status,
            )
            
            result = task_proto_to_model(task_proto)
            assert result.status == expected_model_status

    def test_task_proto_to_model_unknown_status_fallback(self):
        """Test conversion with unknown status falls back to PENDING."""
        task_proto = Task(
            id="507f1f77bcf86cd799439012",
            org_id="507f1f77bcf86cd799439011",
            workflow_id="507f1f77bcf86cd799439013",
            identify_key="test",
            execution_id="507f1f77bcf86cd799439014",
            display_name="Test",
            description="Test",
            status=TaskStatus.TASK_STATUS_UNSPECIFIED,
        )
        
        result = task_proto_to_model(task_proto)
        assert result.status == TaskStatusModel.PENDING

    def test_task_proto_to_model_partial_timestamps(self):
        """Test conversion with partial timestamps."""
        timestamps = TaskTimestampsProto()
        timestamps.created_at.FromDatetime(datetime(2023, 1, 1, 12, 0, 0, tzinfo=UTC))
        # updated_at not set
        
        task_proto = Task(
            id="507f1f77bcf86cd799439012",
            org_id="507f1f77bcf86cd799439011",
            workflow_id="507f1f77bcf86cd799439013",
            identify_key="test",
            execution_id="507f1f77bcf86cd799439014",
            display_name="Test",
            description="Test",
            status=TaskStatus.TASK_STATUS_PENDING,
            timestamps=timestamps,
        )
        
        result = task_proto_to_model(task_proto)
        assert result.timestamps.created_at == datetime(2023, 1, 1, 12, 0, 0, tzinfo=UTC)
        assert result.timestamps.updated_at is None

