from bson import ObjectId
import pytest

from common.utils.auth import (
    AuthPayload,
    get_auth_payload_from_grpc_metadata,
    get_auth_payload_from_http_headers,
)


@pytest.fixture
def user_id() -> ObjectId:
    return ObjectId()


@pytest.fixture
def org_id() -> ObjectId:
    return ObjectId()


@pytest.fixture
def session_id() -> ObjectId:
    return ObjectId()


def test_auth_payload_initialization(user_id, org_id, session_id):
    # Test complete initialization
    payload = AuthPayload(
        user_id=user_id,
        user_email="<EMAIL>",
        org_id=org_id,
        auth_method="bearer",
        session_id=session_id,
        token_type="access",
    )

    assert payload.user_id == user_id
    assert payload.user_email == "<EMAIL>"
    assert payload.org_id == org_id
    assert payload.auth_method == "bearer"
    assert payload.session_id == session_id
    assert payload.token_type == "access"


def test_auth_payload_partial_initialization(user_id):
    # Test partial initialization
    payload = AuthPayload(user_id=user_id, user_email="<EMAIL>")

    assert payload.user_id == user_id
    assert payload.user_email == "<EMAIL>"
    assert payload.org_id is None
    assert payload.auth_method is None
    assert payload.session_id is None
    assert payload.token_type is None


# Create ObjectId instances for use in parametrized tests
test_user_id = ObjectId()
test_org_id = ObjectId()
test_session_id = ObjectId()


@pytest.mark.parametrize(
    "headers, expected_user_id, expected_email, expected_org_id, expected_auth_method, expected_session_id, expected_token_type, want_error",
    [
        (
            {
                "orby-auth-method": "bearer",
                "orby-user-id": str(test_user_id),
                "orby-username": "<EMAIL>",
                "orby-org-id": str(test_org_id),
                "orby-session-id": str(test_session_id),
                "orby-token-type": "access",
            },
            test_user_id,
            "<EMAIL>",
            test_org_id,
            "bearer",
            test_session_id,
            "access",
            False,
        ),  # Complete headers
        (
            {
                "orby-user-id": str(test_user_id),
                "orby-username": "<EMAIL>",
                "orby-org-id": str(test_org_id),
            },
            test_user_id,
            "<EMAIL>",
            test_org_id,
            None,
            None,
            None,
            False,
        ),  # Partial headers
        (
            {},
            None,
            None,
            None,
            None,
            None,
            None,
            False,
        ),  # Empty headers
        (
            {
                "orby-user-id": str(test_user_id),
                "orby-username": "<EMAIL>",
                "content-type": "application/json",
                "authorization": "Bearer token",
            },
            test_user_id,
            "<EMAIL>",
            None,
            None,
            None,
            None,
            False,
        ),  # Headers with non-auth values
        (
            {
                "orby-auth-method": "api-key",
                "orby-org-id": str(test_org_id),
            },
            None,
            None,
            test_org_id,
            "api-key",
            None,
            None,
            False,
        ),  # Only auth method and org
        (
            {
                "orby-user-id": "1",
            },
            None,
            None,
            None,
            None,
            None,
            None,
            True,
        ),  # Invalid user_id
        (
            {
                "orby-org-id": "1",
            },
            None,
            None,
            None,
            None,
            None,
            None,
            True,
        ),  # Invalid org_id
        (
            {
                "orby-session-id": "1",
            },
            None,
            None,
            None,
            None,
            None,
            None,
            True,
        ),  # Invalid session_id
    ],
    ids=[
        "complete",
        "partial",
        "empty",
        "with_other_headers",
        "auth_org_only",
        "invalid_user_id",
        "invalid_org_id",
        "invalid_session_id",
    ],
)
def test_get_auth_payload_from_http_headers(
    headers: dict[str, str],
    expected_user_id: ObjectId | None,
    expected_email: str | None,
    expected_org_id: ObjectId | None,
    expected_auth_method: str | None,
    expected_session_id: ObjectId | None,
    expected_token_type: str | None,
    want_error: bool,
):
    if want_error:
        with pytest.raises(ValueError):
            get_auth_payload_from_http_headers(headers)
        return

    payload = get_auth_payload_from_http_headers(headers)

    assert payload.user_id == expected_user_id
    assert payload.user_email == expected_email
    assert payload.org_id == expected_org_id
    assert payload.auth_method == expected_auth_method
    assert payload.session_id == expected_session_id
    assert payload.token_type == expected_token_type


@pytest.mark.parametrize(
    "metadata, expected_user_id, expected_email, expected_org_id, expected_auth_method, expected_session_id, expected_token_type, want_error",
    [
        (
            [
                ("orby-auth-method", "bearer"),
                ("orby-user-id", str(test_user_id)),
                ("orby-username", "<EMAIL>"),
                ("orby-org-id", str(test_org_id)),
                ("orby-session-id", str(test_session_id)),
                ("orby-token-type", "access"),
            ],
            test_user_id,
            "<EMAIL>",
            test_org_id,
            "bearer",
            test_session_id,
            "access",
            False,
        ),  # Complete metadata
        (
            [
                ("orby-user-id", str(test_user_id)),
                ("orby-username", "<EMAIL>"),
                ("orby-org-id", str(test_org_id)),
            ],
            test_user_id,
            "<EMAIL>",
            test_org_id,
            None,
            None,
            None,
            False,
        ),  # Partial metadata
        (
            [
                ("content-type", "application/grpc"),
                ("user-agent", "grpc-client"),
            ],
            None,
            None,
            None,
            None,
            None,
            None,
            False,
        ),  # No auth metadata
        (
            [
                ("orby-user-id", str(test_user_id)),
                ("orby-username", "<EMAIL>"),
                ("content-type", "application/grpc"),
                ("user-agent", "grpc-client"),
            ],
            test_user_id,
            "<EMAIL>",
            None,
            None,
            None,
            None,
            False,
        ),  # Mixed metadata
        (
            [
                ("orby-user-id", str(test_user_id)),
                ("orby-user-id", str(test_user_id)),  # Duplicate - last wins
                ("orby-username", "<EMAIL>"),
                ("orby-username", "<EMAIL>"),  # Duplicate - last wins
            ],
            test_user_id,
            "<EMAIL>",
            None,
            None,
            None,
            None,
            False,
        ),  # Duplicate keys
        (
            [
                ("orby-user-id", "1"),
            ],
            None,
            None,
            None,
            None,
            None,
            None,
            True,
        ),  # Invalid user_id
        (
            [
                ("orby-org-id", "1"),
            ],
            None,
            None,
            None,
            None,
            None,
            None,
            True,
        ),  # Invalid org_id
        (
            [
                ("orby-session-id", "1"),
            ],
            None,
            None,
            None,
            None,
            None,
            None,
            True,
        ),  # Invalid session_id
    ],
    ids=[
        "complete",
        "partial",
        "no_auth",
        "mixed",
        "duplicates",
        "invalid_user_id",
        "invalid_org_id",
        "invalid_session_id",
    ],
)
def test_get_auth_payload_from_grpc_metadata(
    metadata: list[tuple[str, str]],
    expected_user_id: ObjectId | None,
    expected_email: str | None,
    expected_org_id: ObjectId | None,
    expected_auth_method: str | None,
    expected_session_id: ObjectId | None,
    expected_token_type: str | None,
    want_error: bool,
):
    if want_error:
        with pytest.raises(ValueError):
            get_auth_payload_from_grpc_metadata(metadata)
        return

    payload = get_auth_payload_from_grpc_metadata(metadata)

    assert payload.user_id == expected_user_id
    assert payload.user_email == expected_email
    assert payload.org_id == expected_org_id
    assert payload.auth_method == expected_auth_method
    assert payload.session_id == expected_session_id
    assert payload.token_type == expected_token_type
