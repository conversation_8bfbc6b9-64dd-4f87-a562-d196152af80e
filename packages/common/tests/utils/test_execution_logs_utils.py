from bson import ObjectId
from google.protobuf.struct_pb2 import Struct
from orby.va.public.execution_messages_pb2 import (
    ExecutionLog as ExecutionLogProto,
)
from orby.va.public.execution_messages_pb2 import (
    ExecutionStatus as ExecutionStatusProto,
)
import pytest

from common.models.execution import ExecutionLog, ExecutionStatus
from common.utils.execution_logs import (
    convert_execution_log_model_to_proto,
    convert_execution_log_proto_to_model,
    convert_execution_status_model_to_proto,
    convert_execution_status_proto_to_model,
)


@pytest.fixture
def execution_log_id() -> ObjectId:
    return ObjectId()


@pytest.fixture
def execution_id() -> ObjectId:
    return ObjectId()


@pytest.fixture
def workflow_id() -> ObjectId:
    return ObjectId()


@pytest.fixture
def org_id() -> ObjectId:
    return ObjectId()


@pytest.fixture
def complete_execution_log(
    execution_log_id: ObjectId,
    execution_id: ObjectId,
    workflow_id: ObjectId,
    org_id: ObjectId,
) -> ExecutionLog:
    return ExecutionLog(
        id=execution_log_id,
        execution_id=execution_id,
        workflow_id=workflow_id,
        org_id=org_id,
        step_id=1,
        description="Test step description",
        screenshot="test_screenshot.png",
        status=ExecutionStatus.RUNNING,
        metadata={"key1": "value1", "key2": 42, "key3": True},
    )


@pytest.fixture
def minimal_execution_log(
    execution_log_id: ObjectId,
    execution_id: ObjectId,
    workflow_id: ObjectId,
    org_id: ObjectId,
) -> ExecutionLog:
    return ExecutionLog(
        id=execution_log_id,
        execution_id=execution_id,
        workflow_id=workflow_id,
        org_id=org_id,
        step_id=None,
        description=None,
        screenshot=None,
        status=None,
        metadata={},
    )


@pytest.fixture
def complete_execution_log_proto(
    execution_log_id: ObjectId,
    execution_id: ObjectId,
    workflow_id: ObjectId,
    org_id: ObjectId,
) -> ExecutionLogProto:
    metadata_struct = Struct()
    metadata_struct.update({"key1": "value1", "key2": 42, "key3": True})

    return ExecutionLogProto(
        id=str(execution_log_id),
        execution_id=str(execution_id),
        workflow_id=str(workflow_id),
        org_id=str(org_id),
        step_id=1,
        description="Test step description",
        screenshot="test_screenshot.png",
        status=ExecutionStatusProto.RUNNING,
        metadata=metadata_struct,
    )


@pytest.fixture
def minimal_execution_log_proto() -> ExecutionLogProto:
    return ExecutionLogProto(
        step_id=0,
        description="",
        screenshot="",
        status=ExecutionStatusProto.EXECUTION_STATUS_UNSPECIFIED,
    )


@pytest.mark.parametrize(
    "model_status, expected_proto_status",
    [
        (
            ExecutionStatus.PENDING,
            ExecutionStatusProto.PENDING,
        ),
        (
            ExecutionStatus.RUNNING,
            ExecutionStatusProto.RUNNING,
        ),
        (
            ExecutionStatus.COMPLETED,
            ExecutionStatusProto.COMPLETED,
        ),
        (
            ExecutionStatus.FAILED,
            ExecutionStatusProto.FAILED,
        ),
        (
            ExecutionStatus.CANCELLED,
            ExecutionStatusProto.CANCELLED,
        ),
        (
            ExecutionStatus.TIMEOUT,
            ExecutionStatusProto.TIMEOUT,
        ),
    ],
    ids=[
        "pending",
        "running",
        "completed",
        "failed",
        "cancelled",
        "timeout",
    ],
)
def test_convert_execution_status_model_to_proto(
    model_status: ExecutionStatus,
    expected_proto_status: ExecutionStatusProto,
):
    """Test converting ExecutionStatus model enum to ExecutionStatusProto enum."""
    result = convert_execution_status_model_to_proto(model_status)
    assert result == expected_proto_status


def test_convert_execution_status_model_to_proto_invalid_status():
    """Test converting invalid ExecutionStatus returns EXECUTION_STATUS_UNSPECIFIED."""
    # Create a mock invalid status by using None or a non-existent enum value
    # Since match statement has a default case (_), we need to test with something that won't match
    invalid_status = "invalid_status"

    result = convert_execution_status_model_to_proto(invalid_status)
    assert result == ExecutionStatusProto.EXECUTION_STATUS_UNSPECIFIED


def test_convert_execution_status_model_to_proto_none():
    """Test converting None returns EXECUTION_STATUS_UNSPECIFIED."""
    result = convert_execution_status_model_to_proto(None)
    assert result == ExecutionStatusProto.EXECUTION_STATUS_UNSPECIFIED


@pytest.mark.parametrize(
    "proto_status, expected_model_status",
    [
        (
            ExecutionStatusProto.PENDING,
            ExecutionStatus.PENDING,
        ),
        (
            ExecutionStatusProto.RUNNING,
            ExecutionStatus.RUNNING,
        ),
        (
            ExecutionStatusProto.COMPLETED,
            ExecutionStatus.COMPLETED,
        ),
        (
            ExecutionStatusProto.FAILED,
            ExecutionStatus.FAILED,
        ),
        (
            ExecutionStatusProto.CANCELLED,
            ExecutionStatus.CANCELLED,
        ),
        (
            ExecutionStatusProto.TIMEOUT,
            ExecutionStatus.TIMEOUT,
        ),
    ],
    ids=[
        "pending",
        "running",
        "completed",
        "failed",
        "cancelled",
        "timeout",
    ],
)
def test_convert_execution_status_proto_to_model(
    proto_status: ExecutionStatusProto,
    expected_model_status: ExecutionStatus,
):
    """Test converting ExecutionStatusProto enum to ExecutionStatus model enum."""
    result = convert_execution_status_proto_to_model(proto_status)
    assert result == expected_model_status


def test_convert_execution_status_proto_to_model_unspecified():
    """Test converting EXECUTION_STATUS_UNSPECIFIED returns None."""
    result = convert_execution_status_proto_to_model(
        ExecutionStatusProto.EXECUTION_STATUS_UNSPECIFIED
    )
    assert result is None


def test_convert_execution_status_proto_to_model_invalid_status():
    """Test converting invalid proto status returns None."""
    # Create an invalid proto status value (using an integer that doesn't correspond to any enum)
    invalid_proto_status = (
        999  # This should not match any ExecutionStatusProto enum value
    )

    result = convert_execution_status_proto_to_model(invalid_proto_status)
    assert result is None


def test_convert_execution_log_model_to_proto_complete(
    complete_execution_log: ExecutionLog,
):
    """Test converting complete ExecutionLog model to proto."""
    result = convert_execution_log_model_to_proto(complete_execution_log)

    # Check basic field conversions
    assert result.id == str(complete_execution_log.id)
    assert result.execution_id == str(complete_execution_log.execution_id)
    assert result.workflow_id == str(complete_execution_log.workflow_id)
    assert result.org_id == str(complete_execution_log.org_id)
    assert result.step_id == complete_execution_log.step_id
    assert result.description == complete_execution_log.description
    assert result.screenshot == complete_execution_log.screenshot
    assert result.status == ExecutionStatusProto.RUNNING

    # Check metadata conversion
    assert result.metadata is not None
    metadata_dict = dict(result.metadata)
    assert metadata_dict == complete_execution_log.metadata


def test_convert_execution_log_model_to_proto_minimal(
    minimal_execution_log: ExecutionLog,
):
    """Test converting minimal ExecutionLog model to proto."""
    result = convert_execution_log_model_to_proto(minimal_execution_log)

    # Check basic field conversions
    assert result.id == str(minimal_execution_log.id)
    assert result.execution_id == str(minimal_execution_log.execution_id)
    assert result.workflow_id == str(minimal_execution_log.workflow_id)
    assert result.org_id == str(minimal_execution_log.org_id)
    assert result.step_id == 0  # proto field defaults to 0 for None
    assert (
        result.description == ""
    )  # proto field defaults to empty string for None
    assert (
        result.screenshot == ""
    )  # proto field defaults to empty string for None
    assert result.status == ExecutionStatusProto.EXECUTION_STATUS_UNSPECIFIED

    # Check metadata is not set when empty
    assert not result.HasField("metadata")


def test_convert_execution_log_model_to_proto_with_metadata(
    execution_log_id: ObjectId,
    execution_id: ObjectId,
    workflow_id: ObjectId,
    org_id: ObjectId,
):
    """Test converting ExecutionLog with various metadata types."""
    metadata = {
        "string_key": "string_value",
        "int_key": 42,
        "float_key": 3.14,
        "bool_key": True,
        "nested_dict": {"nested_key": "nested_value"},
        "list_key": ["item1", "item2"],
    }

    execution_log = ExecutionLog(
        id=execution_log_id,
        execution_id=execution_id,
        workflow_id=workflow_id,
        org_id=org_id,
        status=ExecutionStatus.COMPLETED,
        metadata=metadata,
    )

    result = convert_execution_log_model_to_proto(execution_log)

    # Check metadata conversion
    assert result.metadata is not None
    metadata_dict = dict(result.metadata)
    assert metadata_dict == metadata


def test_convert_execution_log_proto_to_model_complete(
    complete_execution_log_proto: ExecutionLogProto,
    execution_log_id: ObjectId,
    execution_id: ObjectId,
    workflow_id: ObjectId,
    org_id: ObjectId,
):
    """Test converting complete ExecutionLogProto to model."""
    result = convert_execution_log_proto_to_model(complete_execution_log_proto)

    # Check basic field conversions
    assert result.id == execution_log_id
    assert result.execution_id == execution_id
    assert result.workflow_id == workflow_id
    assert result.org_id == org_id
    assert result.step_id == 1
    assert result.description == "Test step description"
    assert result.screenshot == "test_screenshot.png"
    assert result.status == ExecutionStatus.RUNNING

    # Check metadata conversion
    expected_metadata = {"key1": "value1", "key2": 42, "key3": True}
    assert result.metadata == expected_metadata
