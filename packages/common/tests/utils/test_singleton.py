from common.utils.singleton import Singleton


@Singleton
class MockClass1:
    def __init__(self):
        self.counter = 0

    def increment(self):
        self.counter += 1

    def get_counter(self):
        return self.counter


class MockClass2:
    def __init__(self):
        self.counter = 0

    def increment(self):
        self.counter += 1

    def get_counter(self):
        return self.counter


def test_singleton_class_using_instance():
    instance1 = MockClass1.instance()
    instance2 = MockClass1.instance()

    # Proof that the same instance is returned
    assert instance1 is instance2

    # Proof that the counter is shared between instances
    assert instance1.get_counter() == 0
    instance1.increment()
    assert instance1.get_counter() == 1
    assert instance2.get_counter() == 1


def test_singleton_class_using_constructor():
    try:
        _ = MockClass1()
        raise AssertionError(
            "Should not be able to instantiate a singleton class using the constructor"
        )
    except TypeError:
        assert True


def test_non_singleton_class_using_constructor():
    instance1 = MockClass2()
    instance2 = MockClass2()

    # Proof that the same instance is returned
    assert instance1 is not instance2

    # Proof that the counter is not shared between instances
    assert instance1.get_counter() == 0
    instance1.increment()
    assert instance1.get_counter() == 1
    assert instance2.get_counter() == 0
    instance2.increment()
    assert instance1.get_counter() == 1
    assert instance2.get_counter() == 1
