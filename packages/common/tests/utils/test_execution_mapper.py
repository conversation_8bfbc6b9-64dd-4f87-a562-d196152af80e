from datetime import datetime

from bson import ObjectId
import pytest
from pytz import UTC

from common.models.execution import (
    Execution as ExecutionModel,
)
from common.models.execution import (
    ExecutionBrowserService,
    ExecutionIdentifierType,
    ExecutionStatus,
    ExecutionTimestamps,
    ExecutionTriggeredBy,
    ExecutionUsers,
    ExecutionWorkflowContext,
)
from common.utils.execution_mapper import execution_model_to_proto


@pytest.fixture(scope="session")
def sample_execution():
    """Sample execution model for testing."""
    return ExecutionModel(
        id=ObjectId("507f1f77bcf86cd799439012"),
        workflow_id=ObjectId("507f1f77bcf86cd799439013"),
        org_id=ObjectId("507f1f77bcf86cd799439011"),
        status=ExecutionStatus.PENDING,
        workflow_context=ExecutionWorkflowContext(commit_hash="abc123"),
        inputs={"key": "value"},
        triggered_by=ExecutionTriggeredBy(
            type=ExecutionIdentifierType.USER, identifier="user123"
        ),
        browser_service=ExecutionBrowserService(
            session_id="session123", context_id="context123"
        ),
        users=ExecutionUsers(cancelled_by="user456"),
        timestamps=ExecutionTimestamps(
            created_at=datetime(2023, 1, 1, 12, 0, 0, tzinfo=UTC),
            updated_at=datetime(2023, 1, 1, 12, 30, 0, tzinfo=UTC),
            started_at=datetime(2023, 1, 1, 12, 15, 0, tzinfo=UTC),
            finished_at=None,
            cancelled_at=None,
        ),
    )


class TestExecutionModelToProto:
    """Test cases for execution_model_to_proto function."""

    def test_execution_model_to_proto_basic_fields(self, sample_execution):
        """Test basic field conversion."""

        # Act
        result = execution_model_to_proto(sample_execution)

        # Assert
        assert result.id == str(sample_execution.id)
        assert result.workflow_id == str(sample_execution.workflow_id)
        assert result.org_id == str(sample_execution.org_id)
        assert result.status == 1  # PENDING

    def test_execution_model_to_proto_status_mapping(self):
        """Test status enum mapping."""

        # Test all status mappings
        status_mappings = [
            (ExecutionStatus.PENDING, 1),
            (ExecutionStatus.RUNNING, 2),
            (ExecutionStatus.COMPLETED, 3),
            (ExecutionStatus.FAILED, 4),
            (ExecutionStatus.CANCELLED, 5),
            (ExecutionStatus.TIMEOUT, 6),
        ]

        for status, expected_value in status_mappings:
            execution = ExecutionModel(
                id=ObjectId(),
                workflow_id=ObjectId(),
                org_id=ObjectId(),
                status=status,
            )

            result = execution_model_to_proto(execution)
            assert result.status == expected_value

    def test_execution_model_to_proto_nested_objects(self):
        """Test nested object conversion."""

        execution = ExecutionModel(
            id=ObjectId(),
            workflow_id=ObjectId(),
            org_id=ObjectId(),
            status=ExecutionStatus.PENDING,
            workflow_context=ExecutionWorkflowContext(commit_hash="test_hash"),
            triggered_by=ExecutionTriggeredBy(
                type=ExecutionIdentifierType.SCHEDULE, identifier="test_id"
            ),
            browser_service=ExecutionBrowserService(
                session_id="test_session", context_id="test_context"
            ),
            users=ExecutionUsers(cancelled_by="test_user"),
        )

        # Act
        result = execution_model_to_proto(execution)

        # Assert
        assert result.workflow_context.commit_hash == "test_hash"
        assert result.triggered_by.type == 2  # SCHEDULE
        assert result.triggered_by.identifier == "test_id"
        assert result.browser_service.session_id == "test_session"
        assert result.browser_service.context_id == "test_context"
        assert result.users.cancelled_by == "test_user"

    def test_execution_model_to_proto_timestamps(self):
        """Test timestamp conversion."""

        test_time = datetime(2023, 1, 1, 12, 0, 0, tzinfo=UTC)
        execution = ExecutionModel(
            id=ObjectId(),
            workflow_id=ObjectId(),
            org_id=ObjectId(),
            status=ExecutionStatus.PENDING,
            timestamps=ExecutionTimestamps(
                created_at=test_time,
                updated_at=test_time,
                started_at=test_time,
                finished_at=test_time,
                cancelled_at=test_time,
            ),
        )

        # Act
        result = execution_model_to_proto(execution)

        # Assert
        assert result.timestamps.created_at.seconds == int(
            test_time.timestamp()
        )
        assert result.timestamps.updated_at.seconds == int(
            test_time.timestamp()
        )
        assert result.timestamps.started_at.seconds == int(
            test_time.timestamp()
        )
        assert result.timestamps.finished_at.seconds == int(
            test_time.timestamp()
        )
        assert result.timestamps.cancelled_at.seconds == int(
            test_time.timestamp()
        )

    def test_execution_model_to_proto_inputs(self):
        """Test inputs dict conversion to Struct."""

        test_inputs = {"key1": "value1", "key2": 123, "key3": True}
        execution = ExecutionModel(
            id=ObjectId(),
            workflow_id=ObjectId(),
            org_id=ObjectId(),
            status=ExecutionStatus.PENDING,
            inputs=test_inputs,
        )

        # Act
        result = execution_model_to_proto(execution)

        # Assert
        assert result.inputs["key1"] == "value1"
        assert result.inputs["key2"] == 123
        assert result.inputs["key3"] is True
