from datetime import UTC, datetime
from unittest.mock import patch

from bson import ObjectId
import pytest

from common.models.execution import Execution, ExecutionStatus
from common.models.organization import Organization, TenantInfo, TenantType
from common.models.task import (
    Task,
    TaskStatus,
    TaskTimestamps,
    TaskUsers,
)
from common.services.task_service import (
    CreateTaskRequest,
    DeleteTaskRequest,
    GetTaskByIdRequest,
    ListTasksFilter,
    ListTasksRequest,
    TaskService,
    UpdateTaskRequest,
)


@pytest.fixture
def task_service():
    """TaskService instance with mocked dependencies."""
    return TaskService()


@pytest.fixture
def tenant_info():
    """TenantInfo fixture."""
    org = Organization(
        id=ObjectId("507f1f77bcf86cd799439012"),
        display_name="Test Organization",
        tenant_type=TenantType.SINGLE_TENANT,
    )
    return TenantInfo(org=org)


@pytest.fixture
def execution():
    """Execution fixture."""
    return Execution(
        id=ObjectId("507f1f77bcf86cd799439014"),
        org_id=ObjectId("507f1f77bcf86cd799439012"),
        workflow_id=ObjectId("507f1f77bcf86cd799439011"),
        status=ExecutionStatus.PENDING,
    )


@pytest.fixture
def task():
    """Task fixture."""
    return Task(
        id=ObjectId("507f1f77bcf86cd799439015"),
        org_id=ObjectId("507f1f77bcf86cd799439012"),
        workflow_id=ObjectId("507f1f77bcf86cd799439011"),
        identify_key="test_identify_key",
        execution_id=ObjectId("507f1f77bcf86cd799439014"),
        display_name="Test Task",
        description="Test Description",
        status=TaskStatus.PENDING,
        users=TaskUsers(
            creator_id=ObjectId("507f1f77bcf86cd799439013"),
        ),
        timestamps=TaskTimestamps(
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC),
        ),
    )


class TestCreateTask:
    @pytest.mark.asyncio
    async def test_create_task_success(
        self, task_service, tenant_info, execution
    ):
        # Arrange
        identify_key = "test_identify_key"
        execution_id = ObjectId("507f1f77bcf86cd799439014")
        display_name = "Test Task"
        description = "Test Description"
        creator_id = ObjectId("507f1f77bcf86cd799439013")
        expected_task_id = ObjectId("507f1f77bcf86cd799439015")

        # Mock execution repository
        with patch.object(
            task_service._execution_repo, "find_one", return_value=execution
        ):
            # Mock task repository
            with patch.object(
                task_service._task_repo,
                "create_task",
                return_value=expected_task_id,
            ):
                # Act
                result = await task_service.create_task(
                    request=CreateTaskRequest(
                        identify_key=identify_key,
                        execution_id=execution_id,
                        display_name=display_name,
                        description=description,
                        creator_id=creator_id,
                        tenant_info=tenant_info,
                    ),
                )

                # Assert
                assert result == expected_task_id
                task_service._execution_repo.find_one.assert_called_once_with(
                    {"_id": execution_id, "org_id": tenant_info.org.id},
                    tenant_info,
                )
                task_service._task_repo.create_task.assert_called_once_with(
                    execution.workflow_id,
                    identify_key,
                    execution_id,
                    display_name,
                    description,
                    creator_id,
                    tenant_info,
                )

    @pytest.mark.asyncio
    async def test_create_task_missing_identify_key(
        self, task_service, tenant_info
    ):
        # Act & Assert
        with pytest.raises(ValueError, match="Identify key is required"):
            await task_service.create_task(
                request=CreateTaskRequest(
                    identify_key="",
                    execution_id=ObjectId(),
                    display_name="Test Task",
                    description="Test Description",
                    creator_id=ObjectId(),
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_create_task_missing_execution_id(
        self, task_service, tenant_info
    ):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for CreateTaskRequest\nexecution_id",
        ):
            await task_service.create_task(
                request=CreateTaskRequest(
                    identify_key="test_key",
                    execution_id=None,
                    display_name="Test Task",
                    description="Test Description",
                    creator_id=ObjectId(),
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_create_task_missing_display_name(
        self, task_service, tenant_info
    ):
        # Act & Assert
        with pytest.raises(ValueError, match="Display name is required"):
            await task_service.create_task(
                request=CreateTaskRequest(
                    identify_key="test_key",
                    execution_id=ObjectId(),
                    display_name="",
                    description="Test Description",
                    creator_id=ObjectId(),
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_create_task_missing_creator_id(
        self, task_service, tenant_info
    ):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for CreateTaskRequest\ncreator_id",
        ):
            await task_service.create_task(
                request=CreateTaskRequest(
                    identify_key="test_key",
                    execution_id=ObjectId(),
                    display_name="Test Task",
                    description="Test Description",
                    creator_id=None,
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_create_task_missing_tenant_info(self, task_service):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for CreateTaskRequest\ntenant_info",
        ):
            await task_service.create_task(
                request=CreateTaskRequest(
                    identify_key="test_key",
                    execution_id=ObjectId(),
                    display_name="Test Task",
                    description="Test Description",
                    creator_id=ObjectId(),
                    tenant_info=None,
                ),
            )

    @pytest.mark.asyncio
    async def test_create_task_execution_not_found(
        self, task_service, tenant_info
    ):
        # Arrange
        execution_id = ObjectId("507f1f77bcf86cd799439014")

        # Mock execution repository to return None
        with patch.object(
            task_service._execution_repo, "find_one", return_value=None
        ):
            # Act & Assert
            with pytest.raises(ValueError, match="Execution not found"):
                await task_service.create_task(
                    request=CreateTaskRequest(
                        identify_key="test_key",
                        execution_id=execution_id,
                        display_name="Test Task",
                        description="Test Description",
                        creator_id=ObjectId(),
                        tenant_info=tenant_info,
                    ),
                )

    @pytest.mark.asyncio
    async def test_create_task_creation_failed(
        self, task_service, tenant_info, execution
    ):
        # Arrange
        execution_id = ObjectId("507f1f77bcf86cd799439014")

        # Mock execution repository
        with patch.object(
            task_service._execution_repo, "find_one", return_value=execution
        ):
            # Mock task repository to return None
            with patch.object(
                task_service._task_repo, "create_task", return_value=None
            ):
                # Act & Assert
                with pytest.raises(ValueError, match="Failed to create task"):
                    (
                        await task_service.create_task(
                            request=CreateTaskRequest(
                                identify_key="test_key",
                                execution_id=execution_id,
                                display_name="Test Task",
                                description="Test Description",
                                creator_id=ObjectId(),
                                tenant_info=tenant_info,
                            ),
                        ),
                    )


class TestUpdateTask:
    @pytest.mark.asyncio
    async def test_update_task_success(self, task_service, tenant_info, task):
        # Arrange
        task_id = ObjectId("507f1f77bcf86cd799439015")
        status = TaskStatus.COMPLETED

        # Mock task repository
        with patch.object(
            task_service._task_repo, "update_task", return_value=True
        ):
            with patch.object(
                task_service._task_repo, "get_by_id", return_value=task
            ):
                # Act
                result = await task_service.update_task(
                    request=UpdateTaskRequest(
                        tenant_info=tenant_info,
                        task_id=task_id,
                        status=status,
                    ),
                )

                # Assert
                assert result == task
                task_service._task_repo.update_task.assert_called_once_with(
                    tenant_info, task_id, status
                )
                task_service._task_repo.get_by_id.assert_called_once_with(
                    task_id, tenant_info
                )

    @pytest.mark.asyncio
    async def test_update_task_missing_task_id(self, task_service, tenant_info):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for UpdateTaskRequest\ntask_id",
        ):
            await task_service.update_task(
                request=UpdateTaskRequest(
                    task_id=None,
                    status=TaskStatus.COMPLETED,
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_update_task_invalid_status(self, task_service, tenant_info):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for UpdateTaskRequest\nstatus\n  Input should be 'pending', 'failed' or 'completed'",
        ):
            await task_service.update_task(
                request=UpdateTaskRequest(
                    tenant_info=tenant_info,
                    task_id=ObjectId(),
                    status="invalid_status",
                ),
            )

    @pytest.mark.asyncio
    async def test_update_task_missing_tenant_info(self, task_service):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for UpdateTaskRequest\ntenant_info",
        ):
            await task_service.update_task(
                request=UpdateTaskRequest(
                    task_id=ObjectId(),
                    status=TaskStatus.COMPLETED,
                    tenant_info=None,
                ),
            )

    @pytest.mark.asyncio
    async def test_update_task_update_failed(self, task_service, tenant_info):
        # Arrange
        task_id = ObjectId("507f1f77bcf86cd799439015")
        status = TaskStatus.COMPLETED

        # Mock task repository to return False
        with patch.object(
            task_service._task_repo, "update_task", return_value=False
        ):
            # Act & Assert
            with pytest.raises(ValueError, match="Failed to update task"):
                await task_service.update_task(
                    request=UpdateTaskRequest(
                        task_id=task_id,
                        status=status,
                        tenant_info=tenant_info,
                    ),
                )

    @pytest.mark.asyncio
    async def test_update_task_task_not_found(self, task_service, tenant_info):
        # Arrange
        task_id = ObjectId("507f1f77bcf86cd799439015")
        status = TaskStatus.COMPLETED

        # Mock task repository
        with patch.object(
            task_service._task_repo, "update_task", return_value=True
        ):
            with patch.object(
                task_service._task_repo, "get_by_id", return_value=None
            ):
                # Act & Assert
                with pytest.raises(ValueError, match="Task not found"):
                    await task_service.update_task(
                        request=UpdateTaskRequest(
                            task_id=task_id,
                            status=status,
                            tenant_info=tenant_info,
                        ),
                    )


class TestDeleteTask:
    @pytest.mark.asyncio
    async def test_delete_task_success(self, task_service, tenant_info):
        # Arrange
        task_id = ObjectId("507f1f77bcf86cd799439015")

        # Mock task repository
        with patch.object(
            task_service._task_repo, "delete_task", return_value=True
        ):
            # Act
            result = await task_service.delete_task(
                request=DeleteTaskRequest(
                    task_id=task_id,
                    tenant_info=tenant_info,
                ),
            )

            # Assert
            assert result is None
            task_service._task_repo.delete_task.assert_called_once_with(
                tenant_info, task_id
            )

    @pytest.mark.asyncio
    async def test_delete_task_missing_task_id(self, task_service, tenant_info):
        # Act & Assert
        with pytest.raises(
            ValueError, match="validation error for DeleteTaskRequest\ntask_id"
        ):
            await task_service.delete_task(
                request=DeleteTaskRequest(
                    task_id=None,
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_delete_task_missing_tenant_info(self, task_service):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for DeleteTaskRequest\ntenant_info",
        ):
            await task_service.delete_task(
                request=DeleteTaskRequest(
                    task_id=ObjectId(),
                    tenant_info=None,
                ),
            )

    @pytest.mark.asyncio
    async def test_delete_task_deletion_failed(self, task_service, tenant_info):
        # Arrange
        task_id = ObjectId("507f1f77bcf86cd799439015")

        # Mock task repository to return False
        with patch.object(
            task_service._task_repo, "delete_task", return_value=False
        ):
            # Act & Assert
            with pytest.raises(ValueError, match="Failed to delete task"):
                await task_service.delete_task(
                    request=DeleteTaskRequest(
                        task_id=task_id,
                        tenant_info=tenant_info,
                    ),
                )


class TestGetTaskById:
    @pytest.mark.asyncio
    async def test_get_task_by_id_success(
        self, task_service, tenant_info, task
    ):
        # Arrange
        task_id = ObjectId("507f1f77bcf86cd799439015")

        # Mock task repository
        with patch.object(
            task_service._task_repo, "get_task_by_id", return_value=task
        ):
            # Act
            result = await task_service.get_task_by_id(
                request=GetTaskByIdRequest(
                    task_id=task_id,
                    tenant_info=tenant_info,
                ),
            )

            # Assert
            assert result == task
            task_service._task_repo.get_task_by_id.assert_called_once_with(
                task_id, tenant_info
            )

    @pytest.mark.asyncio
    async def test_get_task_by_id_missing_task_id(
        self, task_service, tenant_info
    ):
        # Act & Assert
        with pytest.raises(
            ValueError, match="validation error for GetTaskByIdRequest\ntask_id"
        ):
            await task_service.get_task_by_id(
                request=GetTaskByIdRequest(
                    task_id=None,
                    tenant_info=tenant_info,
                ),
            )

    @pytest.mark.asyncio
    async def test_get_task_by_id_missing_tenant_info(self, task_service):
        # Act & Assert
        with pytest.raises(
            ValueError,
            match="validation error for GetTaskByIdRequest\ntenant_info",
        ):
            await task_service.get_task_by_id(
                request=GetTaskByIdRequest(
                    task_id=ObjectId(),
                    tenant_info=None,
                ),
            )

    @pytest.mark.asyncio
    async def test_get_task_by_id_task_not_found(
        self, task_service, tenant_info
    ):
        # Arrange
        task_id = ObjectId("507f1f77bcf86cd799439015")

        # Mock task repository to return None
        with patch.object(
            task_service._task_repo, "get_task_by_id", return_value=None
        ):
            # Act & Assert
            with pytest.raises(ValueError, match="Task not found"):
                await task_service.get_task_by_id(
                    request=GetTaskByIdRequest(
                        task_id=task_id,
                        tenant_info=tenant_info,
                    ),
                )


class TestListTasks:
    @pytest.mark.asyncio
    async def test_list_tasks_success(self, task_service, tenant_info, task):
        # Arrange
        page_size = 10
        page_number = 1
        expected_tasks = [task]
        expected_total_size = 1

        # Mock task repository
        with patch.object(
            task_service._task_repo, "list_tasks", return_value=expected_tasks
        ):
            with patch.object(
                task_service._task_repo,
                "count_tasks",
                return_value=expected_total_size,
            ):
                # Act
                result = await task_service.list_tasks(
                    request=ListTasksRequest(
                        tenant_info=tenant_info,
                        page_size=page_size,
                        page_number=page_number,
                    ),
                )

                # Assert
                assert result.tasks == expected_tasks
                assert result.total_size == expected_total_size
                task_service._task_repo.list_tasks.assert_called_once_with(
                    tenant_info, 10, 0, {}
                )
                task_service._task_repo.count_tasks.assert_called_once_with(
                    tenant_info, {}
                )

    @pytest.mark.asyncio
    async def test_list_tasks_with_filter(
        self, task_service, tenant_info, task
    ):
        # Arrange
        page_size = 5
        page_number = 2
        expected_tasks = [task]
        expected_total_size = 1
        filter_status = TaskStatus.COMPLETED

        # Mock task repository
        with patch.object(
            task_service._task_repo, "list_tasks", return_value=expected_tasks
        ):
            with patch.object(
                task_service._task_repo,
                "count_tasks",
                return_value=expected_total_size,
            ):
                # Act
                result = await task_service.list_tasks(
                    request=ListTasksRequest(
                        tenant_info=tenant_info,
                        page_size=page_size,
                        page_number=page_number,
                        filter=ListTasksFilter(status=filter_status),
                    ),
                )

                # Assert
                assert result.tasks == expected_tasks
                assert result.total_size == expected_total_size
                task_service._task_repo.list_tasks.assert_called_once_with(
                    tenant_info, 5, 5, {"status": filter_status}
                )
                task_service._task_repo.count_tasks.assert_called_once_with(
                    tenant_info, {"status": filter_status}
                )

    @pytest.mark.asyncio
    async def test_list_tasks_page_size_zero(
        self, task_service, tenant_info, task
    ):
        # Arrange
        page_size = 0
        page_number = 1
        expected_tasks = [task]
        expected_total_size = 1

        # Mock task repository
        with patch.object(
            task_service._task_repo, "list_tasks", return_value=expected_tasks
        ):
            with patch.object(
                task_service._task_repo,
                "count_tasks",
                return_value=expected_total_size,
            ):
                # Act
                result = await task_service.list_tasks(
                    request=ListTasksRequest(
                        tenant_info=tenant_info,
                        page_size=page_size,
                        page_number=page_number,
                    ),
                )

                # Assert
                assert result.tasks == expected_tasks
                assert result.total_size == expected_total_size
                # Should use default limit of 10 when page_size is 0
                task_service._task_repo.list_tasks.assert_called_once_with(
                    tenant_info, 10, 0, {}
                )

    @pytest.mark.asyncio
    async def test_list_tasks_page_size_too_large(
        self, task_service, tenant_info
    ):
        # Arrange
        page_size = 21
        page_number = 1

        # Act & Assert
        with pytest.raises(
            ValueError, match="Page size must be less than or equal to 20"
        ):
            await task_service.list_tasks(
                request=ListTasksRequest(
                    tenant_info=tenant_info,
                    page_size=page_size,
                    page_number=page_number,
                ),
            )

    @pytest.mark.asyncio
    async def test_list_tasks_page_number_zero(self, task_service, tenant_info):
        # Arrange
        page_size = 10
        page_number = 0

        # Act & Assert
        with pytest.raises(
            ValueError, match="Page number must be greater than 0"
        ):
            await task_service.list_tasks(
                request=ListTasksRequest(
                    tenant_info=tenant_info,
                    page_size=page_size,
                    page_number=page_number,
                ),
            )

    @pytest.mark.asyncio
    async def test_list_tasks_page_number_negative(
        self, task_service, tenant_info
    ):
        # Arrange
        page_size = 10
        page_number = -1

        # Act & Assert
        with pytest.raises(
            ValueError, match="Page number must be greater than 0"
        ):
            await task_service.list_tasks(
                request=ListTasksRequest(
                    tenant_info=tenant_info,
                    page_size=page_size,
                    page_number=page_number,
                ),
            )

    @pytest.mark.asyncio
    async def test_list_tasks_empty_result(self, task_service, tenant_info):
        # Arrange
        page_size = 10
        page_number = 1
        expected_tasks = []
        expected_total_size = 0

        # Mock task repository
        with patch.object(
            task_service._task_repo, "list_tasks", return_value=expected_tasks
        ):
            with patch.object(
                task_service._task_repo,
                "count_tasks",
                return_value=expected_total_size,
            ):
                # Act
                result = await task_service.list_tasks(
                    request=ListTasksRequest(
                        tenant_info=tenant_info,
                        page_size=page_size,
                        page_number=page_number,
                    ),
                )

                # Assert
                assert result.tasks == expected_tasks
                assert result.total_size == expected_total_size

    @pytest.mark.asyncio
    async def test_list_tasks_pagination_calculation(
        self, task_service, tenant_info, task
    ):
        # Arrange
        page_size = 5
        page_number = 3  # Should calculate offset as (3-1) * 5 = 10
        expected_tasks = [task]
        expected_total_size = 1

        # Mock task repository
        with patch.object(
            task_service._task_repo, "list_tasks", return_value=expected_tasks
        ):
            with patch.object(
                task_service._task_repo,
                "count_tasks",
                return_value=expected_total_size,
            ):
                # Act
                result = await task_service.list_tasks(
                    request=ListTasksRequest(
                        tenant_info=tenant_info,
                        page_size=page_size,
                        page_number=page_number,
                    ),
                )

                # Assert
                assert result.tasks == expected_tasks
                assert result.total_size == expected_total_size
                # Should calculate correct offset: (page_number - 1) * page_size
                task_service._task_repo.list_tasks.assert_called_once_with(
                    tenant_info, 5, 10, {}
                )
