"""
Tests for the Prometheus monitoring module.
"""

import asyncio

from prometheus_client import CollectorRegistry
import pytest

from common.monitoring.base import (
    PrometheusMetrics,
    get_metrics,
    get_metrics_registry,
    monitor_function,
)
from common.monitoring.fastapi import (
    create_prometheus_middleware,
    metrics_endpoint,
)
from common.monitoring.grpc import (
    create_prometheus_interceptor,
)


class TestPrometheusMetrics:
    """Test cases for PrometheusMetrics class."""

    def test_metrics_initialization(self):
        """Test metrics initialization with default registry."""
        metrics = PrometheusMetrics()
        assert metrics.registry is not None
        assert metrics.http_requests_total is not None
        assert metrics.grpc_requests_total is not None
        assert metrics.errors_total is not None

    def test_metrics_initialization_with_custom_registry(self):
        """Test metrics initialization with custom registry."""
        registry = CollectorRegistry()
        metrics = PrometheusMetrics(registry=registry)
        assert metrics.registry == registry

    def test_record_http_request(self):
        """Test HTTP request recording."""
        metrics = PrometheusMetrics()
        metrics.record_http_request("GET", "/api/test", 200, 0.15)

        # Verify the metric was recorded
        samples = list(metrics.http_requests_total._metrics.values())
        assert len(samples) > 0

    def test_record_grpc_request(self):
        """Test gRPC request recording."""
        metrics = PrometheusMetrics()
        metrics.record_grpc_request("/test.Service/Method", "test", "OK", 0.25)

        # Verify the metric was recorded
        samples = list(metrics.grpc_requests_total._metrics.values())
        assert len(samples) > 0

    def test_record_error(self):
        """Test error recording."""
        metrics = PrometheusMetrics()
        metrics.record_error("test_error", "test_service")

        # Verify the metric was recorded
        samples = list(metrics.errors_total._metrics.values())
        assert len(samples) > 0

    def test_record_business_operation(self):
        """Test business operation recording."""
        metrics = PrometheusMetrics()
        metrics.record_business_operation("test_operation", "success")

        # Verify the metric was recorded
        samples = list(metrics.business_operations_total._metrics.values())
        assert len(samples) > 0

    def test_record_business_operation_duration(self):
        """Test business operation duration recording."""
        metrics = PrometheusMetrics()
        metrics.record_business_operation_duration(
            "test_operation", "test_service", 0.25
        )

        # Verify the metric was recorded
        samples = list(
            metrics.business_operation_duration_seconds._metrics.values()
        )
        assert len(samples) > 0

    def test_get_metrics(self):
        """Test metrics export."""
        metrics = PrometheusMetrics()
        metrics.record_http_request("GET", "/api/test", 200, 0.15)

        prometheus_data = metrics.get_metrics()
        assert isinstance(prometheus_data, bytes)
        assert len(prometheus_data) > 0

    def test_track_request_context_manager(self):
        """Test request tracking context manager."""
        metrics = PrometheusMetrics()

        with metrics.track_request("POST", "/api/test"):
            # Simulate some work
            pass

        # Verify metrics were recorded
        samples = list(metrics.http_requests_total._metrics.values())
        assert len(samples) > 0

    def test_track_request_context_manager_with_exception(self):
        """Test request tracking context manager with exception."""
        metrics = PrometheusMetrics()

        with pytest.raises(ValueError):
            with metrics.track_request("POST", "/api/test"):
                raise ValueError("Test error")

        # Verify error metrics were recorded
        samples = list(metrics.errors_total._metrics.values())
        assert len(samples) > 0


class TestGlobalMetrics:
    """Test cases for global metrics functions."""

    def test_get_metrics(self):
        """Test getting global metrics instance."""
        metrics = get_metrics()
        assert isinstance(metrics, PrometheusMetrics)

    def test_get_metrics_registry(self):
        """Test getting global metrics registry."""
        registry = get_metrics_registry()
        assert isinstance(registry, CollectorRegistry)


class TestFastAPIMiddleware:
    """Test cases for FastAPI middleware."""

    def test_create_prometheus_middleware(self):
        """Test FastAPI middleware creation."""
        middleware_factory = create_prometheus_middleware()
        assert middleware_factory is not None
        assert callable(middleware_factory)

        def mock_app(scope, receive, send):
            pass

        middleware = middleware_factory(mock_app)
        assert hasattr(middleware, "dispatch")
        assert hasattr(middleware, "metrics")

    def test_create_prometheus_middleware_with_custom_metrics(self):
        """Test FastAPI middleware creation with custom metrics."""
        custom_metrics = PrometheusMetrics()
        middleware_factory = create_prometheus_middleware(
            metrics=custom_metrics
        )
        assert middleware_factory is not None
        assert callable(middleware_factory)

        def mock_app(scope, receive, send):
            pass

        middleware = middleware_factory(mock_app)
        assert middleware.metrics == custom_metrics


class TestGRPCInterceptor:
    """Test cases for gRPC interceptor."""

    def test_create_prometheus_interceptor(self):
        """Test gRPC interceptor creation."""
        interceptor = create_prometheus_interceptor()
        assert interceptor is not None
        assert hasattr(interceptor, "intercept_service")

    def test_create_prometheus_interceptor_with_custom_metrics(self):
        """Test gRPC interceptor creation with custom metrics."""
        custom_metrics = PrometheusMetrics()
        interceptor = create_prometheus_interceptor(metrics=custom_metrics)
        assert interceptor.metrics == custom_metrics


class TestMonitorFunctionDecorator:
    """Test cases for monitor_function decorator."""

    def test_monitor_function_sync(self):
        """Test monitor_function decorator with sync function."""
        metrics = get_metrics()
        before = sum(
            v._value.get()
            for v in metrics.business_operations_total._metrics.values()
        )

        @monitor_function("test_operation", "test_service")
        def test_func():
            return "success"

        result = test_func()
        assert result == "success"

        after = sum(
            v._value.get()
            for v in metrics.business_operations_total._metrics.values()
        )
        assert after > before

        # Verify duration metrics were recorded
        duration_samples = list(
            metrics.business_operation_duration_seconds._metrics.values()
        )
        assert len(duration_samples) > 0

    @pytest.mark.asyncio
    async def test_monitor_function_async(self):
        metrics = get_metrics()
        before = sum(
            v._value.get()
            for v in metrics.business_operations_total._metrics.values()
        )

        @monitor_function("test_operation", "test_service")
        async def test_func():
            await asyncio.sleep(0.01)
            return "success"

        result = await test_func()
        assert result == "success"

        after = sum(
            v._value.get()
            for v in metrics.business_operations_total._metrics.values()
        )
        assert after > before

        duration_samples = list(
            metrics.business_operation_duration_seconds._metrics.values()
        )
        assert len(duration_samples) > 0

    def test_monitor_function_with_exception(self):
        metrics = get_metrics()
        before = sum(
            v._value.get() for v in metrics.errors_total._metrics.values()
        )

        @monitor_function("test_operation", "test_service")
        def test_func():
            raise ValueError("Test error")

        import pytest

        with pytest.raises(ValueError):
            test_func()

        after = sum(
            v._value.get() for v in metrics.errors_total._metrics.values()
        )
        assert after > before


class TestMetricsEndpoint:
    """Test cases for metrics endpoint."""

    def test_metrics_endpoint(self):
        """Test metrics endpoint creation."""
        from unittest.mock import Mock

        from fastapi import Request

        # Create a mock request
        mock_request = Mock(spec=Request)

        response = metrics_endpoint(mock_request)
        assert response.status_code == 200
        assert (
            response.headers["content-type"]
            == "text/plain; version=0.0.4; charset=utf-8"
        )
        assert len(response.body) > 0


if __name__ == "__main__":
    pytest.main([__file__])
